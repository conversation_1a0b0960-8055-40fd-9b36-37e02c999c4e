apply plugin: 'com.android.application'

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    defaultConfig {
        applicationId "com.ainirobot.home"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 14000
        versionName "1.40.0"
        testInstrumentationRunner rootProject.ext.testInstrumentationRunner

        ndk {
            abiFilters 'armeabi'
        }

        externalNativeBuild {
            cmake {
                cppFlags ""
                abiFilters 'armeabi'

            }
        }
    }

    sourceSets {
        main {
            jni.srcDirs = []
        }
    }

    signingConfigs {
        signConfig {
            keyAlias 'platform'
            keyPassword 'android'
            storeFile file('debug.keystore')
            storePassword 'android'
        }

        signConfig_845 {
            keyAlias 'AiniBox'
            keyPassword 'AiniRobot@9102'
            storeFile file('platform.keystore')
            storePassword 'AiniRobot@9102'
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.signConfig_845
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            signingConfig signingConfigs.signConfig_845
            debuggable true
        }
        release_821 {
            signingConfig signingConfigs.signConfig
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug_821 {
            signingConfig signingConfigs.signConfig
            debuggable true
        }
    }

//    externalNativeBuild {
//        cmake {
//            path "CMakeLists.txt"
//        }
//    }

    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
        disable 'MissingTranslation'
    }

    dataBinding {
        enabled = true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    packagingOptions {
        doNotStrip '**/*.so'
    }
}

def GLIDE_VERSION = "4.9.0"
dependencies {
    //noinspection GradleCompatible
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    androidTestImplementation('com.android.support.test.espresso:espresso-core:2.2.2', {
        exclude group: 'com.android.support', module: 'support-annotations'
    })

    implementation 'com.android.support:appcompat-v7:28.0.0'
    implementation 'com.google.code.gson:gson:2.7'
    implementation 'com.android.support.constraint:constraint-layout:1.0.2'
    implementation 'com.android.support:support-v4:28.0.0'
    implementation 'com.android.support:design:28.0.0'
    implementation 'com.android.support:recyclerview-v7:28.0.0'
    implementation 'com.github.bumptech.glide:glide:3.8.0'
    implementation 'jp.wasabeef:glide-transformations:2.0.1'
    testImplementation 'junit:junit:4.12'

    // webpdecoder
    implementation 'org.jdeferred.v2:jdeferred-android-aar:2.0.0-beta1'
    implementation "com.zlc.glide:webpdecoder:1.4.${GLIDE_VERSION}"
    // glide 4.6.1~4.9.0 (exclude broken version 4.6.0, 4.7.0)
    implementation "com.github.bumptech.glide:glide:${GLIDE_VERSION}"
    annotationProcessor "com.github.bumptech.glide:compiler:${GLIDE_VERSION}"
    //    debugCompile 'com.squareup.leakcanary:leakcanary-android:1.5'
    //    releaseCompile 'com.squareup.leakcanary:leakcanary-android-no-op:1.5'
    //    testCompile 'com.squareup.leakcanary:leakcanary-android-no-op:1.5'
    implementation 'com.liulishuo.filedownloader:library:1.7.7'
    implementation 'io.reactivex.rxjava3:rxjava:3.1.5'
    implementation 'io.reactivex.rxjava3:rxandroid:3.0.2'
}
