# For more information about using CMake with Android Studio, read the
# documentation: https://d.android.com/studio/projects/add-native-code.html

# Sets the minimum version of CMake required to build the native library.

cmake_minimum_required(VERSION 3.4.1)

#set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${PROJECT_SOURCE_DIR}\\..\\jniLibs\\${ANDROID_ABI})
set(ProjectRoot C:\\Users\\<USER>\\Desktop\\321Demo\\Demo_321\\RobotService\\ModuleApp)
#set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${ProjectRoot}\\app\\src\\main\\jniLibs\\${ANDROID_ABI})
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${ProjectRoot}\\app\\libs\\${ANDROID_ABI})

# Creates and names a library, sets it as either STATIC
# or SHARED, and provides the relative paths to its source code.
# You can define multiple libraries, and <PERSON><PERSON><PERSON> builds them for you.
# <PERSON><PERSON><PERSON> automatically packages shared libraries with your APK.

add_library( # Sets the name of the library.
             native-lib

             # Sets the library as a shared library.
             SHARED

             # Provides a relative path to your source file(s).
             src/main/cpp/native-lib.cpp )

#测试canCtrl library
add_library(sctrldevice-32 SHARED IMPORTED)
set_target_properties(sctrldevice-32 PROPERTIES IMPORTED_LOCATION ${ProjectRoot}\\app\\libs\\${ANDROID_ABI}\\libsctrldevice-32.so)

#----------------------

# Searches for a specified prebuilt library and stores the path as a
# variable. Because CMake includes system libraries in the search path by
# default, you only need to specify the name of the public NDK library
# you want to add. CMake verifies that the library exists before
# completing its build.

find_library( # Sets the name of the path variable.
              log-lib

              # Specifies the name of the NDK library that
              # you want CMake to locate.
              log )

# Specifies libraries CMake should link to your target library. You
# can link multiple libraries, such as libraries you define in this
# build script, prebuilt third-party libraries, or system libraries.

target_link_libraries( # Specifies the target library.
                       native-lib

                       sctrldevice-32
                       # Links the target library to the log library
                       # included in the NDK.
                       ${log-lib} )