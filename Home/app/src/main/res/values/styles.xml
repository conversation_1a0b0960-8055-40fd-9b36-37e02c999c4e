<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!--activity animation [begin]-->

    <!--activity animation [end]-->

    <!--OTADialog背景全透明无边框theme -->
    <style name="OTADialog" parent="android:style/Theme.Dialog">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowIsFloating">true</item><!--是否浮现在activity之上-->
    </style>

    <style name="Wake_Word_Dialog" parent="android:style/Theme.Dialog">
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
        <item name="android:windowBackground">@drawable/shape_wake_word_display</item>
    </style>

    <style name="DialogToast" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
        <!-- Dialog 彈出不變暗 -->
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <style name="WindowAnim" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/slide_from_right</item>
        <item name="android:windowExitAnimation">@anim/slide_to_right</item>
    </style>

    <style name="globalDialogStyle" parent="android:Theme.DeviceDefault.NoActionBar.Fullscreen">
        <item name="android:imeOptions">flagNoExtractUi</item>
    </style>

    <style name="CustomNumberStyle" parent="android:Widget.Material.NumberPicker">
        <item name="android:textSize">60px</item>
        <item name="solidColor">@android:color/transparent</item>
        <item name="selectionDivider">@drawable/number_picker_divider_material</item>
        <item name="selectionDividerHeight">1px</item>
        <item name="selectionDividersDistance">110px</item>
        <item name="internalMinWidth">64dp</item>
    </style>


</resources>