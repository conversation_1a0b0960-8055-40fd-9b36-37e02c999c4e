<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="ArcViewStyle">
        <attr name="arc_color" format="color|reference"/>
        <attr name="arc_stroke_width" format="reference|dimension"/>
        <attr name="arc_sweep_angle" format="integer|float"/>


    </declare-styleable>

    <declare-styleable name="FinishViewStyle">
        <attr name="finish_view_duration" format="integer"/>
        <attr name="finish_view_outer_square_width" format="dimension|reference"/>
        <attr name="finish_view_color" format="color|reference"/>
        <attr name="finish_view_stroke_width" format="dimension|reference"/>

    </declare-styleable>

    <declare-styleable name="DrawableTextViewStyle">
        <attr name="drawable_text" format="string|reference"/>
        <attr name="drawable_image" format="reference"/>
        <attr name="drawable_text_size" format="reference|dimension|integer"/>
        <attr name="drawable_text_color" format="reference|color"/>
        <attr name="drawable_image_size" format="reference|integer|dimension"/>
    </declare-styleable>


    <!--for VerticalTextview-->
    <declare-styleable name="VerticalTextView">
        <attr name="delayStart" format="integer" />
        <attr name="resetOnFinish" format="boolean" />
        <attr name="step" format="integer" />
        <attr name="speed" format="integer" />
    </declare-styleable>


    <!--for RoundImageView-->
    <declare-styleable name="RoundImageView">
        <attr name="borderRadius" format="integer" />
        <attr name="radiusType"  format="integer"/>
    </declare-styleable>

    <!--for PlayerView-->
    <declare-styleable name="PlayerView">
        <attr name="resType">
            <flag name="emoji" value="0x0001"/>
            <flag name="image" value="0x0002"/>
            <flag name="video" value="0x0004"/>
            <flag name="text" value="0x0008"/>
            <flag name="webpage" value="0x0010"/>
            <flag name="letter" value="0x0020"/>
            <flag name="view" value="0x0040"/>
            <flag name="fragment" value="0x0080"/>
            <flag name="float_fragment" value="0x0100"/>
            <flag name="speech" value="0x0200"/>
            <flag name="audio" value="0x0400"/>
        </attr>
        <attr name="playerType" >
            <flag name="main_player" value="0"/>
            <flag name="float_player" value="1"/>
            <flag name="other_player" value="2"/>
        </attr>
    </declare-styleable>

    <!-- NumberPicker -->
    <declare-styleable name="NumberPicker">
        <!-- @hide Color for the solid color background if such for optimized rendering. -->
        <attr name="solidColor" format="color|reference" />
        <!-- @hide The divider for making the selection area. -->
        <attr name="selectionDivider" format="reference" />
        <!-- @hide The height of the selection divider. -->
        <attr name="selectionDividerHeight" format="dimension" />
        <!-- @hide The distance between the two selection dividers. -->
        <attr name="selectionDividersDistance" format="dimension" />
        <!-- @hide The min height of the NumberPicker. -->
        <attr name="internalMinHeight" format="dimension" />
        <!-- @hide The max height of the NumberPicker. -->
        <attr name="internalMaxHeight" format="dimension" />
        <!-- @hide The min width of the NumberPicker. -->
        <attr name="internalMinWidth" format="dimension" />
        <!-- @hide The max width of the NumberPicker. -->
        <attr name="internalMaxWidth" format="dimension" />
        <!-- @hide The layout of the number picker. -->
        <attr name="internalLayout" format="reference" />
        <!-- @hide The drawable for pressed virtual (increment/decrement) buttons. -->
        <attr name="virtualButtonPressedDrawable" format="reference" />
        <!-- @hide If true then the selector wheel is hidden until the picker has focus. -->
        <attr name="hideWheelUntilFocused" format="boolean" />

        <attr name="minScaleRatio" format="float"></attr>
    </declare-styleable>
    <attr name="numberPickerStyle" format="reference" />
</resources>