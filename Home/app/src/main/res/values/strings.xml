<resources>
    <string name="app_name">Home</string>
    <!--OTA-->
    <string name="find_new_version">New version has been found</string>
    <string name="ota_upgrade_content">1.AR Photo Function added\n2.Bugs fixed\n3.Beta version upgraded 1\n4.Beta version upgraded 2\n5.Beta version upgraded 3\n 6.Beta version upgraded 4</string>
    <string name="ota_upgrade_complete">The robot will restart after the update. Please don\'t shutdown.</string>
    <string name="upgrade">Update</string>
    <string name="charge">Recharge</string>
    <string name="starting_upgrade">Downloading Update Package…%1$d%%</string>
    <string name="upgrade_complete_restart">The robot will restart after the update. Please don\'t shut down the robot.</string>
    <string name="mobile_data_confirm_upgrade">Are you sure you want to download the update package using mobile data?</string>
    <string name="no_tip">No Reminder</string>
    <string name="tip_text">Reminder</string>
    <string name="ota_rollback_success">Rollback successful</string>
    <string name="ota_rollback_fail">Rollback failed. Please call customer service for help 400&#8211;898&#8211;7779</string>
    <string name="inspect_err_title">The robot encountered an exception. \nPlease try to shut down and restart the robot.</string>
    <string name="charge_lower_power_going_tts">I am going to recharge</string>
    <string name="charge_lower_power_title_des_saiph">Please help me recharge.</string>
    <string name="charge_lower_power_auto_charge">Auto-Recharge (%1$ss)</string>
    <string name="charge_lower_power_would_shut_down">Battery is critically low. The robot is going to shutdown automatically</string>
    <string name="charge_stop_dialog_title">I am charging now</string>
    <string name="charging_text">Recharging</string>
    <string name="charging_warning_dialog_text_saiph">Please keep calm, and try to recover the robot with the following methods:</string>
    <string name="charging_warning_dialog_text">The instruction light on the charging pole will be green when the charging state is normal. \n Otherwise, please check if the following problems exist：</string>
    <string name="charging_warning_dialog_reason_saiph">1. Please check the connection with the electric outlet. \n2. Please check the charger connectors. \n3. Plug in the charger again. \n4. Contact Customer Service for help. </string>
    <string name="charge_fail">Self-charging failed</string>
    <string name="charge_success_voice">Self-charging successful</string>
    <string name="charge_pile_success">Setting successful. Starting to recharge</string>
    <string name="charge_pile_success_wc">Charging pole Setting successful</string>
    <string name="charge_fail_go_pile">Auto-charging failed. Please push me to the charging pole.</string>
    <string name="charge_fail_not_exist_target_pose">Charging failed, please set charging pole in map tool </string>
    <string name="tts_check_chargepile_is_moved">Repositioning failed because the charging pole has been physically moved or doesn\'t match the robot.</string>
    <string name="reposition_title_qrCode">Please push me to find the nearest Coded Target</string>
    <string name="reposition_noset_charge_position">Charging pole has not been set yet.</string>
    <string name="reposition_reset_failed_qrcode">Repositioning failed. Please push me to another Coded Target to retry.</string>
    <string name="reposition_vision_fail_title">Automatic repositioning failed</string>
    <string name="reposition_vision_success">Automatic repositioning successful</string>
    <string name="reposition_manual_success">Repositioning successful</string>
    <string name="reposition_fail">Repositioning failed</string>
    <string name="emergency_describe">After confirming the security, please follow the instructions to restore</string>
    <string name="standby_switchHint_start">Please press the function button behind the host</string>
    <string name="push_map_detail">"The map is updating, please don't move the robot."</string>
    <string name="volume_hight">Volume has reached maximum</string>
    <string name="volume_low">Volume has reached minimum</string>
    <string name="volume_up_done">Volume Increased</string>
    <string name="volume_down_done">Volume Decreased</string>
    <string name="volume_set_done">Volume Setting Successful</string>
    <string name="not_support_chinese_placeholder">Chinese Characters are not supported for now: %1$s</string>
    <string name="charge_low_tips">The battery is critically low and I am going to shutdown automatically. Please push me to the charging pole.</string>
    <string name="reposition_remote_title">Remote Repositioning</string>
    <string name="reposition_tts_turn_circle">Repositioning now. Please don\'t gather around me</string>
    <string name="reposition_vision_relocate_success">Automatic repositioning successful</string>
    <string name="wake_up_not_conform_rules">Wake-up word format invalid. Please view details in the instruction.</string>
    <string name="bind_tts_1">Activation successful, I am %1$s</string>
    <string name="speech_status_navi_out_map_please_edit">Destination is unreachable. Please clear obstacles or reposition.</string>
    <string name="speech_status_navi_global_path_failed_please_edit_map">Destination is unreachable. Please check the map or push the robot to a passable area.</string>
    <string name="string_main_close_emergency">Restore</string>
    <string name="string_main_ota_success">Update successful</string>
    <string name="anchor_content">Positioning Point is a place that is set by technical support during map creation so that the robot can find its position when it is lost. If you have questions about where the positioning spot is, you can contact customer service for help.</string>
    <string name="anchor_subTitle">Please check if any of problems listed below exists：</string>
    <string name="anchor_subContent">· Please check whether the positioning spot has been moved physically. \n· Please check if the robot is facing the correct direction.</string>
    <string name="wait_loading">Factory Resetting. Please Wait.</string>
    <string name="password_error">Password Incorrect</string>
    <string name="remote_control">Remote Control</string>
    <string name="bind_robot_prompt">Please go to the management platform — Settings — Corporate info — Activate Robots Manually and enter the info below</string>
    <string name="reposition_qrcode_reminder_subtitle">Please push me <font color="#FFFFFF">slowly</font> along the road to look for Coded Targets above</string>
    <string name="reposition_qrcode_reminder_tts1">Welcome to the Intelligent Service Robot. Please push me under Coded Targets.</string>
    <string name="reposition_qrcode_reminder_tts2">Welcome to the Intelligent Service Robot. Please push me along the road to look for Coded Targets above.</string>
    <string name="reposition_qrcode_anchor_reminder_subtitle">Please push the robot to the positioning spot and make sure the robot is facing the correct direction. Tap <font color="#FFFFFF">“Start Positioning”</font> to reposition</string>
    <string name="reposition_qrcode_reminder_tts">Welcome to the Intelligent Service Robot. Please push the robot under Coded Targets, and tap start positioning. Please make sure to keep the front clear.</string>
    <string name="shutdown_tips"><Data><![CDATA[Shut Down In<font color="#FF713C">%1$d</font>sec]]></Data></string>
    <string name="shutdown_navi_tips">The power is supposed to be off now in accordance with the shutdown timer.</string>
    <string name="shutdown_start_tts">I am going to shut down</string>
    <string name="auto_charge_low_battery_tip">The battery is critically low. I am about to shut down automatically. Dear Human, please help me recharge.</string>
    <string name="charge_success_title">Please make sure the robot position is aligned with the red line.</string>
    <string name="charge_success_complete_title">Charging Pole Setting Successful </string>
    <string name="charge_start_point_title">Please push the robot back onto the charging pole</string>
    <string name="ota_upgrade_no_complete">Please make sure the battery is over 50%</string>
    <string name="ota_upgrade_no_complete_saiph">Please make sure the battery is over 20%</string>
    <string name="ota_upgrade_no_complete_mini">Please make sure the battery is over 30%</string>
    <string name="upgrade_next_time">Next Time</string>
    <string name="upgrade_now">Auto %1$s（%2$dS）</string>
    <string name="force_upgrade_now">About to %1$s（%2$dS）</string>
    <string name="ota_wait">Preparing to Update</string>
    <string name="pause_download">Paused…%1$d%%</string>
    <string name="installing_upgrade">Installing Update. Please Wait.…</string>
    <string name="night_mobile_data">Use mobile data even during nighttime automatic update.</string>
    <string name="dialog_confirm">Confirm</string>
    <string name="ota_continue">Continue</string>
    <string name="ota_pause">Pause</string>
    <string name="ota_warning">Please release the power button immediately\n Otherwise it may cause damage to the robot.</string>
    <string name="ota_start">Start Updating</string>
    <string name="ota_success">Update Successful</string>
    <string name="ota_fail_rollback">Update failed. Rolling back to the previous version.</string>
    <string name="ota_fail_no_rollback">Updating Failed</string>
    <string name="ota_fail_download">Downloading Failed. Please Retry</string>
    <string name="ota_log_battery">Battery Is Low</string>
    <string name="ota_can_not_use">Updating. Not available for use.</string>
    <!--自检-->
    <string name="inspect_err_service_number">Customer HotLine：400&#8211;898&#8211;7779</string>
    <string name="inspect_err_error">Error Info</string>
    <string name="inspection_bottom_title">Starting up. It may take a while</string>
    <string name="inspect_running">Self Inspecting…</string>
    <string name="inspect_err_navigation_log_id">Error ID：\n</string>
    <string name="inspect_err_navigation_log_id_land">Error ID：</string>
    <string name="inspect_err_robot_sn">Robot SN：\n</string>
    <string name="inspect_err_robot_sn_land">Robot SN：</string>
    <string name="inspect_err_qr_code_tips">After taking a photo or scanning the QR Code for feedback, \n you can try to recover the robot by restarting manually.</string>
    <string name="inspect_err_feedback_tips">After giving feedback, \n you can try to recover the robot by restarting manually.</string>
    <string name="inspect_err_speech_tips">The robot encountered an exception. Please try to shut down and restart the robot.</string>
    <string name="inspect_err_btn_shutdown">Shut down</string>
    <string name="inspect_err_qr_code_tips_saiph">You can shut it down first, then restart it.</string>
    <string name="inspect_err_feedback_tips_saiph">You can shut it down first, then restart it.</string>
    <!-- charge -->
    <string name="charge_setting">Setting…</string>
    <string name="charge_fail_complete_title">Setting Failed</string>
    <string name="charge_fail_title">Auto Recharging Failed</string>
    <string name="charge_fail_des">Please push me back to the charging pole.</string>
    <string name="wire_charge_des_locate_tips">I\'m out of power. Please help me connect the power cord.</string>
    <string name="charge_fail_des_locate">Please push me slowly back to my positioning point.</string>
    <string name="charge_fail_des_not_exist_target_pose">Please set charging pole in map tool</string>
    <string name="charge_confirm">Confirm(%1$ss)</string>
    <string name="charge_lower_power_title_des">Returning to Charging Pole</string>
    <string name="charge_lower_power_title_des_locate">Returning to charging point.</string>
    <string name="charge_stop_dialog_content">Stop Charging?</string>
    <string name="charge_is_going_charge_pile">Heading to the charging pole.</string>
    <string name="charge_is_going_locate_pile">Heading to the positioning point</string>
    <string name="charging_warning_subText">Please check the charger.</string>
    <string name="charging_level">00%</string>
    <string name="charging_warning_title">Charging Rate Is Slow.</string>
    <string name="charging_warning">View Suggestions</string>
    <string name="charging_warning_dialog_title">Charging Rate Is Slow.</string>
    <string name="charging_warning_dialog_reason_1">· Charging area is too small. Please retry. \n· Charger is dirty. Please use paper tissue to wipe it after disconnecting the charging pole.</string>
    <string name="charging_warning_confirm">Confirm</string>
    <string name="charging_warning_stop_charging">Oops, charging is too slow. I am going to recharge</string>
    <string name="charge_pile_fail">Setting Charging Pole Failed</string>
    <string name="charge_fail_retry">Automatic Charging Failed, Retrying…</string>
    <string name="please_check_reason">Please check if the problems below exist：</string>
    <string name="dot_point">·</string>
    <string name="reason_loss_error_large">Positioning error is too large. Please check the position of the charging pole on the map.</string>
    <string name="reason_map_wrong">Map Error. Please check if the map matches correctly.</string>
    <string name="reason_chargepile_moved">Charging pole has been physically moved.</string>
    <string name="reason_multi_chargepile">Multiple charging poles exist. Please retry at the charging pole that matches the robot.</string>
    <string name="reason_reboot">Sorry, Positioning failed due to the connection error. Please restart the robot.</string>
    <string name="tts_check_chargepile_loc">Positioning failed. Please check the position of the charging pole on the map.</string>
    <string name="tts_fail_common">Sorry, positioning failed. Please restart the robot or retry in the robot settings.</string>
    <!--<string name="charge_pile">充电桩</string>-->
    <!--reposition-->
    <string name="reposition_title">Robot Positioning</string>
    <string name="reposition_title_loading">Loading...</string>
    <string name="reposition_anchor_title_qrCode">Please push me to the positioning point.</string>
    <string name="qrcode_reposition_without_map_title">Map Loading Failed</string>
    <string name="reposition_not_anchor_title_qrCode">Positioning Point Undetected</string>
    <string name="reposition_button">Positioning(%1$ss)</string>
    <string name="reposition_noset_locate_position">No positioning point has been set</string>
    <string name="reposition_reset_failed">Repositioning Failed. Please Retry.</string>
    <string name="reposition_reset_remind_msg">Please push me to the charging pole.</string>
    <string name="reposition_reset_remind_msg_locate">Push me to the positioning point</string>
    <string name="reposition_reset_success">Repositioning Successful</string>
    <string name="qrcode_reposition_success">Positioning Successful</string>
    <string name="qrcode_reposition_success_tts">I found my way. I can start working now.</string>
    <string name="qrcode_anchor_point_tts">Please push me to the positioning point and make sure I am facing the same direction as the positioning point. </string>
    <string name="reposition_reset_vision_failure">Automatic Positioning Failed</string>
    <string name="reposition_vision_show_title">Automatic Positioning Processing…</string>
    <string name="reposition_vision_show_subtitle">Please don\'t gather around me</string>
    <string name="reposition_failure_vision_retry">Retry</string>
    <string name="reposition_failure_manaul">Positioning by Charging Pole</string>
    <string name="reposition_doing">Positioning...</string>
    <string name="reposition_fail_des">Please contact customer service</string>
    <string name="locate_pile_reposition">Use Positioning Point to Reposition</string>
    <!--emergency-->
    <string name="emergency_title">Emergency Stop Button Pressed</string>
    <string name="time_warning_title">Incorrect System Time</string>
    <string name="time_warning_describe">Please connect to the Internet to synchronize now.</string>
    <string name="time_warning_set_network_button">Set Up Network</string>
    <string name="time_warning_skip_button">Later</string>
    <string name="time_warning_skip_always_button">Ignore until Restart</string>
    <string name="calibration_tip">Calibrating the depth sensor\nIt takes about 3 minutes…</string>
    <!--standby-->
    <string name="standby">Sleeping</string>
    <string name="standby_hint">Please don\'t push the robot during sleep mode.</string>
    <string name="standby_charging">Charging %1$d%%</string>
    <string name="standby_battery">Battery %1$d%%</string>
    <string name="standby_charging_slowly">Charging Rate is Slow %1$d%%</string>
    <string name="exit_standby">Wake Up</string>
    <string name="standby_restoring">Recovering (%1d)</string>
    <string name="standby_energency">Emergency Stopped…</string>
    <string name="standby_switchHint_end">to wake up robot</string>
    <!--lock-->
    <string name="lock_device">Your robot has been locked</string>
    <string name="contact_owner">Please contact the account manager to unlock the robot.</string>
    <string name="warning">Warning</string>
    <string name="lock_warning">The robot is going to be locked. Please contact the account manager in time.</string>
    <string name="push_map_ok">OK</string>
    <!-- currency -->
    <string name="cancel">Cancel</string>
    <string name="serch_sign">Please look for Coded Targets along the road.</string>
    <!-- volume -->
    <string name="volume_set_ignore_hint">Sorry, authority is required.</string>
    <!-- wakeup word -->
    <string name="delete">Delete</string>
    <string name="no_deletable_word">No wake-up word can be deleted.</string>
    <string name="delete_success">Deleted</string>
    <string name="delete_failture">Failed</string>
    <string name="wake_up_need_chinese">Wake-up word must be 2~6 Chinese characters.</string>
    <string name="set_success">Setting Completed</string>
    <string name="set_failture">Setting Failed</string>
    <string name="polyphone_select_placeholder">Polyphone Character Selection: %1$s</string>
    <string name="rollback_upgrade">Updating failed. Rolling back to the previous version…</string>
    <string name="ota_continue_fail">Previous update failed. About to start recovering.</string>
    <string name="ota_continue_install">Previous update is uncomplete. The robot is about to continue updating.</string>
    <string name="ota_continue_rollback">Previous rollback is uncomplete. The robot is about to continue rolling back.</string>
    <string name="ota_inspection_failed">Updating Failed</string>
    <string name="ota_confirm">Confirm (%1$d)</string>
    <string name="charge_lower_power_title">Battery is low and needs to be charged immediately.</string>
    <string name="charge_lower_power_title_for_saiph">Battery Is Low</string>
    <string name="charge_need_estimate_first">Please start positioning first.</string>
    <string name="charge_success">Connected to the charging pole.</string>
    <!--obstacles avoid-->
    <string name="excuse_me_0">Please make way for me.</string>
    <string name="excuse_me_1">Excuse me.</string>
    <string name="excuse_me_2">Could you please make way for me?</string>
    <string name="excuse_me_3">Please let me do my work.</string>
    <string name="excuse_me_4">Could you please move a little bit?</string>
    <string name="reposition_please_switch_map">Please switch the map first.</string>
    <string name="reposition_reposition">Start Positioning</string>
    <string name="retry_reposition">Reposition</string>
    <string name="qrcode_reposition_without_map_reboot">Restart Now</string>
    <string name="reposition_remote_estimate_success">Remote Positioning Succeeded</string>
    <string name="reposition_remote_estimate_failure">Remote Positioning Failed</string>
    <string name="reposition_remote_estimate_time_out">Remote Positioning Timed Out</string>
    <string name="reposition_vision_fail_subtitle">Please push me to the charging pole.</string>
    <string name="reposition_dialog_reposition">Continue to Reposition</string>
    <string name="give_up">Give Up</string>
    <string name="custom_wake_up_word">Customize Wake-Up Word</string>
    <string name="support_text_length"><font size="10">2-6 chinese characters are allowed.</font></string>
    <string name="inspect_bind_fail_up">The robot is not activated.</string>
    <string name="inspect_bind_fail_down">No Authorization</string>
    <string name="inspect_to_bind">Activate</string>
    <string name="inspect_restore_setting">Factory Reset</string>
    <string name="inspect_not_clear_data">Tapping the Activate button will not cause the local data to be deleted. </string>
    <string name="wx_qr_code_title">Scan the QR Code with Wechat to activate.</string>
    <string name="wx_qr_code_tts">Please scan the QR Code with Wechat to activate me.</string>
    <string name="wx_qr_code_loading">QR Code Requesting…</string>
    <string name="wx_qr_code_loading_success">Please scan the QR Code using Wechat to activate the robot.</string>
    <string name="wx_qr_code_loading_fail">Requesting QR Code failed</string>
    <string name="wx_qr_code_load_retry">Retry</string>
    <string name="wx_qr_code_load_retry_again">Request again</string>
    <string name="wx_qr_code_load_retry_again_des">QR Code Expired</string>
    <string name="bind_success_title">Congratulations! Bind Successful. The robot has been activated.</string>
    <string name="bind_success_has_map">OK</string>
    <!-- onStatusUpdate speech text -->
    <string name="speech_status_navi_avoid">Oops, you are standing in the way to my destination. Could you please make way for me?</string>
    <string name="speech_is_charging">I am charging, please come back to me later.</string>
    <string name="bind_code_for_pc">Activated by PC：</string>
    <string name="sn_number_for_pc">Last 6 digits of Robot SN:</string>
    <string name="string_main_emergency">Emergency Stop</string>
    <string name="string_main_ota">Update</string>
    <string name="string_main_set_charge_pile">Set Charging Pole</string>
    <string name="string_main_go_charge">Go to Recharge</string>
    <string name="string_main_charging">Recharge</string>
    <string name="string_main_not_charge">Stop Charging</string>
    <string name="reposition_cancel_dialog">Give up positioning? \n The robot will not be able to navigate.</string>
    <string name="anchor_title">What is a positioning point?</string>
    <string name="anchor_ensure">Understood</string>
    <string name="speech_charging_warning">Oops, the charging rate is slow. Please help me.</string>
    <string name="radar_is_opening">Radar Activating…</string>
    <string name="radar_open_success">Radar Activated Successfully</string>
    <string name="situ_service_end">In-Situ Service Has Ended</string>
    <string name="situ_service_start">In-Situ Service Has Started.</string>
    <string name="auto_situ_service_start">The environment is too crowded. I am about to enter Situ Service mode</string>
    <string name="hw_state_disconnected">The systems below are in an abnormal state.\n Trying to recover. It may take a few minutes.\n\n  If the process lasts too long, please restart the robot.</string>
    <string name="hw_state_head_error">1.Vision System Error</string>
    <string name="hw_state_chassis_error">1.Chassis System Error</string>
    <string name="hw_state_all_error">1.Vision System Error;\n\n 2.Chassis System Error.\n</string>
    <string name="hw_state_timer">Count Down：(%1$ds)</string>
    <string name="hw_state_reboot">Restart Manually</string>
    <string name="adk_questions">Ask Questions</string>
    <string name="response_lite_lite_page">Please wait until I finish charging.</string>
    <string name="reset">Factory Reset</string>
    <string name="fail">Failed</string>
    <string name="reset_fail">Factory Reset Failed. Please Restart Manually.</string>
    <string name="reboot">Restart The Robot (%1$ss)</string>
    <string name="input_password">Please Enter Password</string>
    <!--以下为未替换的繁体中文信息-->
    <string name="dormancy_text">The robot will enter sleep mode after（%1$sS）.</string>
    <string name="dormancy_play_tts">The robot is going to enter sleep mode</string>
    <string name="dormancy_loading">Entering Sleep Mode</string>
    <string name="dormancy_fail">Failed</string>
    <string name="confirm">Confirm</string>
    <string name="ota_version">1.03</string>
    <string name="bind_robot">Activate the Robot</string>
    <string name="last_sn">Last 6 digits of robot SN</string>
    <string name="binding_code">Binding Code</string>
    <string name="binding_code_expired">Binding Code Expired</string>
    <string name="refresh_code">Refresh Code</string>
    <string name="reposition_anchor_point_exist">Reposition With Another Method</string>
    <string name="reposition_qrcode_reminder_without_map">Please try to restart the robot to recover. If the recovery failed, please contact customer service for help.</string>
    <!-- 定时关机 -->
    <string name="shutdown_now">Shutdown Now</string>
    <string name="shutdown_cancel">Cancel</string>
    <string name="shutdown_title">Shutdown Timer</string>
    <string name="shutdown_speech">The robot will shut down in %1$d sec</string>
    <string name="shutdown_navi_failed">Heading to %1$s Failed</string>
    <string name="shutdown_navi_succeed">%1$s Arrived</string>
    <string name="reception_pose">Reception Point</string>
    <string name="go_location_pose"><Data><![CDATA[Heading to<font color="#FFFFFF">%1$s</font>to Shut Down]]></Data></string>
    <string name="hw_chassis_disconnected">Chassis Error</string>
    <string name="hw_auto_recovery">Trying to Recover…</string>
    <string name="hw_retry_recovery">Recovering(%1$ds)</string>
    <string name="hw_all_error_info">Vision system and chassis system error.</string>
    <string name="hw_head_error_info">Vision system error</string>
    <string name="hw_recovery_suc">Recovery succeeded</string>
    <string name="hw_recovery_fail">Recovery failed</string>
    <string name="hw_recovery_fail_des">Recovery failed. Please tap the button below to restart the robot manually.</string>
    <string name="remote_control_navigation">Heading to</string>
    <string name="inspect_err_after_sales_number">Customer hotline：400&#8211;898&#8211;7779</string>
    <string name="shutdown_speech_delivery">The robot will shut down in %1$d seconds</string>
    <string name="shutdown_speech_reminder">The robot will shut down in %1$d seconds. Please don\'t forget to recharge. </string>
    <string name="positioning_spot">Positioning Point</string>
    <string name="stand_by_spot">Stand-by Point</string>
    <!-- 地图漂移 -->
    <string name="map_drift_cannot_estimate">Unable to reposition precisely.</string>
    <string name="map_drift_robot_was_pushed">The robot has been moved abnormally. Please help the robot with repositioning.</string>
    <string name="map_drift_check_radar_status">Inspecting Radar State</string>
    <string name="map_drift_check_estimate_status">Inspecting Positioning State</string>
    <string name="map_drift_reload_map">Reloading the map</string>
    <string name="map_drift_reload_map_fial">Loading map failed. Please restart the robot.</string>
    <string name="map_drift_start_reposition">Start Repositioning</string>
    <string name="charge_timing_title">Scheduled charging is about to start.</string>
    <string name="charge_timing_describe">Go to the charging pole in %1$s seconds.</string>
    <string name="default_language_changed">%1$s language package has gone offline. It will automatically switch to %2$s</string>
    <string name="language_chinese">Chinese</string>
    <string name="language_english">English</string>
    <string name="language_korean">Korean</string>
    <string name="language_japanese">Japanese</string>
    <!-- 出地图-->
    <string name="map_outside_enter">Robot Has Left The Map Area</string>
    <string name="out_map_warning_alert_title">ALERT!</string>
    <string name="out_map_warning_alert_message">Please Don\'t Push Me</string>
    <string name="please_charging_robot">Please remember to recharge me.</string>
    <string name="push_map_title">Map Updating…</string>
    <string name="push_map_subtitle">Please don\'t move the robot during an update.</string>
    <string name="push_map_success">Map Update Successful</string>
    <string name="push_map_fail">Map Update Failed</string>
    <!-- PSB硬件异常-->
    <string name="hw_psb_error">Critical Error</string>
    <string name="please_try_to_recover">Please try the followings:</string>
    <string name="psb_recover_step1">Step 1: Press and hold the power button for 20 seconds to force the power off.</string>
    <string name="psb_recover_step2">Step 2: After the power off, press the power button again for 5 seconds to turn on.</string>
    <string name="get_help_hint">Or please contact technical support.</string>
    <string name="error_code">Error code:</string>
    <string name="psb_recover_step1_saiph">· Please protect the site and contact technical support in time</string>
    <string name="psb_recover_step2_saiph">· TEL:400&#8211;898&#8211;7779</string>
    <string name="binding_code_loading">Acquiring binding code…</string>
    <!-- BLE 锁定 -->
    <string name="ble_robot_locked_title">Robot Is Locked</string>
    <string name="ble_robot_locked_title_des">Robot is locked near the dangerous area, please contact Admin%1$s to unlock the robot</string>
    <string name="ble_tip_leave_dangerous_place">Please leave the dangerous area first</string>
    <string name="ble_robot_dangerous_tts">The robot is close to the dangerous area and has been locked by the system.</string>
    <string name="ble_exit_by_manual">Unlock By Admin</string>
    <string name="ble_danger_tts">I can\'t find the way, please contact the administrator to help me.</string>
    <!-- Wheel Over -->
    <string name="wheel_over_title">Robot wheels have repeatedly encountered problems</string>
    <string name="wheel_over_des">1. Check wheels for debris, check for problems, and then click "Restore" \n2. If the "Restore" does not resolve, try restarting the robot. \n3. If the restart is still not resolved, please contact technical support in time.</string>
    <string name="wheel_over_cancel">Cancel</string>
    <string name="wheel_over_confirm">Restore</string>
    <string name="wheel_over_again">The current wheel is still abnormal.</string>
    <!-- 多机调度异常-->
    <string name="multi_robot_error_title">The multi-machine scheduling is abnormal</string>
    <string name="multi_robot_error_message">Please take a picture of this screen and contact the after-sales staff, error code:</string>
    <string name="cancel_task">Cancel the task</string>
    <string name="restart">Restart recovery</string>
    <string name="multi_robot_map_not_match">Multi-machine map does not match</string>
    <string name="multi_robot_lora_disconnect">Multi-machine communication disconnection</string>
    <string name="multi_robot_lora_config_fail">Multi-machine information configuration error</string>
    <string name="multi_robot_version_not_match">Multi-machine version does not match</string>
    <!-- 解绑后重新激活时，云服务节点选择-->
    <string name="cloud_server_select">Cloud Server</string>
    <string name="cloud_europe">Europe Cloud</string>
    <string name="cloud_us_west">US West Cloud</string>
    <string name="cloud_other_regions">Cloud for other regions</string>
    <string name="cloud_tips">Please choose the right server that is compliant with your Enterprise Account region. Connect with technical support if you need help.</string>
    <string name="cloud_current">Current cloud server: %1$s</string>
    <string name="cloud_not_this"><u>Not this?</u></string>
    <string name="cloud_reboot">REBOOT</string>
    <string name="cloud_select_ok">OK</string>
    <string name="cloud_select_cancel">Cancel</string>
    <!-- 新增统一定位流程和多楼层定位文案-->
    <string name="remote_stop_charging_title">Robot leaving pile</string>
    <string name="remote_stop_charging_title_sub">Please don not stand in front of the robot</string>
    <string name="elevator_reposition">Please locate</string>
    <string name="elevator_reposition_choose_floor">Please choose the floor first</string>
    <string name="elevator_reposition_current_floor"><![CDATA[<font color="#FFFFFF">“%1$s”</font>is currently floor]]></string>
    <string name="elevator_reposition_try_locate">Try to locate</string>
    <string name="elevator_reposition_confirm_floor">confirm floor</string>
    <string name="elevator_reposition_charge_pile">Push back the %1$s charging pile location</string>
    <string name="elevator_reposition_charge_pile_point_not_exist">The charging pile point does not exist</string>
    <string name="elevator_reposition_switch_map_success">The map is switched successfully</string>
    <string name="elevator_reposition_switch_map_fail">Map switching failed</string>
    <string name="elevator_reposition_back_to_choose_floor">Switch floors</string>
    <string name="elevator_reposition_success">positioning success</string>
    <string name="elevator_reposition_success_des">Locate to: %1$s</string>
    <string name="elevator_reposition_fail">Repositioning failed</string>
    <string name="elevator_reposition_charge_remind_msg">Please push me to the %1$s charging pile</string>
    <string name="elevator_reposition_no_navi_map">Please confirm the map used for navigation first</string>
    <string name="elevator_reposition_not_find_locate_points">The current floor map has no locations to locate</string>
    <string name="elevator_reposition_not_find_locate_map">The current navigation map is not in the multi-floor list</string>
    <string name="relocate_back_text">Relocate</string>
    <string name="relocate_by_anchor_point">Position point locating</string>
    <string name="relocate_by_anchor_point_not_found">The current map has not detected the anchor point, please choose another positioning method</string>
    <string name="relocate_by_charge_pile">Charging pile location</string>
    <string name="relocate_by_charge_pile_not_found">The charging pile is not detected on the current map, please choose another positioning method</string>
    <string name="relocate_by_charge_pile_des">Push the robot to the charging pile set during the map building, and the positioning can be completed after charging</string>
    <string name="charge_fail_not_exist_target_pose_positioning_point">Charging failed, please set positioning point in map tool</string>
    <string name="charge_fail_des_not_exist_target_pose_positioning_point">Please set positioning point in map tool</string>
    <string name="navi_to_goal_exception">Chassis abnormality when navigating to the target point</string>
    <string name="navi_sensor_error">Navigation sensor data is abnormal</string>
    <string name="navi_sensor_error_des">Please restart the robot to restore</string>
    <string name="reposition_state_ok">Positioning is normal. No need to reposition</string>

    <string name="relocate_by_vision">Visual Positioning</string>
    <string name="not_vision_map">Non-Visual map</string>
    <string name="reposition_vision_tips">Please push me slowly along the road to find the direction</string>
    <string name="reposition_vision_reminder_title">Please push me to the positioning area for positioning!</string>
    <string name="reposition_vision_reminder_subtitle">I can\'t find my way, please push me slowly to the positioning area</string>
    <string name="map_not_compatible">The current version is low, please upgrade and try again</string>
    <string name="charging_bms_low_temp_title">Low Temperature Charging</string>
    <string name="charging_bms_low_temp_tip1">Current temperature is %s°C,it\'s in low temperature charging state</string>
    <string name="charging_bms_low_temp_tip2">To ensure battery life, please move to environment with temperature higher than 10°C for charging</string>
    <string name="low_temp_tts">Oops, the current temperature is too low to affect the charging progress, please move me to a warm environment for charging!</string>
    <string name="radar_fail">Radar failed to start</string>
    <string name="cloud_jp_server">Japan Cloud</string>

    <string name="switch_map">Switch Map</string>
    <string name="set_multi_robot_config">Configure multi-machine information</string>

    <string name="lxc_repair">LXC Repair</string>
    <string name="loading_map">Loading map, please wait</string>
    <string name="skip">Skip</string>
    <string name="remote_stop_charging_avoid_failed">There are obstacles in front of the robot.</string>
    <string name="remote_stop_charging_radar_failed">Radar failed to start</string>
    <string name="remote_stop_charging_unknown_failed">Failed to stop charging</string>
    <string name="open_electric_door">Open the door</string>
    <string name="close_electric_door">Close the door</string>
    <string name="close_electric_door_please">Please close the the door first</string>
    <string name="switch_map_loading">Switching maps...</string>
    <string name="stop_charging_tips">When the robot ends charging and leaves the charging pile, please be careful to avoid it.</string>
    <string name="reset_head_failed">Head repositioning failed, please try again</string>
    <string name="import_map_success">Import success</string>
    <string name="import_map_fail">Import fail</string>
    <string name="stop_charge_confirm">It is detected that it is not charging, please confirm whether to stop charging.</string>
    <string name="version_roll_back">Version Rollback</string>
    <string name="downgrade_failed">Downgrade failed</string>
    <string name="android_factory_reset">Android factory reset</string>
    <string name="rollback_factory_reset_tips">Rollback failed, please restore factory settings. After restoring to factory settings, the local data will be cleared, and you need to connect to the Internet to obtain the robot configuration again.</string>
    <string name="find_history_version">Discover historical versions</string>
    <string name="ota_downgrade_content">It is detected that the current version (%1$s) is higher than the historical version. Some functions will be unavailable after installation. Do you want to install the historical version?</string>
    <string name="version_detection">Version detection</string>
    <string name="charge_voice_stop_charging_err">not in charging mode</string>
    <string name="charge_receive_voice_mode_error">Charging mode error</string>
    <string name="charge_leaving_tips">Leaving the charging station, please ensure there is enough space ahead</string>
    <string name="charge_leaving_title">End charging</string>
    <string name="charge_wait_in_area">Waiting for charging</string>
    <string name="charge_wait_in_area_des">The charging station is occupied; currently queuing.</string>
    <string name="cancel_charge">Cancel charging</string>
    <string name="leave_pile_title">Leaving the charging station</string>
    <string name="go_standby_point">Heading to standby point</string>
    <string name="charge_task_has_stopped">The charging task has ended</string>
    <string name="wait_lift_title">Waiting for elevator</string>
    <string name="go_in_lift_title">Entering elevator</string>
    <string name="in_lift_title">In elevator</string>
    <string name="go_out_lift_title">Exiting elevator</string>
    <string name="stop_right_now">Stop now</string>
    <string name="please_set_main_floor">Set main floor for multi-floor charging</string>

    <!-- OTA Factory Reset Dialog -->
    <string name="ota_factory_reset_title">System Upgrade Complete</string>
    <string name="ota_factory_reset_content">To ensure optimal performance after the upgrade, it is recommended to restore factory settings. This will clear all user data and settings. Do you want to proceed?</string>
    <string name="ota_factory_reset_confirm">Restore Now</string>
    <string name="ota_factory_reset_cancel">Skip</string>

    <!-- OTA Factory Reset Dialog - Mandatory Mode -->
    <string name="ota_factory_reset_mandatory_content">Due to significant system changes in this upgrade, factory reset is required to ensure system stability. This will clear all user data and settings. Please click \"Restore Now\" to continue.</string>
    <string name="ota_factory_reset_mandatory_confirm">Restore Now</string>

    <!-- OTA Factory Reset Fragment -->
    <string name="ota_factory_reset_speech_tips">System upgrade requires factory reset to ensure optimal performance.</string>
    <string name="ota_factory_reset_tips">Warning: This operation will clear all user data and settings. Please ensure important data is backed up.</string>

</resources>
