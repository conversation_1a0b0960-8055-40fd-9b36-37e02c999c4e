<?xml version="1.0" encoding="utf-8"?>
<resources>

    <string-array name="hideList">
        <!--<item>com.ainirobot.orionuploadservice</item>-->
        <item>com.orion.os.crashhandler</item>
        <item>com.ainirobot.navigationservice</item>
        <item>com.ainirobot.coreservice</item>
        <item>org.chromium.webview_shell</item>
        <item>com.android.documentsui</item>
        <item>com.ainirobot.remotecontrolservice</item>
        <item>com.ainirobot.ota</item>
        <item>com.ainirobot.headservice</item>
        <item>com.ainirobot.hardwareservice</item>
        <item>com.ainirobot.home</item>
        <item>org.codeaurora.snaplauncher</item>
        <item>org.codeaurora.snapcam</item>
        <item>org.codeaurora.gallery</item>
        <item>com.cyanogenmod.filemanager</item>
        <item>com.android.soundrecorder</item>
        <item>com.android.settings</item>
        <item>com.android.runningtest</item>
        <item>com.android.orionfactorymmi</item>
        <item>com.android.music</item>
        <item>com.android.music</item>
        <item>com.android.messaging</item>
        <item>com.android.dialer</item>
        <item>com.android.contacts</item>
        <item>com.ainirobot.videocall</item>
        <item>com.ainirobot.uploadservice</item>
        <item>com.ainirobot.visionsdk</item>
        <item>com.ainirobot.recordOperation</item>
        <item>com.android.calendar</item>
        <item>com.android.deskclock</item>
        <item>com.android.runningtest</item>
        <item>com.android.email</item>
        <item>com.android.mms</item>
        <item>org.codeaurora.dialer</item>
        <item>com.android.calculator2</item>
        <item>com.android.quicksearchbox</item>
        <item>com.caf.fmradio</item>
        <item>com.example.connmgr</item>
        <item>com.qualcomm.embmstest</item>
        <item>com.qualcomm.qct.dlt</item>
        <item>com.qualcomm.qti.biometrics.fingerprint.qfpcalibration</item>
        <item>com.qualcomm.qti.carrierswitch</item>
        <item>com.qualcomm.qti.presenceapp</item>
        <item>com.qualcomm.qti.presenceappSub2</item>
        <item>com.qualcomm.qti.seccamsample</item>
        <item>com.qualcomm.qti.sensors.qsensortest</item>
        <item>com.qualcomm.qti.server.wigigapp</item>
        <item>com.qualcomm.qti.sva</item>
        <item>com.qualcomm.qti.usta</item>
        <item>com.quicinc.cne.settings</item>
        <item>org.codeaurora.bluetooth.batestapp</item>
        <item>org.codeaurora.bluetooth.bttestapp</item>
        <item>org.codeaurora.bluetooth.hidtestapp</item>
        <item>org.codeaurora.qti.nrNetworkSettingApp</item>
        <item>com.ainirobot.loadnativelib</item>
        <item>com.ainirobot.notificationtest</item>
        <item>com.ainirobot.remotedebug</item>
        <item>com.ainirobot.vpnclient</item>
        <item>com.android.gallery3d</item>
        <item>com.ainirobot.DepthCameraNode</item>
        <item>com.ainirobot.network</item>
    </string-array>

    <string-array name="whiteList">
        <item>com.ainirobot.videocall</item>
        <item>com.ainirobot.settings</item>
        <item>com.ainirobot.moduleapp</item>
        <item>com.ainirobot.maptool</item>
        <item>com.ainirobot.inspection</item>
    </string-array>

    <string-array name="showList">
        <item>com.ainirobot.moduleapp</item>
        <item>com.ainirobot.maptool</item>
        <item>com.ainirobot.speechasrservice</item>
        <item>com.ainirobot.inspection</item>
        <!--<item>com.orion.os.crashhandler</item>-->
        <item>com.ainirobot.orionuploadservice</item>
        <item>com.ainirobot.settings</item>
        <item>com.ainirobot.uwbsetting</item>
        <item>com.ainirobot.knowledge</item>
        <item>com.ainirobot.edoor</item>
        <item>com.ainirobot.callbutton</item>
        <item>com.ainirobot.passgateservice</item>
        <item>com.ainirobot.elevator</item>
    </string-array>

    <string-array name="ko_static_password_showList">
        <item>com.ainirobot.moduleapp</item>
        <item>com.woowahan.orionstar</item>
        <item>com.teamviewer.quicksupport.market</item>
        <item>com.ainirobot.reposition</item>
    </string-array>


</resources>
