<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_window_focused="true">
        <set android:ordering="together">
            <objectAnimator
                android:duration="2000"
                android:propertyName="rotation"
                android:repeatCount="-1"
                android:valueTo="360"
                android:valueType="floatType"></objectAnimator>
            <objectAnimator
                android:duration="2000"
                android:propertyName="ImageLevel"
                android:repeatCount="-1"
                android:valueFrom="0"
                android:valueTo="10000"
                android:valueType="intType"></objectAnimator>
        </set>
    </item>
    <item>
        <set android:ordering="together">
            <objectAnimator
                android:duration="0"
                android:propertyName="rotation"
                android:valueTo="0"
                android:valueType="floatType"></objectAnimator>
            <objectAnimator
                android:duration="0"
                android:propertyName="ImageLevel"
                android:valueFrom="0"
                android:valueTo="0"
                android:valueType="intType"></objectAnimator>
        </set>
    </item>
</selector>