<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:background="@drawable/bg_hw_abnormal">


        <ImageView
            android:id="@+id/hw_status_iv"
            android:layout_width="77dp"
            android:layout_height="77dp"
            android:layout_marginTop="60dp"
            android:scaleType="fitXY"
            android:src="@drawable/ic_warning"/>
        
        
        <TextView
            android:id="@+id/hw_error_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="29dp"
            android:gravity="center"
            android:text="@string/hw_chassis_disconnected"
            android:textColor="@color/white"
            android:textSize="20sp"
            />

        <TextView
            android:id="@+id/hw_subtitle_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:gravity="center"
            android:text="@string/hw_auto_recovery"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="14sp" />

        <LinearLayout
            android:id="@+id/psb_error_hint"
            android:layout_width="match_parent"
            android:layout_height="115dp"
            android:layout_marginTop="5dp"
            android:paddingStart="50dp"
            android:gravity="center_horizontal"
            android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:text="@string/please_try_to_recover"
                    android:textColor="#FF8D31"
                    android:textSize="14sp" />
                <TextView
                    android:id="@+id/tvPsbRecoverStep1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:paddingStart="15dp"
                    android:paddingEnd="10dp"
                    android:text="@string/psb_recover_step1"
                    android:textColor="@color/white"
                    android:textSize="14sp" />
                <TextView
                    android:id="@+id/tvPsbRecoverStep2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:paddingStart="15dp"
                    android:paddingEnd="10dp"
                    android:text="@string/psb_recover_step2"
                    android:textColor="@color/white"
                    android:textSize="14sp" />

        </LinearLayout>>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:paddingStart="50dp"
            android:gravity="center_horizontal"
            android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:text="@string/get_help_hint"
                    android:textColor="@color/white"
                    android:textSize="14sp" />
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:text="@string/error_code"
                    android:textColor="@color/white"
                    android:textSize="14sp" />
                <TextView
                    android:id="@+id/tv_errorCode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:text="code"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
                <TextView
                    android:id="@+id/tv_sn"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:text="sn"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
                <TextView
                    android:id="@+id/tv_timestamp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:text="timestamp"
                    android:textColor="@color/white"
                    android:textSize="12sp" />

        </LinearLayout>>

        <Button
            android:id="@+id/timer_button"
            android:layout_width="286dp"
            android:layout_height="46dp"
            android:layout_marginTop="80dp"
            android:background="@drawable/selector_hw_recovery_btn"
            android:gravity="center"
            android:includeFontPadding="true"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:text="@string/hw_retry_recovery" />


</LinearLayout>