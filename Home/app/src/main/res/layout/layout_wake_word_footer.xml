<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="#E1E7EA" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/cancel"
            android:layout_width="0px"
            android:layout_height="138px"
            android:layout_weight="1"
            android:background="@drawable/bg_btn_upgrade_next_time"
            android:gravity="center"
            android:stateListAnimator="@drawable/translucent_pressed_animator_selector"
            android:text="@string/cancel"
            android:textColor="#FFCCD2D6"
            android:textSize="40px" />

        <View
            android:layout_width="1px"
            android:layout_height="80px"
            android:layout_gravity="center_vertical"
            android:background="#FFE1E7EA" />

        <TextView
            android:id="@+id/confirm"
            android:layout_width="0px"
            android:layout_height="138px"
            android:layout_weight="1"
            android:background="@drawable/bg_btn_upgrade_now"
            android:gravity="center"
            android:stateListAnimator="@drawable/translucent_pressed_animator_selector"
            android:text="@string/confirm"
            android:textColor="#FF55C3FB"
            android:textSize="40px" />
    </LinearLayout>

</LinearLayout>