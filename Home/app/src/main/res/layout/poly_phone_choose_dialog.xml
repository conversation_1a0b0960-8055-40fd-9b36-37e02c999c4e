<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="940px"
    android:layout_height="wrap_content">

    <include
        layout="@layout/layout_wake_word_footer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"/>

    <LinearLayout
        android:id="@+id/ll_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/wake_word_set"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="90px"
            android:layout_marginTop="80px"
            android:gravity="center_vertical"
            android:textColor="@color/ota_big_text"
            android:textSize="60px" />

        <TextView
            android:id="@+id/spell_set"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="90px"
            android:layout_marginTop="10px"
            android:textColor="@color/night_mobile_data"
            android:textSize="46px" />

        <android.support.v7.widget.RecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="80px"
            android:layout_marginBottom="253px"
            android:overScrollMode="never" />

    </LinearLayout>

</FrameLayout>