<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/com_bg">

    <TextView
        android:id="@+id/relocate_choose_type_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="16dp"
        android:drawableStart="@drawable/qrcode_back"
        android:drawablePadding="5dp"
        android:gravity="center_vertical"
        android:padding="6dp"
        android:text="@string/relocate_back_text"
        android:textColor="@color/white"
        android:textSize="13sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <android.support.constraint.ConstraintLayout
        android:id="@+id/choose_point_locate"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginStart="20dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/relocate_choose_type_cancel">

        <TextView
            android:id="@+id/setting_item_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:text="@string/relocate_by_anchor_point"
            android:textColor="@color/white"
            android:textSize="13sp"
            app:layout_constraintBottom_toTopOf="@+id/setting_item_describe"
            app:layout_constraintEnd_toStartOf="@+id/setting_item_arrow"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/setting_item_describe"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:text="@string/reposition_anchor_title_qrCode"
            android:textColor="@color/white50"
            android:textSize="9sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/setting_item_arrow"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/setting_item_title" />

        <ImageView
            android:id="@+id/setting_item_arrow"
            android:layout_width="4dp"
            android:layout_height="8dp"
            android:background="@drawable/right"
            android:scaleType="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@color/white15"
            app:layout_constraintBottom_toBottomOf="parent" />

    </android.support.constraint.ConstraintLayout>

    <android.support.constraint.ConstraintLayout
        android:id="@+id/choose_pile_locate"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/choose_point_locate">

        <TextView
            android:id="@+id/choose_pile_locate_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:text="@string/relocate_by_charge_pile"
            android:textColor="@color/white"
            android:textSize="13sp"
            app:layout_constraintBottom_toTopOf="@+id/choose_pile_locate_describe"
            app:layout_constraintEnd_toStartOf="@+id/choose_pile_locate_arrow"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/choose_pile_locate_describe"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:text="@string/relocate_by_charge_pile_des"
            android:textColor="@color/white50"
            android:textSize="9sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/choose_pile_locate_arrow"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/choose_pile_locate_title" />

        <ImageView
            android:id="@+id/choose_pile_locate_arrow"
            android:layout_width="4dp"
            android:layout_height="8dp"
            android:background="@drawable/right"
            android:scaleType="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@color/white15"
            app:layout_constraintBottom_toBottomOf="parent" />

    </android.support.constraint.ConstraintLayout>

</android.support.constraint.ConstraintLayout>