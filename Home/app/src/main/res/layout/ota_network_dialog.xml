<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2017 OrionStar Technology Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->


<android.support.constraint.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_centerInParent="true"
    android:background="@color/white"
    android:elevation="1dp">

    <TextView
        android:id="@+id/mobile_data_confirm_upgrade"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="80px"
        android:paddingStart="50px"
        android:paddingEnd="50px"
        android:text="@string/mobile_data_confirm_upgrade"
        android:textColor="@color/mobile_data_confirm_upgrade"
        android:textSize="46px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/night_mobile_data"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30px"
        android:text="@string/night_mobile_data"
        android:textColor="@color/night_mobile_data"
        android:textSize="38px"
        android:paddingStart="50px"
        android:paddingEnd="50px"
        app:layout_constraintStart_toStartOf="@id/mobile_data_confirm_upgrade"
        app:layout_constraintTop_toBottomOf="@id/mobile_data_confirm_upgrade" />

    <com.ainirobot.home.ui.view.OTARadioButton
        android:id="@+id/radio_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="90px"
        app:layout_constraintStart_toStartOf="@id/night_mobile_data"
        app:layout_constraintTop_toBottomOf="@id/night_mobile_data" />

    <TextView
        android:id="@+id/no_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20px"
        android:text="@string/no_tip"
        android:textColor="@color/ota_upgrade_complete"
        android:textEditSideNoPasteWindowLayout="@id/mobile_data_confirm_upgrade"
        android:textSize="38px"
        app:layout_constraintBottom_toBottomOf="@id/radio_button"
        app:layout_constraintStart_toEndOf="@id/radio_button"
        app:layout_constraintTop_toTopOf="@id/radio_button" />

    <View
        android:id="@+id/view_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="83px"
        android:background="@color/ota_view_line"
        app:layout_constraintTop_toBottomOf="@id/radio_button" />

    <TextView
        android:id="@+id/cancel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="1px"
        android:background="@drawable/bg_btn_upgrade_next_time"
        android:gravity="center"
        android:paddingBottom="43px"
        android:paddingTop="43px"
        android:text="@string/cancel"
        android:textColor="@color/ota_upgrade_next_time"
        android:textSize="40px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="@id/vertical_view_line"
        app:layout_constraintTop_toBottomOf="@id/view_line" />

    <View
        android:id="@+id/vertical_view_line"
        android:layout_width="1px"
        android:layout_height="80px"
        android:background="@color/ota_vertical_line"
        app:layout_constraintBottom_toBottomOf="@+id/confirm"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/confirm" />

    <TextView
        android:id="@+id/confirm"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="1px"
        android:background="@drawable/bg_btn_upgrade_now"
        android:gravity="center"
        android:paddingBottom="43px"
        android:paddingTop="43px"
        android:text="@string/dialog_confirm"
        android:textColor="@color/ota_upgrade_now"
        android:textSize="40px"
        app:layout_constraintStart_toStartOf="@id/vertical_view_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_line" />

</android.support.constraint.ConstraintLayout>
