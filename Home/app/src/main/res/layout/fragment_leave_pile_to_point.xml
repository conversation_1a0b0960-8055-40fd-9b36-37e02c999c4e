<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    android:background="@drawable/shape_bg_full_lock"
    android:orientation="vertical">

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/leave_pile_title"
        android:textColor="@color/white"
        android:textSize="25sp"
        app:layout_constraintTop_toBottomOf="@+id/gl_top_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <ImageView
        android:id="@+id/emoji"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginTop="40dp"
        android:scaleType="fitCenter"
        android:src="@drawable/emoji"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <android.support.constraint.Guideline
        android:id="@+id/gl_top_icon"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintGuide_percent="0.150"
        android:orientation="horizontal"/>

    <android.support.constraint.Guideline
        android:id="@+id/gl_bottom"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintGuide_percent="0.700"
        android:orientation="horizontal"/>


</android.support.constraint.ConstraintLayout>