<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="268.57dp"
    android:layout_height="132dp"
    android:layout_gravity="center"
    android:background="@drawable/bg_round_rect_white">

    <TextView
        android:id="@+id/dormancy_text"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:gravity="center"
        android:text="@string/dormancy_text"
        android:textColor="@color/ota_big_text"
        android:textSize="13sp"
        app:layout_constraintBottom_toTopOf="@+id/dormancy_cancel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_weight="2" />

    <View
        android:id="@+id/dormancy_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="91dp"
        android:background="@color/ota_view_line"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/dormancy_cancel"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:gravity="center"
        android:text="@string/cancel"
        android:textColor="@color/ota_upgrade_now"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dormancy_text"
        app:layout_constraintVertical_weight="1" />

</android.support.constraint.ConstraintLayout>