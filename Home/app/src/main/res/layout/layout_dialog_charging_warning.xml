<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2017 OrionStar Technology Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->


<android.support.constraint.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/dialog_charging_warning"
    android:layout_width="940px"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@color/white">

    <ImageView
        android:id="@+id/ic_charge_warning"
        android:layout_width="120px"
        android:layout_height="120px"
        android:layout_marginTop="80px"
        android:layout_marginStart="90px"
        android:layout_marginEnd="730px"
        android:layout_marginBottom="608px"
        android:src="@drawable/charge_warning_dialog_img"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/title"
        android:layout_width="364px"
        android:layout_height="84px"
        android:layout_marginTop="100px"
        android:layout_marginBottom="624px"
        android:layout_marginEnd="336px"
        android:layout_marginStart="240px"
        android:text="@string/charging_warning_dialog_title"
        android:textColor="@color/charge_dialog_text"
        android:textSize="60px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/content"
        android:layout_width="760px"
        android:layout_height="wrap_content"
        android:layout_marginTop="56px"
        android:text="@string/charging_warning_dialog_text"
        android:textColor="@color/charge_dialog_text"
        android:textSize="44px"
        android:lineSpacingExtra="2dp"
        android:lineSpacingMultiplier="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title" />

    <TextView
        android:id="@+id/content_reason_1"
        android:layout_width="728px"
        android:layout_height="wrap_content"
        android:layout_marginTop="30px"
        android:text="@string/charging_warning_dialog_reason_1"
        android:textColor="@color/charge_dialog_text"
        android:textSize="40px"
        android:lineSpacingExtra="4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/content" />

<!--    <TextView-->
<!--        android:id="@+id/content_reason_2"-->
<!--        android:layout_width="728px"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_marginTop="20px"-->
<!--        android:text="@string/charging_warning_dialog_reason_2"-->
<!--        android:textColor="@color/charge_dialog_text"-->
<!--        android:textSize="40px"-->
<!--        android:lineSpacingExtra="2dp"-->
<!--        android:lineSpacingMultiplier="1.0"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@id/content_reason_1" />-->

    <View
        android:id="@+id/view_line"
        android:layout_width="match_parent"
        android:layout_height="2px"
        android:layout_marginTop="79px"
        android:background="@color/ota_view_line"
        app:layout_constraintTop_toBottomOf="@id/content_reason_1" />

    <TextView
        android:id="@+id/confirm"
        android:layout_width="match_parent"
        android:layout_height="140px"
        android:background="@drawable/bg_btn_upgrade_next_time"
        android:gravity="center"
        android:text="@string/charging_warning_confirm"
        android:textColor="@color/ota_upgrade_now"
        android:textSize="40px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_line" />

</android.support.constraint.ConstraintLayout>
