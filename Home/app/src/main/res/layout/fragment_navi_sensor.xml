<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/com_bg">

    <ImageView
        android:id="@+id/navi_sensor_icon"
        android:layout_width="92dp"
        android:layout_height="92dp"
        android:src="@drawable/ic_warning"
        android:scaleType="fitCenter"
        app:layout_constraintTop_toBottomOf="@+id/gl_top"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />

    <TextView
        android:id="@+id/tv_sensor_error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/navi_sensor_error"
        android:textSize="@dimen/font_16"
        android:textColor="@color/white"
        android:layout_marginTop="10dp"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@+id/navi_sensor_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />

    <TextView
        android:id="@+id/tv_sensor_error_des"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/navi_sensor_error_des"
        android:textSize="@dimen/font_12"
        android:textColor="@color/white"
        android:layout_marginTop="16dp"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@+id/tv_sensor_error"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />

    <TextView
        android:id="@+id/tv_sensor_reboot"
        android:layout_width="136dp"
        android:layout_height="34dp"
        android:text="@string/cloud_reboot"
        android:textSize="@dimen/font_16"
        android:textColor="@color/white"
        android:layout_marginTop="6dp"
        android:gravity="center"
        android:background="@drawable/charge_confirm_btn_bg"
        app:layout_constraintTop_toBottomOf="@+id/gl_bottom"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />


    <android.support.constraint.Guideline
        android:id="@+id/gl_top"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.200"
        />
    <android.support.constraint.Guideline
        android:id="@+id/gl_bottom"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.800"
        />


</android.support.constraint.ConstraintLayout>