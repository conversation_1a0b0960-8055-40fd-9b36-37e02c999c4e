<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:background="@color/black"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/center_emoji"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:scaleType="fitCenter"
        android:src="@drawable/icon_emoji"/>

    <TextView
        android:id="@+id/please_wait"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/center_emoji"
        android:layout_marginTop="36dp"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textAlignment="center"
        android:text="@string/reposition_title_loading"
        android:gravity="center"
        android:layout_centerHorizontal="true"/>

</RelativeLayout>