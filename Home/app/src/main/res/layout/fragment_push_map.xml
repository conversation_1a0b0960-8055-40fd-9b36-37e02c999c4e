<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <ImageView
        android:layout_width="77dp"
        android:layout_height="77dp"
        android:layout_marginTop="150dp"
        android:src="@drawable/push_map_loading"/>

    <TextView
        android:id="@+id/push_map_title_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/push_map_title"
        android:textSize="18sp"
        android:textColor="@color/white"
        android:layout_marginTop="29dp"
        android:gravity="center" />

    <TextView
        android:id="@+id/push_map_subtitle_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/push_map_subtitle"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:layout_marginTop="6dp"
        />

</LinearLayout>