<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shutdown_bg"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <TextView
        android:id="@+id/shutdown_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        android:gravity="center"
        android:layout_marginBottom="8dp"
        android:text="@string/shutdown_navi_tips"
        android:textColor="@color/white"
        android:textSize="23sp"
        app:layout_constraintBottom_toTopOf="@+id/shutdown_robot"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>


    <ImageView
        android:id="@+id/shutdown_robot"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        android:src="@drawable/shutdown_robot"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.526"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.628"/>

    <TextView
        android:id="@+id/shutdown_navi_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="18dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="100dp"
        android:textColor="#80FFFFFF"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/shutdown_robot"
        app:layout_constraintVertical_bias="0.272"/>


    <ImageView
        android:id="@+id/location"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="70dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:src="@drawable/shutdown_location"
        app:layout_constraintBottom_toTopOf="@+id/shutdown_now"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/shutdown_robot"
        app:layout_constraintVertical_bias="0.923"/>


    <Button
        android:id="@+id/shutdown_now"
        android:layout_width="136dp"
        android:layout_height="26dp"
        android:layout_marginStart="26dp"
        android:layout_marginTop="7dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/shutdown_now_bg"
        android:text="@string/shutdown_now"
        android:textColor="@color/white"
        android:textSize="9sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/shutdown_robot"
        app:layout_constraintVertical_bias="0.715"/>

    <Button
        android:id="@+id/shutdown_cancel"
        android:layout_width="136dp"
        android:layout_height="26dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="7dp"
        android:layout_marginEnd="26dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/shutdown_cancel_bg"
        android:text="@string/shutdown_cancel"
        android:textColor="@color/white"
        android:textSize="9sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toEndOf="@+id/shutdown_now"
        app:layout_constraintTop_toBottomOf="@+id/shutdown_robot"
        app:layout_constraintVertical_bias="0.715"/>


</android.support.constraint.ConstraintLayout>