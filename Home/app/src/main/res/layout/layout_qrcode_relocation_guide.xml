<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/com_bg">

    <ImageView
        android:id="@+id/iv_tip"
        android:layout_marginTop="57dp"
        app:layout_constraintTop_toBottomOf="@id/locate_guide_subtitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@drawable/location_img_qrcode"
        android:layout_width="246dp"
        android:layout_height="246dp"/>

    <TextView
        android:id="@+id/reposition_cancel_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="16dp"
        android:drawableStart="@drawable/charge_set_cancel"
        android:gravity="center_vertical"
        android:text="@string/cancel"
        android:textColor="@color/white"
        android:textSize="13sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/locate_guide_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="69dp"
        android:layout_marginStart="2dp"
        android:layout_marginEnd="2dp"
        android:text="@string/reposition_title_qrCode"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="@dimen/font_locate_guide_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/locate_guide_subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginStart="2dp"
        android:layout_marginEnd="2dp"
        android:text="@string/reposition_qrcode_reminder_subtitle"
        android:textAlignment="center"
        android:textColor="#99FFFFFF"
        android:textSize="13sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/locate_guide_title" />

    <TextView
        android:id="@+id/locate_guide_confirmBtn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="30dp"
        android:padding="6dp"
        android:textAlignment="center"
        android:textColor="#99FFFFFF"
        android:textSize="13sp"
        android:text="@string/reposition_anchor_point_exist"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</android.support.constraint.ConstraintLayout>