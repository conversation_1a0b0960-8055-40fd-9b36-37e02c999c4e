<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2017 OrionStar Technology Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="#********"
    android:orientation="vertical">

    <android.support.constraint.ConstraintLayout
        android:id="@+id/dialog_radar"
        android:layout_width="300px"
        android:layout_height="300px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@drawable/dialog_background_drawable"
        android:alpha="0.6">

    <ImageView
        android:id="@+id/iv_radar_opening"
        android:layout_width="60.3px"
        android:layout_height="60.3px"
        android:layout_marginTop="93.9px"
        android:src="@drawable/dialog_intermediate_progress_ring_drawable"
        android:stateListAnimator="@animator/dialog_animator_selector"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dialog_radar"/>

    <ImageView
        android:id="@+id/iv_radar_opened"
        android:layout_width="60.3px"
        android:layout_height="60.3px"
        android:layout_marginTop="93.9px"
        android:src="@drawable/radar_success"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dialog_radar"/>

    <TextView
        android:id="@+id/tv_message"
        android:layout_width="231px"
        android:layout_height="53px"
        android:layout_marginTop="187px"
        android:text="@string/radar_is_opening"
        android:textColor="@color/white"
        android:textSize="38px"
        android:textAlignment="center"
        android:lineSpacingExtra="53px"
        android:lineSpacingMultiplier="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_radar_opening"/>
</android.support.constraint.ConstraintLayout>
</android.support.constraint.ConstraintLayout>