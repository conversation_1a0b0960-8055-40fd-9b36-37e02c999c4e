<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="269dp"
    android:layout_height="wrap_content"
    android:background="@drawable/dialog_anchor_question_bg"
    android:paddingStart="26dp"
    android:paddingEnd="21dp">

    <TextView
        android:id="@+id/anchor_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="23dp"
        android:drawableStart="@drawable/anchor_question_icon"
        android:drawablePadding="9dp"
        android:gravity="center_vertical"
        android:text="@string/anchor_title"
        android:textColor="#555D61"
        android:textSize="17sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/anchor_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:text="@string/anchor_content"
        android:textColor="#555D61"
        android:textSize="11sp"
        app:layout_constraintStart_toStartOf="@id/anchor_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/anchor_title" />

    <TextView
        android:id="@+id/anchor_subTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="11dp"
        android:text="@string/anchor_subTitle"
        android:textColor="#555D61"
        android:textSize="13sp"
        app:layout_constraintStart_toStartOf="@id/anchor_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/anchor_content" />

    <TextView
        android:id="@+id/anchor_subContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="9dp"
        android:text="@string/anchor_subContent"
        android:textColor="#555D61"
        android:textSize="11sp"
        app:layout_constraintStart_toStartOf="@id/anchor_subTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/anchor_subTitle" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="10dp"
        android:background="#E1E7EA"
        app:layout_constraintTop_toBottomOf="@+id/anchor_subContent" />

    <TextView
        android:id="@+id/anchor_ensure"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="15dp"
        android:paddingBottom="15dp"
        android:gravity="center_horizontal"
        android:text="@string/anchor_ensure"
        android:textColor="#55C3FB"
        android:textSize="11sp"
        android:layout_marginTop="5dp"
        app:layout_constraintTop_toBottomOf="@+id/anchor_subContent" />


</android.support.constraint.ConstraintLayout>