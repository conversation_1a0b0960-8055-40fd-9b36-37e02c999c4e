<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/view_root"
    android:layout_width="950px"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@color/white"
    android:elevation="1dp">

    <ImageView
        android:layout_width="340px"
        android:layout_height="340px"
        android:scaleType="fitXY"
        android:src="@drawable/upgrade_pic_img"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/find_new_version"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="90px"
        android:paddingStart="90px"
        android:paddingEnd="90px"
        android:text="@string/find_new_version"
        android:textColor="@color/ota_big_text"
        android:textSize="60px"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/version"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="90px"
        android:paddingEnd="90px"
        android:text="@string/ota_version"
        android:textColor="@color/ota_big_text"
        android:textSize="59px"
        app:layout_constraintTop_toBottomOf="@id/find_new_version" />

    <com.ainirobot.home.ui.view.MyScrollView
        android:id="@+id/content_scrollview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="125px"
        android:overScrollMode="never"
        android:scrollbars="none"
        app:layout_constraintTop_toBottomOf="@id/version">

        <TextView
            android:id="@+id/ota_upgrade_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:lineSpacingExtra="6px"
            android:paddingStart="90px"
            android:paddingEnd="90px"
            android:text="@string/ota_upgrade_content"
            android:textColor="@color/ota_big_text"
            android:textSize="46px" />
    </com.ainirobot.home.ui.view.MyScrollView>

    <TextView
        android:id="@+id/ota_upgrade_complete"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="90px"
        android:paddingStart="90px"
        android:paddingEnd="90px"
        android:text="@string/ota_upgrade_complete"
        android:textColor="@color/ota_big_text"
        android:textSize="38px"
        app:layout_constraintTop_toBottomOf="@id/content_scrollview" />

    <View
        android:id="@+id/view_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="30px"
        android:background="@color/ota_view_line"
        app:layout_constraintTop_toBottomOf="@id/ota_upgrade_complete" />

    <TextView
        android:id="@+id/upgrade_next_time"
        android:layout_width="0dp"
        android:layout_height="140px"
        android:background="@drawable/bg_btn_upgrade_next_time"
        android:gravity="center"
        android:text="@string/upgrade_next_time"
        android:textColor="@color/ota_upgrade_next_time"
        android:textSize="40px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/vertical_view_line"
        app:layout_constraintTop_toBottomOf="@id/view_line" />

    <View
        android:id="@+id/vertical_view_line"
        android:layout_width="1px"
        android:layout_height="80px"
        android:background="@color/ota_vertical_line"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/upgrade_next_time"
        app:layout_constraintEnd_toStartOf="@+id/upgrade_now"
        app:layout_constraintTop_toTopOf="@id/view_line" />

    <TextView
        android:id="@+id/upgrade_now"
        android:layout_width="0dp"
        android:layout_height="140px"
        android:background="@drawable/bg_btn_upgrade_now"
        android:gravity="center"
        android:text="@string/upgrade_now"
        android:textColor="@color/ota_upgrade_now"
        android:textSize="40px"
        app:layout_constraintStart_toEndOf="@id/vertical_view_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_line" />
</android.support.constraint.ConstraintLayout>
