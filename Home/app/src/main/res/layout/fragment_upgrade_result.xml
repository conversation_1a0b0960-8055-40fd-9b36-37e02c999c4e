<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >

    <ImageView
        android:layout_width="110px"
        android:layout_height="110px"
        android:src="@drawable/upgrade_fail_img"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:id="@+id/iv_upgrade_result"
        />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_upgrade_result"
        android:layout_marginTop="70px"
        android:textSize="40px"
        android:textColor="@android:color/white"
        android:text=""
        android:id="@+id/tv_upgrade_result"
        />

    <TextView
        android:id="@+id/tv_recovery"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingVertical="8dp"
        android:paddingHorizontal="6dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_upgrade_result"
        android:textSize="13sp"
        android:layout_marginTop="20dp"
        android:textColor="@color/white"
        android:background="@drawable/bg_round_stroke_white"
        android:text="@string/android_factory_reset"
        android:visibility="gone"/>

</android.support.constraint.ConstraintLayout>