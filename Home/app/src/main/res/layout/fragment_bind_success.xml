<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/first_bg">

    <RelativeLayout
        android:id="@+id/rl_bind"
        android:layout_width="86dp"
        android:layout_height="86dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/gl_bind_1">

        <ImageView
            android:id="@+id/bind_iv"
            android:layout_width="86dp"
            android:layout_height="86dp"
            android:contentDescription="@string/app_name"
            android:scaleType="fitCenter"
            android:src="@drawable/first_bind_logo1" />

        <ImageView
            android:id="@+id/bind_ok"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_alignParentBottom="true"
            android:layout_alignParentEnd="true"
            android:contentDescription="@string/app_name"
            android:scaleType="fitCenter"
            android:src="@drawable/first_sync_success" />
    </RelativeLayout>


    <TextView
        android:id="@+id/bind_success_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:gravity="center"
        android:singleLine="true"
        android:text="@string/bind_success_title"
        android:textColor="@color/white_ff"
        android:textSize="@dimen/font_18"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rl_bind" />

    <TextView
        android:id="@+id/bing_success_robot_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="@color/white_ff"
        android:textSize="@dimen/font_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/bind_success_title" />

    <TextView
        android:id="@+id/bottomBtn"
        android:layout_width="match_parent"
        android:layout_height="34dp"
        android:layout_marginEnd="32dp"
        android:layout_marginStart="32dp"
        android:background="@drawable/selector_locate_failure_btn"
        android:gravity="center"
        android:includeFontPadding="true"
        android:padding="8dp"
        android:textColor="@color/white_ff"
        android:textSize="@dimen/font_12"
        android:text="@string/bind_success_has_map"
        app:layout_constraintTop_toBottomOf="@+id/gl_bottom" />

    <android.support.constraint.Guideline
        android:id="@+id/gl_bind_1"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintGuide_percent="0.320"
        app:layout_constraintStart_toStartOf="parent" />

    <android.support.constraint.Guideline
        android:id="@+id/gl_bind_2"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintGuide_percent="0.803"
        app:layout_constraintStart_toStartOf="parent" />

    <android.support.constraint.Guideline
        android:id="@+id/gl_bottom"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintGuide_percent="0.771"
        app:layout_constraintStart_toStartOf="parent" />
</android.support.constraint.ConstraintLayout>