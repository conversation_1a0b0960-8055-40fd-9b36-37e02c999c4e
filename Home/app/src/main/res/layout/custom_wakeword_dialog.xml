<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="940px"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/wake_word"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="90px"
        android:layout_marginTop="79px"
        android:drawableStart="@drawable/developer_awaken_img"
        android:drawablePadding="20px"
        android:text="@string/custom_wake_up_word"
        android:textColor="#FF555D61"
        android:textSize="46px" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="90px"
        android:layout_marginTop="54px"
        android:gravity="center_vertical">

        <EditText
            android:id="@+id/input_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@null"
            android:hint="@string/support_text_length"
            android:imeOptions="actionDone|flagNoExtractUi|flagNoFullscreen"
            android:maxLines="1"
            android:textColor="#555D61"
            android:textColorHint="#CCD2D6"
            android:textSize="60px" />

        <ImageView
            android:id="@+id/btn_clear"
            android:layout_width="90px"
            android:layout_height="90px"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="20px"
            android:background="@drawable/delete_btn"
            android:visibility="invisible" />

        <TextView
            android:id="@+id/btn_delete"
            android:layout_width="160px"
            android:layout_height="90px"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:background="@drawable/selector_delete_wake_word"
            android:gravity="center"
            android:text="@string/delete"
            android:textColor="@color/white"
            android:textSize="40px"
            android:visibility="invisible" />

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginHorizontal="90px"
        android:layout_marginTop="20px"
        android:background="#FFE1E7EA" />

    <TextView
        android:id="@+id/spell"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginHorizontal="90px"
        android:layout_marginTop="20px"
        android:textColor="#99A3A8"
        android:textSize="38px" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="38px"
        android:background="#E1E7EA" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="140px"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/cancel"
            android:layout_width="0px"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/bg_btn_upgrade_next_time"
            android:gravity="center"
            android:stateListAnimator="@drawable/translucent_pressed_animator_selector"
            android:text="@string/cancel"
            android:textColor="#FFCCD2D6"
            android:textSize="40px" />

        <View
            android:layout_width="1px"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_marginBottom="30px"
            android:layout_marginTop="30px"
            android:background="#FFE1E7EA" />

        <TextView
            android:id="@+id/confirm"
            android:layout_width="0px"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/bg_btn_upgrade_now"
            android:gravity="center"
            android:stateListAnimator="@drawable/translucent_pressed_animator_selector"
            android:text="@string/confirm"
            android:textColor="#FF55C3FB"
            android:textSize="40px" />
    </LinearLayout>
</LinearLayout>
