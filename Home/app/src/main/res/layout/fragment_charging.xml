<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000"
    android:orientation="vertical"
    tools:context=".ui.fragment.ChargingFragment">

    <include
        android:id="@+id/layout_charging_low_temp"
        android:visibility="gone"
        layout="@layout/charging_low_temp_view"/>

    <LinearLayout
        android:id="@+id/layout_update"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:paddingTop="70px"
        android:paddingEnd="20px"
        app:layout_constraintTop_toTopOf="parent" >

        <android.support.constraint.ConstraintLayout
            android:id="@+id/constraint_layout_update"
            android:layout_width="200px"
            android:layout_height="wrap_content"
            android:visibility="gone">

            <ImageView
                android:id="@+id/emoji_update_icon"
                android:layout_width="100px"
                android:layout_height="100px"
                android:src="@drawable/update_icon"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <TextView
                android:id="@+id/emoji_update_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10px"
                android:text="@string/ota_wait"
                android:textColor="#fff"
                android:textSize="38px"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/emoji_update_icon" />
        </android.support.constraint.ConstraintLayout>
    </LinearLayout>

    <ProgressBar
        android:id="@+id/charging_image"
        style="@style/Widget.AppCompat.ProgressBar.Horizontal"
        android:layout_width="304px"
        android:layout_height="509px"
        android:layout_marginTop="740px"
        android:indeterminate="false"
        android:max="100"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/charge_lightning_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="959px"
        android:src="@drawable/charge_lightning_img"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/lin_charging"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="200px"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="parent"
        android:orientation="vertical">

        <TextView
            android:id="@+id/charging_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/charging_text"
            android:textColor="@color/white"
            android:textSize="100px"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/charging_sub_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="25px"
            android:gravity="center"
            android:text="@string/charging_warning_subText"
            android:textColor="@color/white"
            android:textSize="100px"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/charging_level"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="25px"
            android:gravity="center"
            android:text="@string/charging_level"
            android:textColor="@color/white"
            android:textSize="60px"
            app:layout_constraintTop_toTopOf="parent" />

    </LinearLayout>

    <TextView
        android:id="@+id/btn_end_charging"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="330px"
        android:layout_marginTop="1477px"
        android:layout_marginEnd="330px"
        android:background="@drawable/charge_warning_btn_bg"
        android:gravity="center"
        android:text="@string/stop_right_now"
        android:visibility="gone"
        android:textColor="@color/white"
        android:textSize="40px"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/btn_charging_warning"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="330px"
        android:layout_marginTop="1477px"
        android:layout_marginEnd="330px"
        android:background="@drawable/charge_warning_btn_bg"
        android:gravity="center"
        android:text="@string/charging_warning"
        android:textColor="@color/white"
        android:textSize="40px"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Leaving Layout -->
    <android.support.constraint.ConstraintLayout
        android:id="@+id/leaving_layout"
        android:layout_width="810px"
        android:layout_height="1395px"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <!-- Title Layout -->
        <android.support.constraint.ConstraintLayout
            android:id="@+id/title_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/charge_leaving_title"
                android:textSize="100px"
                android:gravity="center"
                android:textColor="#FFFFFF"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent" />

            <TextView
                android:id="@+id/tv_tips"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/charge_leaving_tips"
                android:textSize="60px"
                android:gravity="center"
                android:textColor="#FFFFFF"
                app:layout_constraintTop_toBottomOf="@+id/tv_title"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent" />
            </android.support.constraint.ConstraintLayout>

            <!-- ImageView -->
            <ImageView
                android:id="@+id/image_off_charger"
                android:layout_width="680px"
                android:layout_height="680px"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                android:layout_marginTop="20dp"
                android:src="@drawable/mini_img_offcharger" />
        </android.support.constraint.ConstraintLayout>

</android.support.constraint.ConstraintLayout>