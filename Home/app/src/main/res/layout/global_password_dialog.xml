<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:orientation="vertical">


    <LinearLayout
        android:id="@+id/password_verify"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/cancel_input_password"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="35px"
            android:layout_marginTop="60px"
            android:drawablePadding="30px"
            android:drawableStart="@drawable/cancel_input_password_btn"
            android:gravity="center_vertical"
            android:text="@string/cancel"
            android:textColor="@android:color/white"
            android:textSize="48px" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="15px"
            android:layout_marginTop="21px"
            android:text="@string/input_password"
            android:textAlignment="center" />


        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/layout_password_display"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="140px"
                    android:layout_height="140px"
                    android:layout_margin="15px"
                    android:background="@drawable/sharp_verification_code"
                    android:gravity="center"
                    android:inputType="textPassword"
                    android:textAlignment="center"
                    android:textSize="60px" />

                <TextView
                    android:layout_width="140px"
                    android:layout_height="140px"
                    android:layout_margin="15px"
                    android:background="@drawable/sharp_verification_code"
                    android:gravity="center"
                    android:inputType="textPassword"
                    android:textAlignment="center"
                    android:textSize="60px" />

                <TextView
                    android:layout_width="140px"
                    android:layout_height="140px"
                    android:layout_margin="15px"
                    android:background="@drawable/sharp_verification_code"
                    android:gravity="center"
                    android:inputType="textPassword"
                    android:textAlignment="center"
                    android:textSize="60px" />

                <TextView
                    android:layout_width="140px"
                    android:layout_height="140px"
                    android:layout_margin="15px"
                    android:background="@drawable/sharp_verification_code"
                    android:gravity="center"
                    android:inputType="textPassword"
                    android:textAlignment="center"
                    android:textSize="60px" />
            </LinearLayout>

            <EditText
                android:id="@+id/robot_global_actions_et_password"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:backgroundTint="@android:color/transparent"
                android:cursorVisible="false"
                android:digits="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
                android:inputType="textPassword"
                android:letterSpacing="2.1"
                android:maxLength="4"
                android:maxLines="1"
                android:textColor="@android:color/transparent"
                android:textSize="60px">

                <requestFocus />
            </EditText>

            <CheckBox
                android:id="@+id/password_show_hide_checkbox"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="end|center_vertical"
                android:layout_marginEnd="170px"
                android:background="@null"
                android:button="@drawable/selector_show_hide_password"
                android:labelFor="@+id/robot_global_actions_et_password" />
        </FrameLayout>

        <LinearLayout
            android:id="@+id/log_ticker"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingBottom="24px"
            android:paddingEnd="40px"
            android:paddingStart="40px"
            android:paddingTop="24px">

            <TextView
                android:id="@+id/time_clock"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:singleLine="true"
                android:textColor="#FFFFFF"
                android:textSize="38px" />

            <TextView
                android:id="@+id/device_serial"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="0"
                android:singleLine="true"
                android:textAllCaps="true"
                android:textColor="#FFFFFF"
                android:textSize="38px"
                android:visibility="gone"/>

            <TextView
                android:id="@+id/device_version"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="30px"
                android:layout_weight="0"
                android:singleLine="true"
                android:textColor="#FFFFFF"
                android:textSize="38px"
                android:visibility="gone"/>
        </LinearLayout>

        <TextView
            android:id="@+id/robot_global_actions_tv_password_error"
            android:visibility="visible"
            android:alpha="0"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="-26dp"
            android:background="@drawable/robot_global_actions_error_background_drawable"
            android:drawablePadding="7px"
            android:drawableStart="@drawable/robot_global_actions_wrong_img"
            android:gravity="center_vertical"
            android:paddingBottom="14px"
            android:paddingEnd="70px"
            android:paddingStart="70px"
            android:paddingTop="14px"
            android:text="@string/password_error"
            android:textSize="30px" />
    </LinearLayout>


</LinearLayout>