<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/com_bg"
    android:gravity="center"
    android:orientation="vertical">

    <ImageButton
        android:id="@+id/reposition_failure_cancel"
        android:layout_width="90px"
        android:layout_height="90px"
        android:layout_marginStart="16px"
        android:layout_marginTop="42px"
        android:src="@drawable/charge_set_cancel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@color/transparent"/>

    <TextView
        android:id="@+id/reposition_cancel_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4px"
        android:layout_marginTop="55px"
        android:text="@string/cancel"
        android:textSize="46px"
        app:layout_constraintStart_toEndOf="@id/reposition_failure_cancel"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/locate_failure_title"
        android:layout_width="match_parent"
        android:layout_height="130px"
        android:layout_marginTop="200px"
        android:text="@string/reposition_fail"
        android:textAlignment="center"
        android:textColor="@color/color_ff713c"
        android:textSize="100px"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/locate_failure_subtitle"
        android:layout_width="match_parent"
        android:layout_height="60px"
        android:layout_marginTop="30px"
        android:text="@string/reposition_fail_des"
        android:visibility="invisible"
        android:textAlignment="center"
        android:textColor="@color/color_ff713c"
        android:textSize="46px"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/locate_failure_title" />

    <ImageView
        android:id="@+id/locate_failure_bg"
        android:scaleType="fitCenter"
        android:visibility="visible"
        android:layout_marginTop="57dp"
        app:layout_constraintTop_toBottomOf="@id/locate_failure_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@drawable/location_img_withoutmap"
        android:layout_width="246dp"
        android:layout_height="246dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="1195px"
        android:drawableLeft="@drawable/phone"
        android:gravity="center"
        android:visibility="invisible"
        android:text="@string/inspect_err_service_number"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/locate_failure_confirmBtn"
        android:layout_width="980px"
        android:layout_height="120px"
        android:layout_marginBottom="400px"
        android:background="@drawable/selector_locate_failure_btn"
        android:text="@string/reposition_failure_vision_retry"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="40px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <Button
        android:id="@+id/locate_failure_see_solution"
        android:layout_width="980px"
        android:layout_height="120px"
        android:layout_marginTop="80px"
        android:background="@drawable/selector_locate_failure_see_solution"
        android:gravity="center"
        android:text="@string/charging_warning"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="40px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/locate_failure_confirmBtn" />

</android.support.constraint.ConstraintLayout>