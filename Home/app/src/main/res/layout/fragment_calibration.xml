<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:background="@color/black"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/center_emoji"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:scaleType="fitCenter"
        android:src="@drawable/icon_emoji"/>

    <TextView
        android:layout_width="300sp"
        android:layout_height="wrap_content"
        android:layout_below="@+id/center_emoji"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="26dp"
        android:lineHeight="70px"
        android:gravity="center"
        android:text="@string/calibration_tip"
        android:textColor="@color/white"
        android:textSize="50px" />

</RelativeLayout>