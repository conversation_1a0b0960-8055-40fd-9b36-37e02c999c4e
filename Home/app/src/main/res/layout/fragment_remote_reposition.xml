<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    >

    <include
        android:visibility="visible"
        android:id="@+id/locate_guide_remote"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/layout_remote_reposition_guide"/>

    <include
        android:visibility="gone"
        android:id="@+id/locate_search"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/layout_reposition_search" />

    <include
        android:visibility="gone"
        android:id="@+id/locate_success"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/layout_reposition_success"/>

    <include
        android:visibility="gone"
        android:id="@+id/locate_failure"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/layout_remote_reposition_failure"/>

</android.support.constraint.ConstraintLayout>
