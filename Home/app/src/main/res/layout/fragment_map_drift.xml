<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    >

    <RelativeLayout
        android:visibility="gone"
        android:id="@+id/layout_tip"
        android:background="@drawable/bg_round_rect_white"
        android:layout_centerInParent="true"
        android:layout_width="216dp"
        android:layout_height="120dp">
        <TextView
            android:layout_marginTop="10dp"
            android:textSize="13sp"
            android:layout_centerHorizontal="true"
            android:text="@string/map_drift_cannot_estimate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <TextView
            android:layout_centerInParent="true"
            android:textSize="12sp"
            android:text="@string/map_drift_robot_was_pushed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <TextView
            android:stateListAnimator="@drawable/translucent_pressed_animator_selector"
            android:textColor="#FF55C3FB"
            android:textAlignment="center"
            android:text="@string/confirm"
            android:textSize="11sp"
            android:layout_marginEnd="10dp"
            android:layout_marginStart="10dp"
            android:paddingTop="5dp"
            android:paddingBottom="10dp"
            android:id="@+id/btn_start_reposition"
            android:layout_centerHorizontal="true"
            android:layout_alignParentBottom="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
    </RelativeLayout>

    <RelativeLayout
        android:visibility="gone"
        android:id="@+id/layout_loading"
        android:background="@drawable/bg_round_rect_white"
        android:layout_centerInParent="true"
        android:layout_width="216dp"
        android:layout_height="120dp">

        <TextView
            android:layout_centerInParent="true"
            android:id="@+id/tv_loading_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

    </RelativeLayout>

</RelativeLayout>