<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/first_bg">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingStart="110px"
        android:paddingEnd="110px"
        android:paddingTop="110px">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/bind_robot"
            android:textColor="#ffffff"
            android:textSize="80px" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="110px"
            android:textSize="40px"
            android:textColor="#ffffff"
            android:text="@string/bind_robot_prompt"/>

        <View
            android:layout_width="wrap_content"
            android:layout_height="1px"
            android:layout_marginTop="60px"
            android:layout_marginBottom="49px"
            android:background="#40ffffff"/>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/sn_number_hint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/last_sn"
                android:textColor="#ffffff"
                android:textSize="46px"
                android:layout_marginEnd="20px"/>

            <TextView
                android:id="@+id/sn_number_tx"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:maxLength="6"
                android:singleLine="true"
                android:textColor="#ffffff"
                android:textSize="46px" />
        </RelativeLayout>

        <View
            android:layout_width="wrap_content"
            android:layout_height="1px"
            android:layout_marginTop="49px"
            android:layout_marginBottom="49px"
            android:background="#40ffffff"/>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/bind_code_hint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="@string/binding_code"
                android:textColor="#ffffff"
                android:textSize="46px"
                android:layout_marginEnd="20px"/>
            <TextView
                android:id="@+id/bind_code_tx"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:singleLine="true"
                android:textSize="46px"
                android:textColor="#ffffff"
                android:maxLength="6"
                android:layout_centerVertical="true"/>
        </RelativeLayout>

        <View
            android:layout_width="wrap_content"
            android:layout_height="1px"
            android:layout_marginTop="49px"
            android:background="#40ffffff"/>

        <TextView
            android:id="@+id/qr_code_loading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="60px"
            android:visibility="gone"
            android:text="@string/binding_code_expired"
            android:textColor="#b2ffffff"
            android:textSize="40px" />

        <Button
            android:id="@+id/qr_code_load_retry"
            android:layout_width="450px"
            android:layout_height="120px"
            android:visibility="gone"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="50px"
            android:textSize="40px"
            android:textColor="#ffffff"
            android:text="@string/refresh_code"
            android:background="@drawable/confirm_btn_bg"/>

        <LinearLayout
            android:id="@+id/cloud_server_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/current_cloud_server"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="120dp"
                android:textColor="@color/white"
                android:textSize="@dimen/font_11"
                android:gravity="start|center"/>

            <TextView
                android:id="@+id/not_this"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:textColor="@color/white"
                android:textSize="@dimen/font_11"
                android:text="@string/cloud_not_this"
                android:gravity="start|center"/>
        </LinearLayout>
    </LinearLayout>

</RelativeLayout>