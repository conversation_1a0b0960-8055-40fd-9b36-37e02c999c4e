<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000"
    android:orientation="vertical"
    tools:context=".ui.fragment.RemoteStopChargingFragment"
    tools:ignore="MissingDefaultResource">


    <ImageView
        android:id="@+id/remote_stop_charge_img"
        android:layout_width="394dp"
        android:layout_height="294dp"
        android:layout_marginTop="196dp"
        android:src="@drawable/remote_stop_charging"
        app:layout_constraintHorizontal_bias="0.495"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/title_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="72dp"
        android:gravity="center"
        android:text="@string/remote_stop_charging_title"
        android:textColor="@color/white"
        android:textSize="40sp"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="0dp" />

    <TextView
        android:id="@+id/title_sub_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/remote_stop_charging_title_sub"
        android:textColor="@color/white"
        android:textSize="15sp"
        app:layout_constraintTop_toBottomOf="@id/remote_stop_charge_img"
        tools:layout_editor_absoluteX="0dp" />


</android.support.constraint.ConstraintLayout>