<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/com_bg"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_tip"
        android:layout_marginTop="57dp"
        app:layout_constraintTop_toBottomOf="@id/locateSuccessTV"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@drawable/location_img_success"
        android:layout_width="246dp"
        android:layout_height="246dp"/>

    <TextView
        android:id="@+id/locateSuccessTV"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="69dp"
        android:text="@string/qrcode_reposition_success"
        android:textColor="@color/white"
        android:textSize="29sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/charge_pile_locate_failure"
        layout="@layout/layout_reposition_failure"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"/>


</android.support.constraint.ConstraintLayout>