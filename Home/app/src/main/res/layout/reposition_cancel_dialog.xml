<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/dialog_cancel_reposition"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="80px"
        android:gravity="center"
        android:text="@string/reposition_cancel_dialog"
        android:lineSpacingExtra="30px"
        android:textColor="@color/charge_dialog_text"
        android:textSize="15sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <View
        android:id="@+id/view_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="80px"
        android:background="@color/ota_view_line"
        app:layout_constraintTop_toBottomOf="@id/title" />

    <TextView
        android:id="@+id/cancel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="1px"
        android:background="@drawable/bg_btn_upgrade_next_time"
        android:gravity="center"
        android:paddingBottom="43px"
        android:paddingTop="43px"
        android:text="@string/give_up"
        android:textColor="@color/ota_upgrade_next_time"
        android:textSize="40px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/vertical_view_line"
        app:layout_constraintTop_toBottomOf="@id/view_line" />


    <View
        android:id="@+id/vertical_view_line"
        android:layout_width="1px"
        android:layout_height="80px"
        android:background="@color/ota_vertical_line"
        app:layout_constraintBottom_toBottomOf="@+id/confirm"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/confirm" />

    <TextView
        android:id="@+id/confirm"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="1px"
        android:background="@drawable/bg_btn_upgrade_now"
        android:gravity="center"
        android:paddingBottom="43px"
        android:paddingTop="43px"
        android:text="@string/reposition_dialog_reposition"
        android:textColor="@color/ota_upgrade_now"
        android:textSize="40px"
        app:layout_constraintStart_toStartOf="@id/vertical_view_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_line" />

</android.support.constraint.ConstraintLayout>