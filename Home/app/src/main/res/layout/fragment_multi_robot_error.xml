<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg">

    <ImageView
        android:id="@+id/iv_standby_bg"
        android:layout_width="92dp"
        android:layout_height="92dp"
        android:src="@drawable/ic_warning"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="146dp" />

    <LinearLayout
        android:id="@+id/ll_detail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/iv_standby_bg"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="24dp"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_alert_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_marginTop="29dp"
            android:text="@string/multi_robot_error_title"
            android:textColor="@color/white"
            android:textSize="20sp" />

        <TextView
            android:id="@+id/tv_alert_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:text="@string/multi_robot_error_message"
            android:textColor="@color/white"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_alert_detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:visibility="gone"
            android:textColor="@color/white"
            android:textSize="12sp" />

    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="80dp"
        android:layout_alignParentBottom="true"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_to_bind"
            android:layout_width="450px"
            android:layout_height="120px"
            android:background="@drawable/selector_locate_failure_see_solution"
            android:gravity="center"
            android:padding="6dp"
            android:text="@string/cancel_task"
            android:textColor="@color/white"
            android:textSize="@dimen/font_12" />

        <TextView
            android:id="@+id/tv_to_restore"
            android:layout_width="450px"
            android:layout_height="120px"
            android:layout_marginStart="20dp"
            android:background="@drawable/selector_locate_failure_btn"
            android:gravity="center"
            android:padding="6dp"
            android:text="@string/restart"
            android:textColor="@color/white"
            android:textSize="@dimen/font_12" />
    </LinearLayout>

</RelativeLayout>