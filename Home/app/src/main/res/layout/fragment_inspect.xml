<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/iv_img"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true"
        android:background="@drawable/inspect" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="300dp">

        <ImageView
            android:id="@+id/inspecting_loading"
            android:layout_width="70px"
            android:layout_height="70px"
            android:layout_gravity="center_horizontal"/>

        <TextView
            android:id="@+id/inspecting_tip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="73px"
            android:gravity="center"
            android:text="@string/inspection_bottom_title"
            android:textColor="@color/white"
            android:alpha="0.5"
            android:textSize="10sp"
            android:singleLine="true"
            android:ellipsize="end"/>

        <ImageView
            android:id="@+id/sample_img"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/sample"
            android:visibility="gone"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:background="@color/transparent" />
    </LinearLayout>


</RelativeLayout>