<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/shape_lite_lock_bg"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="80px"
        >

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:textSize="60px"
            android:textColor="#555D61"
            android:text="@string/tip_text"
            android:gravity="center_horizontal"></TextView>

    </LinearLayout>

    <TextView
        android:layout_marginTop="40px"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="#555D61"
        android:layout_gravity="center_vertical"
        android:gravity="center"
        android:lines="2"
        android:text="@string/push_map_detail"
        android:textSize="46px"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="60px"
        android:background="#55C3FB">

    </View>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="30px"
        android:background="@color/white"
        android:gravity="center"
        android:text="@string/push_map_ok"
        android:textSize="40px"
        android:textColor="#55C3FB"
        android:id="@+id/bt_close">
    </TextView>

</LinearLayout>
