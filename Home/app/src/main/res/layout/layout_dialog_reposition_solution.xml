<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2017 OrionStar Technology Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->


<android.support.constraint.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/dialog_reposition_solution"
    android:layout_width="940px"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/shape_locate_see_solution_dialog">
    <LinearLayout
        android:id="@+id/lin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="25dp"
        android:paddingStart="25dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <ImageView
            android:id="@+id/ic_charge_warning"
            android:layout_width="120px"
            android:layout_height="120px"
            android:layout_marginEnd="10dp"
            android:src="@drawable/charge_warning_dialog_img" />
        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="@string/reposition_fail"
            android:textColor="@color/charge_dialog_text"
            android:textSize="60px" />

    </LinearLayout>

    <TextView
        android:id="@+id/content"
        android:layout_width="760px"
        android:layout_height="65px"
        android:layout_marginTop="20dp"
        android:lineSpacingExtra="2dp"
        android:lineSpacingMultiplier="1.0"
        android:text="@string/please_check_reason"
        android:textColor="@color/charge_dialog_text"
        android:textSize="46px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/lin" />

    <RelativeLayout
        android:id="@+id/layout_reason1"
        android:layout_width="760px"
        android:layout_height="wrap_content"
        android:layout_marginTop="30px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/content">

        <TextView
            android:layout_width="21px"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="@string/dot_point"
            android:textColor="@color/charge_dialog_text"
            android:textSize="40px" />

        <TextView
            android:id="@+id/content_reason_1"
            android:layout_width="728px"
            android:layout_height="wrap_content"
            android:layout_marginStart="32px"
            android:text="@string/reason_loss_error_large"
            android:textColor="@color/charge_dialog_text"
            android:textSize="40px" />

    </RelativeLayout>


    <RelativeLayout
        android:id="@+id/layout_reason2"
        android:layout_width="760px"
        android:layout_height="wrap_content"
        android:layout_marginTop="20px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_reason1">

        <TextView
            android:layout_width="21px"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/dot_point"
            android:textColor="@color/charge_dialog_text"
            android:textSize="40px" />

        <TextView
            android:id="@+id/content_reason_2"
            android:layout_width="728px"
            android:layout_height="wrap_content"
            android:layout_marginStart="32px"
            android:text="@string/reason_map_wrong"
            android:textColor="@color/charge_dialog_text"
            android:textSize="40px" />

    </RelativeLayout>


    <View
        android:id="@+id/view_line"
        android:layout_width="match_parent"
        android:layout_height="2px"
        android:layout_marginTop="80px"
        android:background="@color/ota_view_line"
        app:layout_constraintTop_toBottomOf="@id/layout_reason2"/>

    <TextView
        android:id="@+id/confirm"
        android:layout_width="match_parent"
        android:layout_height="139px"
        android:gravity="center"
        android:text="@string/charging_warning_confirm"
        android:textColor="@color/ota_upgrade_now"
        android:background="@drawable/selector_get_it"
        android:textSize="41px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_line" />

</android.support.constraint.ConstraintLayout>
