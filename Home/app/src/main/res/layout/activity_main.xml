<?xml version="1.0" encoding="utf-8"?><!--
  Copyright (C) 2017 OrionStar Technology Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/fl_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <FrameLayout
        android:id="@+id/activity_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:alpha="50"
        android:visibility="gone">

        <Button
            android:id="@+id/test_emergency"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/string_main_emergency" />

        <Button
            android:id="@+id/test_emergency_close"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/string_main_close_emergency" />

        <Button
            android:id="@+id/test_ota"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/string_main_ota" />

        <Button
            android:id="@+id/test_pile"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/string_main_set_charge_pile" />

        <Button
            android:id="@+id/test_charge"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/string_main_go_charge" />

        <Button
            android:id="@+id/test_ota_result"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/string_main_ota_success" />

        <Button
            android:id="@+id/test_charge_start"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/string_main_charging" />

        <Button
            android:id="@+id/test_charge_end"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/string_main_not_charge" />
    </LinearLayout>

</FrameLayout>
