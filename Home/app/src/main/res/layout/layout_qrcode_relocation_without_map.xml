<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/com_bg">

    <ImageView
        android:layout_marginTop="57dp"
        app:layout_constraintTop_toBottomOf="@id/without_map_subtitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@drawable/location_img_withoutmap"
        android:layout_width="246dp"
        android:layout_height="246dp"/>

    <Button
        android:id="@+id/without_map_reboot_btn"
        android:layout_width="280dp"
        android:layout_height="34dp"
        android:layout_marginBottom="23dp"
        android:background="@drawable/selector_locate_failure_btn"
        android:text="@string/qrcode_reposition_without_map_reboot"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="11sp"
        app:layout_constraintBottom_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />


    <TextView
        android:id="@+id/reposition_without_map_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="16dp"
        android:drawableStart="@drawable/charge_set_cancel"
        android:gravity="center_vertical"
        android:text="@string/cancel"
        android:textColor="@color/white"
        android:textSize="13sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/without_map_subtitle"
        android:layout_width="294dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/reposition_qrcode_reminder_without_map"
        android:textAlignment="center"
        android:textColor="#99FFFFFF"
        android:textSize="13sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/without_map_title" />


    <TextView
        android:id="@+id/without_map_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="69dp"
        android:text="@string/qrcode_reposition_without_map_title"
        android:textAlignment="center"
        android:textColor="#FF713C"
        android:textSize="29sp"
        app:layout_constraintTop_toTopOf="parent" />


</android.support.constraint.ConstraintLayout>