<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent">

    <TextView
        android:id="@+id/locate_vision_show_title"
        android:layout_width="match_parent"
        android:layout_height="130px"
        android:text="@string/reposition_vision_show_title"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="100px"
        android:layout_marginTop="200px"
         />

    <TextView
        android:id="@+id/locate_vision_show_subtitle"
        android:layout_width="match_parent"
        android:layout_height="60px"
        android:layout_marginTop="30px"
        android:text="@string/reposition_vision_show_subtitle"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="46px" />

    <ImageView
        android:id="@+id/locate_vision_show_bg"
        android:layout_width="440px"
        android:layout_height="871px"
        android:layout_marginTop="220px"
        android:scaleType="fitXY"
        android:src="@drawable/reposition_vision_bg_img"
        android:visibility="visible" />


</LinearLayout>