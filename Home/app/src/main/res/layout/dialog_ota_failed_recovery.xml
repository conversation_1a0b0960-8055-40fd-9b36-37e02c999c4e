<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/bg_round_rect_white"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="90px"
        android:layout_marginTop="80px"
        android:drawablePadding="20px"
        android:text="@string/android_factory_reset"
        android:textColor="@color/black"
        android:textSize="46px" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/tv_nsd_confirm_host_prompt"
        android:layout_marginLeft="93px"
        android:layout_marginTop="86px"
        android:layout_marginRight="97px"
        android:text="@string/rollback_factory_reset_tips"
        android:textColor="#FF555D61"
        android:textSize="46px" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="99px"
        android:background="#E1E7EA" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="138px"
        android:dividerPadding="30px"
        android:gravity="center"
        android:orientation="horizontal"
        android:showDividers="middle">

        <TextView
            android:id="@+id/cancel"
            android:layout_width="0px"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/bg_btn_upgrade_next_time"
            android:gravity="center"
            android:text="@string/cancel"
            android:textColor="#FFCCD2D6"
            android:textSize="15sp" />

        <!-- Divider View -->
        <View
            android:layout_width="1px"
            android:layout_height="match_parent"
            android:background="#E1E7EA" />

        <TextView
            android:id="@+id/confirm"
            android:layout_width="0px"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/bg_btn_upgrade_now"
            android:gravity="center"
            android:text="@string/confirm"
            android:textColor="#FF55C3FB"
            android:textSize="15sp" />
    </LinearLayout>

</LinearLayout>