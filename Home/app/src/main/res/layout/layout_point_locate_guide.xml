<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/com_bg">

    <ImageButton
        android:id="@+id/elevator_reposition_cancel"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="10dp"
        android:background="@color/transparent"
        android:padding="10dp"
        android:src="@drawable/charge_set_cancel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:singleLine="true"
        android:text="@string/elevator_reposition"
        android:textColor="@color/white_ff"
        android:textSize="@dimen/font_28"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/gl_top_1" />

    <TextView
        android:id="@+id/current_floor"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:singleLine="true"
        android:text="@string/elevator_reposition_choose_floor"
        android:textColor="@color/white_AF"
        android:textSize="@dimen/font_16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title" />


    <ImageView
        android:id="@+id/bg_select_item"
        android:layout_width="match_parent"
        android:layout_height="38dp"
        android:layout_marginHorizontal="30dp"
        android:layout_marginTop="110dp"
        android:background="@drawable/bg_round_rect_white20"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/current_floor" />

    <ImageView
        android:id="@+id/bg_select_icon"
        android:layout_width="14dp"
        android:layout_height="14dp"
        android:layout_marginEnd="10dp"
        android:background="@drawable/icon_selected"
        app:layout_constraintBottom_toBottomOf="@id/bg_select_item"
        app:layout_constraintEnd_toEndOf="@id/bg_select_item"
        app:layout_constraintTop_toTopOf="@id/bg_select_item" />

    <com.ainirobot.home.ui.view.NumberPicker
        android:id="@+id/floorNumberPicker"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="30dp"
        android:descendantFocusability="blocksDescendants"
        android:minHeight="114dp"
        android:theme="@style/CustomNumberStyle"
        app:layout_constraintBottom_toBottomOf="@id/bg_select_item"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/bg_select_item"
        app:selectionDivider="@null" />

    <com.ainirobot.home.ui.view.NumberPicker
        android:id="@+id/poseNumberPicker"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="30dp"
        android:descendantFocusability="blocksDescendants"
        android:minHeight="114dp"
        android:theme="@style/CustomNumberStyle"
        app:layout_constraintBottom_toBottomOf="@id/bg_select_item"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/bg_select_item"
        app:selectionDivider="@null" />

    <!--    <LinearLayout-->
    <!--        android:id="@+id/bg_number_picker"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:background="@drawable/time_picker_background_drawable"-->
    <!--        app:layout_constraintEnd_toEndOf="parent"-->
    <!--        app:layout_constraintStart_toStartOf="parent"-->
    <!--        app:layout_constraintTop_toTopOf="@+id/floorNumberPicker"-->
    <!--        app:layout_constraintBottom_toBottomOf="@+id/floorNumberPicker"-->
    <!--        android:orientation="horizontal"/>-->


    <TextView
        android:id="@+id/confirmFloor"
        android:layout_width="match_parent"
        android:layout_height="34dp"
        android:layout_marginHorizontal="32dp"
        android:layout_marginBottom="83dp"
        android:background="@drawable/selector_locate_failure_btn"
        android:gravity="center"
        android:includeFontPadding="true"
        android:padding="8dp"
        android:text="@string/elevator_reposition_confirm_floor"
        android:textColor="@color/white_ff"
        android:textSize="@dimen/font_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/backToChoose"
        android:layout_width="match_parent"
        android:layout_height="34dp"
        android:layout_marginHorizontal="32dp"
        android:layout_marginBottom="32dp"
        android:background="@drawable/selector_anchor_question_btn"
        android:gravity="center"
        android:includeFontPadding="true"
        android:padding="8dp"
        android:text="@string/elevator_reposition_back_to_choose_floor"
        android:textColor="@color/white_ff"
        android:textSize="@dimen/font_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <TextView
        android:id="@+id/chargePileReposition"
        android:layout_width="match_parent"
        android:layout_height="34dp"
        android:layout_marginHorizontal="32dp"
        android:layout_marginBottom="32dp"
        android:background="@drawable/selector_anchor_question_btn"
        android:gravity="center"
        android:includeFontPadding="true"
        android:padding="8dp"
        android:text="@string/elevator_reposition_charge_pile"
        android:textColor="@color/white_ff"
        android:textSize="@dimen/font_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <LinearLayout
        android:id="@+id/ll_locate_success"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/success"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="true"
            android:padding="8dp"
            android:layout_marginBottom="57dp"
            android:text="@string/elevator_reposition_success"
            android:textColor="@color/white_ff"
            android:textSize="30sp" />

        <ImageView
            android:layout_width="200dp"
            android:layout_height="200dp"
            android:background="@drawable/location_img_success"
            android:layout_gravity="center_horizontal"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/success_des"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:includeFontPadding="true"
                android:textColor="@color/white_ff"
                android:textSize="@dimen/font_18"/>

            <TextView
                android:id="@+id/success_des_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:includeFontPadding="true"
                android:textColor="@color/white"
                android:textSize="@dimen/font_18"/>

        </LinearLayout>
    </LinearLayout>

    <android.support.constraint.Guideline
        android:id="@+id/gl_top_1"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintGuide_percent="0.120"
        app:layout_constraintStart_toStartOf="parent" />


    <android.support.constraint.Guideline
        android:id="@+id/gl_bottom"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintGuide_end="143dp"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageView
        android:id="@+id/bg_select_list"
        android:layout_width="360dp"
        android:layout_height="135dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/shape_round_rect_gray_8"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/current_floor" />
</android.support.constraint.ConstraintLayout>