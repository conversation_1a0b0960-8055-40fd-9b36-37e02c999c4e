<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:gravity="center"
    android:orientation="vertical">

    <TextView
        android:id="@+id/locate_failure_title"
        android:layout_width="match_parent"
        android:layout_height="130px"
        android:layout_marginTop="200px"
        android:text="@string/reposition_fail"
        android:textAlignment="center"
        android:textColor="@color/color_ff713c"
        android:textSize="100px"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/locate_failure_subtitle"
        android:layout_width="match_parent"
        android:layout_height="60px"
        android:layout_marginTop="30px"
        android:text="@string/reposition_fail_des"
        android:textAlignment="center"
        android:textColor="@color/color_ff713c"
        android:textSize="46px"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/locate_failure_title" />

    <ImageView
        android:id="@+id/locate_failure_bg"
        android:layout_width="match_parent"
        android:layout_height="270px"
        android:layout_marginTop="825px"
        android:scaleType="fitCenter"
        android:src="@drawable/charge_set_fail"
        android:visibility="visible"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="1195px"
        android:drawableLeft="@drawable/phone"
        android:gravity="center"
        android:text="@string/inspect_err_service_number"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</android.support.constraint.ConstraintLayout>