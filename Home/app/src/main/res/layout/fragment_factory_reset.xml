<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <!-- 顶部警告区域 -->
    <RelativeLayout
        android:id="@+id/rel_warning"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="180px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/im_warning"
            android:layout_width="160px"
            android:layout_height="160px"
            android:layout_marginStart="100px"
            android:src="@drawable/inspect_error_img" />

        <TextView
            android:id="@+id/tv_factory_reset_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginStart="30px"
            android:layout_marginEnd="50px"
            android:layout_toEndOf="@+id/im_warning"
            android:gravity="start"
            android:lineSpacingExtra="20px"
            android:text="@string/ota_factory_reset_title"
            android:textColor="@color/color_ff713c"
            android:textSize="50px" />
    </RelativeLayout>

    <!-- 内容说明区域 -->
    <TextView
        android:id="@+id/tv_factory_reset_content"
        android:layout_width="900px"
        android:layout_height="wrap_content"
        android:layout_marginStart="145px"
        android:layout_marginTop="60px"
        android:gravity="start"
        android:lineSpacingExtra="20px"
        android:text="@string/ota_factory_reset_content"
        android:textColor="@color/white"
        android:textSize="45px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rel_warning" />

    <!-- 原因说明区域 -->
    <TextView
        android:id="@+id/tv_factory_reset_reason"
        android:layout_width="900px"
        android:layout_height="wrap_content"
        android:layout_marginStart="145px"
        android:layout_marginTop="40px"
        android:gravity="start"
        android:lineSpacingExtra="16px"
        android:textColor="@color/white"
        android:textSize="40px"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_factory_reset_content" />

    <!-- 按钮区域 -->
    <LinearLayout
        android:id="@+id/ll_buttons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="80px"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_factory_reset_reason">

        <Button
            android:id="@+id/btn_factory_reset_cancel"
            android:layout_width="200dp"
            android:layout_height="60dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/shape_wheel_cancel"
            android:text="@string/ota_factory_reset_cancel"
            android:textColor="@color/white"
            android:textSize="40px" />

        <Button
            android:id="@+id/btn_factory_reset_confirm"
            android:layout_width="200dp"
            android:layout_height="60dp"
            android:layout_marginStart="20dp"
            android:background="@drawable/confirm_btn_bg"
            android:text="@string/ota_factory_reset_confirm"
            android:textColor="@color/white"
            android:textSize="40px" />

    </LinearLayout>

    <!-- 底部提示信息 -->
    <TextView
        android:id="@+id/tv_factory_reset_tips"
        android:layout_width="900px"
        android:layout_height="wrap_content"
        android:layout_marginStart="145px"
        android:layout_marginTop="60px"
        android:gravity="center"
        android:lineSpacingExtra="16px"
        android:text="@string/ota_factory_reset_tips"
        android:textColor="@color/white"
        android:textSize="35px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_buttons" />

</android.support.constraint.ConstraintLayout>
