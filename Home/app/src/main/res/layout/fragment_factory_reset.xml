<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <!-- 主要内容容器 - 垂直居中 -->
    <LinearLayout
        android:id="@+id/main_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_horizontal"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 警告图标和标题区域 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="60px">

            <ImageView
                android:id="@+id/im_warning"
                android:layout_width="160px"
                android:layout_height="160px"
                android:layout_marginEnd="30px"
                android:src="@drawable/inspect_error_img" />

            <TextView
                android:id="@+id/tv_factory_reset_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:lineSpacingExtra="20px"
                android:text="@string/ota_factory_reset_title"
                android:textColor="@color/color_ff713c"
                android:textSize="50px" />

        </LinearLayout>

        <!-- 内容说明区域 -->
        <TextView
            android:id="@+id/tv_factory_reset_content"
            android:layout_width="800px"
            android:layout_height="wrap_content"
            android:layout_marginBottom="40px"
            android:gravity="center"
            android:lineSpacingExtra="20px"
            android:text="@string/ota_factory_reset_content"
            android:textColor="@color/white"
            android:textSize="45px" />

        <!-- 原因说明区域 -->
        <TextView
            android:id="@+id/tv_factory_reset_reason"
            android:layout_width="800px"
            android:layout_height="wrap_content"
            android:layout_marginBottom="40px"
            android:gravity="center"
            android:lineSpacingExtra="16px"
            android:textColor="@color/white"
            android:textSize="40px"
            android:visibility="gone" />

        <!-- 按钮区域 -->
        <LinearLayout
            android:id="@+id/ll_buttons"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="40px"
            android:gravity="center"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_factory_reset_cancel"
                android:layout_width="200dp"
                android:layout_height="80dp"
                android:layout_marginEnd="30dp"
                android:background="@drawable/shape_wheel_cancel"
                android:text="@string/ota_factory_reset_cancel"
                android:textColor="@color/white"
                android:textSize="40px" />

            <Button
                android:id="@+id/btn_factory_reset_confirm"
                android:layout_width="200dp"
                android:layout_height="80dp"
                android:layout_marginStart="30dp"
                android:background="@drawable/confirm_btn_bg"
                android:text="@string/ota_factory_reset_confirm"
                android:textColor="@color/white"
                android:textSize="40px" />

        </LinearLayout>

        <!-- 底部提示信息 -->
        <TextView
            android:id="@+id/tv_factory_reset_tips"
            android:layout_width="800px"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:lineSpacingExtra="16px"
            android:text="@string/ota_factory_reset_tips"
            android:textColor="@color/white"
            android:textSize="35px" />

    </LinearLayout>

</android.support.constraint.ConstraintLayout>
