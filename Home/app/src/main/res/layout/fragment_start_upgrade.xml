<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/ota_bg_start_upgrade">


    <TextView
        android:id="@+id/describe"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="860px"
        android:text="@string/starting_upgrade"
        android:textColor="@android:color/white"
        android:textSize="40px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <android.support.constraint.Guideline
        android:id="@+id/left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="250px" />

    <android.support.constraint.Guideline
        android:id="@+id/right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_end="250px" />

    <ImageView
        android:id="@+id/bg_process"
        android:layout_width="585px"
        android:layout_height="12px"
        android:layout_marginTop="40px"
        android:background="@color/alpha_30"
        android:elevation="1px"
        android:scaleType="fitXY"
        app:layout_constraintStart_toStartOf="@id/left"
        app:layout_constraintEnd_toEndOf="@id/right"
        app:layout_constraintTop_toBottomOf="@id/describe" />

    <ImageView
        android:id="@+id/process_animation"
        android:layout_width="5px"
        android:layout_height="14px"
        android:scaleType="fitXY"
        android:src="@drawable/ota_progress_0"
        app:layout_constraintBottom_toBottomOf="@id/bg_process"
        app:layout_constraintStart_toStartOf="@id/bg_process"
        app:layout_constraintTop_toTopOf="@id/bg_process" />


    <android.support.constraint.Guideline
        android:id="@+id/middle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <ImageView
        android:id="@+id/ota_cancel"
        android:layout_width="50px"
        android:layout_height="50px"
        android:layout_marginTop="110px"
        android:layout_marginEnd="15px"
        android:background="@drawable/selector_ota_cancel"
        android:onClick="btnClick"
        app:layout_constraintEnd_toStartOf="@+id/tv_cancel"
        app:layout_constraintTop_toBottomOf="@id/process_animation" />

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="100px"
        android:clickable="true"
        android:focusable="true"
        android:onClick="btnClick"
        android:text="@string/cancel"
        android:textColor="@drawable/selector_ota_tv"
        android:textSize="38px"
        app:layout_constraintBottom_toBottomOf="@id/ota_cancel"
        app:layout_constraintEnd_toStartOf="@id/middle"
        app:layout_constraintTop_toTopOf="@id/ota_cancel" />

    <ImageView
        android:id="@+id/ota_pause_continue"
        android:layout_width="50px"
        android:layout_height="50px"
        android:layout_marginStart="100px"
        android:layout_marginEnd="15px"
        android:background="@drawable/selector_ota_pause"
        android:onClick="btnClick"
        app:layout_constraintBottom_toBottomOf="@+id/ota_cancel"
        app:layout_constraintStart_toEndOf="@id/middle"
        app:layout_constraintTop_toBottomOf="@id/process_animation"
        app:layout_constraintTop_toTopOf="@id/ota_cancel" />

    <TextView
        android:id="@+id/tv_ota_pause_continue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10px"
        android:onClick="btnClick"
        android:text="@string/ota_pause"
        android:textColor="@drawable/selector_ota_tv"
        android:textSize="38px"
        app:layout_constraintBottom_toBottomOf="@id/ota_cancel"
        app:layout_constraintStart_toEndOf="@id/ota_pause_continue"
        app:layout_constraintTop_toTopOf="@id/ota_cancel" />

    <TextView
        android:id="@+id/view_stub"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="85px"
        android:layout_marginTop="40px"
        android:layout_marginEnd="85px"
        android:gravity="center"
        android:text="@string/upgrade_complete_restart"
        android:textColor="@color/ota_upgrade_restart"
        android:textSize="38px"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/process_animation" />

</android.support.constraint.ConstraintLayout>