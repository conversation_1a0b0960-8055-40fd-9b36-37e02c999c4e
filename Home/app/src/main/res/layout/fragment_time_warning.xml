<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    android:background="@drawable/shape_bg_full_lock"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/top_icon"
        android:layout_width="90dp"
        android:layout_height="90dp"
        android:layout_gravity="center"
        android:src="@drawable/time_warning_img"
        app:layout_constraintTop_toBottomOf="@+id/gl_top_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="26dp"
        android:gravity="center"
        android:text="@string/time_warning_title"
        android:textColor="@color/white"
        android:textSize="20sp"
        app:layout_constraintTop_toBottomOf="@+id/top_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/time_warning_describe"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:id="@+id/time_warning_set_network_button"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_button_bg"
        android:gravity="center"
        android:text="@string/time_warning_set_network_button"
        android:textColor="@color/white"
        android:textSize="@dimen/font_18"
        android:maxLines="1"
        android:padding="8dp"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintTop_toBottomOf="@+id/gl_bottom"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/time_warning_skip_button"/>

    <TextView
        android:id="@+id/time_warning_skip_button"
        android:layout_width="120sp"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_button_bg"
        android:layout_gravity="center"
        android:gravity="center"
        android:text="@string/time_warning_skip_button"
        android:textColor="@color/white"
        android:textSize="@dimen/font_18"
        android:maxLines="1"
        android:padding="8dp"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintTop_toBottomOf="@+id/gl_bottom"
        app:layout_constraintStart_toEndOf="@+id/time_warning_set_network_button"
        app:layout_constraintEnd_toEndOf="parent"/>


    <TextView
        android:id="@+id/time_warning_skip_always_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="6dp"
        android:gravity="center"
        android:text="@string/time_warning_skip_always_button"
        android:textColor="@color/white"
        android:textSize="@dimen/font_12"
        android:maxLines="1"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintTop_toBottomOf="@+id/time_warning_skip_button"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <android.support.constraint.Guideline
        android:id="@+id/gl_top_icon"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintGuide_percent="0.200"
        android:orientation="horizontal"/>

    <android.support.constraint.Guideline
        android:id="@+id/gl_bottom"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintGuide_percent="0.700"
        android:orientation="horizontal"/>


</android.support.constraint.ConstraintLayout>