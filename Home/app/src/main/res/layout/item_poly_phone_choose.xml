<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="90px"
    android:paddingEnd="90px">

    <TextView
        android:id="@+id/spell_choose"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/ota_big_text"
        android:textSize="46px"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <HorizontalScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20px"
        android:overScrollMode="never"
        android:scrollbars="none"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/spell_choose">

        <RadioGroup
            android:id="@+id/radio_group"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

        </RadioGroup>
    </HorizontalScrollView>

</android.support.constraint.ConstraintLayout>