<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/shape_lite_lock_bg"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="80px"
        >
        <ImageView
            android:layout_marginStart="90px"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/ic_lock"
            ></ImageView>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:textSize="60px"
            android:textColor="#555D61"
            android:layout_marginStart="30px"
            android:text="@string/warning"></TextView>

    </LinearLayout>

    <TextView
        android:id="@+id/tv_msg"
        android:layout_marginStart="90px"
        android:layout_marginTop="40px"
        android:layout_width="760px"
        android:layout_height="wrap_content"
        android:textColor="#555D61"
        android:layout_gravity="center_vertical"
        android:gravity="center_vertical"
        android:lines="2"
        android:text="@string/lock_warning"
        android:textSize="46px"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="96px"
        android:layout_marginStart="430px"
        android:background="@color/white"
        android:text="@string/dialog_confirm"
        android:textSize="40px"
        android:textColor="#55C3FB"
        android:id="@+id/bt_close">
    </TextView>

</LinearLayout>
