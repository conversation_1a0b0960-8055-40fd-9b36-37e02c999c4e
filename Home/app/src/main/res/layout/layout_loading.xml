<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/im_loading"
        android:layout_width="70px"
        android:layout_height="70px"
        android:layout_marginTop="800px"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="50px"/>
    <TextView
        android:id="@+id/tv_loading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="140px"
        android:layout_marginEnd="140px"
        android:gravity="center"
        android:layout_centerInParent="true"
        android:textColor="#fff"
        android:layout_below="@+id/im_loading"
        android:text="@string/wait_loading"/>
</RelativeLayout>
