<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg">

    <android.support.constraint.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black_bg_B3">

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="80dp"
            android:layout_height="80dp"
            app:layout_constraintTop_toBottomOf="@+id/gl_top"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:background="@drawable/ble_warning_icon"/>

        <TextView
            android:id="@+id/ble_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_icon"
            android:layout_marginTop="3dp"
            android:text="@string/ble_robot_locked_title"
            android:textSize="34sp"
            android:textColor="@color/ble_title_color"
            android:gravity="center" />

        <TextView
            android:id="@+id/ble_title_des"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ble_title"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:layout_marginTop="3dp"
            android:text="@string/ble_robot_locked_title_des"
            android:textSize="15sp"
            android:textColor="@color/ble_title_des_color"
            android:gravity="center"
            android:ellipsize="end"/>

        <TextView
            android:id="@+id/ble_cancel"
            android:layout_width="200dp"
            android:layout_height="36dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/gl_bottom"
            android:layout_gravity="center"
            android:textSize="@dimen/font_14"
            android:textColor="#ffffff"
            android:text="@string/ble_exit_by_manual"
            android:gravity="center"
            android:background="@drawable/shape_ble_unlock"/>

        <android.support.constraint.Guideline
            android:id="@+id/gl_top"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.195"/>

        <android.support.constraint.Guideline
            android:id="@+id/gl_bottom"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.781"/>

    </android.support.constraint.ConstraintLayout>

</android.support.constraint.ConstraintLayout>