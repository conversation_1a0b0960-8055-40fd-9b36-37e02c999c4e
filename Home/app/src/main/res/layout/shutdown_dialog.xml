<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2017 OrionStar Technology Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->


<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_centerInParent="true"
    android:background="@color/white"
    android:orientation="vertical"
    android:gravity="center"
    android:elevation="1dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:text="@string/shutdown_title"
        android:textColor="#FF272727"
        android:textSize="17sp"
     />


    <TextView
        android:id="@+id/shutdown_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/shutdown_tips"
        android:textColor="#FF555D61"
        android:layout_marginTop="30dp"
        android:textSize="14sp"
        />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="40dp"
        android:background="@color/ota_view_line"
        />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/shutdown_cancel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1.0"
            android:background="@drawable/bg_btn_upgrade_next_time"
            android:gravity="center"
            android:paddingTop="43px"
            android:paddingBottom="43px"
            android:text="@string/shutdown_cancel"
            android:textColor="@color/ota_upgrade_next_time"
            android:textSize="14sp"
            />

        <View
            android:id="@+id/vertical_view_line"
            android:layout_width="1px"
            android:layout_height="80px"
            android:layout_gravity="center"
            android:background="@color/ota_vertical_line"
            />

        <TextView
            android:id="@+id/shutdown_now"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1.0"
            android:background="@drawable/bg_btn_upgrade_now"
            android:gravity="center"
            android:paddingTop="43px"
            android:paddingBottom="43px"
            android:text="@string/shutdown_now"
            android:textColor="@color/ota_upgrade_now"
            android:textSize="14sp"
            />

    </LinearLayout>

</LinearLayout>
