<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <ImageView
        android:id="@+id/iv_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scaleType="fitCenter"
        android:src="@drawable/inspect_title"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/load_map_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="353dp"
        android:alpha="0.5"
        android:ellipsize="end"
        android:gravity="center"
        android:singleLine="true"
        android:text="@string/loading_map"
        android:textColor="@color/white"
        android:textSize="10sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/bg_progress"
        android:layout_width="585px"
        android:layout_height="12px"
        android:layout_marginTop="40px"
        android:background="@color/alpha_30"
        android:elevation="1px"
        android:scaleType="fitXY"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/load_map_tip" />

    <ImageView
        android:id="@+id/load_map_progress"
        android:layout_width="5px"
        android:layout_height="14px"
        android:scaleType="fitXY"
        android:src="@drawable/ota_progress_0"
        app:layout_constraintBottom_toBottomOf="@id/bg_progress"
        app:layout_constraintStart_toStartOf="@id/bg_progress"
        app:layout_constraintTop_toTopOf="@id/bg_progress" />

    <TextView
        android:id="@+id/load_map_percent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:textColor="@android:color/white"
        android:textSize="40px"
        app:layout_constraintBottom_toBottomOf="@id/bg_progress"
        app:layout_constraintStart_toEndOf="@id/bg_progress"
        app:layout_constraintTop_toTopOf="@id/bg_progress" />

    <Button
        android:id="@+id/load_map_skip"
        android:layout_width="100dp"
        android:layout_height="30dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/confirm_btn_bg"
        android:text="@string/skip"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="11sp"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/load_map_percent" />
</android.support.constraint.ConstraintLayout>