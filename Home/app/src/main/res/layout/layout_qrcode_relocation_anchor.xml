<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/com_bg"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:layout_marginTop="57dp"
        app:layout_constraintTop_toBottomOf="@id/locate_guide_subtitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@drawable/location_img_point"
        android:layout_width="246dp"
        android:layout_height="246dp"/>

    <Button
        android:id="@+id/anchor_point_btn"
        android:layout_width="280dp"
        android:layout_height="34dp"
        android:layout_marginBottom="23dp"
        android:background="@drawable/selector_locate_failure_btn"
        android:text="@string/reposition_reposition"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="11sp"
        app:layout_constraintBottom_toTopOf="@+id/anchor_question_btn"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <Button
        android:id="@+id/anchor_question_btn"
        android:layout_width="280dp"
        android:layout_height="34dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/selector_anchor_question_btn"
        android:text="@string/anchor_title"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="11sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />


    <TextView
        android:id="@+id/locate_guide_subtitle"
        android:layout_width="294dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/reposition_qrcode_anchor_reminder_subtitle"
        android:textAlignment="center"
        android:textColor="#99FFFFFF"
        android:textSize="@dimen/font_locate_guide_sub_title2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/locate_result_title" />

    <TextView
        android:id="@+id/reposition_anchor_cancel_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="16dp"
        android:drawableStart="@drawable/qrcode_back"
        android:drawablePadding="10dp"
        android:gravity="center_vertical"
        android:text="@string/serch_sign"
        android:textColor="@color/white"
        android:textSize="13sp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/anchor_cancel_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="16dp"
        android:drawableStart="@drawable/charge_set_cancel"
        android:gravity="center_vertical"
        android:text="@string/cancel"
        android:textColor="@color/white"
        android:textSize="13sp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/locate_result_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="69dp"
        android:text="@string/reposition_anchor_title_qrCode"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="@dimen/font_locate_guide_title2"
        app:layout_constraintTop_toTopOf="parent" />


</android.support.constraint.ConstraintLayout>