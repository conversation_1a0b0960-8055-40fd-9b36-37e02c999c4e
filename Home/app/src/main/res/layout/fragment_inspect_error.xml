<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <RelativeLayout
        android:id="@+id/rel_warning"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="180px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/im_warning"
            android:layout_width="160px"
            android:layout_height="160px"
            android:layout_marginStart="100px"
            android:src="@drawable/inspect_error_img" />

        <TextView
            android:id="@+id/inspect_error_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginStart="30px"
            android:layout_marginEnd="50px"
            android:layout_toEndOf="@+id/im_warning"
            android:gravity="start"
            android:lineSpacingExtra="20px"
            android:text="@string/inspect_err_title"
            android:textColor="@color/color_ff713c"
            android:textSize="50px" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/inspect_shutdown"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@+id/rel_warning"
        tools:layout_editor_absoluteX="16dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/custom_background"
            android:orientation="horizontal"
            android:padding="10dp">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:gravity="center_vertical"
                android:src="@drawable/battery_icon" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:gravity="center_vertical"
                android:text="@string/inspect_err_btn_shutdown"
                android:textColor="@color/white"
                android:textSize="15sp" />
        </LinearLayout>
    </LinearLayout>


    <TextView
        android:id="@+id/tv_id_sn"
        android:layout_width="900px"
        android:layout_height="wrap_content"
        android:layout_marginStart="145px"
        android:layout_marginTop="20dp"
        android:gravity="start"
        android:lineSpacingExtra="16px"
        android:text="@string/inspect_err_robot_sn"
        android:textColor="@color/white"
        android:textSize="55px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/inspect_shutdown" />

    <TextView
        android:id="@+id/tv_id_sn_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="145px"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:text="@string/inspect_err_error"
        android:textColor="@color/white"
        android:textSize="40px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_id_sn" />

    <ScrollView
        android:id="@+id/error_msg_sv"
        android:layout_width="match_parent"
        android:layout_height="280px"
        android:layout_marginLeft="145px"
        android:layout_marginTop="20dp"
        android:scrollbars="none"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_id_sn_content">

        <TextView
            android:id="@+id/inspect_error_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:lineSpacingExtra="40px"
            android:text="xxxxxxxx\nxxxxxxxx\nxxxxxxxx\nxxxxxxxx\nxxxxxxxxxxx\nxxxxxxxxxxxxx\nxxxxxxxx\nxxxxxxxxxxx\nxxxxxxxxxxxxx"
            android:textColor="@color/white"
            android:textSize="40px" />
    </ScrollView>

    <ImageView
        android:id="@+id/check_bg_img"
        android:layout_width="1200px"
        android:layout_height="80px"
        android:layout_marginStart="0px"
        android:layout_marginTop="535dp"
        android:src="@drawable/check_bg_img"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/lxc_repair"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@+id/rl_feedback">

        <Button
            android:id="@+id/butt_lxc_repair"
            android:layout_width="180dp"
            android:layout_height="36dp"
            android:layout_centerHorizontal="true"
            android:background="@drawable/confirm_btn_bg"
            android:text="@string/lxc_repair"
            android:textColor="#ffffff"
            android:textSize="40px" />

        <Button
            android:id="@+id/button_version_roll_back"
            android:layout_width="180dp"
            android:layout_height="36dp"
            android:layout_marginTop="10dp"
            android:layout_centerHorizontal="true"
            android:background="@drawable/confirm_btn_bg"
            android:text="@string/version_detection"
            android:textColor="#ffffff"
            android:textSize="40px" />

    </LinearLayout>

    <RelativeLayout
        android:id="@+id/rl_feedback"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="1600px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_qr_code"
            android:layout_width="148px"
            android:layout_height="148px"
            android:layout_centerVertical="true"
            android:layout_marginStart="141px"
            android:src="@drawable/first_qrcode_logo"
            android:visibility="invisible" />

        <TextView
            android:id="@+id/tv_feedback_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginStart="40px"
            android:layout_marginEnd="50px"
            android:layout_toEndOf="@+id/iv_qr_code"
            android:gravity="start"
            android:lineSpacingExtra="20px"
            android:text="@string/inspect_err_feedback_tips"
            android:textColor="@color/white"
            android:textSize="40px"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tv_qr_code_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginStart="40px"
            android:layout_marginEnd="50px"
            android:layout_toEndOf="@+id/iv_qr_code"
            android:gravity="start"
            android:lineSpacingExtra="20px"
            android:text="@string/inspect_err_qr_code_tips"
            android:textColor="@color/white"
            android:textSize="40px"
            android:visibility="gone" />
    </RelativeLayout>


    <ImageView
        android:id="@+id/iv_phone_number"
        android:layout_width="42px"
        android:layout_height="42px"
        android:layout_marginStart="335px"
        android:layout_marginTop="1810px"
        android:src="@drawable/inspect_error_phone_img"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tv_phone_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="382px"
        android:layout_marginTop="1799px"
        android:text="@string/inspect_err_service_number"
        android:textColor="@color/white"
        android:textSize="40px"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />



</android.support.constraint.ConstraintLayout>