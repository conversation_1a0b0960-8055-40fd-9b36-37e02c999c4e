<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:gravity="center"
    android:orientation="vertical">

    <ImageButton
        android:id="@+id/reposition_cancel"
        android:layout_width="90px"
        android:layout_height="90px"
        android:layout_marginStart="16px"
        android:layout_marginTop="42px"
        android:src="@drawable/charge_set_cancel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@color/transparent"/>

    <TextView
        android:id="@+id/reposition_cancel_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4px"
        android:layout_marginTop="55px"
        android:text="@string/cancel"
        android:textSize="46px"
        app:layout_constraintStart_toEndOf="@id/reposition_cancel"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="invisible"/>

    <TextView
        android:id="@+id/locate_guide_title"
        android:layout_width="match_parent"
        android:layout_height="130px"
        android:layout_marginTop="200px"
        android:text="@string/reposition_title"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="100px"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/locate_guide_subtitle"
        android:layout_width="match_parent"
        android:layout_height="60px"
        android:layout_marginTop="30px"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="46px"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/locate_guide_title" />

    <com.ainirobot.home.ui.view.GifView
        android:id="@+id/locate_guide_bg"
        android:layout_width="wrap_content"
        android:layout_height="1000px"
        android:layout_marginTop="616px"
        android:visibility="visible"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <Button
        android:id="@+id/locate_guide_confirmBtn"
        android:layout_width="909px"
        android:layout_height="120px"
        android:background="@drawable/selector_locate_failure_btn"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="40px"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/locate_guide_bg" />

</android.support.constraint.ConstraintLayout>