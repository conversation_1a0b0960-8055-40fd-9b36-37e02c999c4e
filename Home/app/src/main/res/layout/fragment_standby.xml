<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000">

    <Button
        android:id="@+id/bt_exitStandby"
        android:layout_width="286dp"
        android:layout_height="46dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="68dp"
        android:background="@drawable/shape_standby_bt_resume"
        android:text="@string/exit_standby"
        android:textColor="#FFFFFF"
        android:textSize="18sp"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/iv_standby_bg"
        android:layout_width="282dp"
        android:layout_height="209dp"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="108dp"
        android:src="@drawable/standby_sleep_bg" />

    <TextView
        android:id="@+id/tv_switchHint"
        android:layout_width="match_parent"
        android:layout_height="122dp"
        android:layout_alignParentBottom="true"
        android:background="#55C3FB"
        android:gravity="center"
        android:textColor="#FFFFFF"
        android:textSize="16sp"
        android:visibility="visible" />

    <TextView
        android:id="@+id/tv_chargingMsg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="23dp"
        android:layout_marginEnd="15dp"
        android:drawablePadding="6dp"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_standby"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="297dp"
        android:text="@string/standby"
        android:textColor="#FFFFFF"
        android:textSize="29sp" />

    <TextView
        android:id="@+id/tv_standby_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_standby"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="11dp"
        android:text="@string/standby_hint"
        android:textColor="#FFFFFF"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="63dp"
        android:drawablePadding="6dp"
        android:textColor="#FFFFFF"
        android:textSize="23sp"
        android:visibility="gone" />

    <Button
        android:id="@+id/butt_reboot"
        android:layout_width="286dp"
        android:layout_height="46dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="68dp"
        android:background="@drawable/shape_standby_bt_resume"
        android:textColor="#FFFFFF"
        android:visibility="gone"
        android:text="@string/cloud_reboot"
        android:textSize="@dimen/font_16" />


</RelativeLayout>