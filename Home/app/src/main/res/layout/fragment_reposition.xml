<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    >

    <include
        android:visibility="gone"
        android:id="@+id/locate_vision_show"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/layout_reposition_vision_show"/>

    <include
        android:visibility="gone"
        android:id="@+id/locate_vision_failure"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/layout_reposition_vision_failure"/>

    <include
        android:visibility="gone"
        android:id="@+id/charge_locate_guide"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/layout_reposition_guide"/>

    <include
        android:visibility="gone"
        android:id="@+id/locate_search"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/layout_reposition_search" />

    <include
        android:visibility="gone"
        android:id="@+id/locate_success"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/layout_reposition_success"/>

    <include
        android:visibility="gone"
        android:id="@+id/locate_failure"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/layout_reposition_failure"/>

</android.support.constraint.ConstraintLayout>
