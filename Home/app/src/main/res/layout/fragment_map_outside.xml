<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000">

    <ImageView
        android:id="@+id/iv_standby_bg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@id/tv_alert_title"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="43dp"
        android:src="@drawable/bg_out_map_warning" />

    <TextView
        android:id="@+id/tv_alert_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/tv_alert_message"
        android:layout_marginBottom="29dp"
        android:gravity="center"
        android:text="@string/out_map_warning_alert_title"
        android:textColor="#FFFFFF"
        android:textSize="57sp" />

    <TextView
        android:id="@+id/tv_alert_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="40dp"
        android:gravity="center"
        android:text="@string/out_map_warning_alert_message"
        android:textColor="#FFFFFF"
        android:textSize="29sp" />


</RelativeLayout>