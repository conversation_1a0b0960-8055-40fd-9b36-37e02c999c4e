<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/item_image"
        android:layout_width="180px"
        android:layout_height="180px" />

    <TextView
        android:id="@+id/item_text"
        android:layout_width="190px"
        android:layout_height="wrap_content"
        android:layout_marginTop="30px"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="2"
        android:textSize="10.86sp"
        app:layout_constraintTop_toBottomOf="@id/item_image" />

</android.support.constraint.ConstraintLayout>