<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="940px"
        android:layout_height="820px"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="400px"
        android:background="@drawable/bg_round_rect_white">
        <TextView
            android:id="@+id/tv1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="60px"
            android:text="@string/reset"
            android:layout_marginTop="84px"
            android:layout_marginStart="90px"
            android:textColor="#555D61"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="60px"
            android:text="@string/fail"
            android:layout_below="@+id/tv1"
            android:layout_marginTop="20px"
            android:layout_marginStart="90px"
            android:textColor="@color/text_fail"/>
        <ImageView
            android:layout_width="360px"
            android:layout_height="360px"
            android:layout_alignParentEnd="true"
            android:src="@drawable/pic_error_img"/>
        <TextView
            android:id="@+id/tv_fail_reason"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="46px"
            android:textColor="#555D61"
            android:layout_marginStart="90px"
            android:layout_centerVertical="true"
            android:text=""/>
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#555D61"
            android:textSize="38px"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="220px"
            android:layout_marginStart="95px"
            android:text="@string/reset_fail"/>
        <View
            android:id="@+id/view_line"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="#E1E7EA"
            android:layout_marginBottom="170px"
            android:layout_alignParentBottom="true" />
        <TextView
            android:id="@+id/tv_reboot"
            android:layout_width="match_parent"
            android:layout_height="170px"
            android:text="@string/reboot"
            android:gravity="center"
            android:textSize="40px"
            android:textColor="@color/text_fail"
            android:layout_alignParentBottom="true"/>
    </RelativeLayout>


</RelativeLayout>
