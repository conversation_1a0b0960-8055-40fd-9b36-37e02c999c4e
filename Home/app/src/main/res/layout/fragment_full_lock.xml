<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_bg_full_lock"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <ImageView
        android:background="@drawable/ic_lock_new"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="380px">
    </ImageView>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/lock_device"
        android:textSize="80px"
        android:textColor="#FF713C"
        android:layout_marginTop="120px"
        android:gravity="center_horizontal" />

    <TextView
        android:id="@+id/tv_lockmsg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:text="@string/contact_owner"
        android:textColor="#FF713C"
        android:layout_marginTop="38px"
        android:textSize="60px"
        />
    <TextView
        android:id="@+id/tv_sn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:textColor="#FF713C"
        android:layout_marginTop="30px"
        android:text="SN:"
        android:textSize="60px"/>

</LinearLayout>