<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <TextView
        android:id="@+id/inspect_again"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="57dp"
        android:gravity="center"
        android:text="@string/inspect_bind_fail_up"
        android:textColor="@color/color_ff713c"
        android:textSize="100px"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="113dp"
        android:gravity="center"
        android:text="@string/inspect_bind_fail_down"
        android:textColor="@color/color_ff713c"
        android:textSize="100px"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="77dp"
        android:layout_marginTop="236dp"
        android:src="@drawable/inspect_error_img"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView android:id="@+id/tv_des"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="462dp"
        android:alpha="0.5"
        android:gravity="center"
        android:text="@string/inspect_not_clear_data"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:layout_constraintTop_toTopOf="parent" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="115px"
        android:layout_marginEnd="115px"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="420dp"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/tv_to_bind"
            android:layout_width="450px"
            android:layout_height="120px"
            android:padding="6dp"
            android:gravity="center"
            android:text="@string/inspect_to_bind"
            android:textSize="@dimen/font_12"
            android:textColor="@color/white"
            android:background="@drawable/selector_locate_failure_btn"/>
        <TextView
            android:id="@+id/tv_to_restore"
            android:layout_alignParentEnd="true"
            android:layout_width="450px"
            android:layout_height="120px"
            android:padding="6dp"
            android:gravity="center"
            android:text="@string/inspect_restore_setting"
            android:textSize="@dimen/font_12"
            android:textColor="@color/white"
            android:background="@drawable/selector_locate_failure_btn"/>
    </RelativeLayout>

</android.support.constraint.ConstraintLayout>