<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="49px"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_language"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:layout_gravity="center_vertical"
            android:layout_weight="1.0"
            android:textColor="#ffffff"
            android:textSize="46px"/>

        <ImageView
            android:id="@+id/im_select"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_gravity="center_vertical"
            android:src="@drawable/radio_normal"/>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="49px"
        android:layout_alignParentBottom="true"
        android:background="#26FFFFFF"/>

</LinearLayout>
