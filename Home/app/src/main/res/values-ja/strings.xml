<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">ホーム</string>
    <!--OTA-->
    <string name="find_new_version">新しいバージョンが見つかりました</string>
    <string name="ota_upgrade_content">1.AR写真機能\n2.bug修正\n3.テストアップデート1\n4.テストアップデート2\n5.テストアップデート3\n 6.テストアップデート4</string>
    <string name="ota_upgrade_complete">アップデート完了後、ロボットは自動的に再起動します。その期間はロボットをシャットダウンしないでください。</string>
    <string name="upgrade">アップデート</string>
    <string name="charge">充電</string>
    <string name="starting_upgrade">アップデートパッケージをダウンロード中…%1$d%%</string>
    <string name="upgrade_complete_restart">アップデート完了後ロボットは自動で再起動します。その間はロボットをシャットダウしないでください</string>
    <string name="mobile_data_confirm_upgrade">モバイルデータを使用しています、本当にアップロードパッケージをダウンロードしますか？</string>
    <string name="no_tip">リマインダーなし</string>
    <string name="tip_text">リマインダー</string>
    <string name="ota_rollback_success">ロールバック成功しました</string>
    <string name="ota_rollback_fail">ロールバック失敗しました、顧客サービスに連絡してください400&#8211;898&#8211;7779</string>
    <string name="inspect_err_title">ロボットに例外が発生しました。 \nロボットをシャットダウンして再起動してみてください。</string>
    <string name="charge_lower_power_going_tts">充電に行きます</string>
    <string name="charge_lower_power_title_des_saiph">私に充電してください</string>
    <string name="charge_lower_power_auto_charge">充電に自動帰還(%1$ss)</string>
    <string name="charge_lower_power_would_shut_down">電池残量が低下しています。自動的にシャットダウンします。</string>
    <string name="charge_stop_dialog_title">今は充電中です</string>
    <string name="charging_text">充電中</string>
    <string name="charging_warning_dialog_text_saiph">落ち着いて、以下の方法を試してロボットを回復してください</string>
    <string name="charging_warning_dialog_text">充電台の指示ライトは緑色の時、充電状態正常を示しています \nほかの場合は以下の原因を検査してください：</string>
    <string name="charging_warning_dialog_reason_saiph">1. コンセントとの接続を確認してください。 \n2. 充電器のコネクタを確認してください。 \n3. 充電器を再び差し込みます。 \n4. カスタマー サービスにお問い合わせください。 </string>
    <string name="charge_fail">自動充電失敗</string>
    <string name="charge_success_voice">自動充電成功</string>
    <string name="charge_pile_success">設定成功しました、充電を開始します</string>
    <string name="charge_pile_success_wc">充電台設定成功しました</string>
    <string name="charge_fail_go_pile">自動充電失敗しました、私を充電台に押してください</string>
    <string name="charge_fail_not_exist_target_pose">充電に失敗しました。地図ツール 充電台で描画してください</string>
    <string name="tts_check_chargepile_is_moved">測位失敗しました、充電台は移動されましたか、また充電台とロボットは関連されていません</string>
    <string name="reposition_title_qrCode">ロボットを近くのマーカーまでゆっくり押してください</string>
    <string name="reposition_noset_charge_position">充電台の位置を設定されていません</string>
    <string name="reposition_reset_failed_qrcode">測位失敗しました、もう一度ほかのマーカーの下に押してください</string>
    <string name="reposition_vision_fail_title">自動測位失敗しました</string>
    <string name="reposition_vision_success">自動測位成功しました</string>
    <string name="reposition_manual_success">測位成功しました</string>
    <string name="reposition_fail">測位失敗しました</string>
    <string name="emergency_describe">安全を確認後、画像指示を参照して、ロボットを復元してください</string>
    <string name="standby_switchHint_start">ホスト後ろの機能ボタンを押してください</string>
    <string name="push_map_detail">"マップ更新中、ロボットを移動しないでください"</string>
    <string name="volume_hight">最大音量になりました</string>
    <string name="volume_low">最小音量になりました</string>
    <string name="volume_up_done">音量が上がりました</string>
    <string name="volume_down_done">音量が下がりました</string>
    <string name="volume_set_done">音量設定成功</string>
    <string name="not_support_chinese_placeholder">現在、漢字はサポートされていません: %1$s</string>
    <string name="charge_low_tips">電池切れです、私は自動シャットダウンします。私を充電台に押してください</string>
    <string name="reposition_remote_title">ロボットリモート測位</string>
    <string name="reposition_tts_turn_circle">自動測位中，私の周囲に集まらないでください</string>
    <string name="reposition_vision_relocate_success">自動測位成功しました</string>
    <string name="wake_up_not_conform_rules">ウェイクワードは変則です、詳細はWeChatアカウントに参照してください</string>
    <string name="bind_tts_1">アクティベーション成功しました、私は%1$sと申します。</string>
    <string name="speech_status_navi_out_map_please_edit">目的地へ到着できません、マップのノイズを消すか、または再測位してください</string>
    <string name="speech_status_navi_global_path_failed_please_edit_map">目的地へ到着できません、マップを検査するか、またはロボットを通行可能な場所に押してください</string>
    <string name="string_main_close_emergency">緊急停止を解除</string>
    <string name="string_main_ota_success">アップデート成功しました</string>
    <string name="anchor_content">測位地点とは、設置スタッフが設定したロボットの位置測定のための地点です。測位地点が不明な場合は担当スタッフ、又はサポートセンターへご連絡ください。</string>
    <string name="anchor_subTitle">以下の原因を検査してください：</string>
    <string name="anchor_subContent">· 測位地点は移動されましたか？\n· ロボットが向かっている方向を確認してください</string>
    <string name="wait_loading">ファクトリーリセット処理中、少々お待ちくださ</string>
    <string name="password_error">パスワードが正しくありません</string>
    <string name="remote_control">リモート操作中</string>
    <string name="bind_robot_prompt">ユーザープラットフォーム — 設定 — 企業情報 — ロボットを手動連携認証に入って、そしてインフォメーシを入力してください</string>
    <string name="reposition_qrcode_reminder_subtitle"><font color="#FFFFFF">ゆっくり</font>走行ルートに沿って押してください</string>
    <string name="reposition_qrcode_reminder_tts1">インテリジェント サービス ロボットへようこそ。私をコード ターゲットの下に押し込んでください。</string>
    <string name="reposition_qrcode_reminder_tts2">インテリジェント サービス ロボットへようこそ。上記のコード ターゲットを探す道に沿って私を押してください。</string>
    <string name="reposition_qrcode_anchor_reminder_subtitle">ロボットを測位地点に配置し、ロボットの向きが測地地点の設定と一致することを確認してください。正しく配置をした後に、<font color="#FFFFFF">“測位開始”</font>ボタンをタップして測位を行ってください。</string>
    <string name="reposition_qrcode_reminder_tts">インテリジェント サービス ロボットへようこそ。ロボットを Coded Targets の下に押し込んで、ポジショニング開始をタップしてください。前方を空けるように心がけてください。</string>
    <string name="shutdown_tips">&lt;Data&gt;<![CDATA[予測<font color="#FF713C">%1$d</font>秒後シャットダウンします]]>&lt;/Data&gt;</string>
    <string name="shutdown_navi_tips">現在シャットダウンタイマーの時間内のため、電源がオフになっている可能性があります</string>
    <string name="shutdown_start_tts">シャットダウンします</string>
    <string name="auto_charge_low_battery_tip">バッテリーの残量が極めて少なくなっています。もうすぐ自動的にシャットダウンします。充電をお願いします。</string>
    <string name="charge_success_title">ロボットは赤い線の位置に合わせることを確認してください</string>
    <string name="charge_success_complete_title">設定成功しました</string>
    <string name="charge_start_point_title">充電台に押してください</string>
    <string name="ota_upgrade_no_complete">電池残量が50%以上であることを確認してください</string>
    <string name="ota_upgrade_no_complete_saiph">電池残量が20%以上であることを確認してください</string>
    <string name="ota_upgrade_no_complete_mini">電池残量が30%以上であることを確認してください</string>
    <string name="upgrade_next_time">また今度</string>
    <string name="upgrade_now">自動%1$s（%2$dS）</string>
    <string name="force_upgrade_now">間もなく%1$s（%2$dS）</string>
    <string name="ota_wait">間もなくアップデートします</string>
    <string name="pause_download">一時停止…%1$d%%</string>
    <string name="installing_upgrade">アップデートインストール中、少々お待ちください…</string>
    <string name="night_mobile_data">夜間の自動アップデート中でも、モバイルデータを使用します</string>
    <string name="dialog_confirm">確認</string>
    <string name="ota_continue">次へ</string>
    <string name="ota_pause">一時停止</string>
    <string name="ota_warning">電源スイッチをリリースしてください\nそうしないと機械に損害を与えるかもしれません</string>
    <string name="ota_start">アップデート開始</string>
    <string name="ota_success">アップデート成功しました</string>
    <string name="ota_fail_rollback">アップデート失敗しました、ロールバックをします</string>
    <string name="ota_fail_no_rollback">アップデート失敗しました</string>
    <string name="ota_fail_download">ダウンロード失敗しました、もう一度やり直してください</string>
    <string name="ota_log_battery">電池残量低下</string>
    <string name="ota_can_not_use">アップデート中のためご利用いただけません。</string>
    <!--自检-->
    <string name="inspect_err_service_number">顧客サービス：400&#8211;898&#8211;7779</string>
    <string name="inspect_err_error">エラーメッセージ</string>
    <string name="inspection_bottom_title">起動中です、しばらくお待ちください</string>
    <string name="inspect_running">自主検査中...</string>
    <string name="inspect_err_navigation_log_id">エラーID：\n</string>
    <string name="inspect_err_navigation_log_id_land">エラーID：</string>
    <string name="inspect_err_robot_sn">ロボットSN：\n</string>
    <string name="inspect_err_robot_sn_land">ロボットSN：</string>
    <string name="inspect_err_qr_code_tips">まずはシャットダウンしてから、再起動してください。</string>
    <string name="inspect_err_feedback_tips">まずはシャットダウンしてから、再起動してください。</string>
    <string name="inspect_err_speech_tips">ロボットに例外が発生しました。 ロボットをシャットダウンして再起動してみてください。</string>
    <string name="inspect_err_btn_shutdown">シャットダウン</string>
    <string name="inspect_err_qr_code_tips_saiph">再起動してから再試行してください</string>
    <string name="inspect_err_feedback_tips_saiph">再起動してから再試行してください</string>
    <!-- charge -->
    <string name="charge_setting">設定中</string>
    <string name="charge_fail_complete_title">設定失敗しました</string>
    <string name="charge_fail_title">自動充電失敗しました</string>
    <string name="charge_fail_des">私をゆっくりで充電台に押してください</string>
    <string name="wire_charge_des_locate_tips">電池残量が少ないです、充電器に接続してください</string>
    <string name="charge_fail_des_locate">私をゆっくり測位地点に押してください</string>
    <string name="charge_fail_des_not_exist_target_pose">地図ツール 充電台で描いてください</string>
    <string name="charge_confirm">確認(%1$ss)</string>
    <string name="charge_lower_power_title_des">充電台に戻ります</string>
    <string name="charge_lower_power_title_des_locate">充電地点に帰ります</string>
    <string name="charge_stop_dialog_content">本当に充電を終了しますか？</string>
    <string name="charge_is_going_charge_pile">充電台に行きます</string>
    <string name="charge_is_going_locate_pile">測位地点に行きます</string>
    <string name="charging_warning_subText">充電ケーブルの接続を確認してください。</string>
    <string name="charging_level">00%</string>
    <string name="charging_warning_title">電池残量低下</string>
    <string name="charging_warning">ソリューションを見る</string>
    <string name="charging_warning_dialog_title">電池残量低下</string>
    <string name="charging_warning_dialog_reason_1">· 接触不良かもしれません、改めてコンセントに挿入してください\n· 充電器が汚い場合はティッシュなどで充電器を拭き、もう一度コンセントに挿入してください。</string>
    <string name="charging_warning_confirm">了解</string>
    <string name="charging_warning_stop_charging">充電効率は低いです。再充電をします</string>
    <string name="charge_pile_fail">充電台設定失敗しました</string>
    <string name="charge_fail_retry">自動充電失敗しました、再試行します</string>
    <string name="please_check_reason">以下の原因を検査してください：</string>
    <string name="dot_point">·</string>
    <string name="reason_loss_error_large">測位誤差は大きすぎます、マップの充電台位置を検査してください</string>
    <string name="reason_map_wrong">マップエラー、マップの設定を検査してください</string>
    <string name="reason_chargepile_moved">充電台は移動されました</string>
    <string name="reason_multi_chargepile">複数の充電台が存在します、このロボット関連する充電台でもう一度試してください。</string>
    <string name="reason_reboot">すみません、接続エラーのために測位ができません、ロボットを再起動してください。</string>
    <string name="tts_check_chargepile_loc">測位失敗しました、マップで充電台の位置を検査してください。</string>
    <string name="tts_fail_common">申し訳ありませんが、測位失敗しました。ロボットを再起動するか、ロボットの設定で再試行してください。</string>
    <!--<string name="charge_pile">充电桩</string>-->
    <!--reposition-->
    <string name="reposition_title">ロボット測位</string>
    <string name="reposition_title_loading">読み込み中...</string>
    <string name="reposition_anchor_title_qrCode">測位地点にロボットを配置してください</string>
    <string name="qrcode_reposition_without_map_title">マップ読み込み失敗しました</string>
    <string name="reposition_not_anchor_title_qrCode">測位地点が見つかりません</string>
    <string name="reposition_button">測位(%1$ss)</string>
    <string name="reposition_noset_locate_position">測位地点はまだ設定されていません</string>
    <string name="reposition_reset_failed">再測位失敗しました、もう一度試してください</string>
    <string name="reposition_reset_remind_msg">私を充電台に押してください</string>
    <string name="reposition_reset_remind_msg_locate">私を測位地点に押してください</string>
    <string name="reposition_reset_success">再測位成功しました</string>
    <string name="qrcode_reposition_success">測位成功しました</string>
    <string name="qrcode_reposition_success_tts">測位を完了しました。作業開始できます。</string>
    <string name="qrcode_anchor_point_tts">私を測位地点に押してください、そして測位地点で設定された位置及び方向と一致することを確認してください。 </string>
    <string name="reposition_reset_vision_failure">自動測位失敗しました</string>
    <string name="reposition_vision_show_title">自動測位中...</string>
    <string name="reposition_vision_show_subtitle">私の周囲に集まらないでください</string>
    <string name="reposition_failure_vision_retry">再試行</string>
    <string name="reposition_failure_manaul">充電台で測位</string>
    <string name="reposition_doing">測位中...</string>
    <string name="reposition_fail_des">カスタマーサービスにご連絡ください</string>
    <string name="locate_pile_reposition">測位地点で測位する</string>
    <!--emergency-->
    <string name="emergency_title">緊急停止中</string>
    <string name="time_warning_title">システム時刻が正しくありません</string>
    <string name="time_warning_describe">インターネットに接続して同期してください</string>
    <string name="time_warning_set_network_button">ネットワークを設定</string>
    <string name="time_warning_skip_button">後で対処する</string>
    <string name="time_warning_skip_always_button">再起動までに表示しない</string>
    <string name="calibration_tip">深度カメラのキャリブレーション\n所要時間は約3分です…</string>
    <!--standby-->
    <string name="standby">スリープ中</string>
    <string name="standby_hint">スリープ中にロボットを押さないでください。</string>
    <string name="standby_charging">充電中%1$d%%</string>
    <string name="standby_battery">電池残量%1$d%%</string>
    <string name="standby_charging_slowly">充電効率低いです%1$d%%</string>
    <string name="exit_standby">ウェイクアップ</string>
    <string name="standby_restoring">復元中(%1d)</string>
    <string name="standby_energency">緊急停止中...</string>
    <string name="standby_switchHint_end">ロボットを呼び起こす</string>
    <!--lock-->
    <string name="lock_device">あなたの設備はロックされました</string>
    <string name="contact_owner">アカウントマネージャーに連絡してロックを解除してください</string>
    <string name="warning">警告</string>
    <string name="lock_warning">ロボットはロックされていますアカウントマネージャーに連絡してロックを解除してください</string>
    <string name="push_map_ok">確認</string>
    <!-- currency -->
    <string name="cancel">キャンセル</string>
    <string name="serch_sign">道に沿ってコードターゲットを探してください</string>
    <!-- volume -->
    <string name="volume_set_ignore_hint">すみません、権限が必要です</string>
    <!-- wakeup word -->
    <string name="delete">削除</string>
    <string name="no_deletable_word">削除できるウェイクワードがありません。</string>
    <string name="delete_success">削除成功しました</string>
    <string name="delete_failture">削除失敗しました</string>
    <string name="wake_up_need_chinese">ウェイクワードは2~6文字の中国語しかサポートされていません</string>
    <string name="set_success">設定成功しました</string>
    <string name="set_failture">設定失敗しました</string>
    <string name="polyphone_select_placeholder">多音字選択： %1$s</string>
    <string name="rollback_upgrade">アップデート失敗しました，ロールバック中...</string>
    <string name="ota_continue_fail">アップデート失敗を検出しました、間もなく修復します</string>
    <string name="ota_continue_install">アップデート未完成を検出しました，間もなくアップデートを続行します</string>
    <string name="ota_continue_rollback">ロールバック未完成を検出しました，間もなくロールバックを続行します</string>
    <string name="ota_inspection_failed">アップデートエラー</string>
    <string name="ota_confirm">確認(%1$d)</string>
    <string name="charge_lower_power_title">電池残量が低下しています、今すぐ充電してください</string>
    <string name="charge_lower_power_title_for_saiph">電池残量低下</string>
    <string name="charge_need_estimate_first">先ずは測位してください</string>
    <string name="charge_success">充電台に接触しました</string>
    <!--obstacles avoid-->
    <string name="excuse_me_0">すみません、ちょっと道を譲ってくれませんか</string>
    <string name="excuse_me_1">すみません、私は行かなくちゃいけません</string>
    <string name="excuse_me_2">すみません、すこし道を譲ってください</string>
    <string name="excuse_me_3">すみません、私を通らせてください</string>
    <string name="excuse_me_4">すみません、私を通らせていただけませんか</string>
    <string name="reposition_please_switch_map">先にまっぷを切り替えてください</string>
    <string name="reposition_reposition">測位地点に到着しました、測位を開始します</string>
    <string name="retry_reposition">再測位</string>
    <string name="qrcode_reposition_without_map_reboot">今すぐ再起動</string>
    <string name="reposition_remote_estimate_success">リモート再測位成功しました</string>
    <string name="reposition_remote_estimate_failure">リモート再測位失敗しました</string>
    <string name="reposition_remote_estimate_time_out">リモート再測位時間切れました</string>
    <string name="reposition_vision_fail_subtitle">私を充電台に押してください</string>
    <string name="reposition_dialog_reposition">測位続行</string>
    <string name="give_up">放棄</string>
    <string name="custom_wake_up_word">喚起時のパスワードを設定</string>
    <string name="support_text_length"><font size="10">2-6漢字をサポートされています</font></string>
    <string name="inspect_bind_fail_up">連携されていません</string>
    <string name="inspect_bind_fail_down">使用できません</string>
    <string name="inspect_to_bind">連携認証</string>
    <string name="inspect_restore_setting">初期化</string>
    <string name="inspect_not_clear_data">アクティベートボタンをタップしても、ローカルデータは削除されません。 </string>
    <string name="wx_qr_code_title">WeChatでスキャンして連携を認証</string>
    <string name="wx_qr_code_tts">WeChatでQRコードをスキャンして、ロボット連携を認証してください</string>
    <string name="wx_qr_code_loading">QRコードを取得中…</string>
    <string name="wx_qr_code_loading_success">WeChatでQRコードをスキャンして、ロボットを連携してください</string>
    <string name="wx_qr_code_loading_fail">QRコード取得失敗しました</string>
    <string name="wx_qr_code_load_retry">再試行</string>
    <string name="wx_qr_code_load_retry_again">QRコードを再取得</string>
    <string name="wx_qr_code_load_retry_again_des">QRコード期限切れました</string>
    <string name="bind_success_title">おめでとうございます！連携成功しました</string>
    <string name="bind_success_has_map">OK</string>
    <!-- onStatusUpdate speech text -->
    <string name="speech_status_navi_avoid">すみません、ちょっと道を譲ってくれませんか？</string>
    <string name="speech_is_charging">充電中ですので、しばらくしてからまたお越しください</string>
    <string name="bind_code_for_pc">PCで連携：</string>
    <string name="sn_number_for_pc">ロボットSNの下6桁:</string>
    <string name="string_main_emergency">緊急停止</string>
    <string name="string_main_ota">アップデート</string>
    <string name="string_main_set_charge_pile">充電台に設定</string>
    <string name="string_main_go_charge">充電に行く</string>
    <string name="string_main_charging">充電</string>
    <string name="string_main_not_charge">充電終了</string>
    <string name="reposition_cancel_dialog">測位を中止しますか？\nロボットは動作できません</string>
    <string name="anchor_title">測位地点とは？</string>
    <string name="anchor_ensure">了解</string>
    <string name="speech_charging_warning">充電効率低下です、私を助けてください</string>
    <string name="radar_is_opening">レーダー起動中</string>
    <string name="radar_open_success">レーダー起動成功しました</string>
    <string name="situ_service_end">静止サービス終了しました</string>
    <string name="situ_service_start">静止サービス開始しました</string>
    <string name="auto_situ_service_start">人が多すぎます、静止サービスを開始します</string>
    <string name="hw_state_disconnected">以下のシステム異常があります。\n 回復試行中、数分間がかかります…\n\n  もし長い間画像は変化しないなら、ロボットを再起動してください。</string>
    <string name="hw_state_head_error">1.ビジョンシステム異常です</string>
    <string name="hw_state_chassis_error">1.シャーシシステム異常です</string>
    <string name="hw_state_all_error">1.ビジョンシステム異常です;\n\n 2.シャーシシステム異常です.\n</string>
    <string name="hw_state_timer">カウントダウン：(%1$ds)</string>
    <string name="hw_state_reboot">手動再起動</string>
    <string name="adk_questions">質問</string>
    <string name="response_lite_lite_page">充電終了した後でお客様にサビースを提供します</string>
    <string name="reset">初期化</string>
    <string name="fail">失敗しました</string>
    <string name="reset_fail">ファクトリーリセット失敗しました、強制再起動してください</string>
    <string name="reboot">再起動(%1$ss)</string>
    <string name="input_password">パスワードを入力してください</string>
    <!--以下为未替换的繁体中文信息-->
    <string name="dormancy_text">ロボットは（%1$sS）後スリープに入ります</string>
    <string name="dormancy_play_tts">間もなくスリープモードに入ります</string>
    <string name="dormancy_loading">スリープ開始中</string>
    <string name="dormancy_fail">スリープ失敗しました</string>
    <string name="confirm">確認</string>
    <string name="ota_version">1.03</string>
    <string name="bind_robot">ロボット連携を認証</string>
    <string name="last_sn">ロボットSNの下6桁</string>
    <string name="binding_code">連携コード</string>
    <string name="binding_code_expired">連携コード期限切れました</string>
    <string name="refresh_code">コードを更新する</string>
    <string name="reposition_anchor_point_exist">他の方法で測位</string>
    <string name="reposition_qrcode_reminder_without_map">ロボットを再起動で回復を試してください、回復できない場合はカスタマーサービスに連絡してください。</string>
    <!-- 定时关机 -->
    <string name="shutdown_now">今すぐシャットダウン</string>
    <string name="shutdown_cancel">シャットダウンをキャンセル</string>
    <string name="shutdown_title">シャットダウンタイマー</string>
    <string name="shutdown_speech">ロボットは%1$d秒後シャットダウンします</string>
    <string name="shutdown_navi_failed">%1$sに移動失敗しました</string>
    <string name="shutdown_navi_succeed">%1$sに到着しました</string>
    <string name="reception_pose">接待地点</string>
    <string name="go_location_pose">&lt;Data&gt;<![CDATA[<font color="#FFFFFF">%1$s</font>に移動してシャットダウンします]]>&lt;/Data&gt;</string>
    <string name="hw_chassis_disconnected">シャーシシステムに異常があります</string>
    <string name="hw_auto_recovery">回復試行中...</string>
    <string name="hw_retry_recovery">回復試行中(%1$ds)</string>
    <string name="hw_all_error_info">ビジョンシステムとシャーシシステムに異常があります</string>
    <string name="hw_head_error_info">ビジョンシステムに異常があります</string>
    <string name="hw_recovery_suc">回復成功しました</string>
    <string name="hw_recovery_fail">自動回復失敗しました</string>
    <string name="hw_recovery_fail_des">自動回復失敗しました、下のボタンを押して再起動してください</string>
    <string name="remote_control_navigation">に行きます</string>
    <string name="inspect_err_after_sales_number">顧客サービスライン：400&#8211;898&#8211;7779</string>
    <string name="shutdown_speech_delivery">ロボットは%1$d秒後でシャットダウンします</string>
    <string name="shutdown_speech_reminder">ロボットは%1$d秒後でシャットダウンします、充電することを忘れないでください</string>
    <string name="positioning_spot">測位地点</string>
    <string name="stand_by_spot">待機地点</string>
    <!-- 地图漂移 -->
    <string name="map_drift_cannot_estimate">正確に位置を測定できません</string>
    <string name="map_drift_robot_was_pushed">ロボットの位置が移動しています。正しい位置に再配置してください。</string>
    <string name="map_drift_check_radar_status">レーダー状態検査中</string>
    <string name="map_drift_check_estimate_status">測位状態検査中</string>
    <string name="map_drift_reload_map">マップを再読み込み中</string>
    <string name="map_drift_reload_map_fial">マップ読み込み失敗しました、ロボットを再起動してください</string>
    <string name="map_drift_start_reposition">再測位開始</string>
    <string name="charge_timing_title">充電時間になりました。充電を始まります。</string>
    <string name="charge_timing_describe"> %1$s 秒後には充電に行きます</string>
    <string name="default_language_changed">%1$s言語パックは無効になりました、自動的に%2$sに切り替えました</string>
    <string name="language_chinese">中国語</string>
    <string name="language_english">英語</string>
    <string name="language_korean">韓国語</string>
    <string name="language_japanese">日本語</string>
    <!-- 出地图-->
    <string name="map_outside_enter">ロボットがマップ範囲から離れています</string>
    <string name="out_map_warning_alert_title">警告</string>
    <string name="out_map_warning_alert_message">私をマップの外に押さないでください</string>
    <string name="please_charging_robot">充電することを忘れないでください</string>
    <string name="push_map_title">マップ更新中</string>
    <string name="push_map_subtitle">マップ更新中です、ロボットを移動させないでください</string>
    <string name="push_map_success">マップ更新成功しました</string>
    <string name="push_map_fail">マップ更新失敗しました</string>
    <!-- PSB硬件异常-->
    <string name="hw_psb_error">クリティカル・エラー</string>
    <string name="please_try_to_recover">次のことを試してください。</string>
    <string name="psb_recover_step1">手順1：電源ボタンを20秒間押し続けて、電源を強制的にオフにします。</string>
    <string name="psb_recover_step2">手順2：電源を切った後、もう一度電源ボタンを5秒間押して、電源を入れます。</string>
    <string name="get_help_hint">または、テクニカルサポートにお問い合わせください。</string>
    <string name="error_code">エラーコード：</string>
    <string name="psb_recover_step1_saiph">・現状を維持し、担当者にご連絡ください</string>
    <string name="psb_recover_step2_saiph">· TEL:400&#8211;898&#8211;7779</string>
    <string name="binding_code_loading">連携認証コード取得中</string>
    <!-- BLE 锁定 -->
    <string name="ble_robot_locked_title">ロボットがロックされています</string>
    <string name="ble_robot_locked_title_des">ロボットは危険エリアの近くでロックされています。ロックを解除するには、%1$s</string>
    <string name="ble_tip_leave_dangerous_place">まず危険な場所から離れてください</string>
    <string name="ble_robot_dangerous_tts">ロボットは危険エリアに接近したため、システムからロックされています。</string>
    <string name="ble_exit_by_manual">管理者によるロック解除</string>
    <string name="ble_danger_tts">ルートが見つかりません。管理者に連絡してください。</string>
    <!-- Wheel Over -->
    <string name="wheel_over_title">ロボットホイール異常が発生しました</string>
    <string name="wheel_over_des">1. ホイールに異物がないかチェックし、問題がないかチェックし、[復元] をクリックします。 \n2. [復元] で解決しない場合は、ロボットを再起動してみてください。 \n3. 再起動でも問題が解決しない場合は、できるだけ早くテクニカルサポートに連絡してください。</string>
    <string name="wheel_over_cancel">キャンセル</string>
    <string name="wheel_over_confirm">戻す</string>
    <string name="wheel_over_again">ホイールは異常</string>
    <!-- 多机调度异常-->
    <string name="multi_robot_error_title">スケジューリング異常</string>
    <string name="multi_robot_error_message">画面の写真を撮り、担当者にご連絡ください。エラーコード：</string>
    <string name="cancel_task">タスクをキャンセル</string>
    <string name="restart">再起動で回復</string>
    <string name="multi_robot_map_not_match">マルチマシン マップが一致しません</string>
    <string name="multi_robot_lora_disconnect">複数台の通信切断</string>
    <string name="multi_robot_lora_config_fail">マルチマシン情報設定エラー</string>
    <string name="multi_robot_version_not_match">マルチマシンのバージョンが一致しません</string>
    <!-- 解绑后重新激活时，云服务节点选择-->
    <string name="cloud_server_select">サーバー</string>
    <string name="cloud_europe">ヨーロッパ</string>
    <string name="cloud_us_west">米国西部</string>
    <string name="cloud_other_regions">その他</string>
    <string name="cloud_tips">適切なサーバーを選びください。ご不明な点がある場合は担当者にご連絡ください。</string>
    <string name="cloud_current">使用中のサーバー： %1$s</string>
    <string name="cloud_not_this"><u>このサーバーではない</u></string>
    <string name="cloud_reboot">再起動</string>
    <string name="cloud_select_ok">OK</string>
    <string name="cloud_select_cancel">キャンセル</string>
    <!-- 新增统一定位流程和多楼层定位文案-->
    <string name="remote_stop_charging_title">ロボットを充電パイルから離れてください</string>
    <string name="remote_stop_charging_title_sub">ロボットの前方に立たないでください</string>
    <string name="elevator_reposition">測位してください</string>
    <string name="elevator_reposition_choose_floor">フロアを選択してください</string>
    <string name="elevator_reposition_current_floor"><![CDATA[<font color="#FFFFFF">“%1$s”</font>現在フロアです]]></string>
    <string name="elevator_reposition_try_locate">測位してください</string>
    <string name="elevator_reposition_confirm_floor">フロアを確認</string>
    <string name="elevator_reposition_charge_pile">%1$s 充電パイルまで戻してください</string>
    <string name="elevator_reposition_charge_pile_point_not_exist">充電パイルの地点は存在しません</string>
    <string name="elevator_reposition_switch_map_success">マップ切り替え成功</string>
    <string name="elevator_reposition_switch_map_fail">マップ切り替え失敗</string>
    <string name="elevator_reposition_back_to_choose_floor">フロアを切り替える</string>
    <string name="elevator_reposition_success">測位成功しました</string>
    <string name="elevator_reposition_success_des">測位: %1$s</string>
    <string name="elevator_reposition_fail">再測位失敗しました</string>
    <string name="elevator_reposition_charge_remind_msg">私を充電パイルに戻してください</string>
    <string name="elevator_reposition_no_navi_map">使用するマップをご確認ください</string>
    <string name="elevator_reposition_not_find_locate_points">現在のフロア マップには場所がありません</string>
    <string name="elevator_reposition_not_find_locate_map">現在のナビゲーション マップはマルチフロア リストにありません</string>
    <string name="relocate_back_text">移転する</string>
    <string name="relocate_by_anchor_point">アンカーポイントの配置</string>
    <string name="relocate_by_anchor_point_not_found">現在の地図ではアンカー ポイントが検出されていません。別の配置方法を選択してください</string>
    <string name="relocate_by_charge_pile">充電パイル位置</string>
    <string name="relocate_by_charge_pile_not_found">マップ内に充電パイルを検出できません。他の測位方法を選択してください</string>
    <string name="relocate_by_charge_pile_des">充電パイルに戻すことで測位を完成できます</string>
    <string name="charge_fail_not_exist_target_pose_positioning_point">充電に失敗、マップツールで測位地点を設定してください。</string>
    <string name="charge_fail_des_not_exist_target_pose_positioning_point">マップツールで測位地点を設定してください</string>
    <string name="navi_to_goal_exception">目標地点航行時の車体異常</string>
    <string name="navi_sensor_error">ナビセンサーデータ異常</string>
    <string name="navi_sensor_error_des">復元するにはロボットを再起動してください</string>
    <string name="reposition_state_ok">ポジショニングは正常です。再配置する必要はありません</string>
    <string name="relocate_by_vision">ビジョンモード</string>
    <string name="not_vision_map">SLAMモード</string>
    <string name="reposition_vision_tips">ロボットを押し移動させ、測位を行ってください</string>
    <string name="reposition_vision_reminder_title">測位エリアまで移動し、測位を行ってください</string>
    <string name="reposition_vision_reminder_subtitle">道が分かりません。測位エリアまで移動し測位を行ってください</string>
    <string name="map_not_compatible">現在のバージョンは低いです。アップグレードしてからもう一度お試しください</string>
    <string name="charging_bms_low_temp_title">低温充電</string>
    <string name="charging_bms_low_temp_tip1">現在の温度は %s°C、低温充電状態です</string>
    <string name="charging_bms_low_temp_tip2">バッテリーの寿命を確保するには、充電のために温度が 10°C を超える環境に移動してください。</string>
    <string name="low_temp_tts">おっと、現在の温度は充電の進行に影響を与えるには低すぎます。充電するには暖かい環境に移動してください!</string>
    <string name="radar_fail">レーダーの起動に失敗しました</string>
    <string name="cloud_jp_server">ジャパンクラウド</string>
    <string name="switch_map">スイッチマップ</string>
    <string name="set_multi_robot_config">複数のマシン情報を構成する</string>
    <string name="lxc_repair">LXC修理</string>
    <string name="loading_map">地図を読み込んでいます。お待ちください</string>
    <string name="skip">スキップ</string>
    <string name="remote_stop_charging_avoid_failed">ロボットの前に障害物があります。</string>
    <string name="remote_stop_charging_radar_failed">レーダーの起動に失敗しました</string>
    <string name="remote_stop_charging_unknown_failed">充電の停止に失敗しました</string>
    <string name="open_electric_door">ドアを開ける</string>
    <string name="close_electric_door">ドアを閉める</string>
    <string name="close_electric_door_please">まずドアを閉めてください</string>
    <string name="switch_map_loading">マップを切り替えています...</string>
    <string name="stop_charging_tips">ロボットが充電を終了して充電パイルから離れる場合は、それを避けるように注意してください。</string>
    <string name="reset_head_failed">ヘッドの再配置に失敗しました。もう一度お試しください</string>
    <string name="import_map_success">インポート成功</string>
    <string name="import_map_fail">インポート失敗</string>
    <string name="stop_charge_confirm">充電されていないことが検出されました。充電を停止するかどうかを確認してください。</string>
    <string name="version_roll_back">バージョンロールバック</string>
    <string name="downgrade_failed">ダウングレードに失敗しました</string>
    <string name="android_factory_reset">Androidの工場出荷時設定へのリセット</string>
    <string name="rollback_factory_reset_tips">ロールバックに失敗しました。工場出荷時の設定に戻してください。工場出荷時の設定に戻すと、ローカル データが消去されるため、インターネットに接続してロボットの設定を再度取得する必要があります。</string>
    <string name="find_history_version">歴史的なバージョンを発見</string>
    <string name="ota_downgrade_content">現在のバージョン (%1$s) が過去のバージョンよりも新しいことが検出されました。インストール後、一部の機能が使用できなくなります。過去のバージョンをインストールしますか?</string>
    <string name="version_detection">バージョン検出</string>
    <string name="charge_voice_stop_charging_err">充電モードではありません</string>
    <string name="charge_receive_voice_mode_error">充電モードエラー</string>
    <string name="charge_leaving_tips">充電ステーションを出る際は、前方に十分なスペースがあることを確認してください。</string>
    <string name="charge_leaving_title">充電終了</string>
    <string name="charge_wait_in_area">充電待ち</string>
    <string name="charge_wait_in_area_des">充電ステーションは使用中なので、現在待機中です。</string>
    <string name="cancel_charge">充電をキャンセル</string>
    <string name="leave_pile_title">充電ステーションを出る</string>
    <string name="go_standby_point">待機地点へ向かう</string>
    <string name="charge_task_has_stopped">充電タスクは終了しました</string>
    <string name="wait_lift_title">エレベーター待ち</string>
    <string name="go_in_lift_title">エレベーターに入る</string>
    <string name="in_lift_title">エレベーター内</string>
    <string name="go_out_lift_title">エレベーターから出る</string>
    <string name="stop_right_now">すぐに停止</string>
    <string name="please_set_main_floor">複数階充電のメインフロアを設定してください</string>
</resources>
