<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/first_bg">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/gl_title_top"
        android:layout_marginStart="31dp"
        android:text="@string/wx_qr_code_title"
        android:textColor="@color/white_ff"
        android:textSize="@dimen/font_18"/>


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/qr_code"
        android:layout_marginBottom="10dp"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/sn_number"
            android:layout_width="90dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="131dp"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white_AF"
                android:singleLine="true"
                android:textSize="@dimen/font_11"
                android:text="@string/sn_number_for_pc"/>
            <TextView
                android:id="@+id/sn_number_tx"
                android:layout_width="85dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:singleLine="true"
                android:textColor="@color/white_AF"
                android:textSize="@dimen/font_14"
                android:letterSpacing="0.1"
                android:background="@drawable/first_set_line_img"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/bind_code_pc"
            android:layout_width="90dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginStart="15dp"
            android:layout_marginTop="131dp"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white_AF"
                android:singleLine="true"
                android:textSize="@dimen/font_11"
                android:text="@string/bind_code_for_pc"/>
            <TextView
                android:id="@+id/bind_code_tx"
                android:layout_width="85dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:singleLine="true"
                android:textColor="@color/white_AF"
                android:textSize="@dimen/font_14"
                android:letterSpacing="0.1"
                android:background="@drawable/first_set_line_img"/>

        </LinearLayout>
    </LinearLayout>

    <ImageView
        android:id="@+id/qr_code"
        android:layout_width="140dp"
        android:layout_height="140dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <RelativeLayout
        android:id="@+id/rl_loading"
        android:layout_width="140dp"
        android:layout_height="140dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:gravity="center"
        android:background="@color/white_1A">

        <ImageView
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_centerInParent="true"
            android:src="@drawable/first_qrcode_loading"/>
    </RelativeLayout>

    <TextView
        android:id="@+id/qr_code_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/wx_qr_code_loading"
        android:textColor="@color/white_ff"
        android:textSize="@dimen/font_11"
        android:singleLine="true"
        android:gravity="center"
        android:layout_marginTop="5dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/gl_des_top"/>

    <TextView
        android:id="@+id/qr_code_load_retry"
        android:layout_width="200dp"
        android:layout_height="28dp"
        app:layout_constraintTop_toBottomOf="@+id/gl_bottom"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:padding="6dp"
        android:includeFontPadding="true"
        android:gravity="center"
        android:textColor="@color/white_ff"
        android:textSize="@dimen/font_12"
        android:background="@drawable/selector_locate_failure_btn"/>

    <Button
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:includeFontPadding="true"
        android:layout_marginTop="24dp"
        android:gravity="center"
        android:textSize="@dimen/font_14"
        android:onClick="ignoreBind"
        android:background="@color/transparent"
        android:visibility="gone"/>

    <android.support.constraint.Guideline
        android:id="@+id/gl_title_top"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.057"/>

    <android.support.constraint.Guideline
        android:id="@+id/gl_des_top"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.714"/>
    <android.support.constraint.Guideline
        android:id="@+id/gl_bottom"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.796"/>



</android.support.constraint.ConstraintLayout>