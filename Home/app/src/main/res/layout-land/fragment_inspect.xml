<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <ImageView
        android:id="@+id/iv_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scaleType="fitCenter"
        android:src="@drawable/inspect_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <!--200px * 200px-->
    <ImageView
        android:id="@+id/inspecting_loading"
        android:layout_width="30dp"
        android:layout_height="30dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_img"
        android:layout_marginTop="10dp">
    </ImageView>

    <TextView
        android:id="@+id/inspecting_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:alpha="0.5"
        android:ellipsize="end"
        android:gravity="center"
        android:singleLine="true"
        android:text="@string/inspection_bottom_title"
        android:textColor="@color/white_ff"
        android:textSize="10sp"
        android:layout_marginTop="20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/inspecting_loading"/>

    <ImageView
        android:id="@+id/sample_img"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/sample"
        android:visibility="gone"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true"
        android:background="@color/transparent" />
</android.support.constraint.ConstraintLayout>