<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/system_module_bg"
    android:gravity="center"
    android:orientation="vertical">

    <ImageButton
        android:id="@+id/reposition_cancel"
        android:layout_width="90px"
        android:layout_height="90px"
        android:layout_marginStart="16px"
        android:layout_marginTop="42px"
        android:src="@drawable/charge_set_cancel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@color/transparent"/>

    <TextView
        android:id="@+id/reposition_cancel_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4px"
        android:layout_marginTop="55px"
        android:text="@string/cancel"
        android:textSize="46px"
        app:layout_constraintStart_toEndOf="@id/reposition_cancel"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/locate_guide_title"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:layout_marginTop="35dp"
        android:text="@string/reposition_remote_title"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="24sp"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/locate_guide_subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="@dimen/font_14"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/locate_guide_title" />

    <com.ainirobot.home.ui.view.GifView
        android:id="@+id/locate_guide_bg_remote"
        android:layout_width="400dp"
        android:layout_height="170dp"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/locate_guide_subtitle" />

</android.support.constraint.ConstraintLayout>