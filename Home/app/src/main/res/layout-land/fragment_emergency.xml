<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/system_module_bg">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_emergency"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20px"
            android:gravity="center"
            android:text="@string/emergency_title"
            android:textColor="#ff713c"
            android:textSize="100px" />

        <TextView
            android:id="@+id/tv_emergency2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30px"
            android:gravity="center"
            android:text="@string/emergency_describe"
            android:textColor="#ff713c" />

        <com.ainirobot.home.ui.view.GifView
            android:id="@+id/gif_emergency"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="49px" />
    </LinearLayout>

    <Button
        android:id="@+id/electric_door_btn"
        android:layout_width="180dp"
        android:layout_height="24dp"
        android:layout_gravity="center_horizontal|bottom"
        android:layout_marginBottom="8dp"
        android:background="@drawable/selector_locate_failure_btn"
        android:text="@string/open_electric_door"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="11sp"
        android:visibility="gone" />

</FrameLayout>