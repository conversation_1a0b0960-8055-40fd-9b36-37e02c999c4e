<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <ImageView
        android:background="@drawable/ic_lock_new"
        android:layout_width="63dp"
        android:layout_height="63dp"
        android:layout_marginTop="71dp">
    </ImageView>
    
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/lock_device"
        android:textSize="17sp"
        android:textColor="#FF713C"
        android:layout_marginTop="18dp"
        android:gravity="center_horizontal" />

    <TextView
        android:id="@+id/tv_lockmsg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:text="@string/contact_owner"
        android:textColor="#7FFFFFFF"
        android:layout_marginTop="8dp"
        android:textSize="11sp"
        />
    <TextView
        android:id="@+id/tv_sn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:textColor="#7FFFFFFF"
        android:layout_marginTop="8dp"
        android:text="SN:"
        android:textSize="11sp"/>

</LinearLayout>