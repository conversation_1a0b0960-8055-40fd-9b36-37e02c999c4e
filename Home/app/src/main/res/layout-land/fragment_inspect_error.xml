<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">
    <RelativeLayout
        android:id="@+id/rel_warning"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <ImageView
            android:id="@+id/im_warning"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/inspect_error_img" />

        <TextView
            android:id="@+id/inspect_error_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="30px"
            android:layout_toEndOf="@+id/im_warning"
            android:gravity="start"
            android:lineSpacingExtra="6dp"
            android:maxWidth="425dp"
            android:text="@string/inspect_err_title"
            android:textColor="@color/color_ff713c"
            android:textSize="15sp" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/inspect_shutdown"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@+id/tv_id_sn">

        <LinearLayout
            android:layout_width="130dp"
            android:layout_height="30dp"
            android:padding="5dp"
            android:background="@drawable/shape_wheel_error"
            android:orientation="horizontal"
            android:gravity="center">

            <ImageView
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:gravity="center_vertical"
                android:src="@drawable/battery_icon" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:gravity="center_vertical"
                android:text="@string/inspect_err_btn_shutdown"
                android:textColor="@color/white"
                android:textSize="13sp" />
        </LinearLayout>
    </LinearLayout>

    <TextView
        android:id="@+id/tv_id_sn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:text="@string/inspect_err_robot_sn_land"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:lineSpacingExtra="4dp"
        app:layout_constraintTop_toBottomOf="@+id/rel_warning"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <android.support.constraint.Guideline
        android:id="@+id/gl_top"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.468"/>


    <TextView
        android:id="@+id/errors"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/inspect_err_error"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:layout_marginTop="20dp"
        android:layout_marginStart="90dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/gl_top"/>

    <ScrollView
        android:layout_width="0dp"
        android:layout_height="108dp"
        android:layout_marginTop="2dp"
        android:scrollbars="none"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@id/errors"
        android:layout_marginEnd="20dp"
        app:layout_constraintEnd_toStartOf="@id/layout_qrcode"
        app:layout_constraintTop_toBottomOf="@+id/errors">

        <TextView
            android:id="@+id/inspect_error_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:lineSpacingExtra="6dp"
            android:textColor="@color/white"
            android:textSize="10sp" />
    </ScrollView>

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="80px"
        android:src="@drawable/check_bg_img"
        app:layout_constraintTop_toBottomOf="@+id/gl_top"
        app:layout_constraintStart_toStartOf="@+id/layout_qrcode"
        android:visibility="gone"/>

    <LinearLayout
        android:id="@+id/layout_qrcode"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="300dp"
        app:layout_constraintTop_toBottomOf="@+id/gl_top"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="20dp"
        android:orientation="vertical">
        <ImageView
            android:id="@+id/iv_qr_code"
            android:layout_width="42dp"
            android:layout_height="42dp"
            android:src="@drawable/first_qrcode_logo"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_feedback_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="10dp"
            android:gravity="start"
            android:lineSpacingExtra="5dp"
            android:text="@string/inspect_err_feedback_tips"
            android:textColor="@color/white"
            android:visibility="visible"
            android:textSize="10sp"
            />

        <TextView
            android:id="@+id/tv_qr_code_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:gravity="start"
            android:lineSpacingExtra="4dp"
            android:text="@string/inspect_err_qr_code_tips"
            android:textColor="@color/white"
            android:textSize="@dimen/font_12"
            android:visibility="gone" />
    </LinearLayout>


    <ImageView
        android:id="@+id/iv_phone_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/inspect_error_phone_gray"
        app:layout_constraintTop_toBottomOf="@+id/layout_qrcode"
        app:layout_constraintStart_toStartOf="@id/layout_qrcode"
        android:layout_marginTop="10dp"/>

    <TextView
        android:id="@+id/tv_phone_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/inspect_err_service_number"
        android:textColor="#808591"
        android:textSize="@dimen/font_12"
        android:layout_marginStart="4dp"
        app:layout_constraintTop_toTopOf="@+id/iv_phone_number"
        app:layout_constraintBottom_toBottomOf="@id/iv_phone_number"
        app:layout_constraintStart_toEndOf="@+id/iv_phone_number"/>

    <LinearLayout
        android:id="@+id/lxc_repair"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@+id/tv_phone_number"
        android:orientation="vertical">

        <Button
            android:id="@+id/butt_lxc_repair"
            android:layout_width="140dp"
            android:layout_height="30dp"
            android:layout_centerHorizontal="true"
            android:background="@drawable/confirm_btn_bg"
            android:text="@string/lxc_repair"
            android:textColor="#ffffff"
            android:textSize="40px" />

        <Button
            android:id="@+id/button_version_roll_back"
            android:layout_width="140dp"
            android:layout_height="30dp"
            android:layout_marginTop="10dp"
            android:layout_centerHorizontal="true"
            android:background="@drawable/confirm_btn_bg"
            android:text="@string/version_detection"
            android:textColor="#ffffff"
            android:textSize="40px" />

    </LinearLayout>


</android.support.constraint.ConstraintLayout>