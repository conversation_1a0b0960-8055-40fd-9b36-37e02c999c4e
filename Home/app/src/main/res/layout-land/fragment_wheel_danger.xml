<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg">

    <ImageView
        android:id="@+id/iv_wheel"
        android:layout_width="150dp"
        android:layout_height="150dp"
        app:layout_constraintTop_toBottomOf="@+id/gl_top"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="52dp"
        android:src="@drawable/wheel_danger"
        android:scaleType="fitCenter"/>


    <TextView
        android:id="@+id/wheel_error_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/iv_wheel"
        app:layout_constraintBottom_toTopOf="@+id/wheel_error_title_des"
        app:layout_constraintStart_toEndOf="@+id/gl_start"
        app:layout_constraintEnd_toEndOf="parent"
        android:gravity="start|center"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:singleLine="true"
        android:ellipsize="marquee"
        android:text="@string/wheel_over_title"/>

    <TextView
        android:id="@+id/wheel_error_title_des"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/wheel_error_title"
        app:layout_constraintBottom_toBottomOf="@+id/iv_wheel"
        app:layout_constraintStart_toEndOf="@+id/gl_start"
        app:layout_constraintEnd_toEndOf="parent"
        android:alpha="0.7"
        android:layout_marginTop="8dp"
        android:gravity="start|center"
        android:textSize="13sp"
        android:textColor="@color/white"
        android:text="@string/wheel_over_des"/>

    <TextView
        android:id="@+id/cancel"
        android:layout_width="150dp"
        android:layout_height="36dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/confirm"
        app:layout_constraintTop_toTopOf="@+id/gl_bottom"
        android:gravity="center"
        android:fontFamily="@string/font_family_medium"
        android:textColor="@color/white"
        android:textSize="13sp"
        android:text="@string/wheel_over_cancel"
        android:background="@drawable/shape_wheel_cancel"/>

    <TextView
        android:id="@+id/confirm"
        android:layout_width="150dp"
        android:layout_height="36dp"
        app:layout_constraintTop_toTopOf="@+id/gl_bottom"
        app:layout_constraintStart_toEndOf="@+id/cancel"
        app:layout_constraintEnd_toEndOf="parent"
        android:gravity="center"
        android:fontFamily="@string/font_family_medium"
        android:textColor="@color/white"
        android:textSize="13sp"
        android:text="@string/wheel_over_confirm"
        android:background="@drawable/shape_wheel_confirm"/>


    <android.support.constraint.Guideline
        android:id="@+id/gl_top"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.172"/>

    <android.support.constraint.Guideline
        android:id="@+id/gl_bottom"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.731"/>

    <android.support.constraint.Guideline
        android:id="@+id/gl_start"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.417"/>

</android.support.constraint.ConstraintLayout>