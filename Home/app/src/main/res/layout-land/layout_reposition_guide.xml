<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <ImageButton
        android:id="@+id/reposition_cancel"
        android:layout_width="26dp"
        android:layout_height="26dp"
        android:layout_marginTop="12dp"
        android:layout_marginStart="10dp"
        android:background="@color/transparent"
        android:src="@drawable/charge_set_cancel"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/reposition_cancel_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="1dp"
        android:layout_marginTop="18dp"
        android:text="@string/cancel"
        android:textSize="@dimen/font_10"
        app:layout_constraintStart_toEndOf="@id/reposition_cancel"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="invisible"/>

    <TextView
        android:id="@+id/locate_guide_title"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:layout_marginTop="35dp"
        android:text="@string/reposition_title"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="24sp"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/locate_guide_subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="@dimen/font_14"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/locate_guide_title" />

    <com.ainirobot.home.ui.view.GifView
        android:id="@+id/locate_guide_bg"
        android:layout_width="400dp"
        android:layout_height="170dp"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/locate_guide_subtitle" />

    <Button
        android:id="@+id/locate_guide_confirmBtn"
        android:layout_width="280dp"
        android:layout_height="34dp"
        android:background="@drawable/selector_locate_failure_btn"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="@dimen/font_14"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/locate_guide_bg" />

</android.support.constraint.ConstraintLayout>