<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg"
    tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:gravity="center_horizontal"
        android:layout_marginStart="46dp"
        android:orientation="vertical"
        android:layout_width="139dp"
        android:layout_height="match_parent">

        <ImageView
            android:layout_marginTop="60dp"
            android:id="@+id/hw_status_iv"
            android:layout_width="63dp"
            android:layout_height="63dp"
            android:scaleType="fitXY"
            android:src="@drawable/ic_warning"/>

        <TextView
            android:id="@+id/hw_error_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:gravity="center"
            android:text="@string/hw_chassis_disconnected"
            android:textColor="@color/white"
            android:textSize="17sp"
            />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/psb_error_hint"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="51dp"
        android:layout_marginStart="229dp"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/please_try_to_recover"
            android:textColor="@color/white"
            android:textSize="13dp" />
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:text="@string/psb_recover_step1"
            android:textColor="@color/alpha_70_white"
            android:textSize="11sp" />
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="@string/psb_recover_step2"
            android:textColor="@color/alpha_70_white"
            android:textSize="11sp" />

    </LinearLayout>


    <LinearLayout
        android:layout_below="@id/psb_error_hint"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:layout_marginStart="229dp"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <LinearLayout
            android:layout_gravity="start"
            android:layout_marginTop="6dp"
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/get_help_hint"
                android:textColor="@color/white"
                android:textSize="13sp" />
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/error_code"
                android:textColor="@color/white"
                android:textSize="13sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_errorCode"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:text="code"
            android:textColor="@color/alpha_70_white"
            android:textSize="11sp" />
        <TextView
            android:id="@+id/tv_sn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="sn"
            android:textColor="@color/alpha_70_white"
            android:textSize="11sp" />
        <TextView
            android:id="@+id/tv_timestamp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="timestamp"
            android:textColor="@color/alpha_70_white"
            android:textSize="11sp" />

    </LinearLayout>



    <Button
        android:id="@+id/timer_button"
        android:layout_width="129dp"
        android:layout_height="31dp"
        android:layout_centerHorizontal="true"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="29dp"
        android:background="@drawable/selector_hw_recovery_btn"
        android:gravity="center"
        android:includeFontPadding="true"
        android:textColor="@color/white"
        android:textSize="11sp"
        android:text="@string/hw_retry_recovery" />


</RelativeLayout>
