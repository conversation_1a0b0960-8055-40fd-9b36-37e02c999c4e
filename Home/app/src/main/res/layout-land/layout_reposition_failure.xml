<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:gravity="center"
    android:orientation="vertical">

    <ImageButton
        android:id="@+id/reposition_failure_cancel"
        android:layout_width="26dp"
        android:layout_height="26dp"
        android:layout_marginStart="6dp"
        android:layout_marginTop="12dp"
        android:src="@drawable/charge_set_cancel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@color/transparent"/>

    <TextView
        android:id="@+id/reposition_cancel_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="1dp"
        android:layout_marginTop="16dp"
        android:text="@string/cancel"
        android:textSize="@dimen/font_14"
        app:layout_constraintStart_toEndOf="@id/reposition_failure_cancel"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/locate_failure_title"
        android:layout_width="match_parent"
        android:layout_height="38dp"
        android:layout_marginTop="56dp"
        android:text="@string/reposition_fail"
        android:textAlignment="center"
        android:textColor="@color/color_ff713c"
        android:textSize="@dimen/font_28"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/locate_failure_subtitle"
        android:layout_width="match_parent"
        android:layout_height="18dp"
        android:layout_marginTop="8dp"
        android:text="@string/reposition_fail_des"
        android:visibility="invisible"
        android:textAlignment="center"
        android:textColor="@color/color_ff713c"
        android:textSize="@dimen/font_14"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/locate_failure_title" />

    <ImageView
        android:id="@+id/locate_failure_bg"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:layout_marginVertical="16dp"
        android:scaleType="fitCenter"
        android:src="@drawable/charge_set_fail"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/locate_failure_confirmBtn" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="1195px"
        android:drawableLeft="@drawable/phone"
        android:gravity="center"
        android:visibility="invisible"
        android:text="@string/inspect_err_service_number"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/locate_failure_confirmBtn"
        android:layout_width="280dp"
        android:layout_height="30dp"
        android:background="@drawable/selector_locate_failure_btn"
        android:text="@string/retry_reposition"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="@dimen/font_14"
        android:layout_marginTop="140dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <Button
        android:id="@+id/locate_failure_see_solution"
        android:layout_width="280dp"
        android:layout_height="30dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/selector_locate_failure_see_solution"
        android:gravity="center"
        android:text="@string/charging_warning"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="@dimen/font_14"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/locate_failure_confirmBtn" />

</android.support.constraint.ConstraintLayout>