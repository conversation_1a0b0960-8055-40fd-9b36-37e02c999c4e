<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/luckipro_common_bg_img">

    <Button
        android:id="@+id/without_map_reboot_btn"
        android:layout_width="125dp"
        android:layout_height="30dp"
        android:layout_marginBottom="17dp"
        android:background="@drawable/selector_locate_failure_btn"
        android:text="@string/qrcode_reposition_without_map_reboot"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="10sp"
        app:layout_constraintBottom_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <ImageView
        android:id="@+id/iv_tip"
        android:layout_marginTop="45dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:src="@drawable/location_img_withoutmap"
        android:layout_width="100dp"
        android:layout_height="100dp"/>

    <ImageView
        android:layout_marginStart="12dp"
        android:layout_marginTop="19dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:id="@+id/reposition_without_map_cancel"
        android:src="@drawable/btn_close"
        android:layout_width="23dp"
        android:layout_height="23dp"/>


    <TextView
        android:layout_width="457dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:text="@string/reposition_qrcode_reminder_without_map"
        android:textAlignment="center"
        android:textColor="#99FFFFFF"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/without_map_title" />


    <TextView
        android:id="@+id/without_map_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:text="@string/qrcode_reposition_without_map_title"
        android:textAlignment="center"
        android:textColor="#FFFFFF"
        android:textSize="22sp"
        app:layout_constraintTop_toBottomOf="@id/iv_tip" />


</android.support.constraint.ConstraintLayout>