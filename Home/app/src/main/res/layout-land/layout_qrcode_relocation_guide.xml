<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/luckipro_common_bg_img">

    <ImageView
        android:layout_marginStart="12dp"
        android:layout_marginTop="19dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:id="@+id/reposition_cancel_text"
        android:src="@drawable/btn_close"
        android:layout_width="23dp"
        android:layout_height="23dp"/>

    <ImageView
        android:id="@+id/iv_tip"
        android:layout_marginStart="60dp"
        android:layout_marginTop="84dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:src="@drawable/location_img_qrcode"
        android:layout_width="133dp"
        android:layout_height="133dp"/>

    <TextView
        android:id="@+id/locate_guide_title"
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="17dp"
        android:layout_marginStart="30dp"
        android:text="@string/reposition_title_qrCode"
        android:textAlignment="textStart"
        android:textColor="@color/white"
        android:textSize="@dimen/font_locate_guide_title"
        app:layout_constraintStart_toEndOf="@id/iv_tip"
        app:layout_constraintTop_toTopOf="@id/iv_tip"/>

    <TextView
        android:id="@+id/locate_guide_subtitle"
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/reposition_qrcode_reminder_subtitle"
        android:textAlignment="textStart"
        android:textColor="#99FFFFFF"
        android:textSize="13sp"
        app:layout_constraintStart_toStartOf="@+id/locate_guide_title"
        app:layout_constraintTop_toBottomOf="@+id/locate_guide_title" />

    <TextView
        android:id="@+id/locate_guide_confirmBtn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="30dp"
        android:padding="6dp"
        android:textAlignment="center"
        android:textColor="#99FFFFFF"
        android:textSize="10sp"
        android:text="@string/reposition_anchor_point_exist"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</android.support.constraint.ConstraintLayout>