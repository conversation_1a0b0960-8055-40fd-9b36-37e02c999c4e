<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    android:background="@drawable/shape_bg_full_lock"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/top_icon"
        android:layout_width="90dp"
        android:layout_height="90dp"
        android:layout_gravity="center"
        android:src="@drawable/time_warning_img"
        app:layout_constraintTop_toBottomOf="@+id/gl_top_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:text="@string/stop_charge_confirm"
        android:textColor="@color/white"
        android:textSize="20sp"
        app:layout_constraintTop_toBottomOf="@+id/top_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:id="@+id/confirm_button"
        android:layout_width="120sp"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_button_bg"
        android:layout_gravity="center"
        android:gravity="center"
        android:text="@string/confirm"
        android:textColor="@color/white"
        android:textSize="@dimen/font_18"
        android:maxLines="1"
        android:padding="8dp"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintTop_toBottomOf="@+id/gl_bottom"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <android.support.constraint.Guideline
        android:id="@+id/gl_top_icon"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintGuide_percent="0.150"
        android:orientation="horizontal"/>

    <android.support.constraint.Guideline
        android:id="@+id/gl_bottom"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintGuide_percent="0.700"
        android:orientation="horizontal"/>


</android.support.constraint.ConstraintLayout>