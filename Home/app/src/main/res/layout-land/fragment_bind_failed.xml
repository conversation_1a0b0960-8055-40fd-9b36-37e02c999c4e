<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <TextView
        android:id="@+id/inspect_again"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:text="@string/inspect_bind_fail_up"
        android:textColor="@color/color_ff713c"
        android:textSize="100px"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_dis_use"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:gravity="center"
        android:text="@string/inspect_bind_fail_down"
        android:textColor="@color/color_ff713c"
        android:textSize="100px"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/inspect_again" />

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="77dp"
        android:layout_marginTop="16dp"
        android:src="@drawable/inspect_error_img"
        app:layout_constraintTop_toBottomOf="@+id/tv_dis_use" />

    <TextView
        android:id="@+id/tv_des"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="108dp"
        android:alpha="0.5"
        android:gravity="center"
        android:text="@string/inspect_not_clear_data"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:layout_constraintTop_toBottomOf="@+id/tv_dis_use" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="415px"
        android:layout_marginTop="160dp"
        android:layout_marginEnd="415px"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@+id/tv_dis_use">

        <TextView
            android:id="@+id/tv_to_bind"
            android:layout_width="450px"
            android:layout_height="120px"
            android:background="@drawable/selector_locate_failure_btn"
            android:gravity="center"
            android:padding="6dp"
            android:text="@string/inspect_to_bind"
            android:textColor="@color/white"
            android:textSize="@dimen/font_12" />

        <TextView
            android:id="@+id/tv_to_restore"
            android:layout_width="450px"
            android:layout_height="120px"
            android:layout_alignParentEnd="true"
            android:background="@drawable/selector_locate_failure_btn"
            android:gravity="center"
            android:padding="6dp"
            android:text="@string/inspect_restore_setting"
            android:textColor="@color/white"
            android:textSize="@dimen/font_12" />
    </RelativeLayout>

</android.support.constraint.ConstraintLayout>