<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/system_module_bg">

    <android.support.constraint.Guideline
        android:id="@+id/gl_1"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.104"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <android.support.constraint.Guideline
        android:id="@+id/gl_2"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.856"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/auto_charge_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="85px"
        android:paddingEnd="85px"
        android:fontFamily="@string/font_family_medium"
        android:gravity="center"
        android:text="@string/charge_is_going_charge_pile"
        android:textColor="@color/white"
        android:textSize="@dimen/font_28"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/gl_1" />

    <ImageView
        android:id="@+id/low_power_complete"
        android:layout_width="53dp"
        android:layout_height="118dp"
        android:layout_marginTop="8dp"
        android:scaleType="fitXY"
        android:src="@drawable/auto_charge"
        android:rotation="90"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/auto_charge_title_des" />

    <com.ainirobot.home.ui.view.GifView
        android:id="@+id/charge_gif"
        android:layout_width="300dp"
        android:layout_height="170dp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/auto_charge_title" />

    <TextView
        android:id="@+id/auto_charge_title_des"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="11dp"
        android:ellipsize="end"
        android:fontFamily="@string/font_family_medium"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="@color/color_ff713c"
        android:textSize="13sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/auto_charge_title" />

    <com.ainirobot.home.ui.view.GifView
        android:id="@+id/wire_charge_gif"
        android:layout_width="119dp"
        android:layout_height="141dp"
        android:layout_marginTop="41dp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/auto_charge_title" />

    <ImageView
        android:id="@+id/charge_bg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="100dp"
        android:visibility="gone"
        android:src="@drawable/go_charging"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/auto_charge_title" />

    <TextView
        android:id="@+id/auto_charge_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@string/font_family_regular"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/font_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/gl_2" />

    <ImageView
        android:id="@+id/wait_for_charge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:visibility="gone"
        android:src="@drawable/waitforcharger_pro"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/auto_charge_title" />

    <TextView
        android:id="@+id/cancel_charge_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/confirm_btn_bg"
        android:paddingHorizontal="20dp"
        android:layout_marginTop="5dp"
        android:visibility="gone"
        android:text="@string/cancel_charge"
        android:fontFamily="@string/font_family_regular"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/font_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/gl_2" />

    <ImageView
        android:id="@+id/img_lift"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/auto_charge_title" />

</android.support.constraint.ConstraintLayout>