<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/luckipro_common_bg_img"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_tip"
        android:layout_width="133dp"
        android:layout_height="133dp"
        android:layout_marginStart="60dp"
        android:layout_marginTop="84dp"
        android:src="@drawable/location_img_point"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/locate_guide_subtitle"
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/reposition_qrcode_reminder_subtitle"
        android:textAlignment="textStart"
        android:textColor="#99FFFFFF"
        android:textSize="@dimen/font_locate_guide_sub_title2"
        app:layout_constraintStart_toStartOf="@+id/locate_result_title"
        app:layout_constraintTop_toBottomOf="@+id/locate_result_title" />


    <ImageView
        android:id="@+id/cancel_vision_locate"
        android:layout_width="23dp"
        android:layout_height="23dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="19dp"
        android:src="@drawable/btn_back"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/exit_vision_locate"
        android:layout_width="23dp"
        android:layout_height="23dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="19dp"
        android:src="@drawable/btn_close"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/locate_result_title"
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="30dp"
        android:layout_marginTop="17dp"
        android:text=""
        android:textAlignment="textStart"
        android:textColor="@color/white"
        android:textSize="@dimen/font_locate_guide_title2"
        app:layout_constraintStart_toEndOf="@id/iv_tip"
        app:layout_constraintTop_toTopOf="@id/iv_tip" />

</android.support.constraint.ConstraintLayout>