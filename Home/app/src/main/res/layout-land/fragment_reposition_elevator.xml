<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <include
        android:id="@+id/point_locate_guide"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/layout_point_locate_guide"/>

    <include
        android:visibility="gone"
        android:id="@+id/charge_locate_guide"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/layout_reposition_guide"/>

    <include
        android:visibility="gone"
        android:id="@+id/charge_locating"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/layout_reposition_search" />


</android.support.constraint.ConstraintLayout>