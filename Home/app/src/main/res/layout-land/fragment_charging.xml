<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000"
    android:orientation="vertical"
    tools:context=".ui.fragment.ChargingFragment">

    <include
        android:id="@+id/layout_charging_low_temp"
        android:visibility="gone"
        layout="@layout/charging_low_temp_view"/>

    <LinearLayout
        android:id="@+id/layout_update"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:paddingEnd="20px"
        android:paddingTop="70px"
        app:layout_constraintTop_toTopOf="parent">

        <android.support.constraint.ConstraintLayout
            android:id="@+id/constraint_layout_update"
            android:layout_width="200px"
            android:layout_height="wrap_content"
            android:visibility="gone">

            <ImageView
                android:id="@+id/emoji_update_icon"
                android:layout_width="100px"
                android:layout_height="100px"
                android:src="@drawable/update_icon"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <TextView
                android:id="@+id/emoji_update_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10px"
                android:text="@string/ota_wait"
                android:textColor="#fff"
                android:textSize="38px"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/emoji_update_icon" />
        </android.support.constraint.ConstraintLayout>
    </LinearLayout>

    <ProgressBar
        android:id="@+id/charging_image"
        style="@style/Widget.AppCompat.ProgressBar.Horizontal"
        android:layout_width="480px"
        android:layout_height="214px"
        android:layout_marginTop="457px"
        android:indeterminate="false"
        android:max="100"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/charge_lightning_img"
        android:layout_width="74px"
        android:layout_height="130px"
        android:layout_marginTop="497px"
        android:src="@drawable/charge_lightning_img"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/btn_end_charging"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/stop_right_now"
        android:background="@drawable/charge_warning_btn_bg"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="40px"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/charging_image"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="50dp"/>


    <LinearLayout
        android:id="@+id/lin_charging"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="130px"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="parent"
        android:orientation="vertical">
        <TextView
            android:id="@+id/charging_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/charging_text"
            android:textColor="@color/white"
            android:textSize="100px"
            app:layout_constraintTop_toTopOf="parent" />
        <TextView
            android:id="@+id/charging_level"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10px"
            android:gravity="center"
            android:text="@string/charging_level"
            android:textColor="@color/white"
            android:textSize="60px"
            app:layout_constraintTop_toTopOf="parent" />
    </LinearLayout>

    <TextView
        android:id="@+id/btn_charging_warning"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="877px"
        android:layout_marginStart="330px"
        android:layout_marginEnd="330px"
        android:background="@drawable/charge_warning_btn_bg"
        android:gravity="center"
        android:text="@string/charging_warning"
        android:textColor="@color/white"
        android:textSize="40px"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Leaving Layout -->
    <android.support.constraint.ConstraintLayout
        android:id="@+id/leaving_layout"
        android:layout_width="1620px"
        android:layout_height="933px"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- Title Layout -->

        <android.support.constraint.ConstraintLayout
            android:id="@+id/title_layout"
            android:layout_width="1620px"
            android:layout_height="181px"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="1620px"
                android:layout_height="58px"
                android:gravity="center"
                android:text="@string/charge_leaving_title"
                android:textColor="#FFFFFF"
                android:textSize="46px"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_tips"
                android:layout_width="1620px"
                android:layout_height="75px"
                android:gravity="center"
                android:text="@string/charge_leaving_tips"
                android:textColor="#FFFFFF"
                android:textSize="60px"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent" />
        </android.support.constraint.ConstraintLayout>

        <ImageView
            android:id="@+id/image_off_charger"
            android:layout_width="680px"
            android:layout_height="680px"
            android:layout_marginTop="20dp"
            android:src="@drawable/mini_img_offcharger"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

    </android.support.constraint.ConstraintLayout>

</android.support.constraint.ConstraintLayout>