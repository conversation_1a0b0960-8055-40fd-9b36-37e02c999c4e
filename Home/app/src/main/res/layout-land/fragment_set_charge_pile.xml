<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/system_module_bg">

    <LinearLayout
        android:id="@+id/ll_charge_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="7.5dp"
        android:layout_marginTop="15dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:scaleType="fitCenter"
            android:src="@drawable/charge_set_cancel" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@string/font_family_regular"
            android:padding="4dp"
            android:text="@string/cancel"
            android:textColor="@color/white"
            android:textSize="13sp" />
    </LinearLayout>

    <TextView
        android:id="@+id/charge_set_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="85px"
        android:layout_marginEnd="85px"
        android:fontFamily="@string/font_family_medium"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/font_16"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/gl_1" />

    <ImageView
        android:id="@+id/charge_set_complete"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:scaleType="fitCenter"
        android:src="@drawable/charge_set_success"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/charge_set_title" />

    <TextView
        android:id="@+id/charge_set_complete_failnumber"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="28dp"
        android:drawableLeft="@drawable/phone"
        android:drawablePadding="4dp"
        android:ellipsize="end"
        android:gravity="center"
        android:text="@string/inspect_err_service_number"
        android:textColor="@color/white"
        android:textSize="@dimen/font_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/charge_set_complete" />

    <com.ainirobot.home.ui.view.GifView
        android:id="@+id/charge_gif"
        android:layout_width="wrap_content"
        android:layout_height="200dp"
        android:layout_marginTop="25dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/charge_set_title" />

    <ImageView
        android:id="@+id/iv_setChargeOk"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginTop="26dp"
        android:src="@drawable/set_charge_complete"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/charge_confirm_btn" />


    <TextView
        android:id="@+id/charge_confirm_btn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="36dp"
        android:layout_marginEnd="36dp"
        android:layout_marginBottom="10dp"
        android:background="@drawable/charge_confirm_btn_bg"
        android:fontFamily="@string/font_family_regular"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/font_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <android.support.constraint.Guideline
        android:id="@+id/gl_1"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_begin="60dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</android.support.constraint.ConstraintLayout>