<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/luckipro_common_bg_img"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/shutdown_robot"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginTop="34dp"
        android:src="@drawable/shutdown_img_warn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/shutdown_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="17dp"
        android:gravity="center"
        android:text="@string/shutdown_navi_tips"
        android:textColor="@color/white"
        android:textSize="24sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/shutdown_robot"/>


    <TextView
        android:id="@+id/shutdown_navi_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:textColor="#99FFFFFF"
        android:textSize="13sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/shutdown_tips"/>


<!--    <ImageView-->
<!--        android:id="@+id/location"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_marginTop="8dp"-->
<!--        android:layout_marginBottom="8dp"-->
<!--        android:src="@drawable/shutdown_location"-->
<!--        app:layout_constraintStart_toStartOf="@+id/shutdown_now"-->
<!--        app:layout_constraintEnd_toEndOf="@+id/shutdown_now"-->
<!--        app:layout_constraintBottom_toTopOf="@+id/shutdown_now"-->
<!--        app:layout_constraintTop_toBottomOf="@+id/shutdown_robot"-->
<!--        app:layout_constraintVertical_bias="0.923"/>-->


    <Button
        android:id="@+id/shutdown_now"
        android:layout_width="125dp"
        android:layout_height="30dp"
        android:background="@drawable/selector_anchor_question_btn"
        android:text="@string/shutdown_now"
        android:textColor="@color/white"
        android:textSize="10sp"
        android:layout_marginBottom="17dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintEnd_toStartOf="@+id/shutdown_cancel"/>

    <Button
        android:id="@+id/shutdown_cancel"
        android:layout_width="125dp"
        android:layout_height="30dp"
        android:background="@drawable/selector_locate_failure_btn"
        android:text="@string/shutdown_cancel"
        android:textColor="@color/white"
        android:textSize="10sp"
        android:layout_marginBottom="17dp"
        android:layout_marginStart="13dp"
        app:layout_constraintStart_toEndOf="@+id/shutdown_now"

        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>


</android.support.constraint.ConstraintLayout>