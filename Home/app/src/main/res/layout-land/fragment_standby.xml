<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000">

    <Button
        android:id="@+id/bt_exitStandby"
        android:layout_width="187dp"
        android:layout_height="30dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="17dp"
        android:background="@drawable/selector_locate_failure_btn"
        android:text="@string/exit_standby"
        android:textColor="#FFFFFF"
        android:textSize="10sp"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/iv_standby_bg"
        android:layout_width="297dp"
        android:layout_height="257dp"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:src="@drawable/standby_sleep_bg" />

    <TextView
        android:id="@+id/tv_switchHint"
        android:layout_width="match_parent"
        android:layout_height="122dp"
        android:layout_alignParentBottom="true"
        android:background="#55C3FB"
        android:gravity="center"
        android:textColor="#FFFFFF"
        android:textSize="16sp"
        android:visibility="visible" />

    <TextView
        android:id="@+id/tv_chargingMsg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:drawablePadding="6dp"
        android:textSize="10sp" />

    <TextView
        android:id="@+id/tv_standby"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="190dp"
        android:text="@string/standby"
        android:textColor="#FFFFFF"
        android:textSize="26sp" />

    <TextView
        android:id="@+id/tv_standby_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_standby"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="5dp"
        android:text="@string/standby_hint"
        android:textColor="#FFFFFF"
        android:textSize="17sp" />

    <TextView
        android:background="@drawable/shape_corner_16_yellow"
        android:id="@+id/tv_message"
        android:paddingStart="17dp"
        android:paddingEnd="17dp"
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="28dp"
        android:drawablePadding="6dp"
        android:textColor="#FFFFFF"
        android:textSize="17sp"
        android:visibility="gone" />

    <Button
        android:id="@+id/butt_reboot"
        android:layout_width="187dp"
        android:layout_height="30dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="17dp"
        android:background="@drawable/selector_locate_failure_btn"
        android:textColor="#FFFFFF"
        android:visibility="gone"
        android:text="@string/cloud_reboot"
        android:textSize="@dimen/font_14"/>


</RelativeLayout>