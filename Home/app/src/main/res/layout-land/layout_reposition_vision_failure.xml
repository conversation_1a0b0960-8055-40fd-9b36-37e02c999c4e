<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/reposition_cancel_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="start"
        android:layout_marginStart="10dp"
        android:layout_marginTop="42px"
        android:orientation="horizontal">

        <ImageButton
            android:layout_width="90px"
            android:layout_height="90px"
            android:background="@color/transparent"
            android:src="@drawable/charge_set_cancel" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="4dp"
            android:gravity="center"
            android:text="@string/cancel"
            android:textColor="@color/white"
            android:textSize="20sp" />
    </LinearLayout>

    <TextView
        android:id="@+id/locate_vision_failure_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="@string/reposition_vision_fail_title"
        android:textAlignment="center"
        android:textColor="@color/color_ff713c"
        android:textSize="24sp" />

    <TextView
        android:id="@+id/locate_vision_failure_subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="11dp"
        android:text="@string/reposition_vision_fail_subtitle"
        android:textAlignment="center"
        android:textColor="@color/color_ff713c"
        android:textSize="20sp" />

    <ImageView
        android:id="@+id/locate_vision_failure_bg"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginTop="20dp"
        android:scaleType="fitXY"
        android:src="@drawable/reposition_vision_fail_img"
        android:visibility="visible" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:orientation="horizontal">

        <Button
            android:id="@+id/locate_failure_menual_btn"
            android:layout_width="129dp"
            android:layout_height="31dp"
            android:background="@drawable/selector_locate_failure_btn"
            android:text="@string/reposition_failure_manaul"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <Button
            android:id="@+id/locate_failure_vision_retry_btn"
            android:layout_width="129dp"
            android:layout_height="31dp"
            android:layout_marginStart="16dp"
            android:background="@color/transparent"
            android:text="@string/reposition_failure_vision_retry"
            android:textAlignment="center"
            android:textColor="@color/color_55c3fb"
            android:textSize="15sp" />

    </LinearLayout>

</LinearLayout>