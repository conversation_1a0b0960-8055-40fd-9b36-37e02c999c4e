<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/view_root"
    android:layout_width="950px"
    android:layout_height="800px"
    android:layout_gravity="center"
    android:background="@color/white"
    android:elevation="1dp">

    <ImageView
        android:layout_width="220px"
        android:layout_height="220px"
        android:scaleType="fitXY"
        android:src="@drawable/upgrade_pic_img"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/find_new_version"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:paddingStart="90px"
        android:paddingEnd="90px"
        android:text="@string/find_new_version"
        android:textColor="@color/ota_big_text_land"
        android:textSize="18sp"
        android:fontFamily="@string/font_family_medium"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/version"
        android:layout_width="720px"
        android:layout_height="wrap_content"
        android:paddingStart="25dp"
        android:paddingEnd="25dp"
        android:text="@string/ota_version"
        android:textColor="@color/ota_version_des_land"
        android:textSize="12sp"
        android:fontFamily="@string/font_family_regular"
        app:layout_constraintStart_toStartOf="@id/find_new_version"
        app:layout_constraintTop_toBottomOf="@id/find_new_version" />

    <com.ainirobot.home.ui.view.MyScrollView
        android:id="@+id/content_scrollview"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:overScrollMode="never"
        android:fadeScrollbars="false"
        android:scrollbars="vertical"
        android:scrollbarThumbVertical="@drawable/shape_scroll_bar"
        android:layout_marginTop="15dp"
        android:layout_marginBottom="32dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/version"
        app:layout_constraintBottom_toTopOf="@+id/view_line">

        <TextView
            android:id="@+id/ota_upgrade_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="25dp"
            android:paddingEnd="25dp"
            android:text="@string/ota_upgrade_content"
            android:textColor="@color/ota_big_text"
            android:textSize="11sp"
            android:gravity="top|start"/>
    </com.ainirobot.home.ui.view.MyScrollView>

    <TextView
        android:id="@+id/ota_upgrade_complete"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="25dp"
        android:paddingEnd="25dp"
        android:text="@string/ota_upgrade_complete"
        android:textColor="@color/color_55c3fb"
        android:textSize="8sp"
        android:layout_marginBottom="10dp"
        app:layout_constraintBottom_toTopOf="@+id/view_line" />

    <android.support.constraint.Guideline
        android:id="@+id/view_line"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.817"/>

    <TextView
        android:id="@+id/division_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/ota_division_line"
        app:layout_constraintTop_toBottomOf="@+id/view_line"/>

    <TextView
        android:id="@+id/upgrade_next_time"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/bg_btn_upgrade_next_time"
        android:gravity="center"
        android:text="@string/upgrade_next_time"
        android:textColor="@color/ota_btn_cancel_land"
        android:textSize="12sp"
        android:fontFamily="@string/font_family_medium"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/vertical_view_line"
        app:layout_constraintTop_toBottomOf="@id/division_line"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <View
        android:id="@+id/vertical_view_line"
        android:layout_width="1px"
        android:layout_height="80px"
        android:background="@color/ota_vertical_line"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/upgrade_next_time"
        app:layout_constraintEnd_toStartOf="@+id/upgrade_now"
        app:layout_constraintTop_toTopOf="@id/division_line" />

    <TextView
        android:id="@+id/upgrade_now"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/bg_btn_upgrade_now"
        android:gravity="center"
        android:text="@string/upgrade_now"
        android:textColor="@color/ota_upgrade_now"
        android:textSize="12sp"
        android:fontFamily="@string/font_family_medium"
        app:layout_constraintStart_toEndOf="@id/vertical_view_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/division_line" />
</android.support.constraint.ConstraintLayout>
