<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000">

    <ImageView
        android:id="@+id/iv_standby_bg"
        android:layout_width="264dp"
        android:layout_height="171dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="18dp"
        android:src="@drawable/bg_out_map_warning_land" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="24dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/out_map_warning_light"/>

        <TextView
            android:id="@+id/tv_alert_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="13dp"
            android:gravity="center"
            android:text="@string/out_map_warning_alert_title"
            android:textColor="#FF4D4D"
            android:textSize="26sp" />

        <TextView
            android:id="@+id/tv_alert_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:gravity="center"
            android:text="@string/out_map_warning_alert_message"
            android:textColor="#FF4D4D"
            android:textSize="26sp" />

    </LinearLayout>

</RelativeLayout>