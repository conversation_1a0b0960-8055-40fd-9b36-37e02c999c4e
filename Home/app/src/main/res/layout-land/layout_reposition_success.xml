<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:gravity="center"
    android:orientation="vertical">


    <TextView
        android:id="@+id/locate_success_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="56dp"
        android:text="@string/reposition_vision_success"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="@dimen/font_28"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/locate_success_circle"
        android:layout_width="78dp"
        android:layout_height="78dp"
        android:layout_marginTop="30dp"
        android:scaleType="fitXY"
        android:src="@drawable/locate_success_bg"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/locate_success_title" />

    <com.ainirobot.home.ui.view.MarkView
        android:id="@+id/locate_success_mark_view"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_marginStart="7dp"
        android:layout_marginTop="10dp"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="@+id/locate_success_circle"
        app:layout_constraintTop_toTopOf="@+id/locate_success_circle" />


</android.support.constraint.ConstraintLayout>