<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000"
    android:orientation="vertical"
    tools:context=".ui.fragment.ChargingFragment">
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/charging_bms_low_temp_title"
        android:textColor="@color/color_ff713c"
        android:gravity="center_horizontal"
        android:layout_marginTop="105px"
        android:textSize="100px"/>
    <TextView
        android:id="@+id/tv_current_temp_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/charging_bms_low_temp_tip1"
        android:textColor="@color/color_ff713c"
        android:gravity="center_horizontal"
        android:layout_marginTop="20dp"
        android:textSize="12dp"/>
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/charging_bms_low_temp_tip2"
        android:textColor="@color/color_ff713c"
        android:gravity="center_horizontal"
        android:layout_marginTop="10dp"
        android:textSize="12dp"/>
    <TextView
        android:id="@+id/tv_low_temp_battery_level"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="12dp"
        android:gravity="center_horizontal"
        android:layout_marginTop="15dp"
        android:textColor="@color/white"/>
    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="4dp"
        android:src="@drawable/battery_lowtemp">

    </ImageView>
</LinearLayout>