<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent">

    <TextView
        android:id="@+id/locate_vision_show_title"
        android:layout_width="match_parent"
        android:layout_height="130px"
        android:text="@string/reposition_vision_show_title"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="19dp"
        android:layout_marginTop="36dp"
         />

    <TextView
        android:id="@+id/locate_vision_show_subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:text="@string/reposition_vision_show_subtitle"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="13sp" />

    <ImageView
        android:id="@+id/locate_vision_show_bg"
        android:layout_width="340px"
        android:layout_height="671px"
        android:layout_marginTop="18dp"
        android:scaleType="fitXY"
        android:src="@drawable/reposition_vision_bg_img"
        android:visibility="visible" />

</LinearLayout>