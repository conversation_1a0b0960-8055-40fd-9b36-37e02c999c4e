<?xml version="1.0" encoding="utf-8"?>

<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:gravity="center"
    android:orientation="vertical">


    <TextView
        android:id="@+id/locate_search_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="45dp"
        android:text="@string/reposition_doing"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="@dimen/font_28"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/locate_search_img"
        android:layout_width="78dp"
        android:layout_height="94dp"
        android:layout_marginTop="170dp"
        android:scaleType="fitXY"
        android:src="@drawable/locate_search_icon"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/locate_search_shadow"
        android:layout_width="54dp"
        android:layout_height="10dp"
        android:layout_marginTop="10dp"
        android:scaleType="fitXY"
        android:src="@drawable/locate_search_shadow"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/locate_search_img" />


</android.support.constraint.ConstraintLayout>