<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="203dp"
    android:layout_height="wrap_content"
    android:background="@drawable/dialog_anchor_question_bg_dark"
    android:layout_gravity="center_horizontal"
    android:paddingStart="17dp"
    android:paddingEnd="17dp">

    <TextView
        android:id="@+id/anchor_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="13dp"
        android:gravity="center_vertical|center_horizontal"
        android:text="@string/anchor_title"
        android:textColor="#FFFFFF"
        android:textSize="13sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/anchor_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="11dp"
        android:text="@string/anchor_content"
        android:textColor="#99FFFFFF"
        android:textSize="10sp"
        app:layout_constraintStart_toStartOf="@id/anchor_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/anchor_title" />

    <TextView
        android:id="@+id/anchor_subTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        android:text="@string/anchor_subTitle"
        android:textColor="#FFFFFF"
        android:textSize="10sp"
        android:gravity="center_vertical|start"
        app:layout_constraintStart_toStartOf="@id/anchor_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/anchor_content" />

    <TextView
        android:id="@+id/anchor_subContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        android:text="@string/anchor_subContent"
        android:textColor="#99FFFFFF"
        android:textSize="10sp"
        app:layout_constraintStart_toStartOf="@id/anchor_subTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/anchor_subTitle" />

    <TextView
        android:id="@+id/anchor_ensure"
        android:layout_width="83dp"
        android:layout_height="24dp"
        android:background="@drawable/selector_anchor_question_btn"
        android:gravity="center"
        android:text="@string/anchor_ensure"
        android:textColor="#FFFFFF"
        android:textSize="11sp"
        android:layout_marginTop="17dp"
        android:layout_marginBottom="9dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/anchor_subContent"
        app:layout_constraintBottom_toBottomOf="parent"/>


</android.support.constraint.ConstraintLayout>