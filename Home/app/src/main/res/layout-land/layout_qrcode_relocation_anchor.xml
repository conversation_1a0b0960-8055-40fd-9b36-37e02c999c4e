<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/luckipro_common_bg_img"
    android:gravity="center"
    android:orientation="vertical">

    <Button
        android:id="@+id/anchor_point_btn"
        android:layout_width="125dp"
        android:layout_height="30dp"
        android:layout_marginBottom="17dp"
        android:background="@drawable/selector_locate_failure_btn"
        android:text="@string/reposition_reposition"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="10sp"
        android:layout_marginStart="13dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/anchor_question_btn"/>

    <Button
        android:id="@+id/anchor_question_btn"
        android:layout_width="125dp"
        android:layout_height="30dp"
        android:layout_marginBottom="17dp"
        android:background="@drawable/selector_anchor_question_btn"
        android:text="@string/anchor_title"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="10sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/anchor_point_btn"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent" />


    <ImageView
        android:id="@+id/iv_tip"
        android:layout_marginStart="60dp"
        android:layout_marginTop="84dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:src="@drawable/location_img_point"
        android:layout_width="133dp"
        android:layout_height="133dp"/>

    <TextView
        android:id="@+id/locate_guide_subtitle"
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/reposition_qrcode_anchor_reminder_subtitle"
        android:textAlignment="textStart"
        android:textColor="#99FFFFFF"
        android:textSize="@dimen/font_locate_guide_sub_title2"
        app:layout_constraintStart_toStartOf="@+id/locate_result_title"
        app:layout_constraintTop_toBottomOf="@+id/locate_result_title" />


    <ImageView
        android:layout_marginStart="12dp"
        android:layout_marginTop="19dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="gone"
        android:id="@+id/reposition_anchor_cancel_tv"
        android:src="@drawable/btn_back"
        android:layout_width="23dp"
        android:layout_height="23dp"/>

    <ImageView
        android:id="@+id/anchor_cancel_text"
        android:layout_marginStart="12dp"
        android:layout_marginTop="19dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="gone"
        android:src="@drawable/btn_close"
        android:layout_width="23dp"
        android:layout_height="23dp"/>

    <TextView
        android:id="@+id/locate_result_title"
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="17dp"
        android:layout_marginStart="30dp"
        android:text="@string/reposition_anchor_title_qrCode"
        android:textAlignment="textStart"
        android:textColor="@color/white"
        android:textSize="@dimen/font_locate_guide_title2"
        app:layout_constraintStart_toEndOf="@id/iv_tip"
        app:layout_constraintTop_toTopOf="@id/iv_tip" />


</android.support.constraint.ConstraintLayout>