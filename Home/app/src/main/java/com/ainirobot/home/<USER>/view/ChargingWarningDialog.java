package com.ainirobot.home.ui.view;

import android.animation.ObjectAnimator;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Outline;
import android.os.Bundle;
import android.support.constraint.ConstraintLayout;
import android.view.View;
import android.view.ViewOutlineProvider;
import android.view.WindowManager;
import android.widget.TextView;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.home.R;

public class ChargingWarningDialog extends Dialog implements View.OnClickListener {
    private TextView mConfirm;
    private ConstraintLayout mLayout;
    private TextView mContentTextView;
    private TextView mContentReasonTextView;

    public ChargingWarningDialog(Context context, int theme) {
        super(context, theme);
    }

    private ClickListener mClickListener;

    public interface ClickListener {
        void onConfirmClick();
    }

    public ChargingWarningDialog setDialogClickListener(ClickListener listener) {
        this.mClickListener = listener;
        return this;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_dialog_charging_warning);
        WindowManager.LayoutParams p = this.getWindow().getAttributes();
        this.getWindow().setAttributes(p);
        setCanceledOnTouchOutside(false);

        initView();
    }

    private void initView() {
        mLayout = (ConstraintLayout) findViewById(R.id.dialog_charging_warning);
        mConfirm = (TextView) findViewById(R.id.confirm);
        mConfirm.setOnClickListener(this);
        mContentTextView = (TextView) findViewById(R.id.content);
        mContentReasonTextView = (TextView) findViewById(R.id.content_reason_1);
        if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) {
            mContentTextView.setText(R.string.charging_warning_dialog_text_saiph);
            mContentReasonTextView.setText(R.string.charging_warning_dialog_reason_saiph);
        }
        ViewOutlineProvider viewOutlineProvider = new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                view.setClipToOutline(true);
                outline.setRoundRect(0, 0, view.getWidth(), view.getHeight(), 30);
            }
        };
        mLayout.setOutlineProvider(viewOutlineProvider);
    }

    @Override
    public void show() {
        ObjectAnimator.ofFloat(mLayout, "alpha", 0, 1)
                .setDuration(170)
                .start();
        super.show();
    }

    @Override
    public void onClick(View v) {
        int vId = v.getId();
        switch (vId) {
            case R.id.confirm:
                if (mClickListener != null) {
                    mClickListener.onConfirmClick();
                    dismiss();
                }
                break;
            default:
                break;
        }
    }
}
