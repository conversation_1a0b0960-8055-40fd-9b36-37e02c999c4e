/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.home.ui.fragment;

import android.animation.ValueAnimator;
import android.graphics.Outline;
import android.graphics.drawable.AnimationDrawable;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.view.OTAWarningDialog;

public class OTAProgressFragment extends BaseFragment implements View.OnClickListener {
    private AnimationDrawable mAnimationDrawable;
    private TextView mTvDescribe;
    private String mFormatString;
    private TextView mViewStub;
    private ImageView mOtaCancel;
    private TextView mTvCancel;
    private ImageView mOtaPauseContinue;
    private TextView mTvPauseContinue;
    private ImageView mIvProgress;
    private int mDownloadProgress;

    private boolean pauseState;
    private final String TAG = OTAProgressFragment.class.getSimpleName() + ":Home";
    private boolean mIsForce;
    private View mContentView;
    OTAWarningDialog mWarningDialog;
    private boolean mIsRollback;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        mContentView = inflater.inflate(R.layout.fragment_start_upgrade,null);
        initView();
        initData();
        return mContentView;
    }

    private void initData() {
        mIsForce = getArguments().getBoolean(Definition.JSON_OTA_FORCEUPATE,false);
        boolean needDown = getArguments().getBoolean(Definition.JSON_OTA_NEED_DOWNLOAD,true);
        mIsRollback = getArguments().getBoolean(ModuleDef.PARAM_IS_ROLLBACK, false);
        if(mIsForce || !needDown){
            mTvCancel.setVisibility(View.GONE);
            mOtaCancel.setVisibility(View.GONE);
            mTvPauseContinue.setVisibility(View.GONE);
            mOtaPauseContinue.setVisibility(View.GONE);
            mViewStub.setVisibility(View.VISIBLE);
        }
        if (!needDown) {
            if (mIsRollback) {
                mFormatString = getResources().getString(R.string.rollback_upgrade);
            } else {
                mFormatString = getResources().getString(R.string.installing_upgrade);
            }
            mTvDescribe.setText(mFormatString);
        } else {
            mFormatString = String.format(getResources().getString(R.string.starting_upgrade), 0);
            mTvDescribe.setText(mFormatString);
        }
    }

    private void initView() {
        mIvProgress = (ImageView)mContentView.findViewById(R.id.process_animation);
        mTvDescribe = (TextView)mContentView.findViewById(R.id.describe);
        mViewStub = (TextView)mContentView.findViewById(R.id.view_stub);
        mTvCancel = (TextView)mContentView.findViewById(R.id.tv_cancel);
        mOtaCancel = (ImageView)mContentView.findViewById(R.id.ota_cancel);
        mTvPauseContinue = (TextView)mContentView. findViewById(R.id.tv_ota_pause_continue);
        mOtaPauseContinue = (ImageView)mContentView. findViewById(R.id.ota_pause_continue);

        mIvProgress.setImageResource(R.drawable.frame_upgrade_progress);
        mAnimationDrawable = (AnimationDrawable) mIvProgress.getDrawable();
        mAnimationDrawable.start();

        View view = mContentView.findViewById(R.id.bg_process);
        ViewOutlineProvider viewOutlineProvider = new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                view.setClipToOutline(true);
                outline.setRoundRect(0,0,view.getWidth(),view.getHeight(),6);
            }
        };
        view.setOutlineProvider(viewOutlineProvider);
        mOtaCancel.setOnClickListener(this);
        mTvCancel.setOnClickListener(this);
        mOtaPauseContinue.setOnClickListener(this);
        mTvPauseContinue.setOnClickListener(this);
    }

    public void onClick(View view) {
        switch (view.getId()){
            case R.id.ota_cancel:
            case R.id.tv_cancel:
                ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_CANCEL_OTA);
                break;
            case R.id.ota_pause_continue:
            case R.id.tv_ota_pause_continue:
                if(pauseState){
                    mTvPauseContinue.setText(getResources().getString(R.string.ota_pause));
                    mFormatString = String.format(getResources().getString(R.string.starting_upgrade), mDownloadProgress);
                    mAnimationDrawable.start();
                    ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_CONTINUE_OTA);
                    mOtaPauseContinue.setBackgroundResource(R.drawable.selector_ota_pause);
                }else{
                    mTvPauseContinue.setText(getResources().getString(R.string.ota_continue));

                    mFormatString = String.format(getResources().getString(R.string.pause_download),mDownloadProgress);
                    if(mAnimationDrawable.isRunning()){
                        mAnimationDrawable.stop();
                    }
                    mOtaPauseContinue.setBackgroundResource(R.drawable.selector_ota_continue);
                    ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_PAUSE_OTA);
                }
                mTvDescribe.setText(mFormatString);
                pauseState = !pauseState;
                break;
        }
    }

    private void setWidth(int progress){
        //avoid 0 progress leads to width off normal
        if(progress == 0){
            progress = 1;
        }
        final ViewGroup.LayoutParams layoutParams = mIvProgress.getLayoutParams();
        int width = layoutParams.width;
        int mWidth = 585 * progress/100;
        Log.i(TAG,"original width = "+width);
        Log.i(TAG,"want to change mWidth ="+mWidth);
        ValueAnimator animator = ValueAnimator.ofInt(width,mWidth);
        animator.setDuration(1000);
        animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                layoutParams.width = (int) animation.getAnimatedValue();
                mIvProgress.setLayoutParams(layoutParams);
            }
        });
        animator.start();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if(mAnimationDrawable!=null){
            mAnimationDrawable.stop();
        }
        pauseState = false;
    }

    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
        switch (type) {
            case PROGRESS_DOWNLOAD:
                Log.i(TAG,"download progress = "+message);
                try {
                    mDownloadProgress = Integer.valueOf(message);
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                    break;
                }
                setWidth(mDownloadProgress);
                mFormatString = String.format(getResources().getString(R.string.starting_upgrade), mDownloadProgress);
                mTvDescribe.setText(mFormatString);
                break;
            case PROGRESS_INSTALL:
                Log.i(TAG,"install progress = "+message);
                mTvCancel.setVisibility(View.GONE);
                mOtaCancel.setVisibility(View.GONE);
                mOtaPauseContinue.setVisibility(View.GONE);
                mTvPauseContinue.setVisibility(View.GONE);
                if(mViewStub!=null){
                    mViewStub.setVisibility(View.VISIBLE);
                }
                if (mIsRollback) {
                    mFormatString = getResources().getString(R.string.rollback_upgrade);
                } else {
                    mFormatString = getResources().getString(R.string.installing_upgrade);
                }
                mTvDescribe.setText(mFormatString);
                int progress;
                try {
                    progress = Integer.valueOf(message);
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                    break;
                }
                setWidth(progress);
                break;
            case OTA_WARNING:
                if ("show".equals(message)) {
                    if (mWarningDialog == null) {
                        mWarningDialog = new OTAWarningDialog(getContext());
                        mWarningDialog.show();
                    }
                } else {
                    if (mWarningDialog != null) {
                        mWarningDialog.dismiss();
                        mWarningDialog = null;
                    }
                }
                break;
        }
    }
}
