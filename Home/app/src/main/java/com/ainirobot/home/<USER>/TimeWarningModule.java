package com.ainirobot.home.module;

import android.content.Context;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.bi.TimeWarningReport;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.ui.UIController;

public class TimeWarningModule extends BaseModule {

    private static final String TAG = "TimeWarningModule:Home";

    private static TimeWarningModule sInstance = null;
    private Context mContext;

    public static TimeWarningModule getInstance() {
        if (sInstance == null) {
            sInstance = new TimeWarningModule();
        }
        return sInstance;
    }

    private TimeWarningModule() {
    }

    public void init(Context context) {
        mContext = context;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.d(TAG, "reqId=" + reqId + "; intent=" + intent + "; params=" + params);
        switch (intent) {
            case Definition.REQ_SYSTEM_TIME_WARNING_START:
                SkillManager.getInstance().closeSpeechAsrRecognize();
                SkillManager.getInstance().cancleAudioOperation();
                UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_TIME_WARNING, null, null);
                TimeWarningReport report = new TimeWarningReport();
                report.addAction(TimeWarningReport.ACTION_SHOW_VIEW).report();
                break;
            case Definition.REQ_SYSTEM_TIME_WARNING_STOP:
//                UIController.getInstance().sendMessageToFragment(
//                        UIController.MESSAGE_TYPE.TIME_WARNING_EXIT, String.valueOf(true));
                stop();
                break;
        }
        SystemApi.getInstance().finishModuleParser(reqId, true);
        return false;
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
        switch (type) {
            case ModuleDef.LOCAL_MESSAGE_TIME_WARNING_STOP:
                SystemApi.getInstance().updateTimeWarning(false);
                stop();
                break;
            case ModuleDef.LOCAL_MESSAGE_TIME_WARNING_STOP_ALWAYS:
                SystemApi.getInstance().updateTimeWarning(true);
                stop();
                break;
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        SkillManager.getInstance().openSpeechAsrRecognize();
    }
}
