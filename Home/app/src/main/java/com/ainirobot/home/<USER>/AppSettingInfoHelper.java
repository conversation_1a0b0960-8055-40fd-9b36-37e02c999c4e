package com.ainirobot.home.utils;

import android.util.Log;

import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.bean.ShowAppBean;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;

/**
 * 处理自定义桌面app设置信息
 * resolveInfo.activityInfo中只保留属性字段和applicationInfo，其他结构体字段如metaData去掉，避免序列化时出现崩溃问题。
 * 读取时去掉此字段，避免出现解析异常，兼容旧版本数据。
 * 保存信息时，只保存resolveInfo.activityInfo中的属性字段和applicationInfo，保证后续版本数据解析正常。
 */
public class AppSettingInfoHelper {

    public static final String TAG = "AppSettingInfoHelper:Home:";

    /**
     * 获取app设置信息
     *
     * @return
     */
    public static String getAppSettingInfo() {
        String showAppJson = RobotSettingApi.getInstance().getRobotString("show_app_settings");
        Log.d(TAG, "getAppSettingInfo:: showAppJson——》" + showAppJson);
        if (showAppJson == null || showAppJson.isEmpty()) {
            return showAppJson;
        }
        showAppJson = transformAppSettingInfo(showAppJson);
        return showAppJson;
    }

    /**
     * 保存app设置信息
     */
    public static void saveAppSettingInfo(String appSettingInfo) {
        String jsonString = transformAppSettingInfo(appSettingInfo);
        Log.d(TAG, "saveAppSettingInfo:: jsonString ——》" + jsonString);
        RobotSettingApi.getInstance().setRobotString("show_app_settings", jsonString);
    }

    /**
     * 转换app设置信息:去掉resolveInfo.activityInfo中的metaData字段
     *
     * @param appSettingInfo
     * @return
     */
    public static String transformAppSettingInfo(String appSettingInfo) {
        Gson gson = new Gson();
        Type listType = new TypeToken<List<Map<String, Object>>>() {
        }.getType();
        List<Map<String, Object>> appList = gson.fromJson(appSettingInfo, listType);

        for (Map<String, Object> app : appList) {
            if (app.containsKey("resolveInfo")) {
                Map<String, Object> resolveInfo = (Map<String, Object>) app.get("resolveInfo");
                if (resolveInfo.containsKey("activityInfo")) {
                    Map<String, Object> activityInfo = (Map<String, Object>) resolveInfo.get("activityInfo");
                    activityInfo.remove("metaData");
                }
            }
        }
        String cleanedJson = gson.toJson(appList);
        return cleanedJson;
    }

    public static void test() {
//        String showAppJson = "[{\"isChecked\":false,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":3,\"documentLaunchMode\":0,\"flags\":3146240,\"launchMode\":1,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":1,\"rotationAnimation\":-1,\"screenOrientation\":-1,\"softInputMode\":0,\"taskAffinity\":\"com.ainirobot.robotos\",\"theme\":2131623941,\"uiOptions\":0,\"applicationInfo\":{\"appComponentFactory\":\"androidx.core.app.CoreComponentFactory\",\"category\":-1,\"className\":\"com.ainirobot.robotos.application.RobotOSApplication\",\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":29,\"compileSdkVersionCodename\":\"10\",\"credentialProtectedDataDir\":\"/data/user/0/com.ainirobot.robotos\",\"dataDir\":\"/data/user/0/com.ainirobot.robotos\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/com.ainirobot.robotos\",\"enabled\":true,\"enabledSetting\":0,\"flags\":818462534,\"fullBackupContent\":0,\"hiddenUntilInstalled\":false,\"installLocation\":-1,\"largestWidthLimitDp\":0,\"longVersionCode\":1,\"mHiddenApiPolicy\":-1,\"maxAspectRatio\":0.0,\"minSdkVersion\":16,\"nativeLibraryDir\":\"/data/app/com.ainirobot.robotos-2S0AteLgOtS1VyYNCbxBDg==/lib/arm64\",\"nativeLibraryRootDir\":\"/data/app/com.ainirobot.robotos-2S0AteLgOtS1VyYNCbxBDg==/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":0,\"overrideDensity\":0,\"overrideRes\":0,\"privateFlags\":4096,\"processName\":\"com.ainirobot.robotos\",\"publicSourceDir\":\"/data/app/com.ainirobot.robotos-2S0AteLgOtS1VyYNCbxBDg==/base.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/data/app/com.ainirobot.robotos-2S0AteLgOtS1VyYNCbxBDg==\",\"scanSourceDir\":\"/data/app/com.ainirobot.robotos-2S0AteLgOtS1VyYNCbxBDg==\",\"seInfo\":\"default:targetSdkVersion=29\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/data/app/com.ainirobot.robotos-2S0AteLgOtS1VyYNCbxBDg==/base.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":29,\"taskAffinity\":\"com.ainirobot.robotos\",\"theme\":2131623941,\"uiOptions\":0,\"uid\":10069,\"versionCode\":1,\"banner\":0,\"icon\":2131492864,\"labelRes\":2131558427,\"logo\":0,\"name\":\"com.ainirobot.robotos.application.RobotOSApplication\",\"packageName\":\"com.ainirobot.robotos\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"com.ainirobot.robotos\",\"banner\":0,\"icon\":0,\"labelRes\":0,\"logo\":0,\"name\":\"com.ainirobot.robotos.MainActivity\",\"packageName\":\"com.ainirobot.robotos\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":false,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":false,\"targetUserId\":-2}}]";
        String showAppJson = "[{\"isChecked\":false,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":3,\"documentLaunchMode\":0,\"flags\":3146240,\"launchMode\":1,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":1,\"rotationAnimation\":-1,\"screenOrientation\":-1,\"softInputMode\":0,\"taskAffinity\":\"com.ainirobot.robotos\",\"theme\":2131623941,\"uiOptions\":0,\"applicationInfo\":{\"appComponentFactory\":\"androidx.core.app.CoreComponentFactory\",\"category\":-1,\"className\":\"com.ainirobot.robotos.application.RobotOSApplication\",\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":29,\"compileSdkVersionCodename\":\"10\",\"credentialProtectedDataDir\":\"/data/user/0/com.ainirobot.robotos\",\"dataDir\":\"/data/user/0/com.ainirobot.robotos\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/com.ainirobot.robotos\",\"enabled\":true,\"enabledSetting\":0,\"flags\":818462534,\"fullBackupContent\":0,\"hiddenUntilInstalled\":false,\"installLocation\":-1,\"largestWidthLimitDp\":0,\"longVersionCode\":1,\"mHiddenApiPolicy\":-1,\"maxAspectRatio\":0.0,\"minSdkVersion\":16,\"nativeLibraryDir\":\"/data/app/com.ainirobot.robotos-2S0AteLgOtS1VyYNCbxBDg\\u003d\\u003d/lib/arm64\",\"nativeLibraryRootDir\":\"/data/app/com.ainirobot.robotos-2S0AteLgOtS1VyYNCbxBDg\\u003d\\u003d/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":0,\"overrideDensity\":0,\"overrideRes\":0,\"privateFlags\":4096,\"processName\":\"com.ainirobot.robotos\",\"publicSourceDir\":\"/data/app/com.ainirobot.robotos-2S0AteLgOtS1VyYNCbxBDg\\u003d\\u003d/base.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/data/app/com.ainirobot.robotos-2S0AteLgOtS1VyYNCbxBDg\\u003d\\u003d\",\"scanSourceDir\":\"/data/app/com.ainirobot.robotos-2S0AteLgOtS1VyYNCbxBDg\\u003d\\u003d\",\"seInfo\":\"default:targetSdkVersion\\u003d29\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/data/app/com.ainirobot.robotos-2S0AteLgOtS1VyYNCbxBDg\\u003d\\u003d/base.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":29,\"taskAffinity\":\"com.ainirobot.robotos\",\"theme\":2131623941,\"uiOptions\":0,\"uid\":10069,\"versionCode\":1,\"banner\":0,\"icon\":2131492864,\"labelRes\":2131558427,\"logo\":0,\"name\":\"com.ainirobot.robotos.application.RobotOSApplication\",\"packageName\":\"com.ainirobot.robotos\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"com.ainirobot.robotos\",\"banner\":0,\"icon\":0,\"labelRes\":0,\"logo\":0,\"name\":\"com.ainirobot.robotos.MainActivity\",\"packageName\":\"com.ainirobot.robotos\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":false,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":false,\"targetUserId\":-2}},{\"isChecked\":false,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":3,\"documentLaunchMode\":0,\"flags\":512,\"launchMode\":0,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":1,\"rotationAnimation\":-1,\"screenOrientation\":10,\"softInputMode\":0,\"taskAffinity\":\"com.ainirobot.speechasrservice\",\"theme\":0,\"uiOptions\":0,\"applicationInfo\":{\"appComponentFactory\":\"android.support.v4.app.CoreComponentFactory\",\"category\":-1,\"className\":\"com.ainirobot.speechasrservice.SpeechApp\",\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":28,\"compileSdkVersionCodename\":\"9\",\"credentialProtectedDataDir\":\"/data/user/0/com.ainirobot.speechasrservice\",\"dataDir\":\"/data/user/0/com.ainirobot.speechasrservice\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/com.ainirobot.speechasrservice\",\"enabled\":true,\"enabledSetting\":0,\"flags\":684244551,\"fullBackupContent\":0,\"hiddenUntilInstalled\":false,\"installLocation\":-1,\"largestWidthLimitDp\":0,\"longVersionCode\":14002,\"mHiddenApiPolicy\":-1,\"maxAspectRatio\":0.0,\"minSdkVersion\":24,\"nativeLibraryDir\":\"/system/priv-app/SpeechService/lib/arm\",\"nativeLibraryRootDir\":\"/system/priv-app/SpeechService/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":0,\"overrideDensity\":0,\"overrideRes\":0,\"primaryCpuAbi\":\"armeabi\",\"privateFlags\":1052680,\"processName\":\"com.ainirobot.speechasrservice\",\"publicSourceDir\":\"/system/priv-app/SpeechService/SpeechService.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/system/priv-app/SpeechService\",\"scanSourceDir\":\"/system/priv-app/SpeechService\",\"seInfo\":\"platform:privapp:targetSdkVersion\\u003d10\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/system/priv-app/SpeechService/SpeechService.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":28,\"taskAffinity\":\"com.ainirobot.speechasrservice\",\"theme\":2131689478,\"uiOptions\":0,\"uid\":1000,\"versionCode\":14002,\"banner\":0,\"icon\":2131165268,\"labelRes\":2131623976,\"logo\":0,\"name\":\"com.ainirobot.speechasrservice.SpeechApp\",\"packageName\":\"com.ainirobot.speechasrservice\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"com.ainirobot.speechasrservice\",\"banner\":0,\"icon\":0,\"labelRes\":0,\"logo\":0,\"name\":\"com.ainirobot.speechasrservice.MainActivity\",\"packageName\":\"com.ainirobot.speechasrservice\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":false,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":true,\"targetUserId\":-2}},{\"isChecked\":false,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":3,\"documentLaunchMode\":0,\"flags\":512,\"launchMode\":0,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":1,\"rotationAnimation\":-1,\"screenOrientation\":-1,\"softInputMode\":0,\"taskAffinity\":\"com.ainirobot.settings\",\"theme\":0,\"uiOptions\":0,\"applicationInfo\":{\"category\":-1,\"className\":\"com.ainirobot.settings.ApplicationWrapper\",\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":28,\"compileSdkVersionCodename\":\"9\",\"credentialProtectedDataDir\":\"/data/user/0/com.ainirobot.settings\",\"dataDir\":\"/data/user/0/com.ainirobot.settings\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/com.ainirobot.settings\",\"enabled\":true,\"enabledSetting\":0,\"flags\":952680005,\"fullBackupContent\":0,\"hiddenUntilInstalled\":false,\"installLocation\":-1,\"largestWidthLimitDp\":0,\"longVersionCode\":28,\"mHiddenApiPolicy\":-1,\"maxAspectRatio\":0.0,\"minSdkVersion\":28,\"nativeLibraryDir\":\"/system/priv-app/RobotSettings/lib/arm64\",\"nativeLibraryRootDir\":\"/system/priv-app/RobotSettings/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":0,\"overrideDensity\":0,\"overrideRes\":0,\"primaryCpuAbi\":\"arm64-v8a\",\"privateFlags\":1052936,\"processName\":\"com.ainirobot.settings\",\"publicSourceDir\":\"/system/priv-app/RobotSettings/RobotSettings.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/system/priv-app/RobotSettings\",\"scanSourceDir\":\"/system/priv-app/RobotSettings\",\"seInfo\":\"platform:privapp:targetSdkVersion\\u003d10\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/system/priv-app/RobotSettings/RobotSettings.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":28,\"taskAffinity\":\"com.ainirobot.settings\",\"theme\":2131623936,\"uiOptions\":0,\"uid\":1000,\"versionCode\":28,\"banner\":0,\"icon\":2130837559,\"labelRes\":2131558400,\"logo\":0,\"name\":\"com.ainirobot.settings.ApplicationWrapper\",\"packageName\":\"com.ainirobot.settings\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"com.ainirobot.settings\",\"banner\":0,\"icon\":0,\"labelRes\":2131558400,\"logo\":0,\"name\":\"com.ainirobot.settings.activity.SettingsActivity\",\"packageName\":\"com.ainirobot.settings\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":false,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":true,\"targetUserId\":-2}},{\"isChecked\":true,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":3,\"documentLaunchMode\":0,\"flags\":512,\"launchMode\":0,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":1,\"rotationAnimation\":-1,\"screenOrientation\":-1,\"softInputMode\":0,\"taskAffinity\":\"com.ainirobot.reposition\",\"theme\":2131558729,\"uiOptions\":0,\"applicationInfo\":{\"appComponentFactory\":\"android.support.v4.app.CoreComponentFactory\",\"category\":-1,\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":28,\"compileSdkVersionCodename\":\"9\",\"credentialProtectedDataDir\":\"/data/user/0/com.ainirobot.reposition\",\"dataDir\":\"/data/user/0/com.ainirobot.reposition\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/com.ainirobot.reposition\",\"enabled\":true,\"enabledSetting\":0,\"flags\":684244551,\"fullBackupContent\":0,\"hiddenUntilInstalled\":false,\"installLocation\":-1,\"largestWidthLimitDp\":0,\"longVersionCode\":1,\"mHiddenApiPolicy\":-1,\"maxAspectRatio\":0.0,\"minSdkVersion\":23,\"nativeLibraryDir\":\"/system/priv-app/Reposition/lib/arm64\",\"nativeLibraryRootDir\":\"/system/priv-app/Reposition/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":0,\"overrideDensity\":0,\"overrideRes\":0,\"privateFlags\":1052680,\"processName\":\"com.ainirobot.reposition\",\"publicSourceDir\":\"/system/priv-app/Reposition/Reposition.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/system/priv-app/Reposition\",\"scanSourceDir\":\"/system/priv-app/Reposition\",\"seInfo\":\"platform:privapp:targetSdkVersion\\u003d28\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/system/priv-app/Reposition/Reposition.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":28,\"taskAffinity\":\"com.ainirobot.reposition\",\"theme\":2131558728,\"uiOptions\":0,\"uid\":10036,\"versionCode\":1,\"banner\":0,\"icon\":2131165286,\"labelRes\":2131492903,\"logo\":0,\"packageName\":\"com.ainirobot.reposition\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"com.ainirobot.reposition\",\"banner\":0,\"icon\":0,\"labelRes\":0,\"logo\":0,\"name\":\"com.ainirobot.reposition.MainActivity\",\"packageName\":\"com.ainirobot.reposition\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":false,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":true,\"targetUserId\":-2}},{\"isChecked\":false,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":5555,\"documentLaunchMode\":0,\"flags\":512,\"launchMode\":3,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":1,\"rotationAnimation\":-1,\"screenOrientation\":10,\"softInputMode\":0,\"taskAffinity\":\"com.ainirobot.orionuploadservice\",\"theme\":2131623940,\"uiOptions\":0,\"applicationInfo\":{\"category\":-1,\"className\":\"com.ainirobot.orionuploadservice.ApplicationWrapper\",\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":0,\"credentialProtectedDataDir\":\"/data/user/0/com.ainirobot.orionuploadservice\",\"dataDir\":\"/data/user/0/com.ainirobot.orionuploadservice\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/com.ainirobot.orionuploadservice\",\"enabled\":true,\"enabledSetting\":0,\"flags\":684244551,\"fullBackupContent\":0,\"hiddenUntilInstalled\":false,\"installLocation\":-1,\"largestWidthLimitDp\":0,\"longVersionCode\":12600,\"mHiddenApiPolicy\":-1,\"maxAspectRatio\":0.0,\"minSdkVersion\":23,\"nativeLibraryDir\":\"/system/priv-app/UploadServiceOrion/lib/arm64\",\"nativeLibraryRootDir\":\"/system/priv-app/UploadServiceOrion/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":0,\"overrideDensity\":0,\"overrideRes\":0,\"primaryCpuAbi\":\"arm64-v8a\",\"privateFlags\":1052680,\"processName\":\"com.ainirobot.orionuploadservice\",\"publicSourceDir\":\"/system/priv-app/UploadServiceOrion/UploadServiceOrion.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/system/priv-app/UploadServiceOrion\",\"scanSourceDir\":\"/system/priv-app/UploadServiceOrion\",\"seInfo\":\"platform:privapp:targetSdkVersion\\u003d10\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/system/priv-app/UploadServiceOrion/UploadServiceOrion.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":25,\"taskAffinity\":\"com.ainirobot.orionuploadservice\",\"theme\":16973830,\"uiOptions\":0,\"uid\":1000,\"versionCode\":12600,\"banner\":0,\"icon\":2131230806,\"labelRes\":2131558431,\"logo\":0,\"name\":\"com.ainirobot.orionuploadservice.ApplicationWrapper\",\"packageName\":\"com.ainirobot.orionuploadservice\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"com.ainirobot.orionuploadservice\",\"banner\":0,\"icon\":0,\"labelRes\":0,\"logo\":0,\"name\":\"com.ainirobot.orionuploadservice.NewMainActivity\",\"packageName\":\"com.ainirobot.orionuploadservice\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":false,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":true,\"targetUserId\":-2}},{\"isChecked\":false,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":3,\"documentLaunchMode\":0,\"flags\":512,\"launchMode\":0,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":1,\"rotationAnimation\":-1,\"screenOrientation\":-1,\"softInputMode\":0,\"taskAffinity\":\"com.ainirobot.netcheck\",\"theme\":2131820903,\"uiOptions\":0,\"applicationInfo\":{\"appComponentFactory\":\"android.support.v4.app.CoreComponentFactory\",\"category\":-1,\"className\":\"com.ainirobot.netcheck.MyApplication\",\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":33,\"compileSdkVersionCodename\":\"13\",\"credentialProtectedDataDir\":\"/data/user/0/com.ainirobot.netcheck\",\"dataDir\":\"/data/user/0/com.ainirobot.netcheck\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/com.ainirobot.netcheck\",\"enabled\":true,\"enabledSetting\":0,\"flags\":550026823,\"fullBackupContent\":0,\"hiddenUntilInstalled\":false,\"installLocation\":-1,\"largestWidthLimitDp\":0,\"longVersionCode\":15,\"mHiddenApiPolicy\":-1,\"maxAspectRatio\":0.0,\"minSdkVersion\":23,\"nativeLibraryDir\":\"/system/priv-app/NetCheck/lib/arm64\",\"nativeLibraryRootDir\":\"/system/priv-app/NetCheck/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":0,\"overrideDensity\":0,\"overrideRes\":0,\"primaryCpuAbi\":\"arm64-v8a\",\"privateFlags\":1052680,\"processName\":\"com.ainirobot.netcheck\",\"publicSourceDir\":\"/system/priv-app/NetCheck/NetCheck.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/system/priv-app/NetCheck\",\"scanSourceDir\":\"/system/priv-app/NetCheck\",\"seInfo\":\"platform:privapp:targetSdkVersion\\u003d10\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/system/priv-app/NetCheck/NetCheck.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":33,\"taskAffinity\":\"com.ainirobot.netcheck\",\"theme\":2131820902,\"uiOptions\":0,\"uid\":1000,\"versionCode\":15,\"banner\":0,\"icon\":2131623940,\"labelRes\":2131755047,\"logo\":0,\"name\":\"com.ainirobot.netcheck.MyApplication\",\"packageName\":\"com.ainirobot.netcheck\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"com.ainirobot.netcheck\",\"banner\":0,\"icon\":0,\"labelRes\":2131755047,\"logo\":0,\"name\":\"com.ainirobot.netcheck.MainActivity\",\"packageName\":\"com.ainirobot.netcheck\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":false,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":true,\"targetUserId\":-2}},{\"isChecked\":false,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":5555,\"documentLaunchMode\":0,\"flags\":512,\"launchMode\":2,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":1,\"rotationAnimation\":-1,\"screenOrientation\":-1,\"softInputMode\":0,\"taskAffinity\":\".platform\",\"theme\":0,\"uiOptions\":0,\"applicationInfo\":{\"category\":-1,\"className\":\"com.ainirobot.moduleapp.RobotApp\",\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":28,\"compileSdkVersionCodename\":\"9\",\"credentialProtectedDataDir\":\"/data/user/0/com.ainirobot.moduleapp\",\"dataDir\":\"/data/user/0/com.ainirobot.moduleapp\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/com.ainirobot.moduleapp\",\"enabled\":true,\"enabledSetting\":0,\"flags\":953728581,\"fullBackupContent\":0,\"hiddenUntilInstalled\":false,\"installLocation\":-1,\"largestWidthLimitDp\":0,\"longVersionCode\":21001,\"mHiddenApiPolicy\":-1,\"maxAspectRatio\":0.0,\"minSdkVersion\":23,\"nativeLibraryDir\":\"/system/priv-app/ModuleApp/lib/arm\",\"nativeLibraryRootDir\":\"/system/priv-app/ModuleApp/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":2131689473,\"overrideDensity\":0,\"overrideRes\":0,\"primaryCpuAbi\":\"armeabi-v7a\",\"privateFlags\":1052680,\"processName\":\"com.ainirobot.moduleapp\",\"publicSourceDir\":\"/system/priv-app/ModuleApp/ModuleApp.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/system/priv-app/ModuleApp\",\"scanSourceDir\":\"/system/priv-app/ModuleApp\",\"seInfo\":\"platform:privapp:targetSdkVersion\\u003d10\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/system/priv-app/ModuleApp/ModuleApp.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":28,\"taskAffinity\":\"com.ainirobot.moduleapp\",\"theme\":2131558406,\"uiOptions\":0,\"uid\":1000,\"versionCode\":21001,\"banner\":0,\"icon\":2131165388,\"labelRes\":2131492897,\"logo\":0,\"name\":\"com.ainirobot.moduleapp.RobotApp\",\"packageName\":\"com.ainirobot.moduleapp\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"com.ainirobot.moduleapp\",\"banner\":0,\"icon\":0,\"labelRes\":0,\"logo\":0,\"name\":\"com.ainirobot.moduleapp.MainActivity\",\"packageName\":\"com.ainirobot.moduleapp\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":false,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":true,\"targetUserId\":-2}},{\"isChecked\":false,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":3,\"documentLaunchMode\":0,\"flags\":512,\"launchMode\":2,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":1,\"rotationAnimation\":-1,\"screenOrientation\":-1,\"softInputMode\":0,\"taskAffinity\":\"com.ainirobot.maptool\",\"theme\":16973831,\"uiOptions\":0,\"applicationInfo\":{\"appComponentFactory\":\"androidx.core.app.CoreComponentFactory\",\"category\":-1,\"className\":\"com.ainirobot.maptool.ApplicationWrapper\",\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":28,\"compileSdkVersionCodename\":\"9\",\"credentialProtectedDataDir\":\"/data/user/0/com.ainirobot.maptool\",\"dataDir\":\"/data/user/0/com.ainirobot.maptool\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/com.ainirobot.maptool\",\"enabled\":true,\"enabledSetting\":0,\"flags\":551075783,\"fullBackupContent\":0,\"hiddenUntilInstalled\":false,\"installLocation\":-1,\"largestWidthLimitDp\":0,\"longVersionCode\":14201,\"mHiddenApiPolicy\":-1,\"maxAspectRatio\":0.0,\"minSdkVersion\":23,\"nativeLibraryDir\":\"/data/app/com.ainirobot.maptool-UqHr58PS5OQG12HW0h-U_Q\\u003d\\u003d/lib/arm64\",\"nativeLibraryRootDir\":\"/data/app/com.ainirobot.maptool-UqHr58PS5OQG12HW0h-U_Q\\u003d\\u003d/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":0,\"overrideDensity\":0,\"overrideRes\":0,\"primaryCpuAbi\":\"arm64-v8a\",\"privateFlags\":1052680,\"processName\":\"com.ainirobot.maptool\",\"publicSourceDir\":\"/data/app/com.ainirobot.maptool-UqHr58PS5OQG12HW0h-U_Q\\u003d\\u003d/base.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/data/app/com.ainirobot.maptool-UqHr58PS5OQG12HW0h-U_Q\\u003d\\u003d\",\"scanSourceDir\":\"/data/app/com.ainirobot.maptool-UqHr58PS5OQG12HW0h-U_Q\\u003d\\u003d\",\"seInfo\":\"platform:privapp:targetSdkVersion\\u003d10\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/data/app/com.ainirobot.maptool-UqHr58PS5OQG12HW0h-U_Q\\u003d\\u003d/base.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":28,\"taskAffinity\":\"com.ainirobot.maptool\",\"theme\":2131689477,\"uiOptions\":0,\"uid\":1000,\"versionCode\":14201,\"banner\":0,\"icon\":2131230988,\"labelRes\":2131623991,\"logo\":0,\"name\":\"com.ainirobot.maptool.ApplicationWrapper\",\"packageName\":\"com.ainirobot.maptool\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"com.ainirobot.maptool\",\"banner\":0,\"icon\":0,\"labelRes\":0,\"logo\":0,\"name\":\"com.ainirobot.maptool.activity.GuideInitActivity\",\"packageName\":\"com.ainirobot.maptool\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":false,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":true,\"targetUserId\":-2}},{\"isChecked\":false,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":1203,\"documentLaunchMode\":0,\"flags\":512,\"launchMode\":2,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":1,\"rotationAnimation\":-1,\"screenOrientation\":10,\"softInputMode\":0,\"taskAffinity\":\"com.ainirobot.inspection\",\"theme\":16973831,\"uiOptions\":0,\"applicationInfo\":{\"appComponentFactory\":\"android.support.v4.app.CoreComponentFactory\",\"category\":-1,\"className\":\"com.ainirobot.inspection.ApplicationWrapper\",\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":28,\"compileSdkVersionCodename\":\"9\",\"credentialProtectedDataDir\":\"/data/user/0/com.ainirobot.inspection\",\"dataDir\":\"/data/user/0/com.ainirobot.inspection\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/com.ainirobot.inspection\",\"enabled\":true,\"enabledSetting\":0,\"flags\":818462279,\"fullBackupContent\":0,\"hiddenUntilInstalled\":false,\"installLocation\":-1,\"largestWidthLimitDp\":0,\"longVersionCode\":10200,\"mHiddenApiPolicy\":-1,\"maxAspectRatio\":0.0,\"minSdkVersion\":22,\"nativeLibraryDir\":\"/system/priv-app/Inspection/lib/arm64\",\"nativeLibraryRootDir\":\"/system/priv-app/Inspection/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":0,\"overrideDensity\":0,\"overrideRes\":0,\"primaryCpuAbi\":\"arm64-v8a\",\"privateFlags\":1052680,\"processName\":\"com.ainirobot.inspection\",\"publicSourceDir\":\"/system/priv-app/Inspection/Inspection.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/system/priv-app/Inspection\",\"scanSourceDir\":\"/system/priv-app/Inspection\",\"seInfo\":\"platform:privapp:targetSdkVersion\\u003d10\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/system/priv-app/Inspection/Inspection.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":28,\"taskAffinity\":\"com.ainirobot.inspection\",\"theme\":2131427333,\"uiOptions\":0,\"uid\":1000,\"versionCode\":10200,\"banner\":0,\"icon\":2131099732,\"labelRes\":2131361833,\"logo\":0,\"name\":\"com.ainirobot.inspection.ApplicationWrapper\",\"packageName\":\"com.ainirobot.inspection\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"com.ainirobot.inspection\",\"banner\":0,\"icon\":0,\"labelRes\":0,\"logo\":0,\"name\":\"com.ainirobot.inspection.ui.MainActivity\",\"packageName\":\"com.ainirobot.inspection\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":false,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":true,\"targetUserId\":-2}},{\"isChecked\":false,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":3,\"documentLaunchMode\":0,\"flags\":512,\"launchMode\":0,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":1,\"rotationAnimation\":-1,\"screenOrientation\":-1,\"softInputMode\":0,\"taskAffinity\":\"com.ainirobot.bms\",\"theme\":0,\"uiOptions\":0,\"applicationInfo\":{\"appComponentFactory\":\"androidx.core.app.CoreComponentFactory\",\"category\":-1,\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":28,\"compileSdkVersionCodename\":\"9\",\"credentialProtectedDataDir\":\"/data/user/0/com.ainirobot.bms\",\"dataDir\":\"/data/user/0/com.ainirobot.bms\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/com.ainirobot.bms\",\"enabled\":true,\"enabledSetting\":0,\"flags\":818462279,\"fullBackupContent\":2131820544,\"hiddenUntilInstalled\":false,\"installLocation\":-1,\"largestWidthLimitDp\":0,\"longVersionCode\":1,\"mHiddenApiPolicy\":-1,\"maxAspectRatio\":0.0,\"minSdkVersion\":21,\"nativeLibraryDir\":\"/system/priv-app/RobotBMS/lib/arm64\",\"nativeLibraryRootDir\":\"/system/priv-app/RobotBMS/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":0,\"overrideDensity\":0,\"overrideRes\":0,\"primaryCpuAbi\":\"arm64-v8a\",\"privateFlags\":1052680,\"processName\":\"com.ainirobot.bms\",\"publicSourceDir\":\"/system/priv-app/RobotBMS/RobotBMS.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/system/priv-app/RobotBMS\",\"scanSourceDir\":\"/system/priv-app/RobotBMS\",\"seInfo\":\"platform:privapp:targetSdkVersion\\u003d10\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/system/priv-app/RobotBMS/RobotBMS.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":28,\"taskAffinity\":\"com.ainirobot.bms\",\"theme\":2131689938,\"uiOptions\":0,\"uid\":1000,\"versionCode\":1,\"banner\":0,\"icon\":2131492864,\"labelRes\":2131623963,\"logo\":0,\"packageName\":\"com.ainirobot.bms\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"com.ainirobot.bms\",\"banner\":0,\"icon\":0,\"labelRes\":0,\"logo\":0,\"name\":\"com.ainirobot.bms.MainActivity\",\"packageName\":\"com.ainirobot.bms\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":false,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":true,\"targetUserId\":-2}},{\"isChecked\":false,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":8115,\"documentLaunchMode\":0,\"flags\":7340032,\"launchMode\":2,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":2,\"rotationAnimation\":-1,\"screenOrientation\":-1,\"softInputMode\":16,\"targetActivity\":\"org.chromium.chrome.browser.ChromeTabbedActivity\",\"taskAffinity\":\"com.microsoft.emmx\",\"theme\":2132084232,\"uiOptions\":0,\"applicationInfo\":{\"appComponentFactory\":\"org.chromium.chrome.browser.base.SplitCompatAppComponentFactory\",\"category\":-1,\"className\":\"org.chromium.chrome.browser.base.SplitChromeApplication\",\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":31,\"compileSdkVersionCodename\":\"12\",\"credentialProtectedDataDir\":\"/data/user/0/com.microsoft.emmx\",\"dataDir\":\"/data/user/0/com.microsoft.emmx\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/com.microsoft.emmx\",\"enabled\":true,\"enabledSetting\":0,\"flags\":952647237,\"fullBackupContent\":0,\"hiddenUntilInstalled\":false,\"installLocation\":-1,\"largestWidthLimitDp\":0,\"longVersionCode\":115005515,\"mHiddenApiPolicy\":-1,\"manageSpaceActivityName\":\"org.chromium.chrome.browser.site_settings.ManageSpaceActivity\",\"maxAspectRatio\":0.0,\"minSdkVersion\":23,\"nativeLibraryDir\":\"/system/priv-app/Edge/lib/arm64\",\"nativeLibraryRootDir\":\"/system/priv-app/Edge/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":2132279363,\"overrideDensity\":0,\"overrideRes\":0,\"primaryCpuAbi\":\"arm64-v8a\",\"privateFlags\":1052952,\"processName\":\"com.microsoft.emmx\",\"publicSourceDir\":\"/system/priv-app/Edge/Edge.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/system/priv-app/Edge\",\"scanSourceDir\":\"/system/priv-app/Edge\",\"seInfo\":\"platform:privapp:targetSdkVersion\\u003d31\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/system/priv-app/Edge/Edge.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":31,\"taskAffinity\":\"com.microsoft.emmx\",\"theme\":0,\"uiOptions\":0,\"uid\":10034,\"versionCode\":115005515,\"banner\":0,\"icon\":2131820545,\"labelRes\":2132017541,\"logo\":0,\"name\":\"org.chromium.chrome.browser.base.SplitChromeApplication\",\"packageName\":\"com.microsoft.emmx\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"com.microsoft.emmx\",\"banner\":0,\"icon\":0,\"labelRes\":0,\"logo\":0,\"name\":\"com.microsoft.ruby.Main\",\"packageName\":\"com.microsoft.emmx\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":true,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":true,\"targetUserId\":-2}},{\"isChecked\":false,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":3,\"documentLaunchMode\":0,\"flags\":512,\"launchMode\":0,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":1,\"rotationAnimation\":-1,\"screenOrientation\":-1,\"softInputMode\":0,\"taskAffinity\":\"ua.zt.mezon.myjnacallbacktest\",\"theme\":0,\"uiOptions\":0,\"applicationInfo\":{\"appComponentFactory\":\"androidx.core.app.CoreComponentFactory\",\"category\":-1,\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":33,\"compileSdkVersionCodename\":\"13\",\"credentialProtectedDataDir\":\"/data/user/0/ua.zt.mezon.myjnacallbacktest\",\"dataDir\":\"/data/user/0/ua.zt.mezon.myjnacallbacktest\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/ua.zt.mezon.myjnacallbacktest\",\"enabled\":true,\"enabledSetting\":0,\"flags\":818462534,\"fullBackupContent\":0,\"hiddenUntilInstalled\":false,\"installLocation\":-1,\"largestWidthLimitDp\":0,\"longVersionCode\":10,\"mHiddenApiPolicy\":-1,\"maxAspectRatio\":0.0,\"minSdkVersion\":19,\"nativeLibraryDir\":\"/data/app/ua.zt.mezon.myjnacallbacktest-HmY84pxXLBsMQEns-nulJg\\u003d\\u003d/lib/arm64\",\"nativeLibraryRootDir\":\"/data/app/ua.zt.mezon.myjnacallbacktest-HmY84pxXLBsMQEns-nulJg\\u003d\\u003d/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":0,\"overrideDensity\":0,\"overrideRes\":0,\"primaryCpuAbi\":\"arm64-v8a\",\"privateFlags\":1052672,\"processName\":\"ua.zt.mezon.myjnacallbacktest\",\"publicSourceDir\":\"/data/app/ua.zt.mezon.myjnacallbacktest-HmY84pxXLBsMQEns-nulJg\\u003d\\u003d/base.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/data/app/ua.zt.mezon.myjnacallbacktest-HmY84pxXLBsMQEns-nulJg\\u003d\\u003d\",\"scanSourceDir\":\"/data/app/ua.zt.mezon.myjnacallbacktest-HmY84pxXLBsMQEns-nulJg\\u003d\\u003d\",\"seInfo\":\"platform:privapp:targetSdkVersion\\u003d10\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/data/app/ua.zt.mezon.myjnacallbacktest-HmY84pxXLBsMQEns-nulJg\\u003d\\u003d/base.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":33,\"taskAffinity\":\"ua.zt.mezon.myjnacallbacktest\",\"theme\":2131623941,\"uiOptions\":0,\"uid\":1000,\"versionCode\":10,\"banner\":0,\"icon\":2131492864,\"labelRes\":2131558428,\"logo\":0,\"packageName\":\"ua.zt.mezon.myjnacallbacktest\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"ua.zt.mezon.myjnacallbacktest\",\"banner\":0,\"icon\":0,\"labelRes\":0,\"logo\":0,\"name\":\"ua.zt.mezon.myjnacallbacktest.MainActivity\",\"packageName\":\"ua.zt.mezon.myjnacallbacktest\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":false,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":false,\"targetUserId\":-2}},{\"isChecked\":false,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":3,\"documentLaunchMode\":0,\"flags\":512,\"launchMode\":0,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":1,\"rotationAnimation\":-1,\"screenOrientation\":-1,\"softInputMode\":0,\"taskAffinity\":\"com.heiko.networkdetectionsample\",\"theme\":0,\"uiOptions\":0,\"applicationInfo\":{\"appComponentFactory\":\"androidx.core.app.CoreComponentFactory\",\"category\":-1,\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":28,\"compileSdkVersionCodename\":\"9\",\"credentialProtectedDataDir\":\"/data/user/0/com.heiko.networkdetectionsample\",\"dataDir\":\"/data/user/0/com.heiko.networkdetectionsample\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/com.heiko.networkdetectionsample\",\"enabled\":true,\"enabledSetting\":0,\"flags\":954777414,\"fullBackupContent\":0,\"hiddenUntilInstalled\":false,\"installLocation\":-1,\"largestWidthLimitDp\":0,\"longVersionCode\":1,\"mHiddenApiPolicy\":-1,\"maxAspectRatio\":0.0,\"minSdkVersion\":21,\"nativeLibraryDir\":\"/data/app/com.heiko.networkdetectionsample-sNKDKxvtrEaYv0yJJ1GZ1w\\u003d\\u003d/lib/arm\",\"nativeLibraryRootDir\":\"/data/app/com.heiko.networkdetectionsample-sNKDKxvtrEaYv0yJJ1GZ1w\\u003d\\u003d/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":0,\"overrideDensity\":0,\"overrideRes\":0,\"primaryCpuAbi\":\"armeabi\",\"privateFlags\":4096,\"processName\":\"com.heiko.networkdetectionsample\",\"publicSourceDir\":\"/data/app/com.heiko.networkdetectionsample-sNKDKxvtrEaYv0yJJ1GZ1w\\u003d\\u003d/base.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/data/app/com.heiko.networkdetectionsample-sNKDKxvtrEaYv0yJJ1GZ1w\\u003d\\u003d\",\"scanSourceDir\":\"/data/app/com.heiko.networkdetectionsample-sNKDKxvtrEaYv0yJJ1GZ1w\\u003d\\u003d\",\"seInfo\":\"default:targetSdkVersion\\u003d27\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/data/app/com.heiko.networkdetectionsample-sNKDKxvtrEaYv0yJJ1GZ1w\\u003d\\u003d/base.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":27,\"taskAffinity\":\"com.heiko.networkdetectionsample\",\"theme\":2131558405,\"uiOptions\":0,\"uid\":10067,\"versionCode\":1,\"banner\":0,\"icon\":2131427328,\"labelRes\":2131492891,\"logo\":0,\"packageName\":\"com.heiko.networkdetectionsample\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"com.heiko.networkdetectionsample\",\"banner\":0,\"icon\":0,\"labelRes\":0,\"logo\":0,\"name\":\"com.heiko.networkdetectionsample.MainActivity\",\"packageName\":\"com.heiko.networkdetectionsample\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":false,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":false,\"targetUserId\":-2}},{\"isChecked\":false,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":3,\"documentLaunchMode\":0,\"flags\":512,\"launchMode\":0,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":1,\"rotationAnimation\":-1,\"screenOrientation\":-1,\"softInputMode\":0,\"taskAffinity\":\"com.example.myosthree\",\"theme\":2131886709,\"uiOptions\":0,\"applicationInfo\":{\"appComponentFactory\":\"androidx.core.app.CoreComponentFactory\",\"category\":-1,\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":34,\"compileSdkVersionCodename\":\"14\",\"credentialProtectedDataDir\":\"/data/user/0/com.example.myosthree\",\"dataDir\":\"/data/user/0/com.example.myosthree\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/com.example.myosthree\",\"enabled\":true,\"enabledSetting\":0,\"flags\":550027078,\"fullBackupContent\":2132017152,\"hiddenUntilInstalled\":false,\"installLocation\":-1,\"largestWidthLimitDp\":0,\"longVersionCode\":1,\"mHiddenApiPolicy\":-1,\"maxAspectRatio\":0.0,\"minSdkVersion\":24,\"nativeLibraryDir\":\"/data/app/com.example.myosthree-_BqmNyKSWZbBaONBm2XeIA\\u003d\\u003d/lib/arm64\",\"nativeLibraryRootDir\":\"/data/app/com.example.myosthree-_BqmNyKSWZbBaONBm2XeIA\\u003d\\u003d/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":0,\"overrideDensity\":0,\"overrideRes\":0,\"privateFlags\":4096,\"processName\":\"com.example.myosthree\",\"publicSourceDir\":\"/data/app/com.example.myosthree-_BqmNyKSWZbBaONBm2XeIA\\u003d\\u003d/base.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/data/app/com.example.myosthree-_BqmNyKSWZbBaONBm2XeIA\\u003d\\u003d\",\"scanSourceDir\":\"/data/app/com.example.myosthree-_BqmNyKSWZbBaONBm2XeIA\\u003d\\u003d\",\"seInfo\":\"default:targetSdkVersion\\u003d34\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/data/app/com.example.myosthree-_BqmNyKSWZbBaONBm2XeIA\\u003d\\u003d/base.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":34,\"taskAffinity\":\"com.example.myosthree\",\"theme\":2131886709,\"uiOptions\":0,\"uid\":10070,\"versionCode\":1,\"banner\":0,\"icon\":2131623936,\"labelRes\":2131820573,\"logo\":0,\"packageName\":\"com.example.myosthree\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"com.example.myosthree\",\"banner\":0,\"icon\":0,\"labelRes\":2131820573,\"logo\":0,\"name\":\"com.example.myosthree.MainActivity\",\"packageName\":\"com.example.myosthree\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":false,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":false,\"targetUserId\":-2}},{\"isChecked\":false,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":3,\"documentLaunchMode\":0,\"flags\":512,\"launchMode\":0,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":1,\"rotationAnimation\":-1,\"screenOrientation\":-1,\"softInputMode\":0,\"taskAffinity\":\"com.example.myosone\",\"theme\":2131886709,\"uiOptions\":0,\"applicationInfo\":{\"appComponentFactory\":\"androidx.core.app.CoreComponentFactory\",\"category\":-1,\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":34,\"compileSdkVersionCodename\":\"14\",\"credentialProtectedDataDir\":\"/data/user/0/com.example.myosone\",\"dataDir\":\"/data/user/0/com.example.myosone\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/com.example.myosone\",\"enabled\":true,\"enabledSetting\":0,\"flags\":552124230,\"fullBackupContent\":2132017152,\"hiddenUntilInstalled\":false,\"installLocation\":-1,\"largestWidthLimitDp\":0,\"longVersionCode\":1,\"mHiddenApiPolicy\":-1,\"maxAspectRatio\":0.0,\"minSdkVersion\":24,\"nativeLibraryDir\":\"/data/app/com.example.myosone-sX6A8BemZuQ1Zxng41ZuFw\\u003d\\u003d/lib/arm64\",\"nativeLibraryRootDir\":\"/data/app/com.example.myosone-sX6A8BemZuQ1Zxng41ZuFw\\u003d\\u003d/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":0,\"overrideDensity\":0,\"overrideRes\":0,\"privateFlags\":4096,\"processName\":\"com.example.myosone\",\"publicSourceDir\":\"/data/app/com.example.myosone-sX6A8BemZuQ1Zxng41ZuFw\\u003d\\u003d/base.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/data/app/com.example.myosone-sX6A8BemZuQ1Zxng41ZuFw\\u003d\\u003d\",\"scanSourceDir\":\"/data/app/com.example.myosone-sX6A8BemZuQ1Zxng41ZuFw\\u003d\\u003d\",\"seInfo\":\"default:targetSdkVersion\\u003d34\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/data/app/com.example.myosone-sX6A8BemZuQ1Zxng41ZuFw\\u003d\\u003d/base.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":34,\"taskAffinity\":\"com.example.myosone\",\"theme\":2131886709,\"uiOptions\":0,\"uid\":10068,\"versionCode\":1,\"banner\":0,\"icon\":2131623936,\"labelRes\":2131820573,\"logo\":0,\"packageName\":\"com.example.myosone\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"com.example.myosone\",\"banner\":0,\"icon\":0,\"labelRes\":2131820573,\"logo\":0,\"name\":\"com.example.myosone.MainActivity\",\"packageName\":\"com.example.myosone\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":false,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":false,\"targetUserId\":-2}},{\"isChecked\":false,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":3,\"documentLaunchMode\":0,\"flags\":512,\"launchMode\":0,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":1,\"rotationAnimation\":-1,\"screenOrientation\":-1,\"softInputMode\":0,\"taskAffinity\":\"com.example.myosfour\",\"theme\":2131886709,\"uiOptions\":0,\"applicationInfo\":{\"appComponentFactory\":\"androidx.core.app.CoreComponentFactory\",\"category\":-1,\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":34,\"compileSdkVersionCodename\":\"14\",\"credentialProtectedDataDir\":\"/data/user/0/com.example.myosfour\",\"dataDir\":\"/data/user/0/com.example.myosfour\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/com.example.myosfour\",\"enabled\":true,\"enabledSetting\":0,\"flags\":550027078,\"fullBackupContent\":2132017152,\"hiddenUntilInstalled\":false,\"installLocation\":-1,\"largestWidthLimitDp\":0,\"longVersionCode\":1,\"mHiddenApiPolicy\":-1,\"maxAspectRatio\":0.0,\"minSdkVersion\":24,\"nativeLibraryDir\":\"/data/app/com.example.myosfour-lnfmt_MmZoWthOO_QDfE0A\\u003d\\u003d/lib/arm64\",\"nativeLibraryRootDir\":\"/data/app/com.example.myosfour-lnfmt_MmZoWthOO_QDfE0A\\u003d\\u003d/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":0,\"overrideDensity\":0,\"overrideRes\":0,\"privateFlags\":4096,\"processName\":\"com.example.myosfour\",\"publicSourceDir\":\"/data/app/com.example.myosfour-lnfmt_MmZoWthOO_QDfE0A\\u003d\\u003d/base.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/data/app/com.example.myosfour-lnfmt_MmZoWthOO_QDfE0A\\u003d\\u003d\",\"scanSourceDir\":\"/data/app/com.example.myosfour-lnfmt_MmZoWthOO_QDfE0A\\u003d\\u003d\",\"seInfo\":\"default:targetSdkVersion\\u003d34\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/data/app/com.example.myosfour-lnfmt_MmZoWthOO_QDfE0A\\u003d\\u003d/base.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":34,\"taskAffinity\":\"com.example.myosfour\",\"theme\":2131886709,\"uiOptions\":0,\"uid\":10071,\"versionCode\":1,\"banner\":0,\"icon\":2131623936,\"labelRes\":2131820573,\"logo\":0,\"packageName\":\"com.example.myosfour\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"com.example.myosfour\",\"banner\":0,\"icon\":0,\"labelRes\":2131820573,\"logo\":0,\"name\":\"com.example.myosfour.MainActivity\",\"packageName\":\"com.example.myosfour\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":false,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":false,\"targetUserId\":-2}},{\"isChecked\":false,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":1073758135,\"documentLaunchMode\":0,\"flags\":512,\"launchMode\":1,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":1,\"rotationAnimation\":-1,\"screenOrientation\":-1,\"softInputMode\":16,\"taskAffinity\":\"kr.co.ishift.smartwaiting_robot_serving\",\"theme\":2131689635,\"uiOptions\":0,\"applicationInfo\":{\"appComponentFactory\":\"androidx.core.app.CoreComponentFactory\",\"category\":-1,\"className\":\"android.app.Application\",\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":34,\"compileSdkVersionCodename\":\"14\",\"credentialProtectedDataDir\":\"/data/user/0/kr.co.ishift.smartwaiting_robot_serving\",\"dataDir\":\"/data/user/0/kr.co.ishift.smartwaiting_robot_serving\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/kr.co.ishift.smartwaiting_robot_serving\",\"enabled\":true,\"enabledSetting\":0,\"flags\":814267974,\"fullBackupContent\":0,\"hiddenUntilInstalled\":false,\"installLocation\":-1,\"largestWidthLimitDp\":0,\"longVersionCode\":1,\"mHiddenApiPolicy\":-1,\"maxAspectRatio\":0.0,\"minSdkVersion\":21,\"nativeLibraryDir\":\"/data/app/kr.co.ishift.smartwaiting_robot_serving-inETFjOsPBoDvYK4_b2rJA\\u003d\\u003d/lib/arm64\",\"nativeLibraryRootDir\":\"/data/app/kr.co.ishift.smartwaiting_robot_serving-inETFjOsPBoDvYK4_b2rJA\\u003d\\u003d/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":0,\"overrideDensity\":0,\"overrideRes\":0,\"primaryCpuAbi\":\"arm64-v8a\",\"privateFlags\":4096,\"processName\":\"kr.co.ishift.smartwaiting_robot_serving\",\"publicSourceDir\":\"/data/app/kr.co.ishift.smartwaiting_robot_serving-inETFjOsPBoDvYK4_b2rJA\\u003d\\u003d/base.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/data/app/kr.co.ishift.smartwaiting_robot_serving-inETFjOsPBoDvYK4_b2rJA\\u003d\\u003d\",\"scanSourceDir\":\"/data/app/kr.co.ishift.smartwaiting_robot_serving-inETFjOsPBoDvYK4_b2rJA\\u003d\\u003d\",\"seInfo\":\"default:targetSdkVersion\\u003d34\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/data/app/kr.co.ishift.smartwaiting_robot_serving-inETFjOsPBoDvYK4_b2rJA\\u003d\\u003d/base.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":34,\"taskAffinity\":\"kr.co.ishift.smartwaiting_robot_serving\",\"theme\":0,\"uiOptions\":0,\"uid\":10033,\"versionCode\":1,\"banner\":0,\"icon\":2131558400,\"labelRes\":2131623969,\"logo\":0,\"name\":\"android.app.Application\",\"packageName\":\"kr.co.ishift.smartwaiting_robot_serving\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"kr.co.ishift.smartwaiting_robot_serving\",\"banner\":0,\"icon\":0,\"labelRes\":0,\"logo\":0,\"name\":\"kr.co.ishift.smartwaiting_robot_serving.MainActivity\",\"packageName\":\"kr.co.ishift.smartwaiting_robot_serving\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":false,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":false,\"targetUserId\":-2}},{\"isChecked\":true,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":1187,\"documentLaunchMode\":0,\"flags\":3146240,\"launchMode\":2,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":2,\"rotationAnimation\":-1,\"screenOrientation\":-1,\"softInputMode\":17,\"taskAffinity\":\"com.teamviewer.quicksupport.market.zmpgyjei\",\"theme\":2131951921,\"uiOptions\":0,\"applicationInfo\":{\"appComponentFactory\":\"androidx.core.app.CoreComponentFactory\",\"category\":-1,\"className\":\"com.teamviewer.quicksupport.QSApplication\",\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":32,\"compileSdkVersionCodename\":\"12\",\"credentialProtectedDataDir\":\"/data/user/0/com.teamviewer.quicksupport.market\",\"dataDir\":\"/data/user/0/com.teamviewer.quicksupport.market\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/com.teamviewer.quicksupport.market\",\"enabled\":true,\"enabledSetting\":0,\"flags\":815283781,\"fullBackupContent\":0,\"hiddenUntilInstalled\":false,\"installLocation\":0,\"largestWidthLimitDp\":0,\"longVersionCode\":1533137,\"mHiddenApiPolicy\":-1,\"maxAspectRatio\":0.0,\"minSdkVersion\":22,\"nativeLibraryDir\":\"/system/priv-app/TeamViewerQS/lib/arm64\",\"nativeLibraryRootDir\":\"/system/priv-app/TeamViewerQS/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":0,\"overrideDensity\":0,\"overrideRes\":0,\"primaryCpuAbi\":\"arm64-v8a\",\"privateFlags\":1048,\"processName\":\"com.teamviewer.quicksupport.market\",\"publicSourceDir\":\"/system/priv-app/TeamViewerQS/TeamViewerQS.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/system/priv-app/TeamViewerQS\",\"scanSourceDir\":\"/system/priv-app/TeamViewerQS\",\"seInfo\":\"default:privapp:targetSdkVersion\\u003d32\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/system/priv-app/TeamViewerQS/TeamViewerQS.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":32,\"taskAffinity\":\"com.teamviewer.quicksupport.market\",\"theme\":2131951919,\"uiOptions\":0,\"uid\":10073,\"versionCode\":1533137,\"banner\":2131230807,\"icon\":2131230845,\"labelRes\":2131886320,\"logo\":0,\"name\":\"com.teamviewer.quicksupport.QSApplication\",\"packageName\":\"com.teamviewer.quicksupport.market\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"com.teamviewer.quicksupport.market\",\"banner\":0,\"icon\":0,\"labelRes\":2131886321,\"logo\":0,\"name\":\"com.teamviewer.quicksupport.ui.QSActivity\",\"packageName\":\"com.teamviewer.quicksupport.market\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":false,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":true,\"targetUserId\":-2}},{\"isChecked\":true,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":3,\"documentLaunchMode\":0,\"flags\":512,\"launchMode\":0,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":1,\"rotationAnimation\":-1,\"screenOrientation\":-1,\"softInputMode\":0,\"taskAffinity\":\"com.ainirobot.knowledge\",\"theme\":2131689890,\"uiOptions\":0,\"applicationInfo\":{\"appComponentFactory\":\"androidx.core.app.CoreComponentFactory\",\"category\":-1,\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":30,\"compileSdkVersionCodename\":\"11\",\"credentialProtectedDataDir\":\"/data/user/0/com.ainirobot.knowledge\",\"dataDir\":\"/data/user/0/com.ainirobot.knowledge\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/com.ainirobot.knowledge\",\"enabled\":true,\"enabledSetting\":0,\"flags\":952680007,\"fullBackupContent\":0,\"hiddenUntilInstalled\":false,\"installLocation\":-1,\"largestWidthLimitDp\":0,\"longVersionCode\":1,\"mHiddenApiPolicy\":-1,\"maxAspectRatio\":0.0,\"minSdkVersion\":20,\"nativeLibraryDir\":\"/system/priv-app/Knowledge/lib/arm64\",\"nativeLibraryRootDir\":\"/system/priv-app/Knowledge/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":0,\"overrideDensity\":0,\"overrideRes\":0,\"privateFlags\":1052680,\"processName\":\"com.ainirobot.knowledge\",\"publicSourceDir\":\"/system/priv-app/Knowledge/Knowledge.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/system/priv-app/Knowledge\",\"scanSourceDir\":\"/system/priv-app/Knowledge\",\"seInfo\":\"platform:privapp:targetSdkVersion\\u003d30\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/system/priv-app/Knowledge/Knowledge.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":30,\"taskAffinity\":\"com.ainirobot.knowledge\",\"theme\":2131689889,\"uiOptions\":0,\"uid\":10072,\"versionCode\":1,\"banner\":0,\"icon\":2131492866,\"labelRes\":2131623963,\"logo\":0,\"packageName\":\"com.ainirobot.knowledge\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"com.ainirobot.knowledge\",\"banner\":0,\"icon\":0,\"labelRes\":0,\"logo\":0,\"name\":\"com.ainirobot.knowledge.MainActivity\",\"packageName\":\"com.ainirobot.knowledge\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":false,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":true,\"targetUserId\":-2}},{\"isChecked\":true,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":3,\"documentLaunchMode\":0,\"flags\":512,\"launchMode\":1,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":1,\"rotationAnimation\":-1,\"screenOrientation\":-1,\"softInputMode\":48,\"taskAffinity\":\"com.woowahan.orionstar\",\"theme\":0,\"uiOptions\":0,\"applicationInfo\":{\"appComponentFactory\":\"androidx.core.app.CoreComponentFactory\",\"category\":-1,\"className\":\"com.woowahan.orionstar.presentation.App\",\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":34,\"compileSdkVersionCodename\":\"14\",\"credentialProtectedDataDir\":\"/data/user/0/com.woowahan.orionstar\",\"dataDir\":\"/data/user/0/com.woowahan.orionstar\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/com.woowahan.orionstar\",\"enabled\":true,\"enabledSetting\":0,\"flags\":553172548,\"fullBackupContent\":0,\"hiddenUntilInstalled\":false,\"installLocation\":-1,\"largestWidthLimitDp\":0,\"longVersionCode\":121010000,\"mHiddenApiPolicy\":-1,\"maxAspectRatio\":0.0,\"minSdkVersion\":28,\"nativeLibraryDir\":\"/data/app/com.woowahan.orionstar-Bq2HudQrucMNzO331UhnJw\\u003d\\u003d/lib/arm64\",\"nativeLibraryRootDir\":\"/data/app/com.woowahan.orionstar-Bq2HudQrucMNzO331UhnJw\\u003d\\u003d/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":2132213760,\"overrideDensity\":0,\"overrideRes\":0,\"privateFlags\":4352,\"processName\":\"com.woowahan.orionstar\",\"publicSourceDir\":\"/data/app/com.woowahan.orionstar-Bq2HudQrucMNzO331UhnJw\\u003d\\u003d/base.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/data/app/com.woowahan.orionstar-Bq2HudQrucMNzO331UhnJw\\u003d\\u003d\",\"scanSourceDir\":\"/data/app/com.woowahan.orionstar-Bq2HudQrucMNzO331UhnJw\\u003d\\u003d\",\"seInfo\":\"default:targetSdkVersion\\u003d34\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/data/app/com.woowahan.orionstar-Bq2HudQrucMNzO331UhnJw\\u003d\\u003d/base.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":34,\"taskAffinity\":\"com.woowahan.orionstar\",\"theme\":2132017745,\"uiOptions\":0,\"uid\":10076,\"versionCode\":121010000,\"banner\":0,\"icon\":2131689472,\"labelRes\":2131951673,\"logo\":0,\"name\":\"com.woowahan.orionstar.presentation.App\",\"packageName\":\"com.woowahan.orionstar\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"com.woowahan.orionstar\",\"banner\":0,\"icon\":0,\"labelRes\":0,\"logo\":0,\"name\":\"com.woowahan.orionstar.presentation.activity.MainActivity\",\"packageName\":\"com.woowahan.orionstar\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":false,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":false,\"targetUserId\":-2}},{\"isChecked\":false,\"resolveInfo\":{\"activityInfo\":{\"colorMode\":0,\"configChanges\":1073758135,\"documentLaunchMode\":0,\"flags\":512,\"launchMode\":1,\"lockTaskLaunchMode\":0,\"maxAspectRatio\":0.0,\"maxRecents\":8,\"persistableMode\":0,\"resizeMode\":1,\"rotationAnimation\":-1,\"screenOrientation\":-1,\"softInputMode\":16,\"taskAffinity\":\"kr.co.ishift.smartwaiting_zumju\",\"theme\":2131755171,\"uiOptions\":0,\"applicationInfo\":{\"appComponentFactory\":\"androidx.core.app.CoreComponentFactory\",\"category\":-1,\"className\":\"android.app.Application\",\"compatibleWidthLimitDp\":0,\"compileSdkVersion\":34,\"compileSdkVersionCodename\":\"14\",\"credentialProtectedDataDir\":\"/data/user/0/kr.co.ishift.smartwaiting_zumju\",\"dataDir\":\"/data/user/0/kr.co.ishift.smartwaiting_zumju\",\"descriptionRes\":0,\"deviceProtectedDataDir\":\"/data/user_de/0/kr.co.ishift.smartwaiting_zumju\",\"enabled\":true,\"enabledSetting\":0,\"flags\":816365124,\"fullBackupContent\":0,\"hiddenUntilInstalled\":false,\"installLocation\":-1,\"largestWidthLimitDp\":0,\"longVersionCode\":1,\"mHiddenApiPolicy\":-1,\"maxAspectRatio\":0.0,\"minSdkVersion\":21,\"nativeLibraryDir\":\"/data/app/kr.co.ishift.smartwaiting_zumju-FS3RSa3EDp9gsDwa96UNmw\\u003d\\u003d/lib/arm64\",\"nativeLibraryRootDir\":\"/data/app/kr.co.ishift.smartwaiting_zumju-FS3RSa3EDp9gsDwa96UNmw\\u003d\\u003d/lib\",\"nativeLibraryRootRequiresIsa\":true,\"networkSecurityConfigRes\":0,\"overrideDensity\":0,\"overrideRes\":0,\"primaryCpuAbi\":\"arm64-v8a\",\"privateFlags\":4352,\"processName\":\"kr.co.ishift.smartwaiting_zumju\",\"publicSourceDir\":\"/data/app/kr.co.ishift.smartwaiting_zumju-FS3RSa3EDp9gsDwa96UNmw\\u003d\\u003d/base.apk\",\"requiresSmallestWidthDp\":0,\"scanPublicSourceDir\":\"/data/app/kr.co.ishift.smartwaiting_zumju-FS3RSa3EDp9gsDwa96UNmw\\u003d\\u003d\",\"scanSourceDir\":\"/data/app/kr.co.ishift.smartwaiting_zumju-FS3RSa3EDp9gsDwa96UNmw\\u003d\\u003d\",\"seInfo\":\"default:targetSdkVersion\\u003d34\",\"seInfoUser\":\":complete\",\"sourceDir\":\"/data/app/kr.co.ishift.smartwaiting_zumju-FS3RSa3EDp9gsDwa96UNmw\\u003d\\u003d/base.apk\",\"storageUuid\":\"*************-527a-b3d5-edabb50a7d69\",\"targetSandboxVersion\":1,\"targetSdkVersion\":34,\"taskAffinity\":\"kr.co.ishift.smartwaiting_zumju\",\"theme\":0,\"uiOptions\":0,\"uid\":10077,\"versionCode\":1,\"banner\":0,\"icon\":2131558400,\"labelRes\":2131689505,\"logo\":0,\"name\":\"android.app.Application\",\"packageName\":\"kr.co.ishift.smartwaiting_zumju\",\"showUserIcon\":-10000},\"descriptionRes\":0,\"directBootAware\":false,\"enabled\":true,\"encryptionAware\":false,\"exported\":true,\"processName\":\"kr.co.ishift.smartwaiting_zumju\",\"banner\":0,\"icon\":0,\"labelRes\":0,\"logo\":0,\"metaData\":{\"mClassLoader\":{\"packages\":{\"com.android.org.conscrypt\":{\"implTitle\":\"Unknown\",\"implVendor\":\"Unknown\",\"implVersion\":\"0.0\",\"pkgName\":\"com.android.org.conscrypt\",\"specTitle\":\"Unknown\",\"specVendor\":\"Unknown\",\"specVersion\":\"0.0\"},\"android.security.net.config\":{\"implTitle\":\"Unknown\",\"implVendor\":\"Unknown\",\"implVersion\":\"0.0\",\"pkgName\":\"android.security.net.config\",\"specTitle\":\"Unknown\",\"specVendor\":\"Unknown\",\"specVersion\":\"0.0\"},\"com.android.internal.telephony.dataconnection\":{\"implTitle\":\"Unknown\",\"implVendor\":\"Unknown\",\"implVersion\":\"0.0\",\"pkgName\":\"com.android.internal.telephony.dataconnection\",\"specTitle\":\"Unknown\",\"specVendor\":\"Unknown\",\"specVersion\":\"0.0\"}},\"proxyCache\":{}},\"mFlags\":1536,\"mMap\":{},\"mParcelledByNative\":false,\"mParcelledData\":{\"mNativePtr\":486012755024,\"mNativeSize\":0,\"mOwnsNativeParcelObject\":true,\"mReadWriteHelper\":{}}},\"name\":\"kr.co.ishift.smartwaiting_zumju.MainActivity\",\"packageName\":\"kr.co.ishift.smartwaiting_zumju\",\"showUserIcon\":-10000},\"handleAllWebDataURI\":false,\"icon\":0,\"iconResourceId\":0,\"instantAppAvailable\":false,\"isDefault\":false,\"isInstantAppAvailable\":false,\"labelRes\":0,\"match\":1081344,\"noResourceId\":false,\"preferredOrder\":0,\"priority\":0,\"specificIndex\":-1,\"system\":false,\"targetUserId\":-2}}]";
        try {
            List<ShowAppBean> mAppLists = new Gson().fromJson(showAppJson, new TypeToken<List<ShowAppBean>>() {
            }.getType());
            Log.d(TAG, "test: 原始字符串，解析成功：" + mAppLists.size());
        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, "test: 原始字符串，解析失败：" + e.getMessage());
        }

        String cleanedJson = transformAppSettingInfo(showAppJson);
        try {
            List<ShowAppBean> mAppLists = new Gson().fromJson(cleanedJson, new TypeToken<List<ShowAppBean>>() {
            }.getType());
            Log.d(TAG, "test: 清洗后字符串，解析成功：" + mAppLists.size());
        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, "test: 清洗后字符串，解析失败：" + e.getMessage());
        }

    }

}
