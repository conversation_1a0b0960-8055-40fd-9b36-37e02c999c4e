package com.ainirobot.home.ota.utils;

import android.os.Environment;
import android.os.StatFs;
import android.text.TextUtils;
import android.util.Log;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayDeque;
import java.util.Map;
import java.util.Properties;

/**
 * 文件操作工具类
 *
 */
public final class FileUtils {

    private static final String TAG = FileUtils.class.getSimpleName();
    private final static String MAP_DIR = "/robot/map";

    private static final String DEVICE_PROPERTIES = "device.properties";
    private static final String DEVICE_CONFIG_DIR = "/persist/orionoem/";

    public final static String ROBOT_MAP_DIR = Environment.getExternalStorageDirectory() + MAP_DIR;

    private static final String LXC_VERSION_FILE_PATH = "/data/lxc/rootfs/etc/VERSION";

    public static final String LXC_UNZIP_VERSION_FILE_PATH = "/data/lxc_ota/etc/VERSION";


    /**
     * 从SD卡中读取文件内容
     *
     * @param filePath 文件路径
     * @return
     */
    public static String getFileContent(String filePath) {
        File file = new File(filePath);
        String str = "";
        StringBuilder content = new StringBuilder();
        try {
            InputStream is = new FileInputStream(file);
            InputStreamReader input = new InputStreamReader(is, StandardCharsets.UTF_8);
            BufferedReader reader = new BufferedReader(input);
            while ((str = reader.readLine()) != null) {
                content.append(str);
            }
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } finally {
            return content.toString();
        }
    }

    public static Long getFileSize(String filePath) {
        if (!TextUtils.isEmpty(filePath)) {
            File file = new File(filePath);
            return file.length();
        }
        return 0L;
    }

    public static boolean checkFileExist(String fileName) {
        if (!TextUtils.isEmpty(fileName)) {
            File file = new File(fileName);
            return file.exists();
        }
        return false;
    }

    public static boolean checkDirExist(String dirName) {
        if (!TextUtils.isEmpty(dirName)) {
            File file = new File(dirName);
            return file.exists() && file.isDirectory();
        }
        return false;
    }

    public static void deleteFile(String fileName) {
        File file = new File(fileName);
        if (file.isFile()) {
            file.delete();
        } else {
            ArrayDeque<File> files = new ArrayDeque<>();
            files.add(file);
            do {
                file = files.poll();
                if (file.isFile()) {
                    file.delete();
                } else {
                    File[] fileList = file.listFiles();
                    if (fileList == null || fileList.length < 1) {
                        file.delete();
                    } else {
                        files.addFirst(file);
                        for (File item : fileList) {
                            if (item.isFile()) {
                                item.delete();
                            } else {
                                files.addFirst(item);
                            }
                        }
                    }
                }
            } while (!files.isEmpty());
        }
    }


    public static boolean rename(String name, String desName) {
        try {
            Log.d(TAG, "rename : " + name + " -> " + desName);
            File file = new File(name);
            File desFile = new File(desName);
            boolean ret = file.renameTo(desFile);
            if (!ret) {
                Runtime.getRuntime().exec("mv " + name + " " + desName).wait(500);
                File dst = new File(desName);
                if (dst.exists()) {
                    ret = true;
                }
            }
            return ret;
        } catch (Exception e) {
            try {
                Runtime.getRuntime().exec("mv " + name + " " + desName).wait(500);
            } catch (Exception ex) {
                Log.d(TAG, " Exception: mv : " + e.toString());
            }

        }

        return true;
    }

    public static String getFileSuffix(String fileName) {
        int index = fileName.lastIndexOf('.');
        String suffix = null;
        if (index > 0) {
            suffix = fileName.substring(index);
        }
        return suffix;
    }

    public static boolean find(String dirName, String fileName, boolean nest) {
        boolean found = false;
        if (!TextUtils.isEmpty(dirName) && !TextUtils.isEmpty(fileName)) {
            File file = new File(dirName);
            if (file.isFile()) {
                return false;
            } else {
                boolean addRoot = true;
                ArrayDeque<File> files = new ArrayDeque<>();
                files.add(file);
                do {
                    file = files.poll();
                    if (file.getName().equals(fileName)) {
                        found = true;
                        break;
                    } else if (nest && file.isDirectory() || addRoot) {
                        addRoot = false;
                        File[] fileList = file.listFiles();
                        if (fileList != null && fileList.length > 0) {
                            for (File item : fileList) {
                                if (item.getName().equals(fileName)) {
                                    return true;
                                } else if (item.isDirectory()) {
                                    files.addFirst(item);
                                }
                            }
                        }
                    }
                } while (!files.isEmpty());
            }
        }
        return found;
    }

    /**
     * create directory
     */
    public static void createDirectory(final String directory) {
        File file = new File(directory);
        if (!file.exists()) {
            file.setReadable(true, false);
            file.setWritable(true, false);
            file.mkdirs();
        }
    }

    /**
     * create file given by full file Name, if exist it will delete and create
     */
    public static boolean createFile(final String fullFileName) {
        boolean result = false;
        File file = new File(fullFileName);
        createDirectory(file.getParent());

        if (file.exists()) {
            file.delete();
        }
        try {
            file.setReadable(true, false);
            file.setWritable(true, false);
            result = file.createNewFile();
            Runtime.getRuntime().exec("chmod 777 " + fullFileName);
        } catch (IOException e) {
            result = false;
        }
        return result;
    }

    public static boolean saveResponseToFile(InputStream responseInputStream, String fileNameFinal) {
        InputStream fis = null;
        BufferedInputStream bis = null;
        OutputStream fos = null;
        BufferedOutputStream bos = null;

        try {
            fis = responseInputStream;
            bis = new BufferedInputStream(fis);

            fos = new FileOutputStream(fileNameFinal);
            bos = new BufferedOutputStream(fos);

            byte[] buffer = new byte[4096];
            int len;

            while ((len = bis.read(buffer, 0, 4096)) != -1) {
                bos.write(buffer, 0, len);
            }

            bos.flush();

        } catch (IOException e) {
            e.printStackTrace();
            return false;
        } finally {
            IOUtils.close(fis);
            IOUtils.close(bis);
            IOUtils.close(fos);
            IOUtils.close(bos);
        }
        return true;
    }

    public static File createFile(String parentDir, String finalFileName) {
        File dir = new File(parentDir);
        if (!dir.exists()) {
            boolean made = dir.mkdirs();
            if (!made) {
                return null;
            }
        }
        File file = new File(dir, finalFileName);
        Log.d(TAG, "buildFile file absPath : " + file.getAbsolutePath());
        if (!file.exists()) {
            try {
                boolean created = file.createNewFile();
                if (!created) {
                    return null;
                }
            } catch (Exception e) {
                e.printStackTrace();
                Log.e(TAG, "createNewFile error : Invalid file path ");
                return null;
            }
        }
        return file;
    }

    /**
     * 获得sd卡剩余容量，即可用大小
     */
    public static Long getSDAvailableSize() {
        File path = Environment.getExternalStorageDirectory();
        StatFs stat = new StatFs(path.getPath());
        long blockSize = stat.getBlockSizeLong();
        long availableBlocks = stat.getAvailableBlocksLong();
        //return Formatter.formatFileSize(MainActivity.this, blockSize * availableBlocks);
        Log.i("GETSDSize", String.valueOf(blockSize * availableBlocks));
        return blockSize * availableBlocks;
    }

    public static boolean writeStringToFile(String response, String filePath) {
        PrintWriter pw = null;
        try {
            pw = new PrintWriter(new FileWriter(filePath));
            pw.println(response);
            pw.close();
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            return false;
        } finally {
            IOUtils.close(pw);
        }
        return true;
    }

    public static Properties getDeviceProperties() {
        File file = new File(DEVICE_CONFIG_DIR, DEVICE_PROPERTIES);
        if (!file.exists()){
            Log.d(TAG, "getDeviceProperties file is not exists.");
            return null;
        }
        Properties properties = new Properties();
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
            properties.load(fis);
        } catch (IOException e) {
            e.printStackTrace();
            return properties;
        } finally {
            IOUtils.close(fis);
        }
        Log.d(TAG, "getDeviceProperties: start --->>>");
        printProperty(properties);
        Log.d(TAG, "getDeviceProperties: end <<<---");
        return properties;
    }

    private static void printProperty(Properties properties) {
        if (properties == null) {
            Log.d(TAG, "printProperty properties is null");
            return;
        }
        for (Map.Entry<Object, Object> objectObjectEntry : properties.entrySet()) {
            Object key = objectObjectEntry.getKey();
            Object value = objectObjectEntry.getValue();
            Log.d(TAG, "key=" + key + " value=" + value);
        }
    }

    /**
     * 获取底盘LXC版本号
     * @return
     */
    public static String getLXCVersion(){

         String version = getFileContent(LXC_VERSION_FILE_PATH);
         Log.d(TAG,"getLXCVersion: " + version);
         return version;
    }

    public static String getUnZipLXCVersion(){

        String version = getFileContent(LXC_UNZIP_VERSION_FILE_PATH);
        Log.d(TAG,"getUnZipLXCVersion: " + version);
        return version;
    }

    /**
     * 解压缩后的LXC升级版本文件是否存在
     * @return
     */
    public static boolean isUnZipLxcVersionFileExist(){
        return checkFileExist(LXC_UNZIP_VERSION_FILE_PATH);
    }

    /**
     * 通过文件URL获取文件名
     * @param url
     * @return
     */
    public static String getFileNameFromUrl(String url){
        String fileName = "";
        try {
            URL fileUrl = new URL(url);
            fileName = fileUrl.getFile();
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }

        // 提取文件名
        int lastSlashIndex = fileName.lastIndexOf('/');
        if (lastSlashIndex != -1 && lastSlashIndex < fileName.length() - 1) {
            fileName = fileName.substring(lastSlashIndex + 1);
        }

        return fileName;
    }

    //删除目录文件
    public static void deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }

}
