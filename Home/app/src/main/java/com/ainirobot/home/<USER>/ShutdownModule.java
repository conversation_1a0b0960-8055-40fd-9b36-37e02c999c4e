package com.ainirobot.home.module;

import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.bi.BiShutdownReport;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.UIController.FRAGMENT_TYPE;
import com.ainirobot.home.ui.UIController.MESSAGE_TYPE;
import com.ainirobot.home.utils.MapUtils;

import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

public class ShutdownModule extends BaseModule {

    private static final String TAG = ShutdownModule.class.getSimpleName();
    private ReadWriteLock lock = new ReentrantReadWriteLock();
    private int mType = -1;

    private static ShutdownModule mInstance;
    private BiShutdownReport mBiReporter;

    private volatile boolean isRunning = false;

    /**
     * 语音播报间隔20s
     */
    private final int SPEECH_INTERVAL = 20;

    /**
     * 超时时间
     */
    private final long TIMEOUT = 60 * 1000;
    private CountDownTimer mCountDownTimer;

    /**
     * 重试次数
     */
    private int retryCount = 0;
    private final int MAX_RETRY_COUNT = 5;


    public ShutdownModule() {
        mBiReporter = new BiShutdownReport();
    }

    public static ShutdownModule getInstance() {
        if (mInstance == null) {
            mInstance = new ShutdownModule();
        }
        return mInstance;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.i(TAG, "on new request, intent: " + intent);
        switch (intent) {
            case Definition.REQ_SYSTEM_SHUTDOWN_TIMER:
                if(ProductInfo.isMiniProduct() || ProductInfo.isMeissa2()){
                    shutdown();
                    return true;
                } else if (ProductInfo.isDeliveryProduct() && !isShutdownRunning()) {
                    updateRunning(true);
                    deliveryShutDown(reqId);
                    return true;
                }

                if (isShutdownRunning()) {
                    Log.i(TAG, "Current already is shutdown");
                    return true;
                }

                updateRunning(true);
                Pose pose = MapUtils.getTarget();
                if (pose != null) {
//                    startNavigation(reqId, pose);
                    checkAllowNavigation(reqId, pose);
                } else {
                    showFragment(null);
                    startCountDownTimer();
                }
                return true;

            default:
                return super.onNewSemantics(reqId, intent, text, params);
        }
    }

    /**
     * 招财豹定时关机处理
     */
    private void deliveryShutDown(int reqId){

        Pose pose = MapUtils.getDeliveryTarget();
        Log.d(TAG,"deliveryShutDown pose: " + pose);
        if (pose != null) {
            checkAllowNavigation(reqId, pose);
        } else {
            showFragment(null);
            startCountDownTimer();
        }
    }

    @Override
    public void onMessageFromLocal(int type) {
        switch (type) {
            case ModuleDef.LOCAL_MESSAGE_SHUTDOWN_CANCEL:
                Log.d(TAG, "Shutdown canceled");
                mBiReporter.addType(BiShutdownReport.TYPE_SHUTDOWN_CANCEL).report();
                this.stop();
                break;

            case ModuleDef.LOCAL_MESSAGE_SHUTDOWN_NOW:
                Log.d(TAG, "Shutdown confirm");
                mBiReporter.addType(BiShutdownReport.TYPE_SHUTDOWN_NOW).report();
                SkillManager.getInstance().openSpeechAsrRecognize();
                shutdown();
                break;

            default:
                break;
        }
    }

    private void showFragment(String poseName) {
        Bundle bundle = new Bundle();
        if (!TextUtils.isEmpty(poseName)) {
            bundle.putString("locationName", poseName);
        }
        bundle.putInt("countDown", (int) (TIMEOUT / 1000));
        UIController.getInstance().showFragment(FRAGMENT_TYPE.FRAGMENT_SHUTDOWN_NAVI, bundle, null);
    }

    private void shutdown() {
        Log.d(TAG, "Start shutdown");
        SystemApi.getInstance().shutdown(Definition.DEBUG_REQ_ID, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                ShutdownModule.this.stop();
                if (result == Definition.RESULT_OK) {
                    Log.d(TAG, "Shutdown succeed : " + result + "    " + message);
                } else {
                    //TODO: 关机失败
                    Log.d(TAG, "Shutdown failed : " + result + "    " + message);
                }
            }
        });
    }



    /**
     * 检查是否允许导航
     * 如果在充电或急停中，不再执行导航，直接开始倒计时
     *
     * @return
     */
    private void checkAllowNavigation(final int reqId, final Pose pose) {
        //当前正在充电，直接显示定时关机倒计时
        if (SystemApi.getInstance().getChargeStatus()) {
            Log.d(TAG, "Shutdown current is charging");
            showFragment(null);
            startCountDownTimer();
            return;
        }

        SystemApi.getInstance().getEmergencyStatus(reqId, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                if (ModuleDef.EMERGENCY_PRESS.equals(message)) {
                    Log.d(TAG, "Shutdown current is emergency pressed");
                    showFragment(null);
                    startCountDownTimer();
                } else {
                    startNavigation(reqId, pose);
                }
            }
        });
    }

    /**
     * 重试导航回xx点
     */
    private void retryNavigation(final int reqId,final Pose pose) {
        if (retryCount >= MAX_RETRY_COUNT) {
            onNavigationFinished(false);
            return;
        }
        retryCount++;
        SystemApi.getInstance().stopNavigation(reqId);  //尝试停止导航
        DelayTask.submit(TAG, new Runnable() {
            @Override
            public void run() {
                if (isShutdownRunning()) {
                    Log.i(TAG, "retry shutdown navigation");
                    startNavigation(reqId, pose);
                }
            }
        }, 2000);
    }

    private void updateRunning(boolean isRunning){
        lock.writeLock().lock();
        try {
            this.isRunning = isRunning;
            Log.d(TAG, "updateRunning: " + this.isRunning);
        } finally {
            lock.writeLock().unlock();
        }
    }

    private boolean isShutdownRunning() {
        lock.readLock().lock();
        try {
            return isRunning;
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 开始导航回定位点
     *
     * @param pose
     */
    private void startNavigation(final int reqId,final Pose pose) {
        Log.d(TAG, "Shutdown navigation target pose : " + pose.toString());
        showFragment(pose.getName());
        int taskPriority = Definition.NavigationPriority.PRIORITY_SYSTEM_TIMED_SHUT_DOWN.getPriority();
        SystemApi.getInstance().startNavigation(reqId, pose.getName(), 0.5, 30 * 1000,
                SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED,
                SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED,
                false, 0.65, 5,
                taskPriority, new ActionListener() {
                    @Override
                    public void onResult(int result, String responseString) throws RemoteException {
                        Log.d(TAG, "Shutdown navigation result : " + result + "  " + responseString);
                        if (isShutdownRunning() && !isArrived(pose)) {
                            retryNavigation(reqId, pose);
                            return;
                        }
                        if (result != Definition.ACTION_RESPONSE_STOP_SUCCESS) {
                            onNavigationFinished(true);
                        }
                    }

                    @Override
                    public void onError(int errorCode, String errorString) throws RemoteException {
                        if (errorCode == Definition.ERROR_IN_DESTINATION) {
                            Log.d(TAG, "Shutdown navigation result : " + errorString);
                            onNavigationFinished(true);
                        } else {
                            Log.d(TAG, "Shutdown navigation error : " + errorCode + "  " + errorString);
                            onNavigationFinished(false);
                        }
                    }

                    @Override
                    public void onStatusUpdate(int status, String data) throws RemoteException {
                        Log.d(TAG, "Shutdown navigation status : " + status + "  " + data);
                        switch (status) {
                            case Definition.STATUS_NAVI_OUT_MAP:
                            case Definition.STATUS_NAVI_GLOBAL_PATH_FAILED:
                                SystemApi.getInstance().stopNavigation(reqId);
                                onNavigationFinished(false);
                                break;

                            case Definition.STATUS_START_NAVIGATION:
                                String text = ApplicationWrapper.getContext().getString(R.string.shutdown_start_tts);
                                SkillManager.getInstance().speechPlayText(text);
                                break;
                        }
                    }
                });
    }

    /**
     * 判断是否到达关机位置
     */
    private boolean isArrived(Pose targetPose) {
        Pose currentPose = SystemApi.getInstance().getCurrentPose();
        double distance = Math.sqrt(Math.pow((currentPose.getX() - targetPose.getX()), 2)
                + Math.pow((currentPose.getY() - targetPose.getY()), 2));
        boolean inRange = distance <= 0.5d;
        Log.d(TAG, "is arrived " + targetPose.getName() + " ?: " + inRange);
        return inRange;
    }

    /**
     * 导航结束处理
     *
     * @param result
     */
    private void onNavigationFinished(boolean result) {
        DelayTask.cancel(TAG);
        UIController.getInstance().sendMessageToFragment(MESSAGE_TYPE.SHUTDOWN_NAVI_FINISHED, String.valueOf(result));
        startCountDownTimer();
    }

    /**
     * 开启关机倒计时
     */
    private void startCountDownTimer() {
        cancelCountDownTimer();
        mCountDownTimer = new CountDownTimer(TIMEOUT, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                int second = (int) (millisUntilFinished / 1000);
                if (millisUntilFinished % 1000 > 500) {
                    second += 1;
                }
                UIController.getInstance().sendMessageToFragment(MESSAGE_TYPE.SHUTDOWN_COUNTDOWN, String.valueOf(second));
                Log.d(TAG, "Shutdown on tick module : " + millisUntilFinished + "   " + second);
                if (second == 0) {
                    return;
                }
                excuteDiffRobotProxy(second);
            }

            @Override
            public void onFinish() {
                Log.d(TAG, "Shutdown timeout");
                mBiReporter.addType(BiShutdownReport.TYPE_SHUTDOWN_TIMEOUT).report();
                SkillManager.getInstance().openSpeechAsrRecognize();
                shutdown();
            }
        };
        mCountDownTimer.start();
    }

    private void excuteDiffRobotProxy(int second){
        int remainderValue = second % SPEECH_INTERVAL;
        if (remainderValue == 0) {
            String speechText = "";
            if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) {
                if (second == SPEECH_INTERVAL){
                    speechText = ApplicationWrapper.getContext().getString(R.string.shutdown_speech_reminder, second);
                }else {
                    speechText = ApplicationWrapper.getContext().getString(R.string.shutdown_speech_delivery, second);
                }
            }else {
                speechText = ApplicationWrapper.getContext().getString(R.string.shutdown_speech, second);
            }
            Log.d(TAG, "Shutdown play text : " + speechText);
            SkillManager.getInstance().speechPlayText(speechText);
        }
    }

    private void cancelCountDownTimer() {
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
        }
    }

    @Override
    protected void onStop() {
        DelayTask.cancel(TAG);
        SystemApi.getInstance().stopNavigation(Definition.DEBUG_REQ_ID);

        Log.i(TAG, "onstop");
        SystemApi.getInstance().onShutdownTimerCanceled();
        cancelCountDownTimer();
        updateRunning(false);
        super.onStop();
    }


}
