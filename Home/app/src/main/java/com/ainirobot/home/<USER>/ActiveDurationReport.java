/*
 *   Copyright (C) 2017 OrionStar Technology Project
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.ainirobot.home.bi;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;
import com.ainirobot.home.control.ControlManager;

/**
 * 统计小豹每天的运行时长
 *
 * @埋点名称: gb_active_duration
 * @埋点描述: 活跃-运行时长
 * @埋点目的: 统计小豹每天的运行时长
 * @上报时机: 1. 充电时上报 2. 关机时上
 * @params: duration --- 从上次离开充电桩，到这次开始充电之间的时长 单位：秒
 * @params: eq --- 开始充电时的电量
 * @params: ctime --- 埋点产生时的时间戳 int(8)
 * @FileName: com.ainirobot.home.bi.ActiveDurationReport.java
 * @author: Orion
 * @date: 2019-01-04 11:35
 */
public class ActiveDurationReport extends BaseBiReport {
    private static final long INVALID_TIME = -1L;

    private static long chargeOff = INVALID_TIME;
    private static final String START_TIME = "start_time";

    public ActiveDurationReport() {
        super("gb_active_duration");
    }

    public static void setChargeOff() {
        ActiveDurationReport.chargeOff = System.currentTimeMillis();
    }

    // 记录离开充电桩时的时间
    public void chargeOff() {
        chargeOff = System.currentTimeMillis();
    }

    // 开始充电时上报
    public void durationReport() {
        if (INVALID_TIME == chargeOff) {
            return;
        }
        long duration = (System.currentTimeMillis() - chargeOff) / 1000;
        duration((int) duration);
        eq(ControlManager.getControlManager().getSystemStatusManager().getBatteryLevel());
        ctime();
        addStartTime();
        report();
        chargeOff = INVALID_TIME;
    }

    private void addStartTime() {
        addData(START_TIME, chargeOff);
    }

    private void duration(int duration) {
        addData("duration", duration);
    }

    private void eq(int eq) {
        addData("eq", eq);
    }

    private void ctime() {
        addData("ctime", System.currentTimeMillis());
    }
}
