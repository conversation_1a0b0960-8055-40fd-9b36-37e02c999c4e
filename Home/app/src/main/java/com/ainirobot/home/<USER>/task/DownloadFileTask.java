package com.ainirobot.home.ota.task;

import android.os.Handler;
import android.util.Log;

import com.liulishuo.filedownloader.BaseDownloadTask;
import com.liulishuo.filedownloader.FileDownloadSampleListener;
import com.liulishuo.filedownloader.FileDownloader;
import com.liulishuo.filedownloader.model.FileDownloadStatus;

/**
 * 下载降级包task
 */
public class DownloadFileTask extends ThreadTask {
    private String TAG = DownloadFileTask.class.getSimpleName();

    private String downloadUrl;
    private String localSavePath;
    private String mDownloadType;
    private DownloadTaskListener listener;

    private FileDownloadInterface fileDownloadInterface; // 调用第三方下载文件封装接口
    private int downloadId = Integer.MIN_VALUE;
    private byte mFileDownloadStatus = FileDownloadStatus.INVALID_STATUS;
    private Handler mHandler;

    public DownloadFileTask(DownloadTaskListener listener, Handler handler, String desc) {
        this.listener = listener;
        this.fileDownloadInterface = new FileDownloadInterface();
        this.mHandler = handler;
        TAG = desc + TAG;
    }

    public void setUrl(String url) {
        downloadUrl = url;
    }

    public void setLocalPath(String path) {
        localSavePath = path;
    }

    public void setDownloadType(String downloadType) {
        mDownloadType = downloadType;
    }

    public String getDownloadType() {
        return mDownloadType;
    }

    @Override
    public void stop() {
        if (downloadId != Integer.MIN_VALUE && fileDownloadInterface != null
                && mRunningStatus == STATUS.STATUS_RUNNING ) {
            Log.d(TAG, "pause download id=" + downloadId + " " + fileDownloadInterface);
            fileDownloadInterface.pauseFileDownload(downloadId);
        } else {
            Log.e(TAG, "no downloadId or loadInter is null.");
        }

        mRunningStatus = STATUS.STATUS_FINISH;
    }

    public void pause() {
        if (downloadId != Integer.MIN_VALUE && fileDownloadInterface != null
                && mRunningStatus == STATUS.STATUS_RUNNING) {
            Log.d(TAG, "pause download id=" + downloadId + " " + fileDownloadInterface);
            if (mFileDownloadStatus == FileDownloadStatus.progress) {
                fileDownloadInterface.pauseFileDownload(downloadId);
            } else {
                Log.e(TAG, "File download status is " + fileDownloadStatusToString(mFileDownloadStatus) + ", not progress. Skip pause.");
            }
        } else {
            Log.e(TAG, "no downloadId or loadInter is null. Skip pause");
        }

        mRunningStatus = STATUS.STATUS_FINISH;
    }

    @Override
    protected void onRun() {
        Log.d(TAG, "start downloading... url=" + downloadUrl + "\npath=" + localSavePath);

        if (downloadUrl == null || downloadUrl.isEmpty()
                || localSavePath == null || localSavePath.isEmpty()) {
            Log.e(TAG, "url or localPath is null");
            listener.error(null, new Throwable("url or localPath is null"));
            mRunningStatus = STATUS.STATUS_FINISH;
            return;
        }

        downloadId = fileDownloadInterface.startDownloadTask(downloadUrl, localSavePath, new
                FileDownloadSampleListener() {

                    @Override
                    protected void pending(BaseDownloadTask task, int soFarBytes, int totalBytes) {
                        super.pending(task, soFarBytes, totalBytes);
                        mFileDownloadStatus = task.getStatus();
                        Log.i(TAG, "pending mFileDownloadStatus：" + fileDownloadStatusToString(mFileDownloadStatus) + ", osFarBytes: " + String.valueOf(soFarBytes));
                    }

                    @Override
                    protected void progress(BaseDownloadTask task, int soFarBytes, int totalBytes) {
                        super.progress(task, soFarBytes, totalBytes);
                        mFileDownloadStatus = task.getStatus();
                        Log.i(TAG, "progress：" + fileDownloadStatusToString(mFileDownloadStatus) + ", osFarBytes: " + soFarBytes + ", totalBytes: " + totalBytes);
                        listener.onProgress(task, soFarBytes, totalBytes);
                    }

                    @Override
                    protected void error(BaseDownloadTask task, Throwable e) {
                        super.error(task, e);
                        mFileDownloadStatus = task.getStatus();
                        Log.i(TAG, "error mFileDownloadStatus: " + fileDownloadStatusToString(mFileDownloadStatus) + ", error：" + e.getMessage());
                        listener.error(task, e);
                        mRunningStatus = STATUS.STATUS_FINISH;
                    }

                    @Override
                    protected void connected(BaseDownloadTask task, String etag, boolean isContinue, int
                            soFarBytes, int totalBytes) {
                        super.connected(task, etag, isContinue, soFarBytes, totalBytes);
                        mFileDownloadStatus = task.getStatus();
                        Log.i(TAG, "connected mFileDownloadStatus: " + fileDownloadStatusToString(mFileDownloadStatus) + ", soFarBytes：" + String.valueOf(soFarBytes));
                    }

                    @Override
                    protected void paused(BaseDownloadTask task, int soFarBytes, int totalBytes) {
                        super.paused(task, soFarBytes, totalBytes);
                        mFileDownloadStatus = task.getStatus();
                        Log.i(TAG, "paused mFileDownloadStatus: " + fileDownloadStatusToString(mFileDownloadStatus) + ", soFarBytes：" + String.valueOf(soFarBytes));

                        mRunningStatus = STATUS.STATUS_FINISH;
                    }

                    @Override
                    protected void completed(BaseDownloadTask task) {
                        super.completed(task);
                        mFileDownloadStatus = task.getStatus();
                        Log.i(TAG, "completed mFileDownloadStatus: " + fileDownloadStatusToString(mFileDownloadStatus) + ",task：" + task);

                        listener.completed(task);
                        mRunningStatus = STATUS.STATUS_FINISH;
                        // 下载成功发送消息
                    }

                    @Override
                    protected void warn(BaseDownloadTask task) {
                        super.warn(task);
                        mFileDownloadStatus = task.getStatus();
                        Log.i(TAG, "warn mFileDownloadStatus: " + fileDownloadStatusToString(mFileDownloadStatus) + ", task: " + task);
                    }

                    @Override
                    protected boolean isInvalid() {
                        return super.isInvalid();
                    }

                    @Override
                    protected void blockComplete(BaseDownloadTask task) {
                        super.blockComplete(task);
                        mFileDownloadStatus = task.getStatus();
                        Log.i(TAG, "blockComplete mFileDownloadStatus: " + fileDownloadStatusToString(mFileDownloadStatus) + ", task: " + task);
                    }

                    @Override
                    protected void retry(BaseDownloadTask task, Throwable ex, int retryingTimes, int soFarBytes) {
                        super.retry(task, ex, retryingTimes, soFarBytes);
                        mFileDownloadStatus = task.getStatus();
                        Log.i(TAG, "mFileDownloadStatus: " + fileDownloadStatusToString(mFileDownloadStatus) + ", task: " + task);
                    }

                    @Override
                    protected void started(BaseDownloadTask task) {
                        super.started(task);
                        mFileDownloadStatus = task.getStatus();
                        Log.i(TAG, "started mFileDownloadStatus: " + fileDownloadStatusToString(mFileDownloadStatus) + ", task: " + task);
                    }
                });

        Log.d(TAG, "start downloadId=" + downloadId);
    }

    private void clearCurrentDownload() {
        Log.d(TAG, "clear download, download id = " + downloadId + ", localSavePath: " + localSavePath);
        if (downloadId != Integer.MIN_VALUE && localSavePath != null) {
            FileDownloader.getImpl().clear(downloadId, localSavePath);
        } else {
            Log.d(TAG, "clear download: no downloadId or localSavePath is null.");
        }
    }

    private void clearLocalData(){
        mDownloadType = "";
        downloadId = Integer.MIN_VALUE;
        mRunningStatus = STATUS.STATUS_FINISH;
        mFileDownloadStatus = FileDownloadStatus.INVALID_STATUS;
    }

    private String fileDownloadStatusToString(byte status) {
        switch (status) {
            case FileDownloadStatus.pending:
                return "pending";

            case FileDownloadStatus.started:
                return "started";

            case FileDownloadStatus.connected:
                return "connected";

            case FileDownloadStatus.progress:
                return "progress";

            case FileDownloadStatus.blockComplete:
                return "blockComplete";

            case FileDownloadStatus.retry:
                return "retry";

            case FileDownloadStatus.error:
                return "error";

            case FileDownloadStatus.paused:
                return "paused";

            case FileDownloadStatus.completed:
                return "completed";

            case FileDownloadStatus.warn:
                return "warn";

            default:
                return "";
        }
    }

    public synchronized void clearAll() {
        clearCurrentDownload();
        clearLocalData();
    }
}
