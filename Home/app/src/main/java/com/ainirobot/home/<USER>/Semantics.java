/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.home.bean;

public class Semantics {

    public static class DBFiled {
        public static final String TABLE_NAME = "intent";
        public static final String FILED_ID = "_id";
        public static final String FILED_INTENT = "module_intent";
        public static final String FILED_FEATURE = "module_feature";
        public static final String FILED_PATTERN = "module_pattern";
        public static final String FILED_TYPE = "module_pattern_type";
    }

    private int _id;
    private String pattern;
    private String intent;
    private int patternType;
    private int feature;

    public void setSemanticsId(int id) {
        this._id = id;
    }

    public String getPattern() {
        return pattern;
    }

    public void setPattern(String pattern) {
        this.pattern = pattern;
    }

    public String getIntent() {
        return intent;
    }

    public void setIntent(String intent) {
        this.intent = intent;
    }

    public int getPatternType() {
        return patternType;
    }

    public void setPatternType(int patternType) {
        this.patternType = patternType;
    }

    public int getFeature() {
        return feature;
    }

    public void setFeature(int feature) {
        this.feature = feature;
    }

    @Override
    public String toString() {
        return "Semantics pattern:" + pattern + " intent:" + intent + " patternType:" +
                patternType +
                " featureId:" + feature;
    }
}
