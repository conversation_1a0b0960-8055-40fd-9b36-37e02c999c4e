/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.home.module;

import android.content.Context;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.utils.DelayTimer;
import com.ainirobot.home.R;
import com.ainirobot.home.bi.DormancyReport;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.view.DormancyDialog;
import com.ainirobot.home.utils.SystemUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Method;

public class DormancyModule extends BaseModule {

    private static final String TAG = "DormancyModule";
    private static final int DORMANCY_TIMEOUT_TIME = 10000;

    private static DormancyModule sInstance = null;
    private Context mContext;
    private DormancyDialog mDormancyDialog;
    private DelayTimer mTimeoutTimer;
    private DormancyReport mReport;
    private boolean mIsInterrupt = true;

    public static DormancyModule getInstance() {
        if (sInstance == null) {
            sInstance = new DormancyModule();
        }
        return sInstance;
    }

    private DormancyModule() {
        mReport = new DormancyReport();
    }

    public void init(Context context) {
        mContext = context;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.i(TAG, "reqId=" + reqId + "; intent=" + intent + "; params=" + params);
        switch (intent) {
            case Definition.REQ_START_DORMANCY:
                try {
                    JSONObject jsonObject = new JSONObject(params);
                    int dormancyTime = jsonObject.getInt(Definition.JSON_DORMANCY_TIME);
                    mReport.setDormancyName(
                            jsonObject.optString(Definition.JSON_DORMANCY_NAME, ""));
                    showDormancyDialog(dormancyTime);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                break;
            default:
                break;
        }
        SystemApi.getInstance().finishModuleParser(reqId, true);
        return false;
    }

    private void showDormancyDialog(final int dormancyTime) {
        Log.i(TAG, "show dormancy dialog");
        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.DOEMANCY_START,"dormancy_start");
        SystemUtils.setThreeFinger(false);
        mReport.new DormancyStartReport().report();
        SkillManager.getInstance().speechPlayText(mContext.getString(R.string.dormancy_play_tts));
        this.recordShutDownSleep();
        mDormancyDialog = new DormancyDialog(mContext, new DormancyDialog.DialogEvent() {
            @Override
            public void onConfirm() {
                Log.i(TAG, "dormancy dialog confirm");
                SystemApi.getInstance().dormancyStart(0, dormancyTime);
                mTimeoutTimer = new DelayTimer(DORMANCY_TIMEOUT_TIME, new Runnable() {
                    @Override
                    public void run() {
                        Log.i(TAG, "dormancy fail in 10s");
                        mReport.new DormancyErrorReport().addReason("timeout").report();
                        cleanSysShutReason();
                        SkillManager.getInstance().speechWithFinishCallBack(
                                mContext.getString(R.string.dormancy_fail),
                                new SkillManager.SpeechFinishCallBack() {
                                    @Override
                                    public void finish() {
                                        mDormancyDialog.dismiss();
                                        mIsInterrupt = false;
                                        stop();
                                    }
                                });
                    }
                });
                mTimeoutTimer.start();
            }

            @Override
            public void onCancel() {
                Log.i(TAG, "dormancy dialog cancel");
                mReport.new DormancyCancelReport().addReason("click").report();
                cleanSysShutReason();
                mIsInterrupt = false;
                stop();
            }
        });
        mDormancyDialog.show();
    }

    @Override
    protected void onStop() {
        super.onStop();
        Log.i(TAG, "onStop");
        if (mTimeoutTimer != null) {
            mTimeoutTimer.destroy();
            mTimeoutTimer = null;
        }
        if (mIsInterrupt) {
            mReport.new DormancyCancelReport().addReason("OTA").report();
            mDormancyDialog.dismiss();
            mDormancyDialog = null;
        }
        mIsInterrupt = true;
        SkillManager.getInstance().stopTTSOnly();
        SystemApi.getInstance().onDormancyFinished();
        SystemUtils.setThreeFinger(true);
    }

    private void cleanSysShutReason(){
        Log.i(TAG, "clean shutdown reason");
        Method systemPropertiesSet = null;
        try {
            systemPropertiesSet = Class.forName("android.os.SystemProperties").getMethod("set",
                    String.class, String.class);
            systemPropertiesSet.invoke(null, "debug.tp.c_shutdown", "1");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void recordShutDownSleep(){
        Log.i(TAG, "record shutdown reason: sleep");
        try {
            Method systemPropertiesSet = Class.forName("android.os.SystemProperties").getMethod("set",
                    String.class, String.class);
            systemPropertiesSet.invoke(null, "debug.tp.s_shutdown", "1");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
