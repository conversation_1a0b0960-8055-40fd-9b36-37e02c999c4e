package com.ainirobot.home.module;

import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.ui.UIController;

public class LeavePileToPointModule extends BaseModule {
    private static final String TAG = LeavePileToPointModule.class.getSimpleName();

    private static class SingletonHolder {
        private static final LeavePileToPointModule mInstance = new LeavePileToPointModule();
    }

    public static LeavePileToPointModule getInstance() {
        return LeavePileToPointModule.SingletonHolder.mInstance;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.i(TAG, "reqId=" + reqId + "; intent=" + intent + "; params=" + params + " " + getState());
        switch (intent) {
            case Definition.REQ_LEAVE_PILE_GO_POINT:
                startHandleLeavePile();
                break;
        }
        return super.onNewSemantics(reqId, intent, text, params);
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
        Log.i(TAG, "type =" + type);
        switch (type) {
            case ModuleDef.LOCAL_MESSAGE_START_GO_STANDBY_POINT:
                startGoStandByPoint();
                break;
            default:
                break;
        }
    }

    private void showView() {
        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_LEAVE_PILE_TO_POINT, null, null);
    }
    private void startHandleLeavePile() {
        showView();
        float speed = 0.2f;
        float distance = ProductInfo.isSaiphXdOrBigScreen()
                || ProductInfo.isSaiphChargeIr() || ProductInfo.isSaiphPro()
                || ProductInfo.isAlnilamPro() || ProductInfo.isMeissaPlus() ? 0.25f : 0.1f;
        SystemApi.getInstance().leaveChargingPile(0, speed, distance, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                if (result == Definition.RESULT_OK) {
                    UIController.getInstance().sendMessageToFragment(
                            UIController.MESSAGE_TYPE.LEAVE_PILE_SUCCESS, null);
                } else {
                    onFinished();
                }
            }
        });
    }

    private void startGoStandByPoint() {
        SystemApi.getInstance().startNavigationByType(0, Definition.STAND_BY_POINT_TYPE, 0,
                0.3, 0.3, 15000, new ActionListener() {
                    @Override
                    public void onResult(int result, String responseString, String extraData) throws RemoteException {
                        Log.d(TAG, "startGoStandByPoint navigation result : " + result + "  " + responseString);
                        onFinished();
                    }

                    @Override
                    public void onError(int errorCode, String errorString, String extraData) throws RemoteException {
                        Log.d(TAG, "startGoStandByPoint navigation error : " + errorCode + "  " + errorString);
                        onFinished();
                    }

                    @Override
                    public void onStatusUpdate(int status, String data, String extraData) throws RemoteException {
                        Log.d(TAG, "startGoStandByPoint navigation status : " + status + "  " + data);
                        switch (status) {
                            case Definition.STATUS_NAVI_OUT_MAP:
                            case Definition.STATUS_NAVI_GLOBAL_PATH_FAILED:
                                SystemApi.getInstance().stopNavigation(0);
                                break;

                            case Definition.STATUS_START_NAVIGATION:
                                break;
                        }
                    }
                });
    }

    private void onFinished() {
        stop();
    }

    @Override
    protected void onStop() {
        SystemApi.getInstance().onLeavePileToPointFinished();
        super.onStop();
    }
}
