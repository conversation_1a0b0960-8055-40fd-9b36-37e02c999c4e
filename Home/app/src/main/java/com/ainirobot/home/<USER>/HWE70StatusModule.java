package com.ainirobot.home.module;

import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.bi.BiHwAbnormalReport;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.utils.LocationUtil;
import com.ainirobot.home.utils.SystemHookUtils;

import java.io.IOException;
import java.util.Timer;
import java.util.TimerTask;

public class HWE70StatusModule extends BaseModule{
    private static final String TAG = "HWE70StatusModule";
    private static HWE70StatusModule mInstance;
    private Context mContext;
    private int mReqId = 0;
    private HandlerThread mHandlerThread ;
    private Handler mE70Handler;
    private Timer mE70RecoveryTimer;
    private Timer mCmdOrionlxcTimer;
    private int mOrder = 0;
    private final static int E70_RECOVERY_ORDER = 1;
    private final static int ORION_LXC_ORDER = 2;

    private HWE70StatusModule() {
        super();
    }

    public void init(Context context) {
        this.mContext = context;
    }

    public static HWE70StatusModule getInstance() {
        if (null == mInstance) {
            mInstance = new HWE70StatusModule();
        }
        return mInstance;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.i(TAG, "onNewSemantics reqId=" + reqId + " intent=" + intent + "  param=" + params);
        switch (intent){
            case Definition.REQ_HW_E70_RECOVERY:
                mOrder = ORION_LXC_ORDER;
                exeOrder();
                sendHwRecoveryStatus(true);
                UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.HW_E70_RECOVERY, params);
                return true;
            case Definition.REQ_HW_E70:
                mOrder = E70_RECOVERY_ORDER;
                mHandlerThread  = new HandlerThread("exeOrder");
                mHandlerThread .start();
                LocationUtil.getInstance().startRecordPose();
                exeOrder();
                UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_HW_E70, null, null);
                UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.HW_E70_ERROR, params);
                sendHwAbnormalStatus(false);
                return true;
            default:
                SystemApi.getInstance().finishModuleParser(reqId, true);
                Log.w(TAG, "unknown intent=" + intent + " text=" + text + " params=" + params);
                break;
        }
        return false;
    }

    private void exeOrder() {
        Log.d(TAG, " exeOrder:: ");
        mE70Handler = new Handler(mHandlerThread.getLooper()){
            @Override
            public void handleMessage(Message msg) {
                Log.d(TAG, " order: " + mOrder);
                switch (mOrder){
                    case E70_RECOVERY_ORDER:
                        timerE70Recovery();
                        break;
                    case ORION_LXC_ORDER:
                        timerOrionlxc();
                        break;
                    default:
                        Log.d(TAG, " No matching order");
                        break;
                }

            }
        };
        mE70Handler.sendEmptyMessage(1);
    }

    private void timerE70Recovery() {
        mOrder = 0;
        mE70RecoveryTimer = new Timer();
        SystemHookUtils.setSystemProperties("debug.pwrctl.gpio", "0");
        TimerTask timerTask = new TimerTask() {
            @Override
            public void run() {
                Log.d(TAG, " timerE70Recovery:: ");
                //必须写两遍，不要删
                SystemHookUtils.setSystemProperties("debug.pwrctl.gpio", "1");
                SystemHookUtils.setSystemProperties("debug.pwrctl.gpio", "1");
            }
        };
        mE70RecoveryTimer.schedule(timerTask, 3000);
    }

    /**
     * stop orionlxc
     * start orionlxc
     */
    private void timerOrionlxc() {
        mOrder = 0;
//        String cmdStop = "stop orionlxc";
//        final String cmdStart= "start orionlxc";
//        mCmdOrionlxcTimer = new Timer();
//        exeCmdOrionlxc(cmdStop);
//        TimerTask timerTask = new TimerTask() {
//            @Override
//            public void run() {
//                Log.d(TAG, " exeCmdOrionlxc:: ");
//                exeCmdOrionlxc(cmdStart);
//                stop();
//            }
//        };
//        mCmdOrionlxcTimer.schedule(timerTask, 3000);

        SystemHookUtils.setSystemProperties("vendor.sys.orion_lpm", "1");
        mCmdOrionlxcTimer = new Timer();
        TimerTask timerTask = new TimerTask() {
            @Override
            public void run() {
                Log.d(TAG, " setSystemProperties Orionlxc:: ");
                SystemHookUtils.setSystemProperties("vendor.sys.orion_lpm", "0");
                stop();
            }
        };
        mCmdOrionlxcTimer.schedule(timerTask, 3000);
    }

    private void exeCmdOrionlxc(String cmd){
        try {
            Runtime.getRuntime().exec(cmd).waitFor();
        } catch (InterruptedException | IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
        switch (type) {
            case ModuleDef.LOCAL_MESSAGE_HW_STATUS_CANNOT_RECOVERY:
                Intent intent = new Intent(Intent.ACTION_REBOOT);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                mContext.startActivity(intent);
                break;
            default:
                break;
        }

    }

    @Override
    protected void onStop() {
        Log.d(TAG, " onStop::");
        super.onStop();
        SystemApi.getInstance().finishModuleParser(mReqId, true);
        SystemApi.getInstance().onHardwareE70Recovery();;
        if (null != mHandlerThread){
            mHandlerThread.quit();
        }
       if (null != mE70RecoveryTimer){
           mE70RecoveryTimer.cancel();
       }
        if (null != mCmdOrionlxcTimer){
            mCmdOrionlxcTimer.cancel();
        }
    }

    private void sendHwAbnormalStatus(boolean isFirst){
        int firstStatus = isFirst ? BiHwAbnormalReport.HW_VALUE_YES:BiHwAbnormalReport.HW_VALUE_NO;
        sendHwStatusReport(BiHwAbnormalReport.HW_TYPE_CHASSIS_REMOTE_CRASH,
                BiHwAbnormalReport.HW_STATUS_ERROR,
                firstStatus,
                BiHwAbnormalReport.HW_VALUE_DEFAULT);
    }

    private void sendHwRecoveryStatus(boolean isSuc){
        int hwResult = isSuc ? BiHwAbnormalReport.HW_VALUE_YES: BiHwAbnormalReport.HW_VALUE_NO;
        sendHwStatusReport(BiHwAbnormalReport.HW_TYPE_CHASSIS_REMOTE_CRASH,
                BiHwAbnormalReport.HW_STATUS_RECOVERY,
                BiHwAbnormalReport.HW_VALUE_DEFAULT,
                hwResult);
    }

    private void sendHwStatusReport(int hwType, int hwStatus, int firstStatus, int result){
        BiHwAbnormalReport hwReport = new BiHwAbnormalReport();
        hwReport.addHwType(hwType)
                .addStatus(hwStatus)
                .addIsFirst(firstStatus)
                .addResult(result)
                .report();
    }
}
