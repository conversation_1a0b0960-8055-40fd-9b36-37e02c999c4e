/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.home.ui.view;

import static com.ainirobot.home.ui.UIController.MESSAGE_TYPE.REMOTE_STOP_CHARGING_UNKNOWN_FAILED;
import static com.ainirobot.home.utils.ResUtil.getString;

import android.app.AlertDialog;
import android.content.Context;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.home.R;
import com.ainirobot.home.ui.UIController;

public class RemoteStopChargingFailedDialog extends AlertDialog {

    private static final String TAG = "RemoteStopChargingFailedDialog";

    private Context mContext;
    private CountDownTimer mTimer;
    private static final int COUNT_DOWN_TIME = 15;
    private TextView mTextView;
    private TextView mConfirm;
    private DialogEvent mEvent;
    private UIController.MESSAGE_TYPE type = REMOTE_STOP_CHARGING_UNKNOWN_FAILED;

    public RemoteStopChargingFailedDialog(Context context, DialogEvent event) {
        super(context);
        this.mContext = context;
        this.mEvent = event;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        Log.i(TAG, "create");
        super.onCreate(savedInstanceState);
        View view = LayoutInflater.from(mContext).inflate(R.layout.remote_stop_charging_failed_dialog, null);
        setContentView(view);
        setViewStyle();

        mTextView = (TextView) view.findViewById(R.id.text);
        mConfirm = (TextView) view.findViewById(R.id.confirm);

        mConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mEvent != null) {
                    mEvent.onConfirm();
                }
                dismiss();
            }
        });

        mTimer = new CountDownTimer(COUNT_DOWN_TIME * Definition.SECOND, Definition.SECOND) {
            @Override
            public void onTick(long millisUntilFinished) {
                int second = (int) (millisUntilFinished / 1000);
                mConfirm.setText(getString(R.string.ota_confirm, second));
            }

            @Override
            public void onFinish() {
                if (mEvent != null) {
                    mEvent.onConfirm();
                }
                mConfirm.setText(R.string.confirm);
            }
        };
        mTimer.start();
        updateFailedText();
    }

    private void updateFailedText() {
        if (null == mTextView) {
            return;
        }
        switch (type) {
            case REMOTE_STOP_CHARGING_AVOID_FAILED:
                mTextView.setText(getString(R.string.remote_stop_charging_avoid_failed));
                break;
            case REMOTE_STOP_CHARGING_RADAR_FAILED:
                mTextView.setText(getString(R.string.remote_stop_charging_radar_failed));
                break;
            case REMOTE_STOP_CHARGING_UNKNOWN_FAILED:
                mTextView.setText(getString(R.string.remote_stop_charging_unknown_failed));
                break;
        }
    }

    private void setViewStyle() {
        Window window = getWindow();
        if (window == null) {
            return;
        }
        window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
        WindowManager.LayoutParams lp = window.getAttributes();
        lp.width = 940;
        lp.height = 462;
        lp.verticalMargin = 0.1f;
        lp.gravity = Gravity.CENTER;
        window.setAttributes(lp);
        window.setBackgroundDrawableResource(R.color.transparent);
        setCanceledOnTouchOutside(false);
    }

    public void updateFailedType(UIController.MESSAGE_TYPE type) {
        this.type = type;
        updateFailedText();
        if (mTimer != null) {
            mTimer.cancel();
            mTimer.start();
        }
    }

    public interface DialogEvent {
        void onConfirm();
    }

    @Override
    public void dismiss() {
        Log.i(TAG, "dismiss");
        if (mTimer != null) {
            mTimer.cancel();
            mTimer = null;
        }
        super.dismiss();
    }
}
