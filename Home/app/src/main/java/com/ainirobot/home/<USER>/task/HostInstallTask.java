package com.ainirobot.home.ota.task;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.AsyncTask;

import com.ainirobot.home.ota.constants.OtaConstants;
import com.ainirobot.home.ota.utils.Preferences;

public class HostInstallTask extends AsyncTask<Void, Integer, Integer> {

    public final String TAG = OtaConstants.TAG_PREFIX  + HostInstallTask.class.getSimpleName();

    @SuppressLint("StaticFieldLeak")
    public Context mContext;
    public HostProgressCallback mListener;
    Preferences mPrefs;

    public HostInstallTask(Context context) {
        mContext = context ;
        mPrefs = Preferences.getInstance();
    }

    public void setOnProgressListener(HostProgressCallback OtaListener) {
        mListener = OtaListener;
    }

    @Override
    protected Integer doInBackground(Void... voids) {
        return null;
    }

    @Override
    protected void onPreExecute() {
        super.onPreExecute();
    }

    @Override
    protected void onPostExecute(Integer integer) {
        super.onPostExecute(integer);
    }

    @Override
    protected void onProgressUpdate(Integer... values) {
        super.onProgressUpdate(values);
    }

    public interface HostProgressCallback{
        void onProgress(int progress);

        void onVerifyFailed(int errorCode, Object object);

        void onCopyProgress(int progress);

        void onCopyResult(int errorCode, Object object);

        void onInstallError(Exception e);

        void onInstallSuccess();
    }
}