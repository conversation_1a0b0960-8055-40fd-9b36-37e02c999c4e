package com.ainirobot.home.bi;

import android.os.Environment;
import android.os.StatFs;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.util.Log;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * PerformanceAnalysis class be used to
 * time to obtain cupInfo memInfo and sdcard usage
 *
 * @version V1.0.0
 * @date 2019/3/2 16:43
 */
public class PerformanceAnalysis {

    private static final String TAG = "PerformanceAnalysis";
    private static final String THREAD_NAME = "bi_performance_thread";
    private static final String MEM_INFO_PATH = "proc/meminfo";
    private static final String CMD_DUMPSYS_MEM = "dumpsys meminfo";
    private static final String CMD_TOP_CPU = "top -m 5 -d 1800 -s cpu";
    private static final int CORE_THREAD_NUM = 1;
    private static final int SCHEDULE_CORE_THREAD_NUM = 2;
    private static final int MAX_THREAD_NUM = 1;
    private static final long ALIVE_TIMES = 0L;
    private static final int REPORT_PERIOD = 30;
    private static final int RADIX=1024;
    private static PerformanceAnalysis mInstance;
    private BiCpuInfoReport biCpuInfoReport;
    private BiMemInfoReport biMemInfoReport;
    private BiSdcardInfoReport biSdcardInfoReport;
    private AtomicInteger atomicInteger = new AtomicInteger(0);
    private ThreadFactory factory = new ThreadFactory() {
        @Override
        public Thread newThread(@NonNull Runnable r) {
            return new Thread(r, THREAD_NAME + "-" + atomicInteger.incrementAndGet());
        }
    };
    private ThreadPoolExecutor executor = new ThreadPoolExecutor(CORE_THREAD_NUM
            , MAX_THREAD_NUM
            , ALIVE_TIMES
            , TimeUnit.SECONDS
            , new LinkedBlockingQueue<Runnable>(), factory);
    private ScheduledThreadPoolExecutor scheduledExecutorService
            = new ScheduledThreadPoolExecutor(SCHEDULE_CORE_THREAD_NUM, factory);

    private PerformanceAnalysis() {
    }

    public static PerformanceAnalysis getInstance() {
        if (mInstance == null) {
            synchronized (PerformanceAnalysis.class) {
                if (mInstance == null) {
                    mInstance = new PerformanceAnalysis();
                }
            }
        }
        return mInstance;
    }

    /**
     * start thread obtain cpu top five
     */
    public void getCpuTopFive() {
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    Process process = Runtime.getRuntime().exec(CMD_TOP_CPU);
                    InputStream inputStream = process.getInputStream();
                    BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
                    String firstLine;
                    while ((firstLine = reader.readLine()) != null) {
                        if (TextUtils.isEmpty(firstLine)) {
                            continue;
                        }
                        biCpuInfoReport = new BiCpuInfoReport();
                        //obtain cup total proportion
                        getCpuTotal(firstLine);
                        //newline filter useless line
                        for (int i = 1; i < 4; i++) {
                            reader.readLine();
                        }
                        //obtain cpu top five order by proportion
                        for (int i = 1; i < 6; i++) {
                            String text = reader.readLine().trim();
                            String[] result = getCpuPerByOrder(text);
                            addCpuTopFive(i, result);
                        }
                        biCpuInfoReport.report();
                    }
                    process.waitFor();
                } catch (IOException e) {
                    e.printStackTrace();
                } catch (Throwable e) {
                    Log.e(TAG, "getCpuTopFive Exception " + e.getMessage());
                }
            }
        });

    }

    /**
     * start timer thread obtain memInfo
     */
    public void getMemTopFive() {
        scheduledExecutorService.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                BufferedReader reader = null;
                InputStreamReader streamReader = null;
                InputStream inputStream = null;

                biMemInfoReport = new BiMemInfoReport();
                try {
                    int total = getTotalMem() / RADIX;
                    int available = getAvailableMem() / RADIX;
                    int free = getFreeMem() / RADIX;
                    biMemInfoReport.addAvailable(available);
                    biMemInfoReport.addFree(free);
                    biMemInfoReport.addTotal(total);
                    Process process = Runtime.getRuntime().exec(CMD_DUMPSYS_MEM);
                    inputStream = process.getInputStream();
                    streamReader = new InputStreamReader(inputStream);
                    reader = new BufferedReader(streamReader);
                    //newline
                    for (int i = 1; i < 5; i++) {
                        reader.readLine();
                    }
                    //obtain top five mem
                    for (int i = 1; i < 6; i++) {
                        String line = reader.readLine();
                        String[] result = getMemByOrder(line);
                        addMemTopFive(i, result);
                    }
                    biMemInfoReport.report();
                    process.waitFor();
                } catch (IOException e) {
                    e.printStackTrace();
                } catch (Throwable e) {
                    Log.e(TAG, "getMemTopFive Exception" + e.getMessage());
                } finally {
                    closeStream(reader, inputStream, streamReader);
                }
            }
        }, 0, REPORT_PERIOD, TimeUnit.MINUTES);

    }

    /**
     * obtain sdcardInfo
     */
    public void getSdcardUsage() {
        scheduledExecutorService.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                if (!isMountedSdcard()) {
                    Log.w(TAG, "sdcard don't mounted ");
                    return;
                }
                File path = Environment.getExternalStorageDirectory();
                StatFs statfs = new StatFs(path.getPath());
                int availableSize = getAvailableSize(statfs);
                int totalSize = getTotalSize(statfs);
                Log.i(TAG, " sdcard total:" + totalSize + " availableSize:" + availableSize);
                biSdcardInfoReport = new BiSdcardInfoReport();
                biSdcardInfoReport.addAvailable(availableSize);
                biSdcardInfoReport.addTotal(totalSize);
                biSdcardInfoReport.report();

            }
        }, 0, REPORT_PERIOD, TimeUnit.MINUTES);


    }


    /**
     * obtain sdcard available size
     *
     * @param stat StatFs
     * @return sdcard available size
     */
    private int getAvailableSize(StatFs stat) {
        long blockSize = stat.getBlockSizeLong();
        long availableBlocks = stat.getAvailableBlocksLong();
        return (int) (availableBlocks * blockSize / RADIX / RADIX);
    }


    /**
     * obtain sdcard size
     *
     * @param stat StatFs
     * @return sdcard size
     */
    private int getTotalSize(StatFs stat) {
        long blockSize = stat.getBlockSizeLong();
        long totalBlocks = stat.getBlockCountLong();
        return (int) (totalBlocks * blockSize / RADIX / RADIX);
    }


    private boolean isMountedSdcard() {
        return Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED);
    }

    private void addCpuTopFive(int order, String[] result) {
        int cup = Integer.valueOf(result[1]);
        String name = result[0];
        switch (order) {
            case 1:
                biCpuInfoReport.addNum1(cup);
                biCpuInfoReport.addTop1(name);
                break;
            case 2:
                biCpuInfoReport.addNum2(cup);
                biCpuInfoReport.addTop2(name);
                break;
            case 3:
                biCpuInfoReport.addNum3(cup);
                biCpuInfoReport.addTop3(name);
                break;
            case 4:
                biCpuInfoReport.addNum4(cup);
                biCpuInfoReport.addTop4(name);
                break;
            case 5:
                biCpuInfoReport.addNum5(cup);
                biCpuInfoReport.addTop5(name);
                break;
            default:
        }
    }

    private String[] getCpuPerByOrder(String line) {
        Log.i(TAG, "cpu:" + line);
        String[] array = line.split("\\s+");
        String cup = array[4];
        String name = array[array.length - 1];
        if (cup != null) {
            cup = cup.replace("%", "");
        }
        Log.i(TAG, "cpu:" + cup + "%");
        Log.i(TAG, "name:" + name);
        String[] result = new String[2];
        result[0] = name;
        result[1] = cup;
        return result;
    }

    private void getCpuTotal(String totalString) {
        String[] totalArray = totalString.split("\\s+|%|,");
        int user = Integer.valueOf(totalArray[1]);
        int system = Integer.valueOf(totalArray[5]);
        int iow = Integer.valueOf(totalArray[9]);
        int irq = Integer.valueOf(totalArray[13]);
        int total = user + system + iow + irq;
        biCpuInfoReport.addUsage(total);
        Log.i(TAG, "total:" + total + "%");
    }

    private void addMemTopFive(int order, String[] result) {
        int mem = Integer.valueOf(result[1]) / RADIX;
        String name = result[0];
        switch (order) {
            case 1:
                biMemInfoReport.addNum1(mem);
                biMemInfoReport.addTop1(name);
                break;
            case 2:
                biMemInfoReport.addNum2(mem);
                biMemInfoReport.addTop2(name);
                break;
            case 3:
                biMemInfoReport.addNum3(mem);
                biMemInfoReport.addTop3(name);
                break;
            case 4:
                biMemInfoReport.addNum4(mem);
                biMemInfoReport.addTop4(name);
                break;
            case 5:
                biMemInfoReport.addNum5(mem);
                biMemInfoReport.addTop5(name);
                break;
            default:
        }
    }

    private void closeStream(BufferedReader reader
            , InputStream inputStream
            , InputStreamReader streamReader) {
        try {
            if (reader != null) {
                reader.close();
            }
            if (inputStream != null) {
                inputStream.close();
            }
            if (streamReader != null) {
                streamReader.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private String[] getMemByOrder(String line) {
        String[] array = line.split("\\s+");
        String name = array[2];
        String size = array[1];
        if (size != null) {
            size = size.replace("K:", "");
            size = size.replace(",", "");
        }
        Log.i(TAG, "name:" + name);
        Log.i(TAG, "size:" + size);
        String[] result = new String[2];
        result[0] = name;
        result[1] = size;
        return result;
    }


    private int getTotalMem() {
        return readMemInfoFile(1);
    }

    private int getFreeMem() {
        return readMemInfoFile(2);
    }

    private int getAvailableMem() {
        return readMemInfoFile(3);
    }


    private int readMemInfoFile(int lineNum) {
        try {
            FileReader fr = new FileReader(MEM_INFO_PATH);
            BufferedReader br = new BufferedReader(fr);
            for (int i = 1; i < lineNum; i++) {
                br.readLine();
            }
            String text = br.readLine();
            String[] array = text.split("\\s+");
            Log.i(TAG, text);
            return Integer.valueOf(array[1]);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return -1;
    }
}
