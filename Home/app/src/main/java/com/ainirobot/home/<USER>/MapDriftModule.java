package com.ainirobot.home.module;

import android.content.Context;
import android.content.Intent;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.control.RadarManager;
import com.ainirobot.home.control.RadarManager.RadarListener;
import com.ainirobot.home.ui.UIController;

import org.json.JSONException;
import org.json.JSONObject;

public class MapDriftModule extends BaseModule {
    private static final String TAG = "MapDriftModule:Home";

    private static MapDriftModule sInstance = null;

    private int mReqId;
    private Context mContext;

    private StatusListener mPoseEstimateStatusListener;

    public static MapDriftModule getInstance() {
        if (sInstance == null) {
            sInstance = new MapDriftModule();
        }
        return sInstance;
    }

    private MapDriftModule() {
    }

    public void init(Context context) {
        mContext = context;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.d(TAG, "onNewSemantics text = " + text + ", params = " + params + ", intent = " + intent);
        this.mReqId = reqId;
        switch (intent) {
            case "req_map_drift":
                UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_MAP_DRIFT, null, null);
                break;
        }
        return true;
    }

    @Override
    public void onMessageFromLocal(int type) {
        Log.d(TAG, "type: " + type);
        switch (type) {
            case ModuleDef.LOCAL_MESSAGE_MAP_DRIFT_START_DROP_POSITION:
                openRadar();
            default:
                break;
        }
    }

    private void openRadar() {
        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.MAP_DRIFT_STATUS_CHECK_RADAR, "");
        RadarManager.openRadar(new RadarListener() {
            @Override
            public boolean onSucceed() {
                Log.d(TAG, "open_radar_succeed");
                checkIsEstimate();
                return true;
            }
        });
    }

    private void checkIsEstimate() {
        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.MAP_DRIFT_STATUS_CHECK_ESTIMATE, "");
        SystemApi.getInstance().isRobotEstimate(mReqId, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                if (message.equals("true")) {
                    registerEstimateListener();
                    reloadMap();
                } else if (message.equals("false")){
                    startReposition();
                }
            }
        });
    }

    private void reloadMap() {
        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.MAP_DRIFT_STATUS_RELOAD_CURRENT_MAP, "");
        SystemApi.getInstance().loadCurrentMap(mReqId, false, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                if (message.equals(Definition.SUCCEED)) {

                } else {
                    UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.MAP_DRIFT_STATUS_RELOAD_MAP_FAIL, "");
                }
            }
        });
    }

    private void registerEstimateListener(){
        mPoseEstimateStatusListener = new StatusListener(){
            @Override
            public void onStatusUpdate(String type, String data) throws RemoteException {
                try {
                    JSONObject json = new JSONObject(data);
                    boolean isPoseEstimate = json.getBoolean("isPoseEstimate");
                    Log.d(TAG, "Pose estimate : " + isPoseEstimate);

                    if (!isPoseEstimate) {
                        startReposition();
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        };
        SystemApi.getInstance().registerStatusListener(Definition.STATUS_POSE_ESTIMATE, mPoseEstimateStatusListener);
    }



    private void startReposition() {
        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.MAP_DRIFT_STATUS_START_REPOSITION, "");
        Log.d(TAG, "startReposition");
        Intent visionIntent = new Intent();
        visionIntent.setAction("action_reposition");
        visionIntent.putExtra("repositionVision", true);
        mContext.sendBroadcast(visionIntent);
    }

    @Override
    protected void onStop() {
        super.onStop();
        if (mPoseEstimateStatusListener != null) {
            SystemApi.getInstance().unregisterStatusListener(mPoseEstimateStatusListener);
        }
    }
}
