/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *     *
 *     * Licensed under the Apache License, Version 2.0 (the "License");
 *     * you may not use this file except in compliance with the License.
 *     * You may obtain a copy of the License at
 *     *
 *     *      http://www.apache.org/licenses/LICENSE-2.0
 *     *
 *     * Unless required by applicable law or agreed to in writing, software
 *     * distributed under the License is distributed on an "AS IS" BASIS,
 *     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     * See the License for the specific language governing permissions and
 *     * limitations under the License.
 */

package com.ainirobot.home.utils;

import android.content.Context;
import android.content.pm.PackageManager;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

/**
 * Created by Orion on 2018/6/19.
 */
public class LocalUtils {

    private static final String TAG = "LocalUtils:Home";
    private static final String WRITE_SETTINGS = "android.permission.WRITE_SETTINGS";
    private static final String WRITE_SECURE_SETTINGS = "android.permission.WRITE_SECURE_SETTINGS";

    public static void storage2SystemSettings(Context ctx, final String key, final String value) {
        if (TextUtils.isEmpty(key) || null == ctx) {
            Log.e(TAG, "can't put system.settings a empty key or null context!");
            return;
        }

        // 检测是否有 写系统settings权限
        int writePerm = ctx.checkCallingOrSelfPermission(WRITE_SETTINGS);
        int writeSecurePerm = ctx.checkCallingOrSelfPermission(WRITE_SECURE_SETTINGS);
        if (PackageManager.PERMISSION_GRANTED != writePerm || PackageManager.PERMISSION_GRANTED != writeSecurePerm) {
            Log.e(TAG, "This app have no WRITE_SETTINGS or WRITE_SECURE_SETTINGS permission!");
            return;
        }
        // put to system.settings
        if (!Settings.Global.putString(ctx.getContentResolver(), key, value)) {
            Log.w(TAG, "Settings.Global put error! key: " + key + ", value: " + value);
        }
    }
}
