/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.home.utils;

import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Enumeration;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

public class SettingUtils {

    private static final String TAG = SettingUtils.class.getSimpleName();
    private static final String CONFIG_DIR = "/robot/config";
    private static final String MAP_TOOL_CONFIG_FILE = "maptool.properties";

    private static final String STATE = "STATE";
    public static final String TYPE_MAP_UUID = "_map_uuid";

    /**
     * Map Tool Config file, begin
     **/
    public static final String TYPE_MAP_LOCAL_VERSION = "_local_version";//本地地图的version，随着编辑和设点，都会更新,最初在没有的时候，给赋值一个默认值，就是现在

    public synchronized static void setTypeLocalMapVersion(String mapName, String version) {
        saveMapConfig(mapName + TYPE_MAP_LOCAL_VERSION, version);
    }

    private static boolean saveMapConfig(String key, String value) {
        Properties properties = getMapProperties();
        if (properties != null) {
            properties.setProperty(key, value);
            return saveMapProperties(properties);
        }
        return false;
    }

    private static Properties getMapProperties() {
        Properties properties = new Properties();
        File file = getMapConfigFile();
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
            properties.load(fis);
        } catch (Exception e) {
            e.printStackTrace();
            return properties;
        } finally {
            IOUtils.close(fis);
        }
        return properties;
    }

    private static boolean saveMapProperties(Properties properties) {
        File file = getMapConfigFile();
        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(file);
            properties.store(fos, "config");
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        } finally {
            IOUtils.close(fos);
        }
        return true;
    }

    private static File getMapConfigFile() {
        File root = Environment.getExternalStorageDirectory();
        File dir = new File(root, CONFIG_DIR);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        File file = new File(dir, MAP_TOOL_CONFIG_FILE);
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return file;
    }

    public synchronized static void setMapFinishState(String mapName, int state) {
        saveMapConfig(STATE + "_" + mapName, state + "");
    }

    public synchronized static void setMapUuid(String mapName, String uuid) {
        saveMapConfig(mapName + TYPE_MAP_UUID, uuid);
    }

    private static String getSystemLanguage() {
        String language = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_LANGUAGE);
        Log.d(TAG, "robot language" + language);
        if (TextUtils.isEmpty(language)) {
            language = Locale.SIMPLIFIED_CHINESE.toString();
            Log.d(TAG, "default language:" + language);
        }
        return language;
    }

    /**
     * 当前是否为 中文 以外的语言环境
     * @return
     */
    public static boolean isOverSea(){
        String robotLang = getSystemLanguage();
        Log.d(TAG, "isOverSea robotLang : "+robotLang);
        return !TextUtils.isEmpty(robotLang) && !robotLang.contains("zh");
    }

}