package com.ainirobot.home.ota.service;

import android.os.Bundle;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.input.OtaCallback;
import com.ainirobot.home.ota.constants.OtaConstants;

public class OtaCallbackApi extends OtaCallback {
    private static final String TAG = OtaConstants.TAG_PREFIX + OtaCallbackApi.class.getSimpleName();

    private IOtaCallbackInterface manager;

    public OtaCallbackApi(IOtaCallbackInterface manager) {
        this.manager = manager;
    }

    @Override
    public void onCmdResponse(int cmdId, String cmdType, String cmdResponse) throws
            RemoteException {
        Log.d(TAG, "cmdId=" + cmdId + " cmdType=" + cmdType + " cmdResponse=" + cmdResponse);

        if (cmdType == null || cmdType.isEmpty() || cmdResponse == null || cmdResponse.isEmpty()) {
            Log.e(TAG, "bad command.");
            return;
        }

        switch (cmdType) {
            case Definition.CMD_HEAD_START_SCP:
                manager.headScpResult(cmdResponse);
                break;

            case Definition.CMD_HEAD_START_UPDATE:
                manager.headUpdateResult(cmdResponse);
                break;

            case Definition.CMD_HEAD_GET_VERSION:
            case Definition.CMD_HEAD_OTA_GET_VERSION:
                manager.headVersionResult(cmdResponse);
                break;

            case Definition.CMD_HEAD_GET_UPDATE_PARAMS:
                manager.headUpdateParamsResult(cmdResponse);
                break;

            case Definition.CMD_NAVI_START_UPDATE:
                manager.navigationUpdateResult(cmdResponse);
                break;

            case Definition.CMD_NAVI_GET_VERSION:
                manager.navigationVersionResult(cmdResponse);
                break;

            case Definition.CMD_CAN_OTA_START:
                manager.canOtaStartResult(cmdResponse);
                break;

            case Definition.CMD_CAN_OTA_GET_STATE:
                manager.canOtaGetStateResult(cmdResponse);
                break;

            case Definition.CMD_CAN_GET_BOARD_VERSION:
                manager.canGetBoardVersionResult(cmdResponse);
                break;

            case Definition.CMD_CAN_GET_PSB_VERSION:
                manager.canGetPsbVersionResult(cmdResponse);
                break;

            case Definition.CMD_CAN_GET_MOTOR_H_VERSION:
                manager.canGetMotorHVersionResult(cmdResponse);
                break;

            case Definition.CMD_CAN_GET_MOTOR_V_VERSION:
                manager.canGetMotorVVersionResult(cmdResponse);
                break;

            case Definition.CMD_CAN_GET_AUTO_CHARGE_VERSION:
                manager.canGetAcClientVersionResult(cmdResponse);
                break;

            case Definition.CMD_CAN_GET_BATTERY_VERSION:
                manager.canGetBmsVersionResult(cmdResponse);
                break;

            case Definition.CMD_CAN_GET_PSB_S_VERSION:
                manager.canGetPsbSVersionResult(cmdResponse);
                break;

            case Definition.CMD_HEAD_IS_HEADER_CONNECTED:
                manager.updateHeadConnectStatus(cmdResponse);
                break;

            default:
                Log.e(TAG, "onCmdResponse:: unknown command response(cmdType=" + cmdType + ")", new Throwable());
                break;
        }
    }

    @Override
    public boolean onOtaUpgradeStart(boolean needDownload) throws RemoteException {
        return manager.onOtaUpgradeStart(needDownload);
    }

    @Override
    public boolean onOtaRollbackStart() throws RemoteException {
        return manager.onOtaRollbackStart();
    }

    @Override
    public String onOtaGetDescription() throws RemoteException {
        return manager.onOtaGetDescription();
    }

    @Override
    public boolean onOtaCancelDownload() throws RemoteException {
        return manager.onOtaCancelDownload();
    }

    @Override
    public void installPatch(Bundle bundle) throws RemoteException {
        manager.installPatch(bundle);
    }

    @Override
    public void onOtaInterrupted(String reason) throws RemoteException {
        manager.onOtaInterrupted(reason);
    }
}
