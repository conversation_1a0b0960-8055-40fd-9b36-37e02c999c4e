package com.ainirobot.home.bi;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

/**
 * memInfo bi report
 *
 * @version V1.0.0
 * @date 2019/3/4 18:53
 */
public class BiMemInfoReport extends BaseBiReport {
    private static final String TABLE_NAME = "gb_perf_memory";
    private static final String AVAILABLE = "available";
    private static final String FREE = "free";
    private static final String TOTAL = "total";
    private static final String TOP1 = "top1";
    private static final String NUM1 = "num1";
    private static final String TOP2 = "top2";
    private static final String NUM2 = "num2";
    private static final String TOP3 = "top3";
    private static final String NUM3 = "num3";
    private static final String TOP4 = "top4";
    private static final String NUM4 = "num4";
    private static final String TOP5 = "top5";
    private static final String NUM5 = "num5";
    private static final String CTIME = "ctime";

    public BiMemInfoReport() {
        super(TABLE_NAME);
        initData();
    }

    private void initData() {
        addData(AVAILABLE, "");
        addData(FREE, "");
        addData(TOTAL, "");
        addData(TOP1, "");
        addData(NUM1, "");
        addData(TOP2, "");
        addData(NUM2, "");
        addData(TOP3, "");
        addData(NUM3, "");
        addData(TOP4, "");
        addData(NUM4, "");
        addData(TOP5, "");
        addData(NUM5, "");
        addData(CTIME, "");
    }

    public void addAvailable(int available) {
        addData(AVAILABLE, available);
    }

    public void addFree(int free) {
        addData(FREE, free);
    }

    public void addTotal(int total) {
        addData(TOTAL, total);
    }

    public void addTop1(String top1) {
        addData(TOP1, top1);
    }

    public void addNum1(int num1) {
        addData(NUM1, num1);
    }

    public void addTop2(String top2) {
        addData(TOP2, top2);
    }

    public void addNum2(int num2) {
        addData(NUM2, num2);
    }

    public void addTop3(String top3) {
        addData(TOP3, top3);
    }

    public void addNum3(int num3) {
        addData(NUM3, num3);
    }

    public void addTop4(String top4) {
        addData(TOP4, top4);
    }

    public void addNum4(int num4) {
        addData(NUM4, num4);
    }

    public void addTop5(String top5) {
        addData(TOP5, top5);
    }

    public void addNum5(int num5) {
        addData(NUM5, num5);
    }

    private void addCTime() {
        addData(CTIME, System.currentTimeMillis());
    }

    @Override
    public void report() {
        addCTime();
        super.report();
    }
}
