package com.ainirobot.home.ota.bean;

import android.support.annotation.NonNull;

import com.ainirobot.coreservice.client.Definition;

/**
 * Ota降级 消息收发实体类
 */
public class OtaMessage {
    public String result;
    public boolean needReboot;
    public boolean needClearData;
    public String updateType;
    public String message;
    public String extra;

    public OtaMessage(@NonNull String result, @NonNull String message) {
        this.result = result;
        this.needReboot = false;
        this.updateType = Definition.JSON_OTA_TYPE_NORMAL;
        this.message = message;
        this.needClearData = true;
    }

    public OtaMessage(String result, boolean reboot, String updateType,
                      String message, String extra) {
        this.result = result;
        this.needReboot = reboot;
        this.updateType = updateType;
        this.message = message;
        this.extra = extra;
        this.needClearData = true;
    }

    public OtaMessage(String result, boolean reboot, String updateType,
                      String message, String extra, boolean needClearData) {
        this.result = result;
        this.needReboot = reboot;
        this.updateType = updateType;
        this.message = message;
        this.extra = extra;
        this.needClearData = needClearData;
    }
}
