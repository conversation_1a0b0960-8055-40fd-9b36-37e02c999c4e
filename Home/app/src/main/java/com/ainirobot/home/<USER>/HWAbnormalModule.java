package com.ainirobot.home.module;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.bi.BiHwAbnormalReport;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.control.LxcMemoryManager;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.report.Constant;
import com.ainirobot.home.report.TaskReport;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.utils.LocationUtil;
import com.ainirobot.home.utils.SharedPrefUtil;

import org.json.JSONException;
import org.json.JSONObject;


public class HWAbnormalModule extends BaseModule {

    private static final String TAG = "HWAbnormalModule";
    private static HWAbnormalModule mInstance;
    private Context mContext;
    private int mReqId = 0;
    private boolean mIsShowError = false;
    private Pose mPose;

    public enum HWState {
        CONNECTED, DISCONNECTED
    }

    private HWAbnormalModule() {
        super();
    }

    public void init(Context context) {
        this.mContext = context;
    }

    public static HWAbnormalModule getInstance() {
        if (null == mInstance) {
            mInstance = new HWAbnormalModule();
        }
        return mInstance;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.i(TAG, "onNewSemantics reqId=" + reqId + " intent=" + intent + "  param=" + params);

        switch (intent) {
            case ModuleDef.REQ_WAKEUP:
            case Definition.REQ_SPEECH_WAKEUP:
            case ModuleDef.REQ_STOP:
                SystemApi.getInstance().finishModuleParser(reqId, false);
                return false;

            case Definition.REQ_HW_MALFUNCTION:
                mReqId = reqId;
                if (mIsShowError){
                    UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.HW_STATUS_UPDATE, params);
                    sendHwAbnormalStatus(false);
                }else {
                    mIsShowError = true;
                    resetHeadState();
                    closeSpeechAsrRecognize();
                    Bundle bundle = new Bundle();
                    if (!TextUtils.isEmpty(params)){
                        bundle.putString(ModuleDef.HW_ABNORMAL_STATUS, params);
                    }
                    UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_HW_ABNORMAL, bundle, null);
                    sendHwAbnormalStatus(true);
                    //判断lxc进城是否存在
                    LxcMemoryManager.getInstance().checkLxcMem();
                }
                SystemApi.getInstance().finishModuleParser(reqId, true);
                return true;

            case Definition.REQ_HW_RECOVERY:
                sendHwRecoveryStatus(true);
                getNearPose();
                loadCurrentMap();
                if (mIsShowError){
                    UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.HW_STATUS_RECOVERY, params);
                    return true;
                }
                TaskReport.getInstance().setTaskResult(Constant.TaskEvent.task_cancel);
                mReqId = reqId;
                stop();
                return true;
            default:
                SystemApi.getInstance().finishModuleParser(reqId, true);
                Log.w(TAG, "unknown intent=" + intent + " text=" + text + " params=" + params);
                break;
        }
        return false;
    }

    private void getNearPose(){
        String poseInfo = SharedPrefUtil.getInstance().getString(ModuleDef.KEY_SP_NEAR_POSE, "");
        Log.d(TAG, " poseInfo: " + poseInfo);
        if (!TextUtils.isEmpty(poseInfo)){
            JSONObject object = null;
            mPose = new Pose();
            try {
                object = new JSONObject(poseInfo);
                mPose.setX((float) object.optDouble("px"));
                mPose.setY((float) object.optDouble("py"));
                mPose.setTheta((float) object.optDouble("theta"));
            } catch (JSONException e) {
                e.printStackTrace();
            }
            Log.d(TAG, " px: " + object.optDouble("px") + " py: " + object.optDouble("py") +  object.optDouble("theta"));
        }
    }

    /**
     * 强制切地图
     */
    private void loadCurrentMap(){
        SystemApi.getInstance().loadCurrentMap(mReqId, false, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                if (message.equals(Definition.SUCCEED)) {
                    Log.d(TAG," Pose: " + mPose);
                    if (null != mPose){
                        SystemApi.getInstance().setFixedEstimate(mReqId, mGson.toJson(mPose), new CommandListener(){
                            @Override
                            public void onResult(int result, String message, String extraData) {
                                Log.d(TAG," result: " + result + " message: " + message );
                                if (result == Definition.RESULT_OK &&
                                        !TextUtils.isEmpty(message) && message.equals("succeed")) {
                                    Log.d(TAG," 重定位成功" );
                                }else {
                                    Log.d(TAG," 重定位失败重新加载地图");
                                    Intent intent = new Intent();
                                    intent.setAction("action_e70_recovery_reposition");
                                    mContext.sendBroadcast(intent);
                                }
                                stop();
                            }
                        });
                    }else {
                        Log.d(TAG," 重新加载地图");
                        Intent intent = new Intent();
                        intent.setAction("action_e70_recovery_reposition");
                        mContext.sendBroadcast(intent);
                        stop();
                    }
                }else {
                    //TODO:后续修改，Stop非线程安全
                    stop();
                }
            }
        });
    }

    @Override
    protected void onStop() {
        super.onStop();
        mIsShowError = false;
        SystemApi.getInstance().finishModuleParser(mReqId, true);
        recoverSpeechAsrRecognize();
        SystemApi.getInstance().onHardwareRecovery();
        LocationUtil.getInstance().clearData();
        LxcMemoryManager.getInstance().clearCheckLxcTimer();
    }

    private void recoverSpeechAsrRecognize() {
        SkillManager.getInstance().openSpeechAsrRecognize();
    }

    private void closeSpeechAsrRecognize() {
        SkillManager.getInstance().closeSpeechAsrRecognize();
    }

    private void resetHeadState(){
        SystemApi.getInstance().resetHead(mReqId, new CommandListener(){
            @Override
            public void onResult(int result, String message) {
                super.onResult(result, message);
                Log.d(TAG, "resethead result:" + result + " message:" + message);
            }

            @Override
            public void onError(int errorCode, String errorString) throws RemoteException {
                super.onError(errorCode, errorString);
                Log.d(TAG, "resethead errorCode:" + errorCode + " errorString:" + errorString);
            }
        });
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
        switch (type) {
            case ModuleDef.LOCAL_MESSAGE_HW_STATUS_CANNOT_RECOVERY:
                Intent intent = new Intent(Intent.ACTION_REBOOT);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                mContext.startActivity(intent);
                break;
            case ModuleDef.LOCAL_MESSAGE_EXIT_HW_MODULE:
                Log.d(TAG, "exit hw abnormal module:" + mIsShowError);
                if (mIsShowError){
                    TaskReport.getInstance().setTaskResult(Constant.TaskEvent.task_finish);
                    stop();
                }
                break;
            case ModuleDef.LOCAL_MESSAGE_HW_AUTOMATIC_RECOVERY_TIMEOUT:
                sendHwRecoveryStatus(false);
                break;
            default:
                break;
        }
    }

    private void sendHwAbnormalStatus(boolean isFirst){
        int firstStatus = isFirst ? BiHwAbnormalReport.HW_VALUE_YES:BiHwAbnormalReport.HW_VALUE_NO;
        sendHwStatusReport(BiHwAbnormalReport.HW_TYPE_CHASSIS_REMOTE_CRASH,
                BiHwAbnormalReport.HW_STATUS_ERROR,
                firstStatus,
                BiHwAbnormalReport.HW_VALUE_DEFAULT);
    }

    private void sendHwRecoveryStatus(boolean isSuc){
        int hwResult = isSuc ? BiHwAbnormalReport.HW_VALUE_YES: BiHwAbnormalReport.HW_VALUE_NO;
        sendHwStatusReport(BiHwAbnormalReport.HW_TYPE_CHASSIS_REMOTE_CRASH,
                BiHwAbnormalReport.HW_STATUS_RECOVERY,
                BiHwAbnormalReport.HW_VALUE_DEFAULT,
                hwResult);
    }

    private void sendHwStatusReport(int hwType, int hwStatus, int firstStatus, int result){
        BiHwAbnormalReport hwReport = new BiHwAbnormalReport();
        hwReport.addHwType(hwType)
                .addStatus(hwStatus)
                .addIsFirst(firstStatus)
                .addResult(result)
                .report();
    }

}
