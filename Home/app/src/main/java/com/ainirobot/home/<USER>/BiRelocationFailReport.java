package com.ainirobot.home.bi;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;
import com.ainirobot.home.ApplicationWrapper;

public class BiRelocationFailReport extends BaseBiReport {

    private static final String TABLE_NAME = "gb_error";
    private static final String SYSTEM = "system";
    private static final String APPNAME = "appname";
    private static final String TYPE = "type";
    private static final String REQUEST = "request";
    private static final String ACTION_TYPE = "action_type";
    private static final String TRACK_ID = "track_id";
    private static final String STATUS_CODE = "status_code";
    private static final String MSG = "msg";
    private static final String ERROR_ID = "error_id";
    private static final String CTIME = "ctime";
    private static final String SYSTEM_821 = "821";
    public static final String TYPE_FUNCTION = "function ";
    public static final String RELOCATION_FAILURE = "relocation_failure";

    public static final String MSG_RELOCATION_SUCCESS = "relocation_success";
    public static final String MSG_RELOCATION_FAIL = "relocation_fail";

    public static final String STATUS_CODE_LOST_OUT_MAP = "8_lost_outmap";
    public static final String STATUS_CODE_LOST = "8_lost";

    public static final int STATUS_CODE_RELOCATION_SUCCESS = 0;
    public static final int STATUS_CODE_RELOCATION_FAIL = -1;


    public static volatile String trackId = "";

    public BiRelocationFailReport() {
        super(TABLE_NAME);
        initData();
    }

    private void initData() {
        addSystem("");
        addAppName("");
        addType("");
        addRequest("");
        addActionType("");
        addTrackId();
        addStatusCode("");
        addMsg("");
        addErrorId("");
        addRelocationFailure(1);
    }

    private BiRelocationFailReport addSystem(String system) {
        addData(SYSTEM, system);
        return this;
    }

    private BiRelocationFailReport addAppName(String appName) {
        addData(APPNAME, appName);
        return this;
    }

    public BiRelocationFailReport addType(String type) {
        addData(TYPE, type);
        return this;
    }

    public BiRelocationFailReport addRequest(String request) {
        addData(REQUEST, request);
        return this;
    }

    public BiRelocationFailReport addActionType(String actionType) {
        addData(ACTION_TYPE, actionType);
        return this;
    }

    private BiRelocationFailReport addTrackId() {
        addData(TRACK_ID, trackId);
        return this;
    }

    public BiRelocationFailReport addStatusCode(Object statusCode) {
        addData(STATUS_CODE, statusCode);
        return this;
    }

    public BiRelocationFailReport addMsg(String msg) {
        addData(MSG, msg);
        return this;
    }

    public BiRelocationFailReport addErrorId(String errorId) {
        addData(ERROR_ID, errorId);
        return this;
    }

    public BiRelocationFailReport addRelocationFailure(int isHomeFailure) {
        addData(RELOCATION_FAILURE, isHomeFailure);
        return this;
    }

    @Override
    public void report() {
        addSystem(SYSTEM_821);
        String appName = ApplicationWrapper.getContext().getPackageName();
        appName = appName == null ? "" : appName;
        addAppName(appName);
        addTrackId();
        addData(CTIME, System.currentTimeMillis());
        super.report();
    }
}
