/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.home.ui.view;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Outline;
import android.net.Uri;
import android.provider.Settings;
import android.support.annotation.NonNull;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewOutlineProvider;
import android.view.WindowManager;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.R;


public class OTANetworkDialog extends Dialog {
    private final View mOtaDialogView;
    private DialogEvent mDialogEvent;
    private OTARadioButton mRadioButton;
    private TextView mConfirm;
    private TextView mCancel;
    private View mContentView;
    private TextView mTvNoRemind;
    private final String TAG = "OTANetworkDialog:Home";



    public OTANetworkDialog setDialogEvent(DialogEvent mDialogEvent) {
        this.mDialogEvent = mDialogEvent;
        return this;
    }

    public OTANetworkDialog(@NonNull Context context) {
        super(context,R.style.OTADialog);
        mOtaDialogView = LayoutInflater.from(context).inflate(R.layout.ota_network_dialog,null);
        setContentView(mOtaDialogView);
        //configuration
        WindowManager.LayoutParams p = this.getWindow().getAttributes();
        p.width = 950;
        p.height = WindowManager.LayoutParams.WRAP_CONTENT;
        this.getWindow().setAttributes(p);
        setCanceledOnTouchOutside(false);
        initView();
        initListener();
    }



    private void updateMobileDataState() {
        int mobile = RobotSettingApi.getInstance().getRobotInt(Definition.VERSION_UPGRADE_ON_MOBILE);
        Log.i(TAG, "mobile =" + mobile);
        mRadioButton.setChecked(mobile == 1);
    }

    private void initListener() {
        mConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(mDialogEvent!=null){
                    mDialogEvent.onConfirm(OTANetworkDialog.this);
                }
            }
        });
        mCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mDialogEvent.onCancel(OTANetworkDialog.this);
            }
        });

        mTvNoRemind.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                boolean isChecked = !mRadioButton.isChecked();
                mRadioButton.setChecked(isChecked);
                writeGlobal(isChecked);
            }
        });
        mRadioButton.setOnCheckedChangeListener(new OTARadioButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(boolean isChecked) {
                Log.i(TAG,"isChecked = "+isChecked);
                writeGlobal(isChecked);
            }
        });
    }

    private void writeGlobal(boolean isChecked){
        RobotSettingApi.getInstance().setRobotInt(
                Definition.VERSION_UPGRADE_ON_MOBILE, isChecked ? 1 : 0);
    }



    private void initView() {
        mRadioButton = (OTARadioButton) mOtaDialogView.findViewById(R.id.radio_button);
        mConfirm = (TextView) mOtaDialogView.findViewById(R.id.confirm);
        mCancel = (TextView) mOtaDialogView.findViewById(R.id.cancel);
        mContentView = mOtaDialogView.findViewById(R.id.content);
        mTvNoRemind = (TextView) mOtaDialogView.findViewById(R.id.no_tip);
        updateMobileDataState();
        ViewOutlineProvider viewOutlineProvider = new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                view.setClipToOutline(true);
                outline.setRoundRect(0,0,view.getWidth(),view.getHeight(),30);
            }
        };
        mContentView.setOutlineProvider(viewOutlineProvider);
    }


    public interface DialogEvent{
        void onConfirm(OTANetworkDialog dialog);
        void onCancel(OTANetworkDialog dialog);
    }

    @Override
    public void show() {
        if(!(getContext() instanceof Activity)){
            getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_DIALOG);
            getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
        }
        permission();
        ObjectAnimator.ofFloat(mContentView,"alpha",0,1)
                .setDuration(170)
                .start();
    }

    @Override
    public void dismiss() {
        ObjectAnimator animator = ObjectAnimator
                .ofFloat(mContentView,"alpha",1,0)
                .setDuration(170);
        animator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                OTANetworkDialog.super.dismiss();
            }
        });
        animator.start();
    }


    private void permission(){
        if (!Settings.canDrawOverlays(getContext())) {
            Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:" + getContext().getPackageName()));
            getContext().startActivity(intent);
        } else {
            // Already hold the SYSTEM_ALERT_WINDOW permission,
            Log.e(TAG,"has ACTION_MANAGE_OVERLAY_PERMISSION");
            super.show();
        }
    }
}
