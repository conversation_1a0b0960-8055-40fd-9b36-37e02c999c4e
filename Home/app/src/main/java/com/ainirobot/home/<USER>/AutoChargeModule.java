package com.ainirobot.home.module;

import static com.ainirobot.coreservice.client.hardware.RobotCore.registerStatusListener;
import static com.ainirobot.home.bi.anotation.GoChargingPoint.NAV_INTERCEPT;

import android.content.Context;
import android.os.Bundle;
import android.os.RemoteException;
import android.support.annotation.IntDef;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.bean.MultiFloorInfo;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.BatteryBean;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.listener.IMusicListener;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.bi.BiBackChargingReport;
import com.ainirobot.home.bi.BiGbCharging;
import com.ainirobot.home.bi.BiGoChargingPointReport;
import com.ainirobot.home.bi.anotation.BackCharging;
import com.ainirobot.home.bi.anotation.BiContants;
import com.ainirobot.home.bi.anotation.GoChargingPoint;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.control.GlobalResponseManager;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.report.Constant;
import com.ainirobot.home.report.TaskReport;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.view.OTADialog;
import com.ainirobot.home.utils.LocationUtil;
import com.ainirobot.home.utils.MapUtils;
import com.ainirobot.home.utils.ResUtil;
import com.ainirobot.home.utils.ToastUtil;
import com.ainirobot.home.utils.VoiceContentUtil;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.atomic.AtomicInteger;

public class AutoChargeModule extends BaseModule {

    private static final String TAG = "AutoChargeModule";
    private int mReqId = 0;
    private static final int DEFAULT_ATOMIC = 0;
    private static final int ALL_CHARGE_COUNT = 10;
    private static final long CHARGE_TIMEOUT = 30 * Definition.SECOND;
    public static final String NO_NAVIGATION = "-1"; //不导航

    private AtomicInteger mFailCount = new AtomicInteger(DEFAULT_ATOMIC);
    private State mCurState = State.IDLE;
    private Context mContext;
    private volatile boolean mCharging = false;
    private Timer mDelayTimer;
    private static final float MOVE_LINE_DISTANCE = 0.5f;
    private boolean mResult = false;
    private String mStartMode;
    private OTADialog mOtaDialog;
    private Timer voiceTimer;
    private volatile boolean mHasReachChargePoint = false;
    private volatile boolean isWaitInChargeAreaTTSPlaying = false;
    private MultiFloorInfo mChargeFloorInfo;      //充电楼层信息

    //for Bi
    private BiGbCharging mBiGbCharging = new BiGbCharging();
    private static volatile boolean isReachTargetPoint = false;

    private int mStopMode = StopMode.normal;
    private String mRobotPushListener;

    private AutoChargeModule() {
    }

    private static class SingletonHolder {
        private static final AutoChargeModule mInstance = new AutoChargeModule();
    }

    public static AutoChargeModule getInstance() {
        return AutoChargeModule.SingletonHolder.mInstance;
    }

    @IntDef({StopMode.normal, StopMode.noLocation})
    private @interface StopMode {
        int normal = -1;
        int noLocation = 1;
    }

    public enum State {
        IDLE,
        START_CHARGE_UI,
        START_CHARGE_ACTION,
        START_CHARGE_FAILED,
        START_CHARGE_END,
        START_WECHAT_CHARGE,
    }

    public void init(Context context) {
        mContext = context;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        mCharging = ControlManager.getControlManager().getSystemStatusManager().getIsCharging();

        Log.d(TAG, "onNewSemantics intent= " + intent + ", params = " + params +
                ", status" + mCurState + ", mCharging = " + mCharging + ", reqId = " + reqId);
        if (mCurState != State.IDLE && mCurState != State.START_CHARGE_FAILED) { // 前一次回充失败后,下一次可以继续触发回充.
            SystemApi.getInstance().finishModuleParser(reqId, true);
            return false;
        }
        mReqId = reqId;
        switch (intent) {
            case Definition.REQ_START_CHARGE:
                if (mCharging) {
                    SystemApi.getInstance().onStartChargeFinished(null);
                    SystemApi.getInstance().finishModuleParser(reqId, true);
                    break;
                }
                SystemApi.getInstance().resetSystemStatus();
                SkillManager.getInstance().stopTTSOnly();
                SkillManager.getInstance().openSpeechAsrRecognizeMode();
                try {
                    JSONObject json = new JSONObject(params);
                    mStartMode = json.getString("reason");
                    if (Definition.CHARGE_OTA.equals(mStartMode)) {
                        String otaInfo = json.getString("data");
                        showOtaChargeDialog(otaInfo);
                        return true;
                    }
                    showAutoCharge(mStartMode);
                } catch (JSONException e) {
                    e.printStackTrace();
                    showAutoCharge(Definition.CHARGE_SETTING);
                }
                break;
            case Definition.WECHAT_FIRST_RECHARGING:
                if (mCharging) {
                    SystemApi.getInstance().onStartChargeFinished(null);
                    SystemApi.getInstance().finishModuleParser(reqId, true);
                    break;
                }
                startWeChatAutoCharge();
                break;
            default:
                GlobalResponseManager.getInstance().showGlobalToastResponse();
                Log.d(TAG, "NO intent match");
                break;
        }
        return true;
    }

    private void weChatChargeEstimate() {
        checkEstimate(true);
    }

    /**
     * query robot estimate is success , or can't set pose or navigate.
     */
    private void checkEstimate(final boolean isWechat) {
        Log.d(TAG, "checkEstimate start ------- mCurState : " + mCurState);
        SystemApi.getInstance().isRobotEstimate(0, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                Log.d(TAG, "isRobotEstimate result : " + result + ", msg :" + message + ", extraData：" + extraData);
                switch (result) {
                    case Definition.RESULT_OK:
                        if ("true".equals(message)) {
                            estimateSuccess(isWechat);
                        } else {
                            estimateFailed(isWechat);
                        }
                        break;
                    case Definition.RESULT_FAILURE:
                    case Definition.RESULT_STOP:
                        estimateFailed(isWechat);
                        break;
                }
            }
        });
    }

    private void estimateSuccess(boolean isWechat) {
        if (isEnableMultiNavigation()) {
            getMultiFloorInfo(isWechat);
        } else {
            startChargeAction(isWechat);
        }
    }

    /**
     * 是否开启了电梯功能
     */
    private boolean isEnableMultiNavigation() {
        return RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_ELEVATOR_CONTROL_ENABLED)
                == Definition.ROBOT_SETTING_ENABLE;
    }

    private void getMultiFloorInfo(boolean isWechat) {
        SystemApi.getInstance().queryMultiFloorConfig(mReqId, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                Log.d(TAG, "queryMultiFloorConfig onResult msg : " + message + ", extraData : " + extraData);
                if (Definition.RESULT_OK == result && !TextUtils.isEmpty(message)) {
                    Type type = new TypeToken<ArrayList<MultiFloorInfo>>(){}.getType();
                    ArrayList<MultiFloorInfo> mapInfo = mGson.fromJson(message, type);

                    for (int i = 0; i < mapInfo.size(); i++) {
                        MultiFloorInfo info = mapInfo.get(i);
                        if (info.getFloorState() == MultiFloorInfo.MultiFloorType.MAIN_FLOOR) {
                            mChargeFloorInfo = info;
                            break;
                        }
                    }
                    
                    if (mChargeFloorInfo != null) {
                        getCurrentMapName(isWechat);    
                    } else {
                        ToastUtil.showToast(mContext, mContext.getString(R.string.please_set_main_floor));
                    }

                } else {
                    finishAutoCharge(0);
                }
            }
        });
    }

    private void getCurrentMapName(boolean isWechat) {
        SystemApi.getInstance().getMapName(mReqId, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                Log.d(TAG, "getMapName onResult message : " + message + ", extraData : " + extraData);
                if (!TextUtils.isEmpty(message)){
                    if (TextUtils.equals(message, mChargeFloorInfo.getMapName()) && !robotIsInElevator()) {
                        startChargeAction(isWechat);
                    } else {
                        startElevatorNavigation(isWechat);
                    }
                }
            }
        });
    }

    private boolean robotIsInElevator() {
        String alreadyInElevator = SystemApi.getInstance().isAlreadyInElevator();
        Log.d(TAG, "checkIsInElevator: " + alreadyInElevator);
        Map<String, Object> map = mGson.fromJson(alreadyInElevator, Map.class);
        Double resultDouble = (Double) map.get(Definition.JSON_TASK_EXEC_RESULT);
        int result = resultDouble.intValue();
        return result == Definition.RESULT_ROBOT_IN_ELEVATOR;
    }

    private void startElevatorNavigation(boolean isWechat) {
        int targetFloor = mChargeFloorInfo.getFloorIndex();
        //不在充电楼层，先跨层导航到充电楼层的"电梯口"，再回充
        String multiDestination = mChargeFloorInfo.getAvailableElevators().get(0) + '-' +Definition.ELEVATOR_ENTER_POSE;

        SystemApi.getInstance().startElevatorNavigation(mReqId,
                multiDestination, targetFloor, true,
                new ActionListener() {
                    @Override
                    public void onResult(int status, String message, String extraData) throws RemoteException {
                        Log.e(TAG, "onResult: " + status + " responseString：" + message + " extraData：" + extraData);
                        if (status == Definition.RESULT_OK) {
                            // 乘梯成功
                            Log.d(TAG, "Elevator navigation successful, checking gate passing for final destination");
                            mCurState = State.START_CHARGE_UI;
                            startChargeAction(isWechat);
                        } else {
                            // 乘梯失败
                            Log.e(TAG, "Elevator navigation failed");
                            finishAutoCharge(0);
                        }
                    }

                    @Override
                    public void onError(int errorCode, String errorString, String extraData) throws RemoteException {
                        Log.e(TAG, "onError: " + errorCode + " errorString：" + errorString + " extraData：" + extraData);
                        if (errorCode == Definition.ERROR_IN_DESTINATION) {
                            Log.d(TAG, "Elevator navigation successful, is in destination, checking gate passing for final destination");
                            startChargeAction(isWechat);
                        } else {
                            finishAutoCharge(0);
                        }
                    }

                    @Override
                    public void onStatusUpdate(int status, String data, String extraData) throws RemoteException {
                        Log.e(TAG, "onStatusUpdate: " + status + " data：" + data + " extraData：" + extraData);
                        switch (status) {
                            case Definition.STATUS_ARRIVED_ELEVATOR_GATE:
                                // 等电梯
                                UIController.getInstance().sendMessageToFragment(
                                        UIController.MESSAGE_TYPE.WAIT_LIFT, "");
                                break;
                            case Definition.STATUS_START_GO_ELEVATOR_CENTER:
                                // 进电梯
                                UIController.getInstance().sendMessageToFragment(
                                        UIController.MESSAGE_TYPE.GO_IN_LIFT, "");
                                break;
                            case Definition.STATUS_ARRIVED_ELEVATOR_CENTER_CORRECT_ORIENTATION:
                                // 正在乘梯
                                UIController.getInstance().sendMessageToFragment(
                                        UIController.MESSAGE_TYPE.IN_LIFT, "");
                                break;
                            case Definition.STATUS_EXIT_ELEVATOR_START:
                                // 出电梯
                                UIController.getInstance().sendMessageToFragment(
                                        UIController.MESSAGE_TYPE.GO_OUT_LIFT, "");
                                break;
                            case Definition.STATUS_EXIT_ELEVATOR_SUC:
                                // 出梯成功
                                UIController.getInstance().sendMessageToFragment(
                                        UIController.MESSAGE_TYPE.GO_OUT_LIFT_SUCCESS, "");
                                break;
                        }
                    }
                });
    }

    private void startChargeAction(boolean isWechat) {
        if (isWechat) {
            if (isNeedPlayMusic()) {
                try {
                    SkillManager.getInstance().playMusicByLocalPath(R.raw.piano, true, true, null);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
            autoChargeAction();
        } else {
            if (mCurState == State.START_CHARGE_UI) {
                if (isNeedPlayMusic()) {
                    try {
                        SkillManager.getInstance().playMusicByLocalPath(R.raw.piano, true, true, null);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                startAutoChargeAction();
            }
        }
    }

    private boolean isNeedPlayMusic() {
        return RobotSettingApi.getInstance().getRobotInt("robot_setting_charging_sound") == 1;
    }

    private void estimateFailed(boolean isWechat) {
        voice(ApplicationWrapper.getContext().getResources().getString(R.string.charge_need_estimate_first));
        if (isWechat) {
            SystemApi.getInstance().remoteFirstCharge(mReqId, "finish", false, null);
        } else {
            goChargingPointReport(GoChargingPoint.NOT_ESTIMATE);
            if (mCurState == State.START_CHARGE_UI) {
                mStopMode = StopMode.noLocation;
                mCurState = State.START_CHARGE_FAILED;
                finishAutoChargeByModel(1500);
            }
        }
    }

    private void showOtaChargeDialog(String data) {
        mCurState = State.START_CHARGE_UI;
        if (TextUtils.isEmpty(data)) {
            Log.d(TAG, "Ota desc is null");
            finishAutoCharge(0);
            return;
        }

        try {
            JSONObject params = new JSONObject(data);
            boolean isForce = params.getBoolean("isForce");
            String versionCode = params.optString(Definition.JSON_OTA_TARGET_VERSION);
            String versionDescription = params.optString(Definition.JSON_OTA_TARGET_DESCRITION);
            mOtaDialog = new OTADialog(mContext, false)
                    .isForce(isForce)
                    .setVersionText(versionCode)
                    .setUpgradeContentText(versionDescription)
                    .setOnUpdateListener(new OTADialog.OnUpdateListener() {
                        @Override
                        public void onUpdateNextTime(OTADialog dialog) {
                            Log.d(TAG, "Cancel start charge : ota");
                            mOtaDialog = null;
                            finishAutoCharge(0);
                        }

                        @Override
                        public void onUpdateNow(OTADialog dialog) {
                            Log.d(TAG, "Start charge : ota");
                            mOtaDialog = null;
                            showAutoCharge(Definition.CHARGE_OTA);
                        }
                    });
            mOtaDialog.setCanceledOnTouchOutside(false);
            mOtaDialog.setCancelable(false);
            mOtaDialog.show();
        } catch (JSONException e) {
            e.printStackTrace();
            Log.d(TAG, "Show ota charge dialog failed : params error");
        }
    }

    private void showAutoCharge(String type) {
        String showType = type;
        Log.d(TAG, "showAutoCharge  mCurState : " + mCurState);
        chargeBi(type);

        boolean isBatteryLowAutoCharge =
                RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_AUTO_CHARGE)
                        == Definition.ROBOT_SETTING_ENABLE;
        if (type != null && type.equals(Definition.CHARGE_SLOW) &&
                ControlManager.getControlManager().getSystemStatusManager().isBatteryLow() &&
                isBatteryLowAutoCharge) {
            showType = Definition.CHARGE_LOW;
        }

        Log.d(TAG, "showAutoCharge  mCurState: " + mCurState + ", type: " + type + ", showType: " + showType
                + ", isbatteryLowAutoCharge: " + isBatteryLowAutoCharge);

        mCurState = State.START_CHARGE_UI;
        Bundle bundle = new Bundle();
        bundle.putString(ModuleDef.CHARGE_TYPE, showType);
        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_AUTO_CHARGE, bundle, null);
    }

    /**
     * 当wx端 confirm robot to start auto_charge with startChargePose.
     * 微信小程序开启自动充电
     * 充电成功:唤醒和焦点跟随　由于是在远程控制中,所以无效.
     */
    public void startWeChatAutoCharge() {
        Log.d(TAG, "WeChat AutoCharge mCurState = " + mCurState);
        mCurState = State.START_WECHAT_CHARGE;
        //先前行0.5米 ,避免充电桩的位置在地图外面.
        SystemApi.getInstance().motionLine(mReqId, Definition.CMD_NAVI_MOVE_SUB_FORWARD, 0.2f, MOVE_LINE_DISTANCE, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                super.onResult(result, message);
                Log.d(TAG, "MotionLine status = " + result + ", msg = " + message);
                if ("succeed".equals(message)) {
                    weChatChargeEstimate();
                } else {
                    remoteAutoChargeResult(Definition.RESULT_FAILURE);
                }

            }
        });
    }

    /**
     * start auto_charge after navi to start_charge_pose .
     */
    private void startAutoChargeAction() {
        Log.d(TAG, "start naviToAutoChargeAction mCurState = " + mCurState);
        if (mCurState == State.IDLE) {
            return;     // cover charge_module is stopped already ,but onResult callback later.
        }
        boolean chargingTypePile = SystemApi.getInstance().hasChargeIR() &&
                LocationUtil.getInstance().isChargingTypePile();//有ChargeIR且选择桩充
        Log.i(TAG, "checkChargingTypeIsPile isChargingTypePile = " + chargingTypePile);
        if (!chargingTypePile &&
                TextUtils.equals(NO_NAVIGATION,
                        RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_SETTING_LOW_BATTERY_NAVI_LOCATION))) {
            Log.d(TAG, "location = -1  no navigation");
            // 线充不再导航时，此时标识状态为State.START_CHARGE_UI　
            return;
        }
        mCurState = State.START_CHARGE_ACTION;

        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.AUTO_CHARGE_GOING, "");
        startPeriodVoice(10 * Definition.SECOND);
        SystemApi.getInstance().startAutoChargeAction(mReqId, CHARGE_TIMEOUT, mActionListener);
    }

    private ActionListener mActionListener = new ActionListener() {
        @Override
        public void onResult(int status, String responseString) throws RemoteException {
            super.onResult(status, responseString);
            // settings or low power charge don't need to report server.
            Log.d(TAG, "startNaviToAutoCharge onResult status = " + status +
                    ", msg = " + responseString + ", mCharging : "
                    + ControlManager.getControlManager().getSystemStatusManager().getIsCharging());
            cancelPeriodVoice();
            if (status == Definition.RESULT_OK) {
                Log.d(TAG, "auto charge success ------------");
                backChargingReport(BackCharging.BACK_CHARGING_SUCCESS);
                TaskReport.getInstance().warningReport("auto_charging_succ");
                mResult = true;
                finishAutoChargeByModel(0);
            } else if (status == Definition.RESULT_FAILURE) {
                if (mCharging) {     // battery broadcast may be received faster than onResult .
                    finishAutoCharge(0);
                    return;
                }
                //多机问题
                if(handleMultiRobotResult(responseString)){
                    return;
                }
                biCheckFailure(responseString);
                mFailCount.incrementAndGet();
                if (mFailCount.get() < ALL_CHARGE_COUNT) {
                    Log.d(TAG, "charge action fail : " + mFailCount.get());
                    voice(mContext.getString(R.string.charge_fail_retry));
                    int statusCode = getAutoChargeFailStatus(responseString, -1);
                    if (statusCode == Definition.CHARGE_FAIL_WHEN_PSB_CHARGE
                            || statusCode == Definition.CHARGE_FAIL_WHEN_PSB_NO_SIGNAL
                            || statusCode == Definition.CHARGE_FAIL_WHEN_VISION_CHARGE_START) {
                        float distance = 0.1f;
                        distance += MapUtils.getStopChargeMoveOffset();
                        SystemApi.getInstance().goForward(0, 0.2f, distance, new CommandListener() {
                            @Override
                            public void onResult(int result, String message) {
                                super.onResult(result, message);
                                Log.d(TAG, "charge action fail and retry status = " + result + " , msg = " + message);
                            }
                        });
                    }
                    Timer statusTimer = new Timer();
                    statusTimer.schedule(new TimerTask() {
                        @Override
                        public void run() {
                            startAutoChargeAction();
                        }
                    }, 3 * Definition.SECOND);

                } else {
                    chargeFailed(R.string.charge_fail_go_pile, UIController.MESSAGE_TYPE.AUTO_CHARGE_FAIL, "");
                }
            } else if (status == Definition.ACTION_RESPONSE_STOP_SUCCESS) {
                Log.d(TAG, "startAutoChargeAction ACTION_RESPONSE_STOP_SUCCESS");
                if (!isReachTargetPoint) {
                    goChargingPointReport(NAV_INTERCEPT);
                } else if (mCharging) {
                    backChargingReport(BackCharging.BACK_CHARGING_SUCCESS);
                } else {
                    backChargingReport(BackCharging.BACK_CHARGING_FAIL);
                }
                mFailCount.set(DEFAULT_ATOMIC);
                finishAutoChargeByModel(0);
                TaskReport.getInstance().warningReport("auto_charging_succ");
            } else if (status == Definition.CHARGE_FAIL_NOT_EXIST_CHARGING_WIRE_POSE) {
                chargeFailed(mContext.getString(R.string.charge_fail_not_exist_target_pose_positioning_point),
                        UIController.MESSAGE_TYPE.AUTO_CHARGE_FAIL_NOT_EXIST_CHARGING_PILE_POSE,
                        mContext.getString(R.string.charge_fail_des_not_exist_target_pose_positioning_point));
            }else if (status == Definition.CHARGE_FAIL_NOT_EXIST_CHARGING_PILE_POSE) {
                chargeFailed(mContext.getString(R.string.charge_fail_not_exist_target_pose),
                        UIController.MESSAGE_TYPE.AUTO_CHARGE_FAIL_NOT_EXIST_CHARGING_PILE_POSE,
                        mContext.getString(R.string.charge_fail_des_not_exist_target_pose));
            }else {
                chargeFailed(R.string.charge_fail_go_pile, UIController.MESSAGE_TYPE.AUTO_CHARGE_FAIL, "");
            }
            isReachTargetPoint = false;
        }

        @Override
        public void onStatusUpdate(int status, String data) {
            Log.d(TAG, "start auto charge status:" + status);

            switch (status) {
                case Definition.STATUS_NAVI_OBSTACLES_AVOID:
                    if (mCurState == State.START_CHARGE_ACTION) {
                        voice(VoiceContentUtil.getRandomVoiceContent(
                                mContext.getString(R.string.excuse_me_0),
                                mContext.getString(R.string.excuse_me_1),
                                mContext.getString(R.string.excuse_me_2),
                                mContext.getString(R.string.excuse_me_3),
                                mContext.getString(R.string.excuse_me_4)));
                    }
                    break;
                case Definition.STATUS_GOAL_OCCLUDED:
                case Definition.STATUS_NAVI_AVOID:
                    Log.i(TAG, "onStatusUpdate: STATUS_NAVI_AVOID ");
                    voice(ResUtil.getString(R.string.speech_status_navi_avoid));
                    break;
                case Definition.STATUS_ALREADY_AT_START_CHARGE_POINT:   // already at start charge point
                    isReachTargetPoint = true;
                    goChargingPointReport(GoChargingPoint.GO_CHARGING_POINT_SUCCESS);
                    mHasReachChargePoint = true;
                    UIController.getInstance().sendMessageToFragment(
                            UIController.MESSAGE_TYPE.AUTO_CHARGE_NAVI_ARRIVED, "");
                    break;
                case Definition.STATUS_NAVI_OUT_MAP:
                    Log.i(TAG, "onStatusUpdate: STATUS_NAVI_OUT_MAP ");
                    voice(ResUtil.getString(R.string.speech_status_navi_out_map_please_edit));
                    goChargingPointReport(GoChargingPoint.NAV_OUT_MAP);
                    break;
                case Definition.STATUS_NAVI_GLOBAL_PATH_FAILED:
                    Log.i(TAG, "onStatusUpdate: STATUS_NAVI_GLOBAL_PATH_FAILED ");
                    voice(ResUtil.getString(R.string.speech_status_navi_global_path_failed_please_edit_map));
                    goChargingPointReport(GoChargingPoint.NAV_GLOBAL_PATH_FAILED);
                    break;
                case Definition.STATUS_CHARGE_WAIT_IN_AREA:
                    registerRobotPushListener();
                    waitInChargeArea(mContext.getString(R.string.charge_wait_in_area_des),
                        UIController.MESSAGE_TYPE.AUTO_CHARGE_WAIT_IN_AREA,
                        mContext.getString(R.string.charge_wait_in_area_des));
                    break;
                case Definition.STATUS_CHARGE_GO_CHARGE_POINT:
                    unregisterRobotPushListener();
                    UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.AUTO_CHARGE_GO_CHARGE_POINT, "");
                    break;
                default:
                    break;
            }
        }

        @Override
        public void onError(int errorCode, String errorString) throws RemoteException {
            super.onError(errorCode, errorString);
            Log.d(TAG, "start auto charge Error" + errorCode + errorString);
            cancelPeriodVoice();
            mFailCount.set(DEFAULT_ATOMIC);
//            finishAutoCharge(0);
        }
    };

    private boolean isStartHandleRobotPush = false;
    private StatusListener robotPushStatusListener = new StatusListener() {
        @Override
        public void onStatusUpdate(String type, String data) throws RemoteException {
            Log.d(TAG, "RobotBeingPushed data:" + data );

            if (!isStartHandleRobotPush) {
                isStartHandleRobotPush = true;
                try {
                    SkillManager.getInstance().speechPlayText(
                            mContext.getString(R.string.charge_task_has_stopped),
                            new TextListener() {
                                @Override
                                public void onComplete() {
                                    super.onComplete();
                                    stopNaviAction();
                                }
                            });
                } catch (RemoteException e) {
                    Log.e(TAG, "processUpdate error: " + e.getMessage());
                }
            }

        }
    };

    private void registerRobotPushListener() {
        SystemApi.getInstance().registerStatusListener(Definition.STATUS_ROBOT_HAS_BEING_PUSHED, robotPushStatusListener);
    }

    private void unregisterRobotPushListener() {
        isStartHandleRobotPush = false;
        SystemApi.getInstance().unregisterStatusListener(robotPushStatusListener);
    }

    private boolean handleMultiRobotResult(String responseString){
        try {
            JSONObject object = new JSONObject(responseString);
            int code = object.getInt("charge_fail_status");
            return handleMultiRobot(code);
        }catch (Exception e){
            e.printStackTrace();
        }
        return false;
    }

    private boolean handleMultiRobot(int code) {
        boolean isMultiRobot = false;
        String errorMsg = "";
        switch (code) {
            case Definition.STATUS_NAVI_MULTI_MAP_NOT_MATCH:
                isMultiRobot = true;
                errorMsg = mContext.getString(R.string.multi_robot_map_not_match);
                break;
            case Definition.STATUS_NAVI_MULTI_LORA_DISCONNECT:
                isMultiRobot = true;
                errorMsg = mContext.getString(R.string.multi_robot_lora_disconnect);
                break;
            case Definition.STATUS_NAVI_MULTI_LORA_CONFIG_FAIL:
                isMultiRobot = true;
                errorMsg = mContext.getString(R.string.multi_robot_lora_config_fail);
                break;
            case Definition.STATUS_NAVI_MULTI_VERSION_NOT_MATCH:
                isMultiRobot = true;
                errorMsg = mContext.getString(R.string.multi_robot_version_not_match);
                break;
        }

        Log.d(TAG, "handleMultiRobot code:" + code + " ,msg:" + errorMsg);
        if (!isMultiRobot) {
            return false;
        }
        Bundle bundle = new Bundle();
        bundle.putInt("errorType", code);
        bundle.putString("errorMsg", errorMsg);
        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_MULTI_ROBOT_ERROR, bundle, null);
        return isMultiRobot;
    }

    private void waitInChargeArea(String voice, UIController.MESSAGE_TYPE uiMsg, String messageStr) {
        Log.d(TAG, "auto charge wait in charge area ");
        voice(voice);
        UIController.getInstance().sendMessageToFragment(uiMsg, messageStr);
    }

    private void chargeFailed(int failedVoiceRes, UIController.MESSAGE_TYPE uiMsg, String messageStr) {
        chargeFailed(mContext.getString(failedVoiceRes), uiMsg, messageStr);
    }

    private void chargeFailed(String failedVoice, UIController.MESSAGE_TYPE uiMsg, String messageStr) {
        Log.d(TAG, "auto charge fail after charged " + mFailCount.get());
        mFailCount.set(DEFAULT_ATOMIC);
        voice(failedVoice);
        UIController.getInstance().sendMessageToFragment(uiMsg, messageStr);
        mCurState = State.START_CHARGE_FAILED;
        TaskReport.getInstance().warningReport("auto_charging_failure");
        TaskReport.getInstance().setTaskResult(Constant.TaskEvent.task_fail);
        naviCmdTimeOutReport();
    }

    private void naviCmdTimeOutReport() {
        Log.d(TAG, "nviCmdTimeOutReport");
        long timestamp = System.currentTimeMillis();
        String cacheId = RobotSettings.getSystemSn() + "-" + timestamp;
        SystemApi.getInstance().naviTimeOutCmdReport(mReqId, timestamp, cacheId,
                Definition.TYPE_ACTION_AUTO_NAVI_CHARGE, "TimeOut", new CommandListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.i(TAG, "nviCmdTimeOutReport: " + result + " message:" + message);
                    }
                });
    }

    private int getAutoChargeFailStatus(String msg, int defaultValue) {
        int status = defaultValue;
        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject(msg);
            status = jsonObject.getInt(BiContants.CHARGE_FAIL_STATUS);
        } catch (JSONException e) {
            Log.w(TAG, "getChargeStatusFailure: msg:= " + msg);
            e.printStackTrace();
        }
        return status;
    }

    /**
     * @param periodTime if 0  ,Note that run execution one-time In fixed-delay.
     */
    private void startPeriodVoice(long periodTime) {
        cancelPeriodVoice();
        if (periodTime == 0) {
            voiceTimer = new Timer();
            voiceTimer.schedule(new TimerTask() {
                @Override
                public void run() {
                    voice(ResUtil.getString(R.string.charge_lower_power_going_tts));
                }
            }, 10 * Definition.SECOND);
        } else {
            voiceTimer = new Timer();
            voiceTimer.schedule(new TimerTask() {
                @Override
                public void run() {
                    try {
                        voiceListen(ResUtil.getString(R.string.charge_lower_power_going_tts));
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
            }, 10 * Definition.SECOND, periodTime);
        }
    }

    private void voiceListen(String tts) throws RemoteException {
        Log.d(TAG, "voiceListen tts : " + tts);
        if (isWaitInChargeAreaTTSPlaying) {
            Log.d(TAG, "Skip TTS because waitInChargeArea message is playing");
            return;
        }
        SkillManager.getInstance().speechPlayText(tts, new TextListener() {

            @Override
            public void onStart() {
                super.onStart();
                Log.d(TAG, "voiceListen onStart ");
            }

            @Override
            public void onStop() {
                super.onStop();
                Log.d(TAG, "voiceListen onStop ");
                if (mHasReachChargePoint) {
                    startPeriodVoice(0);
                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
                Log.d(TAG, "voiceListen onComplete ");
                if (mHasReachChargePoint) {
                    startPeriodVoice(0);
                }
            }

            @Override
            public void onError() {
                super.onError();
                Log.d(TAG, "voiceListen onError ");
                if (mHasReachChargePoint) {
                    startPeriodVoice(0);
                }
            }
        });
    }

    private void autoChargeAction() {
        Log.d(TAG, "start WeChat naviToAutoCharge ");
        mCurState = State.START_CHARGE_ACTION;
        SystemApi.getInstance().startNaviToAutoChargeAction(mReqId, CHARGE_TIMEOUT, new ActionListener() {
            @Override
            public void onResult(int status, String responseString) throws RemoteException {
                super.onResult(status, responseString);
                Log.d(TAG, "WeChat startNaviToAutoCharge onResult status = " + status + ", msg = " + responseString);
                SystemApi.getInstance().onWeChatFirstRechargingFinished();
                remoteAutoChargeResult(status);
            }

            @Override
            public void onStatusUpdate(int status, String data) throws RemoteException {
                super.onStatusUpdate(status, data);
            }

            @Override
            public void onError(int errorCode, String errorString) throws RemoteException {
                super.onError(errorCode, errorString);
                SystemApi.getInstance().onWeChatFirstRechargingFinished();
                remoteAutoChargeResult(Definition.RESULT_FAILURE);
                Log.d(TAG, "startNaviToAutoCharge onError code = " + errorCode + ",msg = " + errorString);
            }
        });
    }

    /**
     * report result to wx_server  after startAutoChargeAction.
     *
     * @param status
     */
    private void remoteAutoChargeResult(int status) {
        Log.d(TAG, "remoteAutoChargeResult status : " + status + ", mCharging : " + mCharging);
        if (status == Definition.RESULT_OK) {
            chargeOk();
        } else if (status == Definition.ACTION_RESPONSE_STOP_SUCCESS) {  // 处理小程序已经启动回充action,但是底盘错误,此时用户手动推到充电桩的情况.
            if (mCharging) {
                chargeOk();
            } else {
                chargeFail();
            }
        } else {
            chargeFail();
        }
    }

    private void chargeOk() {
        voice(mContext.getString(R.string.charge_success_voice));
        SystemApi.getInstance().remoteFirstCharge(mReqId, "finish", true, null);
        postSetPlaceToServer();
        Log.d(TAG, "remoteAutoChargeResult result_OK");
    }

    /**
     * get place pose and report to server the pose .
     */
    private void postSetPlaceToServer() {
        SystemApi.getInstance().postSetPlaceToServer(mReqId, Definition.START_CHARGE_PILE_POSE);
        SystemApi.getInstance().postSetPlaceToServer(mReqId, Definition.START_BACK_CHARGE_POSE);
    }

    private void chargeFail() {
        voice(mContext.getString(R.string.charge_fail));
        SystemApi.getInstance().remoteFirstCharge(mReqId, "finish", false, null);
    }

    private void goChargingPointReport(int status) {
        BiGoChargingPointReport biGoChargingPointReport = new BiGoChargingPointReport();
        biGoChargingPointReport.addResult(status)
                .report();
    }

    private void chargeBi(String reason) {
        if (TextUtils.isEmpty(reason)) {
            return;
        }
        Log.d(TAG, "chargeBi() start reason: " + reason);
        mBiGbCharging.charging().occur(reason).report();
    }

    /**
     * 播报功能
     * 不需要区分机器人类型时调用
     */
    private void voice(String tts) {
        Log.d(TAG, "voice : " + tts);
        if (isWaitInChargeAreaTTSPlaying) {
            Log.d(TAG, "Skip TTS because waitInChargeArea message is playing");
            return;
        }
        if (tts.equals(mContext.getString(R.string.charge_wait_in_area_des))) {
            try {
                isWaitInChargeAreaTTSPlaying = true;
                SkillManager.getInstance().speechPlayText(tts, new TextListener() {
                    @Override
                    public void onComplete() {
                        super.onComplete();
                        isWaitInChargeAreaTTSPlaying = false;
                        Log.d(TAG, "voice finish onComplete");
                    }

                    @Override
                    public void onError() {
                        super.onError();
                        isWaitInChargeAreaTTSPlaying = false;
                        Log.d(TAG, "voice finish onError");
                    }

                    @Override
                    public void onStop() {
                        super.onStop();
                        isWaitInChargeAreaTTSPlaying = false;
                        Log.d(TAG, "voice finish onStop");
                    }
                });
            } catch (RemoteException e) {
                throw new RuntimeException(e);
            }
        } else {
            SkillManager.getInstance().speechPlayText(tts);
        }
    }

    /**
     * 根据不同类型的机器人选择是否调用该方法，如需要区分机器人类型时调用,如无需区分调用finishAutoCharge()
     *
     * @param delayTime
     */
    private void finishAutoChargeByModel(long delayTime) {
        finishAutoCharge(delayTime);
    }

    /**
     * 注意：
     * Before you stop this Module personally, call this method to update state first;
     *
     * @param delayTime
     */
    private void finishAutoCharge(long delayTime) {
        Log.d(TAG, "finishAutoCharge, curState " + mCurState);
        if (mCurState.equals(State.IDLE)) return;
        mCurState = State.START_CHARGE_END;
        releaseDelay(delayTime);
    }

    private void releaseDelay(long time) {
        Log.d(TAG, "release delay, status:" + mCurState);
        synchronized (this) {
            mDelayTimer = new Timer();
            mDelayTimer.schedule(new TimerTask() {
                @Override
                public void run() {
                    stop();
                    cancel();
                }
            }, time);
        }
    }

    private void cancelPeriodVoice() {
        if (voiceTimer != null) {
            voiceTimer.cancel();
            voiceTimer = null;
        }
    }

    private void batteryChanged(boolean isCharging) {
        Log.d(TAG, "battery changed:" + isCharging + ",current state:" + mCurState);
        mCharging = isCharging;
        if (isCharging) {
            switch (mCurState) {
                case START_CHARGE_ACTION:
                    startStopChargeActionTimer();
                    break;
                case START_CHARGE_FAILED:
                    finishAutoCharge(0);
                    break;
            }
        } else {
            switch (mCurState) {
                case START_WECHAT_CHARGE:
                    Log.d(TAG, "exit charging UI");
                    UIController.getInstance().moveToBack();
                    break;
            }
        }
    }

    private void startStopChargeActionTimer() {
        Log.d(TAG, "call stop ChargeActionTimer ");
        Timer stopTimer = new Timer();
        stopTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                Log.d(TAG, "timer stopAutoChargeAction");
                stopNaviAction();
                cancel();
            }
        }, Definition.SECOND);
    }

    /**
     * 结束Action
     */
    private void stopNaviAction() {
        SystemApi.getInstance().stopAutoChargeAction(mReqId, false);
    }

    private void backChargingReport(int status) {
        BiBackChargingReport biBackChargingReport = new BiBackChargingReport();
        biBackChargingReport.addWhich(mFailCount.get() + BiContants.COUNT_OFFSET)
                .addResult(status)
                .report();
    }

    private void biCheckFailure(String msg) {
        try {
            JSONObject jsonObject = new JSONObject(msg);
            int status = jsonObject.getInt(BiContants.CHARGE_FAIL_STATUS);
            switch (status) {
                case BiContants.CHARGE_FAIL_WHEN_NOT_ESTIMATE:
                    goChargingPointReport(GoChargingPoint.NOT_ESTIMATE);
                    break;
                case BiContants.CHARGE_FAIL_WHEN_NAVIGATION:
                    goChargingPointReport(GoChargingPoint.NAVIGATION_FAIL);
                    break;
                case BiContants.CHARGE_FAIL_WHEN_LARGE_MAP_NAV_TIMEOUT:
                    goChargingPointReport(GoChargingPoint.LARGE_MAP_NAV_TIMEOUT);
                    break;
                case BiContants.CHARGE_FAIL_WHEN_PARSE_IN_LOCATION:
                    goChargingPointReport(GoChargingPoint.PARSE_LOCATION_FAIL);
                    break;
                case BiContants.CHARGE_FAIL_WHEN_NOT_MOVE_FOR_20S:
                    goChargingPointReport(GoChargingPoint.NOT_MOVE_20S);
                    break;
                case BiContants.CHARGE_FAIL_WHEN_PSB_CHARGE:
                    backChargingReport(BackCharging.BACK_CHARGING_FAIL);
                    break;
                case BiContants.CHARGE_FAIL_WHEN_PSB_NO_SIGNAL:
                    backChargingReport(BackCharging.BACK_CHARGING_FAIL_NO_SIGNAL);
                    break;
                default:
            }
        } catch (JSONException e) {
            Log.w(TAG, "biCheckFailure: msg:= " + msg);
            e.printStackTrace();
        }
    }

    @Override
    protected void onStop() {
        unregisterRobotPushListener();
        SkillManager.getInstance().openSpeechAsrRecognize();
        if (isNeedPlayMusic()) {
            SkillManager.getInstance().stopMusic();
        }
        cancelDelayTimer();
        mCharging = false;
        mHasReachChargePoint = false;
        //mFailCount.set(DEFAULT_ATOMIC);
        Log.d(TAG, "onStop ==== mCurState : " + mCurState);
        if (mCurState == State.START_CHARGE_END) { // 低电量时为保证急停释放后,依然触发回充.不可调 onStartChargeFinished remove status.
            try {
                Log.d(TAG, "notify to CoreService onStartChargeFinished");
                JSONObject json = new JSONObject();
                json.put("result", mResult);
                json.put("startMode", mStartMode);
                SystemApi.getInstance().onStartChargeFinished(json.toString());
            } catch (JSONException e) {
                e.printStackTrace();
                SystemApi.getInstance().onStartChargeFinished(null);
            }
        } else if (mCurState == State.START_CHARGE_ACTION) {
            stopNaviAction();
            Log.d(TAG, "stopModule and stopAutoChargeAction");
        }
        mCurState = State.IDLE;
        mResult = false;
        cancelPeriodVoice();
        if (mOtaDialog != null) {
            mOtaDialog.dismiss();
        }
        SystemApi.getInstance().finishModuleParser(mReqId, true);
        if (mStopMode == StopMode.noLocation) {
            mStopMode = StopMode.normal;
            //调用重定位
            sendVisionRepositionBroadcast(mContext);
        }
        release(mReqId, RESULT_OK, null);
        Log.d(TAG, "finish and release ==========");
    }

    private void cancelDelayTimer() {
        synchronized (this) {
            if (mDelayTimer != null) {
                mDelayTimer.cancel();
                mDelayTimer = null;
            }
        }
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
        switch (type) {
            case ModuleDef.LOCAL_MESSAGE_ESTIMATE:
                checkEstimate(false);
                break;
            case ModuleDef.LOCAL_MESSAGE_CHARGE_START:
                batteryChanged(true);
                break;
            case ModuleDef.LOCAL_MESSAGE_CHARGE_END:
                batteryChanged(false);
                break;
            case ModuleDef.LOCAL_MESSAGE_AUTO_CHARGE_CANCEL:
                stopNaviAction();
                break;
            case ModuleDef.LOCAL_MESSAGE_REPOSITION_CANCEL:
                finishAutoCharge(0);
                break;

            default:
                break;
        }
    }

    @Override
    public void onMessageFromLocal(int type, Object param) {
        if (!TextUtils.equals(NO_NAVIGATION,
                RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_SETTING_LOW_BATTERY_NAVI_LOCATION))){
            Log.d(TAG, "Need to start navigating to charge");
            return;
        }
        super.onMessageFromLocal(type, param);
        switch (type) {
            case ModuleDef.LOCAL_MESSAGE_LEVEL_CHANGED:
                if (param instanceof BatteryBean){
                    BatteryBean bean = (BatteryBean) param;
                    if (mCurState == State.START_CHARGE_UI && bean.isCharging()){
                        finishAutoCharge(0);
                    }
                }
                break;
            default:
                break;
        }
    }
}