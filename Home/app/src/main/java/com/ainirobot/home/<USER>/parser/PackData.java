package com.ainirobot.home.ota.parser;


public class PackData {
    public final static String MD5 = "_pack_md5";
    public final static String CONFIG_CHECK = "_config_check";
    private String osName;
    private String fileName;
    private String md5;
    private String osMd5Name;
    private String targetVersion;
    private String motors;//电机型号

    public PackData() {
    }

    public String getMotors() {
        return motors;
    }

    public void setMotors(String motors) {
        this.motors = motors;
    }

    public void setOsName(String name) {
        this.osName = name;
    }

    public String getOsName() {
        return osName;
    }

    public void setUpdateFileName(String name) {
        this.fileName = name;
    }

    public String getUpdateFileName() {
        return fileName;
    }

    public void setUpdateFileMd5(String md5) {
        this.md5 = md5;
    }

    public String getUpdateFileMd5() {
        return md5;
    }

    public void setOsMd5Name(String str) {
        this.osMd5Name = str;
    }

    public String getOsMd5Name() {
        return osMd5Name;
    }

    public void setTargetVersion(String version) {
        this.targetVersion = version;
    }

    public String getTargetVersion() {
        return this.targetVersion;
    }

    @Override
    public String toString() {
        return "PackData{" +
                "osName='" + osName + '\'' +
                ", fileName='" + fileName + '\'' +
                ", md5='" + md5 + '\'' +
                ", osMd5Name='" + osMd5Name + '\'' +
                ", targetVersion='" + targetVersion + '\'' +
                ", motors='" + motors + '\'' +
                '}';
    }
}
