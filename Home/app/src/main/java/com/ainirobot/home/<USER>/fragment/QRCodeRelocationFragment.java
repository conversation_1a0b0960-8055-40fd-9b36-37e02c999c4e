package com.ainirobot.home.ui.fragment;

import android.content.Context;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.view.LoadingView;
import com.ainirobot.home.ui.view.RelocationAnchorView;
import com.ainirobot.home.ui.view.RelocationGuideView;
import com.ainirobot.home.ui.view.RelocationResultView;
import com.ainirobot.home.ui.view.RelocationSearchView;
import com.ainirobot.home.ui.view.RelocationWithoutMapView;
import com.ainirobot.home.ui.view.RepositionAnchorQuestionDialog;
import com.ainirobot.home.ui.view.RepositionCancelDialog;

public class QRCodeRelocationFragment extends BaseFragment implements View.OnClickListener {

    private static final String TAG = QRCodeRelocationFragment.class.getSimpleName();
    private LoadingView mLoadingView;
    private RelocationSearchView mSearchView;
    private RelocationResultView mResultView;
    private RelocationGuideView mGuideView;
    private RelocationAnchorView mAnchorView;
    private RelocationWithoutMapView mWithoutMapView;
    private RepositionCancelDialog mDialog;
    public View mView;
    private Context mContext;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        Log.i(TAG, "on create");
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        mView = inflater.inflate(R.layout.layout_qrcode_relocation, null);
        mLoadingView = (LoadingView) mView.findViewById(R.id.relocation_loading_view);
        mResultView = (RelocationResultView) mView.findViewById(R.id.relocation_result_view);

        mGuideView = (RelocationGuideView) mView.findViewById(R.id.relocation_guide_view);
        mGuideView.mCancelTV.setOnClickListener(this);
        mGuideView.mConfirmBtn.setOnClickListener(this);

        mAnchorView = (RelocationAnchorView) mView.findViewById(R.id.relocation_anchor_view);
        mAnchorView.mAnchorCancelTV.setOnClickListener(this);
        mAnchorView.mAnchorExitTV.setOnClickListener(this);
        mAnchorView.mResultBtn.setOnClickListener(this);
        mAnchorView.mQuestionBtn.setOnClickListener(this);

        mSearchView = (RelocationSearchView) mView.findViewById(R.id.relocation_search);

        mWithoutMapView = (RelocationWithoutMapView) mView.findViewById(R.id.relocation_without_map);
        mWithoutMapView.mRebootBtn.setOnClickListener(this);
        mWithoutMapView.mCancelBtn.setOnClickListener(this);

        assert getArguments() != null;
        showDefaultView(getArguments());
        return mView;
    }

    public void showDefaultView(@NonNull Bundle bundle) {
        boolean isLoading = bundle.getBoolean(ModuleDef.QRCODE_LOADING);
        if (isLoading) {
            showLoadingView();
            return;
        }

        String failure_type = bundle.getString(ModuleDef.QRCODE_FAILURE_TYPE,
                ModuleDef.QRCODE_FAILURE_LOSE_POINT_WITH_ANCHOR);
        switch (failure_type) {
            case ModuleDef.QRCODE_FAILURE_LOSE_POINT_WITH_ANCHOR:
                showGuideView(true);
                break;
            case ModuleDef.QRCODE_FAILURE_LOSE_POINT_WITHOUT_ANCHOR:
                showGuideView(false);
                break;
            case ModuleDef.QRCODE_FAILURE_WITHOUT_MAP:
                showLoseMapView();
                break;
            case ModuleDef.QRCODE_FAILURE_LOSE_POINT_WITHOUT_TARGET:
                showAnchorView(mAnchorView.TYPE_WITH_ANCHOR_INIT);
                break;
        }
    }

    private void showLoadingView() {
        mLoadingView.setVisibility(View.VISIBLE);
        mGuideView.setVisibility(View.GONE);
        mResultView.setVisibility(View.GONE);
        mSearchView.setVisibility(View.GONE);
        mAnchorView.setVisibility(View.GONE);
        mWithoutMapView.setVisibility(View.GONE);
    }

    private void showLoseMapView() {
        mLoadingView.setVisibility(View.GONE);
        mGuideView.setVisibility(View.GONE);
        mResultView.setVisibility(View.GONE);
        mSearchView.setVisibility(View.GONE);
        mAnchorView.setVisibility(View.GONE);
        mWithoutMapView.setVisibility(View.VISIBLE);
    }

    private void showGuideView(boolean hasAnchorPoint) {
        mLoadingView.setVisibility(View.GONE);
        mGuideView.setVisibility(View.VISIBLE);
        if (hasAnchorPoint) {
            mGuideView.mConfirmBtn.setVisibility(View.VISIBLE);
        } else {
            mGuideView.mConfirmBtn.setVisibility(View.GONE);
        }
        mResultView.setVisibility(View.GONE);
        mSearchView.setVisibility(View.GONE);
        mAnchorView.setVisibility(View.GONE);
        mWithoutMapView.setVisibility(View.GONE);
    }


    private void showAnchorView(int type) {
        mLoadingView.setVisibility(View.GONE);
        mGuideView.setVisibility(View.GONE);
        mResultView.setVisibility(View.GONE);
        mSearchView.setVisibility(View.GONE);
        mSearchView.stopRepositionAnimate();
        mAnchorView.setVisibility(View.VISIBLE);
        mAnchorView.showAnchorView(type);
        mWithoutMapView.setVisibility(View.GONE);
    }

    private void showSearchView() {
        mLoadingView.setVisibility(View.GONE);
        mGuideView.setVisibility(View.GONE);
        mResultView.setVisibility(View.GONE);
        mSearchView.setVisibility(View.VISIBLE);
        mSearchView.startRepositionAnimation();
        mAnchorView.setVisibility(View.GONE);
        mWithoutMapView.setVisibility(View.GONE);
    }

    private void showSuccessView() {
        mLoadingView.setVisibility(View.GONE);
        mGuideView.setVisibility(View.GONE);
        mResultView.setVisibility(View.VISIBLE);
        mSearchView.setVisibility(View.GONE);
        mSearchView.stopRepositionAnimate();
        mAnchorView.setVisibility(View.GONE);
        mWithoutMapView.setVisibility(View.GONE);
    }

    private void clickCancel() {
        if (mDialog == null) {
            mDialog = new RepositionCancelDialog(mContext, R.style.OTADialog)
                    .setDialogClickListener(new RepositionCancelDialog.ClickListener() {
                        @Override
                        public void onConfirmClick() {
                            mDialog = null;
                        }

                        @Override
                        public void onCancelClick() {
                            ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_REPOSITION_CANCEL);
                            mDialog = null;
                        }
                    });
            mDialog.show();
        }
    }

    public void destoryAllViews() {
        if (mDialog != null) {
            mDialog.dismiss();
        }
        if (mSearchView != null) {
            mSearchView.stopRepositionAnimate();
            mSearchView = null;
        }
        mGuideView = null;
        mResultView = null;
    }


    private void clickAnchorPointRetryBtn() {
        showSearchView();
        ControlManager.getControlManager().sendMessageToModule(
                ModuleDef.MSG_REPOSITION_QRCODE_ANCHOR_SET);
    }

    private void clickGuideConfirmBtn() {
        showAnchorView(mAnchorView.TYPE_WITH_ANCHOR);
        ControlManager.getControlManager().sendMessageToModule(
                ModuleDef.MSG_REPOSITION_QRCODE_CHOOSE_OTHER);
    }

    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
        switch (type) {
            case QRCODE_REPOSITION_SUCCEED:
                showSuccessView();
                break;
            case ANCHOR_POINT_REPOSITION_FAILURE:
                showAnchorView(mAnchorView.TYPE_NOT_FIND_ANCHOR);
                break;
            case REPOSITION_EXIT:
                destoryAllViews();
                break;
            default:
                break;
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.reposition_cancel_text:
                clickCancel();
                break;
            case R.id.reposition_anchor_cancel_tv:
                ControlManager.getControlManager().sendMessageToModule(ModuleDef.MSG_REPOSITION_QRCODE_REPOSITION);
                showGuideView(true);
                break;
            case R.id.anchor_point_btn:
                clickAnchorPointRetryBtn();
                break;
            case R.id.locate_guide_confirmBtn:
                clickGuideConfirmBtn();
                break;
            case R.id.anchor_question_btn:
                showAnchorDialog();
                break;
            case R.id.reposition_without_map_cancel:
                ControlManager.getControlManager().sendMessageToModule(
                        ModuleDef.LOCAL_MESSAGE_REPOSITION_CANCEL);
                break;
            case R.id.without_map_reboot_btn:
                ControlManager.getControlManager().sendMessageToModule(
                        ModuleDef.MSG_REPOSITION_QRCODE_REPOSITION_REBOOT);
                break;
            default:
                break;
        }
    }

    private void showAnchorDialog() {
        RepositionAnchorQuestionDialog dialog = new RepositionAnchorQuestionDialog(mContext);
        dialog.show();
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        mContext = context;
        Log.d(TAG, "onAttach");
    }

    @Override
    public void onStart() {
        super.onStart();
        Log.i(TAG, "onstart");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.d(TAG, "onDestroyView");
        destoryAllViews();
    }

}
