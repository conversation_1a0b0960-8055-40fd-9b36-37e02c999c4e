package com.ainirobot.home.utils;

import android.util.Log;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

public class SystemHookUtils {
    private static final String TAG = "SystemHookUtils";

    public static void setSystemProperties(String key, String value) {
        try {
            Class cls = Class.forName("android.os.SystemProperties");
            Method method = cls.getMethod("set", String.class, String.class);

            method.invoke(null, key, value);
        } catch (ClassNotFoundException |
                NoSuchMethodException |
                IllegalArgumentException |
                IllegalAccessException |
                InvocationTargetException e) {
            e.printStackTrace();
            Log.e(TAG, "setSystemProperties error: "+ e.toString());
        }
    }
}
