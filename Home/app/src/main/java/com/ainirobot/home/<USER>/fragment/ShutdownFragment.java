package com.ainirobot.home.ui.fragment;

import android.os.Bundle;
import android.support.annotation.Nullable;
import android.text.Html;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.ui.UIController.MESSAGE_TYPE;
import com.ainirobot.home.utils.ResType;

/**
 * 定时关机导航界面
 */
public class ShutdownFragment extends BaseFragment {

    private static final String TAG = "ShutdownNaviFragment:Home";

    /**
     * 目标点名称
     */
    private String locationName;
    /**
     * 倒计时
     */
    private int countDown;
    private TextView mShutdownTips;
    private TextView mShutdownNaviTips;
    private ImageView mShutdownRobot;
    private View mLocationIcon;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {

        locationName = getArguments().getString("locationName");
        countDown = getArguments().getInt("countDown");

        View view = inflater.inflate(R.layout.fragment_shutdown, null);
        initView(view);

        if (TextUtils.isEmpty(locationName)) {
            if (mLocationIcon != null)
                mLocationIcon.setVisibility(View.GONE);
            mShutdownNaviTips.setVisibility(View.GONE);
            updateCountDown(countDown);
        } else {
            mShutdownNaviTips.setText(Html.fromHtml(getContext().getString(R.string.go_location_pose, locationName)));
        }
        return view;
    }

    private void initView(View view) {
        mShutdownTips = view.findViewById(R.id.shutdown_tips);
        mShutdownNaviTips = view.findViewById(R.id.shutdown_navi_tips);
        mLocationIcon = view.findViewById(R.id.location);

        mShutdownRobot = (ImageView) view.findViewById(R.id.shutdown_robot);
        mShutdownRobot.setImageResource(ResType.SHUTDOWN_ROBOT.getResIdByType());

        //立即关机
        view.findViewById(R.id.shutdown_now).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_SHUTDOWN_NOW);
            }
        });

        //取消关机
        view.findViewById(R.id.shutdown_cancel).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_SHUTDOWN_CANCEL);
            }
        });
    }

    /**
     * 定时关机前往目标点结束
     *
     * @param result
     */
    private void onNavigationFinished(boolean result) {
        int strId = result ? R.string.shutdown_navi_succeed : R.string.shutdown_navi_failed;
        mShutdownNaviTips.setText(getContext().getString(strId, locationName));
        updateCountDown(countDown);
    }

    /**
     * 开始显示倒计时
     */
    private void updateCountDown(int time) {
        mShutdownTips.setText(Html.fromHtml(getContext().getString(R.string.shutdown_tips, time)));
    }

    @Override
    public void onMessage(MESSAGE_TYPE type, String message) {
        switch (type) {
            case SHUTDOWN_NAVI_FINISHED:
                onNavigationFinished(Boolean.parseBoolean(message));
                break;

            case SHUTDOWN_COUNTDOWN:
                updateCountDown(Integer.parseInt(message));
                break;

            default:
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
    }
}