package com.ainirobot.home.bi.anotation;

import android.support.annotation.IntDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static com.ainirobot.home.bi.anotation.BackCharging.*;


/**
 * back charging annotation
 *
 * @version V1.0.0
 * @date 2019/4/10 15:50
 */
@Retention(RetentionPolicy.SOURCE)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@IntDef({BACK_CHARGING_SUCCESS
        , BACK_CHARGING_FAIL
        , BACK_CHARGING_FAIL_NO_SIGNAL})
public @interface BackCharging {
    final int BACK_CHARGING_SUCCESS = 30000;
    final int BACK_CHARGING_FAIL = -30200;
    final int BACK_CHARGING_FAIL_NO_SIGNAL = -30201;
}
