package com.ainirobot.home.ui.fragment;

import android.annotation.SuppressLint;
import android.content.ComponentName;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.bi.TimeWarningReport;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.ui.view.InputPasswordDialog;
import com.ainirobot.home.utils.Blur;

public class TimeWarningFragment extends BaseFragment implements View.OnClickListener {

    private static final String TAG = "TimeWarningFragment:Home";
    private TextView mSetNetworkView;
    private TextView mSkipView;
    private TextView mSkipAlwaysView;
    private InputPasswordDialog mPasswordDialog;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @SuppressLint("LongLogTag")
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        Log.i(TAG, "on create view");
        View view = inflater.inflate(R.layout.fragment_time_warning, null);

        mSetNetworkView = (TextView) view.findViewById(R.id.time_warning_set_network_button);
        mSkipView = (TextView) view.findViewById(R.id.time_warning_skip_button);
        mSkipAlwaysView = (TextView) view.findViewById(R.id.time_warning_skip_always_button);
        mSetNetworkView.setOnClickListener(this);
        mSkipView.setOnClickListener(this);
        mSkipAlwaysView.setOnClickListener(this);
        return view;
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onStop() {
        super.onStop();
        if (mPasswordDialog != null) {
            mPasswordDialog.dismiss();
        }
    }

    @Override
    public void onClick(View v) {
        View decorView = getActivity().getWindow().getDecorView();
        decorView.setDrawingCacheQuality(View.DRAWING_CACHE_QUALITY_LOW);
        decorView.setDrawingCacheEnabled(true);
        decorView.buildDrawingCache();
        Bitmap image = decorView.getDrawingCache();
        Bitmap bitmap = Blur.apply(getActivity(), image, 15);
        if (mPasswordDialog != null) {
            mPasswordDialog.dismiss();
            mPasswordDialog = null;
        }
        mPasswordDialog = (new InputPasswordDialog(getActivity(),
                android.R.style.Theme_DeviceDefault_NoActionBar_Fullscreen, bitmap));
        boolean isDropPasswordOpen = RobotSettingApi.getInstance()
                .getRobotInt(Definition.ROBOT_DROPDOWN_BAR_PASSWORD) == 1;
        switch (v.getId()) {
            case R.id.time_warning_set_network_button:
                if (isDropPasswordOpen) {
                    mPasswordDialog.setUseMd5(false);
                    mPasswordDialog.setCallback(new InputPasswordDialog.DialogCallback() {
                        @Override
                        public void confirm() {
                            ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_TIME_WARNING_STOP);
                            startSettingsActivity();
                        }
                    });
                    mPasswordDialog.show();
                } else {
                    ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_TIME_WARNING_STOP);
                    startSettingsActivity();
                }
                break;
            case R.id.time_warning_skip_button:
                ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_TIME_WARNING_STOP);
                TimeWarningReport report = new TimeWarningReport();
                report.addAction(TimeWarningReport.ACTION_SKIP).report();
                break;
            case R.id.time_warning_skip_always_button:
                mPasswordDialog.setUseMd5(true);
                mPasswordDialog.setCallback(new InputPasswordDialog.DialogCallback() {
                    @Override
                    public void confirm() {
                        ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_TIME_WARNING_STOP_ALWAYS);
                        TimeWarningReport report = new TimeWarningReport();
                        report.addAction(TimeWarningReport.ACTION_SKIP_ALWAYS).report();
                    }
                });
                mPasswordDialog.show();
                break;
        }
    }

    private void startSettingsActivity() {
        Intent intent = new Intent();
        ComponentName cn = new ComponentName("com.ainirobot.settings", "com.ainirobot.settings.SettingsActivity");
        try {
            intent.setComponent(cn);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
        TimeWarningReport report = new TimeWarningReport();
        report.addAction(TimeWarningReport.ACTION_SET_NETWORK).report();
    }
}