package com.ainirobot.home.ota.httpclient;

import android.util.Log;

import com.ainirobot.home.ota.constants.OtaConstants;

import java.util.ArrayDeque;
import java.util.Deque;
import java.util.Iterator;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public final class Dispatcher {
    private final static String TAG = OtaConstants.TAG_PREFIX + Dispatcher.class.getSimpleName();

    private int mMaxRequests = 64;
    private volatile boolean isAvailable = false;
    private final ExecutorService mExecutor;
    private final Deque<NetworkRunnable> mRunningRunnables = new ArrayDeque<>();
    private final Deque<NetworkRunnable> mWaitingRunnables = new ArrayDeque<>();
    private final Deque<NetworkRunnable> mPendingRunnables = new ArrayDeque<>();

    public Dispatcher() {
        mExecutor = new ThreadPoolExecutor(0, Integer.MAX_VALUE,
                60, TimeUnit.SECONDS, new SynchronousQueue<Runnable>());
    }

    public void setMaxRequests(int maxRequests) {
        if (maxRequests < 1) {
            throw new IllegalArgumentException("maxRequests < 1 : " + maxRequests);
        }
        this.mMaxRequests = maxRequests;
        performRunnable();
    }

    public int getMaxRequests() {
        return mMaxRequests;
    }

    public synchronized void cancelAll() {
        for (NetworkRunnable runnable : mRunningRunnables) {
            runnable.cancel();
        }

        mWaitingRunnables.clear();
    }

    public synchronized void cancel(Object tag) {
        for (NetworkRunnable runnable : mRunningRunnables) {
            Log.d(TAG, "runnable.getTag():" + runnable.getTag() + ",tag:" + tag);
            if (runnable.getTag() == tag) {
                runnable.cancel();
                return;
            }
        }

        Iterator<NetworkRunnable> iterator = mWaitingRunnables.iterator();
        while (iterator.hasNext()) {
            NetworkRunnable runnable = iterator.next();
            if (runnable.getTag() == tag) {
                iterator.remove();
            }
        }
    }

    private synchronized void performRunnable() {
        Log.v(TAG, "running:" + mRunningRunnables.size()
                + ", waiting:" + mWaitingRunnables.size()
                + ", pending:" + mPendingRunnables.size());

        if (mRunningRunnables.size() >= mMaxRequests
                || (mWaitingRunnables.isEmpty() && mPendingRunnables.isEmpty())) {
            return;
        }

        if (isAvailable) {
            Iterator<NetworkRunnable> iterator = mPendingRunnables.iterator();
            while (iterator.hasNext()) {
                NetworkRunnable runnable = iterator.next();
                mWaitingRunnables.add(runnable);
                iterator.remove();
            }
        }
        Iterator<NetworkRunnable> iterator = mWaitingRunnables.iterator();
        while (iterator.hasNext()) {
            NetworkRunnable runnable = iterator.next();
            mRunningRunnables.add(runnable);
            mExecutor.execute(runnable);
            iterator.remove();

            if (mRunningRunnables.size() > mMaxRequests) {
                return;
            }
        }
    }

    public synchronized void submit(NetworkRunnable runnable, boolean isPendingMsg) {
        if (!isAvailable && isPendingMsg) {
            mPendingRunnables.add(runnable);
        } else if (mRunningRunnables.size() < mMaxRequests) {
            mRunningRunnables.add(runnable);
            mExecutor.execute(runnable);
        } else {
            mWaitingRunnables.add(runnable);
        }
    }

    public synchronized boolean finished(NetworkRunnable runnable) {
        if (mRunningRunnables.remove(runnable)) {
            performRunnable();
            return true;
        }
        return false;
    }

    public synchronized void onAvailable(boolean status) {
        isAvailable = status;
        if (isAvailable) {
            performRunnable();
        }
    }
}
