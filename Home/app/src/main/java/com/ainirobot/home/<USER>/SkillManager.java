/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.home.control;

import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.coreservice.client.listener.ToneListener;
import com.ainirobot.coreservice.client.speech.SkillApi;
import com.ainirobot.coreservice.client.speech.entity.TTSEntity;
import com.ainirobot.coreservice.listener.IMusicListener;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.bean.SpeechResult;
import com.google.gson.Gson;

import java.io.File;
import java.util.ArrayList;
import java.util.concurrent.CopyOnWriteArrayList;

public class SkillManager {

    public static final int STATE_LISTENING = 1;
    private static final int STATE_NO_LISTENING = 0;

    private static final String TAG = "SkillManager:Home";
    private static SkillManager sSkillManager;

    private ArrayList<SpeechParResultListener> mParListeners;
    private CopyOnWriteArrayList<SpeechCompleteListener> mCompleteListeners;
    private ArrayList<SpeechRecognitionStopListener> mRecognitionStopListeners;
    private SkillApi mSkill;
    private Gson mGson;
    private volatile int listeningState;
    private TalkState mTalkState = TalkState.IDLE;
    private Object lockListeningState = new Object();

    private enum TalkState {
        IDLE, TALKING
    }

    private SkillManager() {
        super();
        mCompleteListeners = new CopyOnWriteArrayList<SpeechCompleteListener>();
        mParListeners = new ArrayList<SpeechParResultListener>();
        mRecognitionStopListeners = new ArrayList<SpeechRecognitionStopListener>();
        mGson = new Gson();
    }

    public static SkillManager getInstance() {
        if (null == sSkillManager) {
            sSkillManager = new SkillManager();
        }
        return sSkillManager;
    }

    public void setSkillApi(SkillApi skillApi) {
        Log.i(TAG, "registerCallBack");
        this.mSkill = skillApi;
    }

    public void onSpeechParResult(String result) {
        Log.d(TAG, "onSpeechParResult result:" + result);
        for (SpeechParResultListener listener : mParListeners) {
            listener.onSpeechParResult(result);
        }
    }

    public void onSpeechRecognitionStart() {

    }

    public void onSpeechRecognitionStop() {
        Log.d(TAG, "onSpeechRecognitionStop");
        for (SpeechRecognitionStopListener listener : mRecognitionStopListeners) {
            listener.onSpeechRecognitionStop();
        }
    }

    public void onQueryEnded(int i) {
        Log.d(TAG, "onQueryEnded " + i);//0-normal 1-other 2-noise 4-timeout
        for (SpeechCompleteListener listener : mCompleteListeners) {
            listener.onQueryEnded();
        }
    }

    public interface SpeechRecognitionStopListener {
        void onSpeechRecognitionStop();
    }

    public interface SpeechParResultListener {
        void onSpeechParResult(String result);
    }

    public interface SpeechCompleteListener {
        void onSpeechComplete(String intent, SpeechResult params);

        void onTTSSpeechComplete(boolean isComplete); //true complete false:stopTTS

        void onQueryEnded();
    }

    public void cancleAudioOperation() {
        if (null != mSkill) {
            mSkill.cancleAudioOperation();
        } else {
            Log.w(TAG, "can't stop content, mSkillCallback is null!");
        }
    }

    public boolean speechPlayText(String text, TextListener listener) throws RemoteException {
        if (null != mSkill) {
            if (!TextUtils.isEmpty(text)) {
                mSkill.playText(new TTSEntity(text), listener);
                return true;
            }
        }
        return false;
    }

    public void speechPlayTone(int rawResourceId, ToneListener listener) throws RemoteException {
        String path = "android.resource://" + ApplicationWrapper.getContext().getPackageName()
                + File.separator + rawResourceId;
        speechPlayTone(path, listener);
    }

    public void speechPlayTone(String localPath, ToneListener listener) throws RemoteException {
        if (null != mSkill) {
            mSkill.playToneByLocalPath(localPath, listener);
        }
    }

    public void playMusicByLocalPath(int rawResourceId, boolean looping, boolean enableAudioFocus,
                                     IMusicListener listener) throws RemoteException {
        String path = "android.resource://" + ApplicationWrapper.getContext().getPackageName()
                + File.separator + rawResourceId;
        playMusicByLocalPath(path, looping, enableAudioFocus, listener);
    }

    public void playMusicByLocalPath(String localPath, boolean looping, boolean enableAudioFocus,
                                     IMusicListener listener) throws RemoteException {
        if (null != mSkill) {
            mSkill.playMusicByLocalPath(localPath, looping, enableAudioFocus, listener);
        }
    }

    public void stopMusic() {
        if (null != mSkill) {
            mSkill.stopMusicPlay();
        }
    }

    public void speechPlayText(String text) {
        if (mSkill == null) {
            return;
        }
        boolean isPlayTTS = ControlManager.getControlManager().getIsPlayTTS();
        boolean isControlModule = ControlManager.getControlManager().getIsControlModule();
        if (!isPlayTTS && !isControlModule) {
            return;
        }
        try {
            speechPlayText(text, null);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void stopTTSOnly() {
        Log.d(TAG, "stopTTSOnly");
        if (null != mSkill) {
            mSkill.stopTTS();
        }
    }

    public boolean isInListeningState() {
        boolean result = false;
        if (listeningState == STATE_LISTENING) {
            result = true;
        }
        return result;
    }

    public void openSpeechAsrRecognize() {
        if (null != mSkill) {
            mSkill.setRecognizable(true);
        }
    }

    public void openSpeechAsrRecognizeMode() {
        if (null != mSkill) {
            mSkill.setRecognizeMode(true);
        }
    }

    public void closeSpeechAsrRecognize() {
        if (null != mSkill) {
            mSkill.setRecognizable(false);
        }
    }

    public String queryUserSetWakeUpWord() {
        if (mSkill != null) {
            String setWakeUpWord = mSkill.queryUserSetWakeUpWord();
            Log.d(TAG, "queryUserSetWakeUpWord:" + "setWakeUpWord: " + setWakeUpWord);
            return setWakeUpWord;
        }
        return null;
    }

    public int closeCustomizeWakeUpWord() {
        if (mSkill != null) {
            return mSkill.closeCustomizeWakeUpWord();
        }
        return -1;
    }

    public String queryPinYinFromChinese(String chinese) {
        if (mSkill != null) {
            String spell = mSkill.queryPinYinFromChinese(chinese);
            Log.d(TAG, "queryPinYinFromChinese:" + chinese + ", spell: " + spell);
            return spell;
        }
        return null;
    }

    public int getPinYinScore(String pinyin, String separator) {
        if (mSkill != null) {
            int score = mSkill.getPinYinScore(pinyin, separator);
            Log.d(TAG, "getPinYinScore:" + score);
            return score;
        }
        return -1;
    }

    public boolean setCustomizeWakeUpWord(String wakeUpWordChinese, String wakeUpWordSpell, String sperator) {
        if (mSkill != null) {
            int wakeUp = mSkill.setCustomizeWakeUpWord(wakeUpWordChinese, wakeUpWordSpell, sperator);
            Log.d(TAG, "setCustomizeWakeUpWord:" + wakeUpWordSpell + ", wakeUp: " + wakeUp);
            return wakeUp == 0;
        }
        return false;
    }

    public String queryPinYinMappingTable(String pinyin) {
        if (mSkill != null) {
            String spellMapping = mSkill.queryPinYinMappingTable(pinyin);
            Log.d(TAG, "queryPinYinMappingTable:" + "spellMapping: " + spellMapping);
            return spellMapping;
        }
        return null;
    }

    public interface SpeechFinishCallBack {
        void finish();
    }

    public void speechWithFinishCallBack(String text, final SpeechFinishCallBack callBack) {
        try {
            speechPlayText(text, new TextListener() {
                @Override
                public void onStop() {
                    callBack.finish();
                }

                @Override
                public void onComplete() {
                    callBack.finish();
                }

                @Override
                public void onError() {
                    callBack.finish();
                }
            });
        } catch (RemoteException e) {
            e.printStackTrace();
            callBack.finish();
        }
    }

}
