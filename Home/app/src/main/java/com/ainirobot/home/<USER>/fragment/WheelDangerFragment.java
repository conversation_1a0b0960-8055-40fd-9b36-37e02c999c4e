package com.ainirobot.home.ui.fragment;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Point;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.TextView;
import android.widget.Toast;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.view.RobotGlobalActionsDialog;
import com.ainirobot.home.utils.Blur;
import com.ainirobot.home.utils.ResUtil;

public class WheelDangerFragment extends BaseFragment implements View.OnClickListener {

    private static final String TAG = "WheelDangerFragment:Home";
    private static final int CONSTANT_BLUR_RADIUS = 15;
    private RobotGlobalActionsDialog mDropdownBarPasswordDialog ;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @SuppressLint("LongLogTag")
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        Log.i(TAG, "on create view");

        View view = inflater.inflate(R.layout.fragment_wheel_danger, null);
        TextView cancelBtn = view.findViewById(R.id.cancel);
        TextView confirmBtn = view.findViewById(R.id.confirm);
        cancelBtn.setOnClickListener(this);
        confirmBtn.setOnClickListener(this);

        return view;
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.cancel:
            case R.id.confirm:
                ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_WHEEL_OVER_UNLOCK);
                break;
            default:
                break;
        }
    }

    @SuppressLint("LongLogTag")
    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);

    }


    private void showDialogInputPassWord() {
        if (mDropdownBarPasswordDialog != null) {
            mDropdownBarPasswordDialog.dismiss();
        }

        final Point screenSize = new Point();
        ((WindowManager) ApplicationWrapper.getContext().getSystemService(Context.WINDOW_SERVICE)).getDefaultDisplay().getRealSize(screenSize);
        View decorView = getActivity().getWindow().getDecorView();
        decorView.setDrawingCacheQuality(View.DRAWING_CACHE_QUALITY_LOW);
        decorView.setDrawingCacheEnabled(true);
        decorView.buildDrawingCache();
        Bitmap image = decorView.getDrawingCache();
        Bitmap bitmap = Blur.apply(getActivity(), image, CONSTANT_BLUR_RADIUS);
        mDropdownBarPasswordDialog = new RobotGlobalActionsDialog(getContext(), R.style.globalDialogStyle,bitmap, new DialogInterface.OnClickListener() {

            @SuppressLint("LongLogTag")
            @Override
            public void onClick(DialogInterface dialog, int which) {
                // TODO Auto-generated method stub
                if (which == DialogInterface.BUTTON_POSITIVE) {
                    Log.d(TAG, "onClick to unLock ");
                    ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_BLE_PASSWORD_UNLOCKED);
                }
            }
        });
        mDropdownBarPasswordDialog.show();
    }

    @Override
    public void onPause() {
        super.onPause();
    }
}