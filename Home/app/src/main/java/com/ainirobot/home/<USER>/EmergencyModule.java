package com.ainirobot.home.module;

import android.content.Context;
import android.content.Intent;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ElectricDoorManager;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.receiver.BiActiveStateReceiver;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.utils.ToastUtil;

public class EmergencyModule extends BaseModule {

    private static final String TAG = "EmergencyModule:Home";

    private static EmergencyModule sInstance = null;
    private Context mContext;

    public static EmergencyModule getInstance() {
        if (sInstance == null) {
            sInstance = new EmergencyModule();
        }
        return sInstance;
    }

    private EmergencyModule() {
    }

    public void init(Context context) {
        mContext = context;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.d(TAG, "reqId=" + reqId + "; intent=" + intent + "; params=" + params);
        switch (intent) {
            case Definition.REQ_EMERGENCY_PRESS:
                mContext.sendBroadcast(new Intent(BiActiveStateReceiver.ACTION_PRESS_EMERGENCY));
                SkillManager.getInstance().closeSpeechAsrRecognize();
                SkillManager.getInstance().cancleAudioOperation();
                UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_EMERGENCY, null, null);
                releaseElevator();

                SystemApi.getInstance().resetHead(0, null);
                break;
            case Definition.REQ_EMERGENCY_RELEASE:
                mContext.sendBroadcast(new Intent(BiActiveStateReceiver.ACTION_RELEASE_EMERGENCY));
                stop();
                break;
        }
        SystemApi.getInstance().finishModuleParser(reqId, true);
        return false;
    }

    private void releaseElevator() {
        SystemApi.getInstance().releaseElevator(0, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                Log.d(TAG, "releaseElevator result : " + result + ", message : " + message + ", extraData :" + extraData);
            }

            @Override
            public void onError(int errorCode, String errorString, String extraData) throws RemoteException {
                super.onError(errorCode, errorString, extraData);
            }
        });
    }

    @Override
    protected void onStop() {
        super.onStop();
        SkillManager.getInstance().openSpeechAsrRecognize();
        if (ProductInfo.isDeliveryProduct() && ProductInfo.hasElectricDoor()) {
            Log.d(TAG, "onStop: show electric tips!");
            if (!ElectricDoorManager.getInstance().isAllClose()) {
                ToastUtil.showToast(mContext, mContext.getString(R.string.close_electric_door_please));
            }
            ElectricDoorManager.getInstance().release();
        }
    }
}
