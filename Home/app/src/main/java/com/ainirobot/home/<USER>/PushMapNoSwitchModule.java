package com.ainirobot.home.module;

import android.provider.Settings;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.R;
import com.ainirobot.home.bean.PushMapBean;
import com.ainirobot.home.utils.MapUtils;
import com.ainirobot.home.utils.ToastUtil;
import com.google.gson.JsonSyntaxException;


public class PushMapNoSwitchModule extends BaseBackGroundModule {
    private final static String TAG = PushMapNoSwitchModule.class.getSimpleName();
    private static PushMapNoSwitchModule mInstance = new PushMapNoSwitchModule(TAG);

    private PushMapNoSwitchModule(String threadName) {
        super(threadName);
    }

    public static PushMapNoSwitchModule getInstance() {
        return mInstance;
    }

    private PushMapBean pushMapInfo;

    @Override
    protected void onThreadNewSemantics(int reqId, String intent, String text, final String params) {
        Log.i(TAG, "push_map onThreadNewSemantics, intent: " + intent + "params = " + params);

        switch (intent) {
            case Definition
                    .REMOTE_PUSH_MAP_NO_SWITCH:
                try {
                    if (pushMapInfo == null) {
                        pushMapInfo = mGson.fromJson(params, PushMapBean.class);
                        Log.d(TAG, "onNewSemantics: pushMapInfo = " + pushMapInfo.toString());
                    } else {
                        Log.d(TAG, "onNewSemantics: push map is Executing, do nothing");
                        return;
                    }
                } catch (JsonSyntaxException e) {
                    e.printStackTrace();
                }
                if (pushMapInfo != null) {
                    int mapCompatibleVersion = Settings.Global.getInt(ApplicationWrapper.getContext().getContentResolver(),
                            Definition.ROBOT_MAP_COMPATIBLE_VERSION, 0);
                    Log.d(TAG, "mapCompatibleVersion : " + mapCompatibleVersion);
                    if (pushMapInfo.getMapVersion() <= mapCompatibleVersion) {
                        updateStatus(Definition.PUSH_MAP_WAIT_TO_PULL);
                        Log.d(TAG, "download map");
                        downLoadPkg();
                    } else {
                        ToastUtil.showToast(ApplicationWrapper.getContext(), ApplicationWrapper.getContext().getString(R.string.map_not_compatible));
                        Log.e(TAG, "Robot support version is smaller than map version");
                        stopAndUpdateStatus();
                    }
                } else {
                    stopAndUpdateStatus();
                }
                break;

            default:
                break;
        }

        super.onThreadNewSemantics(reqId, intent, text, params);
    }

    private void updateStatus(String state) {
        Log.d(TAG, "updateStatus: state = " + state);
        if (pushMapInfo == null) {
            Log.e(TAG, "updateStatus: error ," + "no Executing push map task");
            return;
        }
        pushMapInfo.setState(state);
        pushMapInfo.setNaviMap(false);
        SystemApi.getInstance().sendStatusReport(Definition.STATUS_PUSH_MAP_PROCESS, mGson.toJson(pushMapInfo));
    }

    private void downLoadPkg() {
        MapUtils.downLoadWholeMapPkg(pushMapInfo, mGson, new CommandListener(){
            @Override
            public void onResult(int result, String message, String extraData) {
                Log.d(TAG, "downLoadPkg:downLoadMapPkg:onResult: result=" + result + ", message=" + message);
                if (message.equals(Definition.SUCCEED)) {
                    updateStatus(Definition.PUSH_MAP_SWITCH_FAIL_OR_NO_SWITCH);
                    stop();
                } else {
                    stopAndUpdateStatus();
                }
            }
        });
    }

    private void stopAndUpdateStatus() {
        updateStatus(Definition.PUSH_MAP_DOWNLOAD_FAIL);
        stop();
    }

    @Override
    protected void onStop() {
        Log.i(TAG, "push_map onStop");
        pushMapInfo = null;
        SystemApi.getInstance().onPushMapNoSwitchModuleFinish();
        super.onStop();
    }
}
