package com.ainirobot.home.bean;

import android.os.Environment;

/**
 * @version V1.0.0
 * @date 2020/7/28 14:19
 */
public class MapInfo implements Cloneable {
    public static final String MAP_PATH = Environment.getExternalStorageDirectory() + "/robot/map/";

    /**
     * 服务端仍以uuid作为全局唯一标记，保持对旧版本兼容。
     * 本地生成mapId作为预留字段，目前只作为数据库索引使用(即本机地图唯一标记)。
     */
    private String mapId;
    private String mapName;
    /**
     * 兼容旧架够版本预留字段
     */
    private String naviMapName;
    /**
     * 1-普通地图
     * 2-高级地图
     */
    private int mapType = MapType.NORMAL;
    /**
     * 0-未使用
     * 1-使用中
     */
    private int useState = UseState.NO_USE;
    private String mapPath = MAP_PATH;
    /**
     * 地图上传云端状态,新建地图活编辑地图之后都需要同步到云端
     * 0-未上传
     * 1-已上传
     */
    private int syncState = SyncState.NOT_UPLOADED;
    /**
     * 0-没有禁行线
     * 1-有禁行线
     */
    private int forbidLine = ForbidLine.NO_FORBID_LINE;
    private String createTime = "";
    private String updateTime = "";
    private String md5;
    private String mapLanguage;
    /**
     * 该地图支持的最大机器人宽度
     */
    private double transit_max_width;
    /**
     * 云端生成
     */
    private String mapUuid = "";
    /**
     * 巡逻路线
     */
    private String patrolRoute;
    /**
     * 结束状态
     * 0-没有地图
     * 1-开始创建
     * 2-充电桩已设置
     * 4-接待点已设置
     * 7-已完成
     */
    private int finishState;
    /**
     * 定位点
     */
    private String poseEstimate;
    /**
     * 是否有Target数据 1表示有 其他表示没有
     */
    private int targetData = TargetDataState.NO_TARGET_DATA;
    /**
     * 是否有MappingTrack数据 1表示有 其他表示没有
     */
    private int trackData = TrackDataState.NO_TRACK_DATA;

    /**
     * 地图版本号
     */
    private int mapVersion;
    /**
     * mapHasVision、mapHasTarget,需要调用{@link #updateNaviType()}方法更新，才能使用
     */
    private boolean mapHasVision;
    private boolean mapHasTarget;

    /**
     * 地图视觉/标识码类型，地图版本1之后。
     * 此信息打包进 mapinfo.json，服务端用此信息做地图版本管理校验，并且push地图时后台发回给机器端使用，
     */
    private int mapVisionType; //[0,1]
    private int mapTargetType; //[0,1,2,3]

    public static class FinishState {
        public final static int STATE_NO_MAP = 0;
        public final static int STATE_CREATE_DONE = 1 << 0;//开始建图成功-1
        public final static int STATE_CHARGE_SET_DONE = 1 << 1;//充电桩设置成功-2
        public final static int STATE_RECEPTION_SET_DONE = 1 << 2;//接待点设置成功-4
        public final static int STATE_COMPLETE_MAP = STATE_CREATE_DONE | STATE_CHARGE_SET_DONE | STATE_RECEPTION_SET_DONE;//完成-7
    }

    public static class SyncState {
        public static final int NOT_UPLOADED = 0;
        public static final int UPLOADED_MAP = 1;
        public static final int UPLOADED_PLACE = 1 << 1;
        public static final int UPLOADED = UPLOADED_MAP | UPLOADED_PLACE;
    }

    public static class MapType {
        public static final int NORMAL = 1;
        public static final int VISION = 2;
        public static final int TARGET_NORMAL = 3;
        public static final int TARGET_CUSTOM_QL = 4;
        public static final int TARGET_CUSTOM_PD = 5;
    }

    public static class ForbidLine {
        public static final int NO_FORBID_LINE = 0;
        public static final int HAS_FORBID_LINE = 1;
    }

    public static class UseState {
        public static final int NO_USE = 0;
        public static final int IN_USE = 1;
    }

    public static class TargetDataState {
        public static final int NO_TARGET_DATA = 0;
        public static final int HAS_TARGET_DATA = 1;
    }

    public static class TrackDataState {
        public static final int NO_TRACK_DATA = 0;
        public static final int HAS_TRACK_DATA = 1;
    }

    public boolean hasVision() {
        return this.mapType == MapType.VISION;
    }

    public String getMapId() {
        return mapId;
    }

    public MapInfo setMapId(String mapId) {
        this.mapId = mapId;
        return this;
    }

    public String getMapName() {
        return mapName;
    }

    public MapInfo setMapName(String mapName) {
        this.mapName = mapName;
        return this;
    }

    public int getMapType() {
        return mapType;
    }

    public MapInfo setMapType(int mapType) {
        this.mapType = mapType;
        return this;
    }

    public int getUseState() {
        return useState;
    }

    public MapInfo setUseState(int useState) {
        this.useState = useState;
        return this;
    }

    public String getMapPath() {
        return mapPath;
    }

    public MapInfo setMapPath(String mapPath) {
        this.mapPath = mapPath;
        return this;
    }

    public int getSyncState() {
        return syncState;
    }

    public MapInfo setSyncState(int syncState) {
        this.syncState = syncState;
        return this;
    }

    public String getCreateTime() {
        return createTime;
    }

    public MapInfo setCreateTime(String createTime) {
        this.createTime = createTime;
        return this;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public MapInfo setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getMd5() {
        return md5;
    }

    public MapInfo setMd5(String md5) {
        this.md5 = md5;
        return this;
    }

    public boolean hasForbidLine() {
        return this.forbidLine == ForbidLine.HAS_FORBID_LINE;
    }

    public int getForbidLine() {
        return this.forbidLine;
    }

    public MapInfo setForbidLine(int forbidLine) {
        this.forbidLine = forbidLine;
        return this;
    }

    public MapInfo setForbidLine(boolean forbidLine) {
        this.forbidLine = forbidLine ? 1 : 0;
        return this;
    }

    public String getMapLanguage() {
        return mapLanguage;
    }

    public MapInfo setMapLanguage(String mapLanguage) {
        this.mapLanguage = mapLanguage;
        return this;
    }

    public double getTransitMaxWidth() {
        return transit_max_width;
    }

    public MapInfo setTransitMaxWidth(double transitMaxWidth) {
        this.transit_max_width = transitMaxWidth;
        return this;
    }

    public String getMapUuid() {
        return mapUuid;
    }

    public MapInfo setMapUuid(String mapUuid) {
        this.mapUuid = mapUuid;
        return this;
    }

    public String getPatrolRoute() {
        return patrolRoute;
    }

    public MapInfo setPatrolRoute(String patrolRoute) {
        this.patrolRoute = patrolRoute;
        return this;
    }

    public int getFinishState() {
        return finishState;
    }

    public MapInfo setFinishState(int finishState) {
        this.finishState = finishState;
        return this;
    }

    public String getPoseEstimate() {
        return poseEstimate;
    }

    public MapInfo setPoseEstimate(String poseEstimate) {
        this.poseEstimate = poseEstimate;
        return this;
    }

    public String getNaviMapName() {
        return naviMapName;
    }

    public MapInfo setNaviMapName(String naviMapName) {
        this.naviMapName = naviMapName;
        return this;
    }

    public int getTargetData() {
        return targetData;
    }

    public MapInfo setTargetData(int targetData) {
        this.targetData = targetData;
        return this;
    }

    public int getTrackData() {
        return trackData;
    }

    public MapInfo setTrackData(int trackData) {
        this.trackData = trackData;
        return this;
    }

    public int getMapVersion() {
        return mapVersion;
    }

    public MapInfo setMapVersion(int mapVersion) {
        this.mapVersion = mapVersion;
        return this;
    }

    @Override
    public String toString() {
        return "MapInfo{" +
                "mapId='" + mapId + '\'' +
                ", mapName='" + mapName + '\'' +
                ", naviMapName='" + naviMapName + '\'' +
                ", mapType=" + mapType +
                ", useState=" + useState +
                ", mapPath='" + mapPath + '\'' +
                ", syncState=" + syncState +
                ", forbidLine=" + forbidLine +
                ", createTime='" + createTime + '\'' +
                ", updateTime='" + updateTime + '\'' +
                ", md5='" + md5 + '\'' +
                ", mapLanguage='" + mapLanguage + '\'' +
                ", transitMaxWidth='" + transit_max_width + '\'' +
                ", mapUuid='" + mapUuid + '\'' +
                ", patrolRoute='" + patrolRoute + '\'' +
                ", finishState='" + finishState + '\'' +
                ", poseEstimate='" + poseEstimate + '\'' +
                ", targetData='" + targetData + '\'' +
                ", trackData='" + trackData + '\'' +
                ", mapVersion='" + mapVersion + '\'' +
                '}';
    }

    @Override
    public MapInfo clone() {
        try {
            return (MapInfo) super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
            return null;
        }
    }

}
