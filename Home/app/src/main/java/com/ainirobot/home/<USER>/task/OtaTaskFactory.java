package com.ainirobot.home.ota.task;

import android.content.Context;
import android.os.Handler;

import com.ainirobot.home.ota.config.OtaConfig;
import com.ainirobot.home.ota.database.DataBaseManager;
import com.ainirobot.home.ota.executor.ThreadPoolManager;
import com.ainirobot.home.ota.network.NetHelper;
import com.ainirobot.home.ota.parser.IParserInterface;
import com.ainirobot.home.ota.service.OtaApiHelper;
import com.ainirobot.home.ota.utils.Preferences;
import com.ainirobot.home.ota.utils.Scp;

/**
 * Ota 工作线程 工厂类
 */
public class OtaTaskFactory {
    private final Context context;
    private final Preferences preferences;
    private final NetHelper netHelper;
    private final OtaApiHelper otaApiHelper;
    private final DataBaseManager dataBaseManager;
    private final Scp scpHelper;
    protected Handler mainHandler;
    protected IParserInterface mUpdateConfigParser;
    protected OtaConfig mOtaConfig;
    protected OtaTaskFactory mOtaTaskFactory;
    protected ThreadPoolManager mThreadPoolManager;

    public OtaTaskFactory(Context context, Preferences preferences, NetHelper netHelper,
                          OtaApiHelper otaApiHelper, DataBaseManager dataBaseManager, Scp scpHelper,
                          Handler mainHandler, IParserInterface iParserInterface, OtaTaskFactory otaTaskFactory,
                          OtaConfig otaConfig, ThreadPoolManager threadPoolManager) {
        this.context = context;
        this.preferences = preferences;
        this.netHelper = netHelper;
        this.otaApiHelper = otaApiHelper;
        this.dataBaseManager = dataBaseManager;
        this.scpHelper = scpHelper;
        this.mainHandler = mainHandler;
        this.mUpdateConfigParser = iParserInterface;
        this.mOtaTaskFactory = otaTaskFactory;
        this.mOtaConfig = otaConfig;
        this.mThreadPoolManager = threadPoolManager;
    }

    public InstallThreadTask createInstallThreadTask() {
        return new InstallThreadTask(context, preferences, netHelper, otaApiHelper, dataBaseManager, scpHelper, mainHandler, mUpdateConfigParser, mOtaTaskFactory, mOtaConfig, mThreadPoolManager);
    }

    public PreInstallThreadTask createPreInstallThreadTask() {
        return new PreInstallThreadTask(context, preferences, netHelper, otaApiHelper, dataBaseManager, scpHelper, mainHandler, mUpdateConfigParser, mOtaTaskFactory, mOtaConfig, mThreadPoolManager);
    }

    public FileCheckTask createFileCheckTask() {
        return new FileCheckTask(context, preferences, netHelper, otaApiHelper, dataBaseManager, scpHelper, mainHandler, mUpdateConfigParser, mOtaTaskFactory, mOtaConfig, mThreadPoolManager);
    }
}