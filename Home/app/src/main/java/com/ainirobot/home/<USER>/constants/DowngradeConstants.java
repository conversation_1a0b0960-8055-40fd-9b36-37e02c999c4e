package com.ainirobot.home.ota.constants;

public class DowngradeConstants {

    /**
     * DowngradeService intent key
     */
    public static final String KEY_START_COMMAND = "start_command";
    public static final String KEY_START_MODE = "start_mode"; // 降级启动模式
    public static final String KEY_START_COMMAND_PARAMS = "is_user_touch"; // 是否用户主动降级
    public static final String KEY_START_COMMAND_PATH = "file_path";

    /**
     * DowngradeService intent command type
     */
    public static final int START_COMMAND_NEW_VERSION_CHECKING = 101;
    public static final int START_COMMAND_DOWNLOAD = 102;
    public static final int START_COMMAND_CHECKING = 103;
    public static final int START_COMMAND_INSTALL = 104;
    public static final int START_COMMAND_STOP_SERVICE = 105;
    public static final int START_COMMAND_DOWNLOAD_PAUSE = 106;
    public static final int START_COMMAND_DOWNLOAD_CONTINUE = 107;
    public static final int START_COMMAND_SILENT_INSTALL = 108;
    public static final int START_COMMAND_FILE_CHECKING = 109;
    public static final int START_COMMAND_FILE_CHECKING_STOP = 110;
    public static final int START_COMMAND_REAL_INSTALL_ROM = 111;

    /**
     * 业务层广播
     */
    public static final String ACTION_NEW_VERSION_FOUND = "com.ainirobot.moduleapp.ota.user_touch";

    /**
     * downgrade message
     */
    public static final int MSG_BASE = 1;
    public static final int MSG_NO_UPDATE = MSG_BASE;
    public static final int MSG_NOT_EXIST = MSG_BASE + 1;
    public static final int MSG_UNZIP_FAILED = MSG_BASE + 2;
    public static final int MSG_MD5_CHECK_FAILED = MSG_BASE + 3;
    public static final int MSG_FILE_DOWNLOAD_FAILED = MSG_BASE + 4;
    public static final int MSG_SUB_FILE_MD5_CHECK_FAILED = MSG_BASE + 5;
    // 全包下载成功
    public static final int MSG_FILE_DOWNLOAD_DONE = MSG_BASE + 6;
    public static final int MSG_INTERNAL_ERROR = MSG_BASE + 7;
    public static final int MSG_UPDATE_SUCCESS = MSG_BASE + 8;
    public static final int MSG_DOWNLOAD_SUCCESS = MSG_BASE + 9;
    public static final int MSG_UPDATE_FAILED = MSG_BASE + 10;
    public static final int MSG_UPDATE_UPDATING = MSG_BASE + 11;

    /**
     * downgrade manager update status description
     */
    public static final String UPLOAD_STATUS_DESC_DOWNLOAD_SUCCESS = "full package download success";

    public enum InstallStatus {
        IDLE,
        INSTALLING,
        ROLLBACK_INSTALLING
    }
}
