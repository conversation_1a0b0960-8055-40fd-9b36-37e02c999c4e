package com.ainirobot.home.ui.fragment;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Point;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.view.RobotGlobalActionsDialog;
import com.ainirobot.home.utils.Blur;
import com.ainirobot.home.utils.ResUtil;

public class BleDistanceFragment extends BaseFragment implements View.OnClickListener {

    private static final String TAG = "BleDistanceFragment:Home";
    private static final int CONSTANT_BLUR_RADIUS = 15;
    private TextView cancelBleBtn;
    private TextView mTitleDes;
    private RobotGlobalActionsDialog mDropdownBarPasswordDialog ;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        Log.i(TAG, "on create view");

        int mOrientation = ApplicationWrapper.getContext().getResources().getConfiguration().orientation;

        View view = inflater.inflate(R.layout.fragment_ble_close, null);
        mTitleDes = view.findViewById(R.id.ble_title_des);
        cancelBleBtn = view.findViewById(R.id.ble_cancel);
        cancelBleBtn.setOnClickListener(this);
        return view;
    }

    @Override
    public void onStart() {
        super.onStart();
        updateTitleDes();
    }

    @SuppressLint("LongLogTag")
    private void updateTitleDes() {
        String corpName = RobotSettings.getCorpName(ApplicationWrapper.getContext());
        Log.d(TAG, "onStart corpName :"+corpName);

        mTitleDes.setText(ResUtil.getString(R.string.ble_robot_locked_title_des,corpName));
        String title = mTitleDes.getText().toString();
        int index = title.indexOf(corpName);
        SpannableStringBuilder builder = new SpannableStringBuilder(title);
        ForegroundColorSpan whiteSpan = new ForegroundColorSpan(Color.WHITE);
        builder.setSpan(whiteSpan, index, index+corpName.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
        mTitleDes.setText(builder);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.ble_cancel:
                // 需要用户输入密码
                int debug = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_BLE_DEBUG);
                if (debug == 1) {
                    //Settings 中debug 开关开启后,无需密码,直接解锁
                    ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_BLE_PASSWORD_UNLOCKED);
                } else {
                    showDialogInputPassWord();
                }
                break;
            default:
                break;
        }
    }

    @SuppressLint("LongLogTag")
    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
        Log.d(TAG, "onMessage : " + type + ", " + message);

        switch (type) {
            case CURRENT_BLE_NEAR_DANGER:
                Toast.makeText(getActivity(),getString(R.string.ble_tip_leave_dangerous_place),Toast.LENGTH_SHORT).show();
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
    }




    private void showDialogInputPassWord() {
        if (mDropdownBarPasswordDialog != null) {
            mDropdownBarPasswordDialog.dismiss();
        }

        final Point screenSize = new Point();
        ((WindowManager) ApplicationWrapper.getContext().getSystemService(Context.WINDOW_SERVICE)).getDefaultDisplay().getRealSize(screenSize);
        View decorView = getActivity().getWindow().getDecorView();
        decorView.setDrawingCacheQuality(View.DRAWING_CACHE_QUALITY_LOW);
        decorView.setDrawingCacheEnabled(true);
        decorView.buildDrawingCache();
        Bitmap image = decorView.getDrawingCache();
        Bitmap bitmap = Blur.apply(getActivity(), image, CONSTANT_BLUR_RADIUS);
        mDropdownBarPasswordDialog = new RobotGlobalActionsDialog(getContext(), R.style.globalDialogStyle,bitmap, new DialogInterface.OnClickListener() {

            @SuppressLint("LongLogTag")
            @Override
            public void onClick(DialogInterface dialog, int which) {
                // TODO Auto-generated method stub
                if (which == DialogInterface.BUTTON_POSITIVE) {
                    Log.d(TAG, "onClick to unLock ");
                    ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_BLE_PASSWORD_UNLOCKED);
                }
            }
        });
        mDropdownBarPasswordDialog.show();
    }

    @Override
    public void onPause() {
        super.onPause();
    }
}