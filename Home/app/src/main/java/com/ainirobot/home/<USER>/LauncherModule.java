/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.home.module;

import android.util.Log;

import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.bi.BiModuleChangeNotifier;
import com.ainirobot.home.ui.UIController;

public class LauncherModule extends BaseModule {
    private static final String TAG = "LauncherModule:Home";

    private static LauncherModule mIntance = null;

    private LauncherModule() {
    }

    public static LauncherModule getInstance() {
        if (null == mIntance) {
            mIntance = new LauncherModule();
        }
        return mIntance;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.d(TAG, "intent:" + intent + ", params:" + params);

        switch (intent) {
            case ModuleDef.REQ_WAKEUP:
//                Context context = ApplicationWrapper.getContext();
//                ComponentName topComponent = SystemUtils.getActivityTop(context);
//                if (topComponent != null &&
//                        topComponent.getPackageName().equals(context.getPackageName())) {
//                    ComponentName componentName = UIController.getInstance().getLastApp();
//                    if (componentName == null) {
//                        Log.d(TAG, "last component is null");
//                        //UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_LAUNCHER, null, null);
//                    } else {
//                       // UIController.getInstance().moveToBack();
//                    }
//
//                    UIController.getInstance().moveToBack();
//                } else {
//                    Log.d(TAG, "current package :"
//                            + (topComponent == null ? null : topComponent.getPackageName()));
//                    UIController.getInstance().setLastApp(null);
//                }


                BiModuleChangeNotifier.resetCurrentModule();
                UIController.getInstance().moveToBack();
                break;
            default:
                break;
        }

        return true;
    }
}
