package com.ainirobot.home.ui.view;

import android.app.AlertDialog;
import android.content.Context;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.util.Log;
import android.view.Display;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.home.R;

public class ResetErrorDoalog extends AlertDialog {

    private static final String TAG = "ResetErrorDoalog";
    private Context context;
    private TextView mTvReboot;
    private int mCountTime = 61;
    private CountDownTimer mTimer;
    private TextView mTvFailReason;

    public ResetErrorDoalog(Context context) {
        super(context);
        this.context = context;
    }

    protected ResetErrorDoalog(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    protected ResetErrorDoalog(Context context, int themeResId) {
        super(context, themeResId);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        View view = LayoutInflater.from(context).inflate(R.layout.layout_reset_error, null);
        setContentView(view);
        Window win = getWindow();
        win.setBackgroundDrawableResource(R.color.alpha_70);
        WindowManager m = win.getWindowManager();
        Display d = m.getDefaultDisplay();
        WindowManager.LayoutParams p = getWindow().getAttributes();
        p.height = d.getHeight() + 300;
        p.width = d.getWidth();
        getWindow().setAttributes(p);
        WindowManager.LayoutParams lp = win.getAttributes();
        lp.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
        lp.gravity = Gravity.CENTER;
        setCanceledOnTouchOutside(false);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
        initView();
    }

    private void initView() {
        mTvFailReason = (TextView) findViewById(R.id.tv_fail_reason);
        mTvReboot = (TextView) findViewById(R.id.tv_reboot);
        findViewById(R.id.tv_reboot).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (isShowing()) {
                    dismissResetErrorDialog();
                    SystemApi.getInstance().canRobotReboot(0,null);
                }
            }
        });
        mTimer = new CountDownTimer(mCountTime * 1000, 1000) {
            @Override
            public void onTick(long l) {
                mTvReboot.setText(context.getString(R.string.reboot, l / 1000 + ""));
            }

            @Override
            public void onFinish() {
                dismissResetErrorDialog();
                SystemApi.getInstance().canRobotReboot(0,null);
            }
        };
        mTimer.start();
    }

    public void setFailReason(String failReason){
        mTvFailReason.setText(failReason);
    }

    public void showResetErrorDialog() {
        this.show();
    }

    public void dismissResetErrorDialog() {
        if (isShowing()) {
            this.dismiss();
            mTimer.cancel();
        }
    }
}




