package com.ainirobot.home.module;

import android.content.Context;
import android.os.Bundle;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.bean.MultiFloorInfo;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.AutoChargeBean;
import com.ainirobot.coreservice.client.actionbean.PlaceBean;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.utils.ZipUtils;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.bi.BiGbCharging;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.ui.UIController;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Timer;
import java.util.TimerTask;

public class SetChargePileModule extends BaseModule {

    private static final String TAG = SetChargePileModule.class.getSimpleName();
    private Context mContext;
    private int mReqId = 0;
    private State mCurState = State.IDLE;
    private Timer mDelayTimer;
//    private int mSetPileType = ModuleDef.SET_PILE_FROM_CORE;
    private volatile boolean mCharging = false;
    //for Bi
    private BiGbCharging mBiGbCharging = new BiGbCharging();

    private final static int CHARGING_ENVIRONMENT_INVALID_VALUE = -1;
    private int mPreChargingEnvironment = CHARGING_ENVIRONMENT_INVALID_VALUE;
    private int mChargingEnvironment = CHARGING_ENVIRONMENT_INVALID_VALUE;
    private boolean isSetChargingEnvironment = false;

    private static class SingletonHolder {
        private static final SetChargePileModule mInstance = new SetChargePileModule();
    }

    public static SetChargePileModule getInstance() {
        return SetChargePileModule.SingletonHolder.mInstance;
    }

    public enum State {
        IDLE,
        SET_PILE_UI,
        SET_PILE_ACTION,
        WECHAT_CHARGE_PILE
    }

    public void init(Context context) {
        mContext = context;
    }

    private int getChargingEnvironment(String params) {
        if (TextUtils.isEmpty(params)) {
            return -1;
        }
        try {
            JSONObject json = new JSONObject(params);
            return json.getInt(Definition.SET_CHARGING_ENVIRONMENT);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return -1;
    }

    private void setChargingEnvironmentToSettings(int chargingEnvironment) {
        RobotSettingApi.getInstance().setRobotInt(
                Definition.ROBOT_SETTINGS_CHARGING_ENVIRONMENT, chargingEnvironment);
    }

    private int getChargingEnvironmentFromSettings() {
        return RobotSettingApi.getInstance().
                getRobotInt(Definition.ROBOT_SETTINGS_CHARGING_ENVIRONMENT);
    }
    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.d(TAG, "onNewSemantics intent= " + intent + ", params = " + params +
                ", status = " + mCurState + ", mCharging = " + mCharging + ", reqId = " + reqId);
        mChargingEnvironment = getChargingEnvironment(params);
        isSetChargingEnvironment = mChargingEnvironment != CHARGING_ENVIRONMENT_INVALID_VALUE;
        if (!isSetChargingEnvironment) {
            chargeBi(params);
        }

        mCharging = ControlManager.getControlManager().getSystemStatusManager().getIsCharging();
        if (mCurState != State.IDLE){
            SystemApi.getInstance().finishModuleParser(reqId, true);
            return false;
        }
        mReqId = reqId;

        switch (intent) {
            case Definition.REQ_SET_CHARGE_PILE:
                SkillManager.getInstance().closeSpeechAsrRecognize();
                startSetPile();
                break;
            case Definition.WECHAT_SET_CHARGING_PILE:
                mCurState = State.WECHAT_CHARGE_PILE;
                if (mCharging) {
                    queryWeChatRobotEstimate();
                }
                break;
            default:
                Log.d(TAG, "NO intent match");
                break;
        }
        return true;
    }


    private void startSetPile() {
        Log.d(TAG, "start set pile, mCurState = " + mCurState);
        mCurState = State.SET_PILE_UI;
//        mSetPileType = type;
        Bundle bundle = new Bundle();
//        if (type == ModuleDef.SET_PILE_FROM_CHARGE) {
//            voice(mContext.getString(R.string.pile_reset));
//        }
//        bundle.putInt(ModuleDef.SET_PILE_TYPE, type);
        int startType = ModuleDef.SET_PILE_STATUS_START;
        if (mCharging) {
//            startType = ModuleDef.SET_PILE_STATUS_CHARGING;
            checkEstimate();
        }
        bundle.putInt(ModuleDef.SET_PILE_START_TYPE, startType);
        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_CHARGE_PILE, bundle, null);
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
        switch (type) {
            case ModuleDef.LOCAL_MESSAGE_ESTIMATE:
                checkEstimate();
                break;
            case ModuleDef.LOCAL_MESSAGE_STOP_SET_PILE:
                switch (mCurState) {
                    case SET_PILE_ACTION:
                        SystemApi.getInstance().stopSetChargePileAction(mReqId);
                        break;
                    case SET_PILE_UI:
                        finishSetPile(0);
                }
                break;
            case ModuleDef.LOCAL_MESSAGE_CHARGE_START:
                batteryChanged(true);
                break;
            case ModuleDef.LOCAL_MESSAGE_CHARGE_END:
                batteryChanged(false);
                break;

        }
    }


    /**
     * query robot estimate is success , or can't set pose or navigate.
     */
    private void checkEstimate() {
        Log.d(TAG, "checkEstimate start ------- mCurState : " + mCurState);
        SystemApi.getInstance().isRobotEstimate(0, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                super.onResult(result, message);
                Log.d(TAG, "isRobotEstimate result : " + result + ", msg :" + message);
                switch (result) {
                    case Definition.RESULT_OK:
                        if ("true".equals(message)) {
                            switch (mCurState) {
                                case SET_PILE_UI:
                                    setChargePose();
                                    break;
//                                case START_CHARGE_UI:
//                                    getChargePlace();
//                                    break;
                            }
                        } else {
                            estimateFailed();
                        }
                        break;
                    case Definition.RESULT_FAILURE:
                    case Definition.RESULT_STOP:
                        estimateFailed();
                        break;
                }
            }
        });
    }

    private void estimateFailed() {
        voice(ApplicationWrapper.getContext().getResources().getString(R.string.charge_need_estimate_first));
        switch (mCurState) {
            case SET_PILE_UI:
                UIController.getInstance().sendMessageToFragment(
                        UIController.MESSAGE_TYPE.SET_PILE_STATUS, ModuleDef.SET_PILE_STATUS_FAIL + "");
                finishSetPile(1500);
                break;
//            case START_CHARGE_UI:
//                finishAutoCharge(1500);
//                break;
        }
    }

    // 多个特殊点位后，设置特换充电桩替换优先级为0的。这时候需要提前获取充电桩优先级为0的名称。调用getPlacesByType方法。
    private void setChargePose() {
        SystemApi.getInstance().getPlacesByType(mReqId, Definition.CHARGING_POLE_TYPE, new CommandListener(){
            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                Log.d(TAG, "setChargePose onResult result: " + result + " message: " + message);
                if (result == Definition.RESULT_OK && !TextUtils.isEmpty(message) && !"timeout".equals(message)) {
                    try {
                        Gson gson = new Gson();
                        message = ZipUtils.unzipMapData(gson, message);
                        List<PlaceBean> mPlaces = gson.fromJson(message, new TypeToken<List<PlaceBean>>() {}.getType());
                        if (!mPlaces.isEmpty() && mPlaces.get(0) != null && mPlaces.get(0).getPlaceName() != null &&
                            mPlaces.get(0).getPriority() == Definition.SPECIAL_PLACE_HIGH_PRIORITY) {
                            Log.d(TAG, "name: " + mPlaces.get(0).getPlaceName());
                            setStartChargePose(mPlaces.get(0).getPlaceName());
                        } else {
                            setStartChargePose(Definition.START_CHARGE_PILE_POSE);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else {
                    setStartChargePose(Definition.START_CHARGE_PILE_POSE);
                }
            }

            @Override
            public void onError(int errorCode, String errorString, String extraData) throws RemoteException {
                super.onError(errorCode, errorString, extraData);
                Log.d(TAG, "setChargePose onError errorCode: " + errorCode + " errorString: " + errorString);
                setStartChargePose(Definition.START_CHARGE_PILE_POSE);
            }
        });
    }

    /**
     * set_charge_pile
     */
    private void setStartChargePose(String placeName) {
        Log.d(TAG, "setStartChargePose startAction , mCurState = " + mCurState);
        mCurState = State.SET_PILE_ACTION;
//        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.SET_PILE_STATUS,
//                ModuleDef.SET_PILE_STATUS_SETTING + "");

        if (isSetChargingEnvironment) {
            mPreChargingEnvironment = getChargingEnvironmentFromSettings();
            setChargingEnvironmentToSettings(mChargingEnvironment);
        }

        SystemApi.getInstance().setStartChargePoseAction(mReqId, 0,
                AutoChargeBean.CHARGE_MODE_NORMAL,
                "",
                placeName, Definition.CHARGING_POLE_TYPE, Definition.SPECIAL_PLACE_HIGH_PRIORITY,
                Locale.SIMPLIFIED_CHINESE.toString(), new ActionListener() {
                    @Override
                    public void onResult(final int status, final String responseString) throws RemoteException {
                        super.onResult(status, responseString);
                        Log.d(TAG, "setStartChargePose status = " + status + " , response = " + responseString);
                        if (Definition.RESULT_OK == status) {
                            voice(mContext.getString(R.string.charge_pile_success));
                            if (!isSetChargingEnvironment) {
                                RobotSettingApi.getInstance().setRobotInt(Definition.CHARGING_PILE_CONFIGURED,
                                        Definition.ROBOT_SETTING_ENABLE);
                            }

                            UIController.getInstance().sendMessageToFragment(
                                    UIController.MESSAGE_TYPE.SET_PILE_STATUS, ModuleDef.SET_PILE_STATUS_SUCCESS + "");
                            postSetPlaceToServer();
                            finishSetPile(3000);
                        } else {
                            voice(mContext.getString(R.string.charge_pile_fail));

                            if (isSetChargingEnvironment) {
                                RobotSettingApi.getInstance().setRobotInt(
                                        Definition.ROBOT_SETTINGS_CHARGING_ENVIRONMENT,
                                        mPreChargingEnvironment);
                            }

                            UIController.getInstance().sendMessageToFragment(
                                    UIController.MESSAGE_TYPE.SET_PILE_STATUS, ModuleDef.SET_PILE_STATUS_FAIL + "");
                            finishSetPile(1500);
                        }
                    }

                    @Override
                    public void onStatusUpdate(int status, String data) throws RemoteException {
                        super.onStatusUpdate(status, data);
                    }

                    @Override
                    public void onError(int errorCode, String errorString) throws RemoteException {
                        super.onError(errorCode, errorString);
                        Log.d(TAG, "setStartChargePoseAction onError code = " + errorCode + ", msg = " + errorString);
                        voice(mContext.getString(R.string.charge_pile_fail));
                        if (isSetChargingEnvironment) {
                            RobotSettingApi.getInstance().setRobotInt(
                                    Definition.ROBOT_SETTINGS_CHARGING_ENVIRONMENT, mPreChargingEnvironment);
                        }
                        UIController.getInstance().sendMessageToFragment(
                                UIController.MESSAGE_TYPE.SET_PILE_STATUS, ModuleDef.SET_PILE_STATUS_FAIL + "");
                        finishSetPile(1500);
                    }
                });
    }

    private void finishSetPile(int delayTime) {
        Log.d(TAG, "finish set pile, mCurState = " + mCurState);
        if (mCurState.equals(State.IDLE))
            return;
        releaseDelay(delayTime);
    }

    private void batteryChanged(boolean isCharging) {
        Log.d(TAG, "battery changed:" + isCharging + ",current state:" + mCurState);
        mCharging = isCharging;
        if (isCharging) {
            switch (mCurState) {
                case SET_PILE_UI:
//                    UIController.getInstance().sendMessageToFragment(
//                            UIController.MESSAGE_TYPE.SET_PILE_STATUS, ModuleDef.SET_PILE_STATUS_CHARGING + "");
                    checkEstimate();
                    break;
                case WECHAT_CHARGE_PILE:
                    queryWeChatRobotEstimate();
                    break;
            }
        } else {
            switch (mCurState) {
                case SET_PILE_UI:
                    UIController.getInstance().sendMessageToFragment(
                            UIController.MESSAGE_TYPE.SET_PILE_STATUS, ModuleDef.SET_PILE_STATUS_START + "");
                    break;
            }
        }

    }


    private void voice(String tts) {
        Log.d(TAG, "voice : " + tts);
        SkillManager.getInstance().speechPlayText(tts);
    }

    /**
     * query robot estimate is success , or can't set pose or navigate.
     */
    private void queryWeChatRobotEstimate() {
        SystemApi.getInstance().isRobotEstimate(mReqId, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                super.onResult(result, message);
                Log.d(TAG, "isRobotEstimate result : " + result + ", msg :" + message);
                switch (result) {
                    case Definition.RESULT_OK:
                        if ("true".equals(message)) {
                            setWeChatChargingPile();
                        } else {
                            voice(ApplicationWrapper.getContext().getResources().getString(R.string.charge_need_estimate_first));
                            SystemApi.getInstance().remoteChargePile(mReqId, "finish", message, false, null);
                            stop();
                        }
                        break;
                    case Definition.RESULT_FAILURE:
                    case Definition.RESULT_STOP:
                        voice(ApplicationWrapper.getContext().getResources().getString(R.string.charge_need_estimate_first));
                        SystemApi.getInstance().remoteChargePile(mReqId, "finish", message, false, null);
                        stop();
                        break;
                }
            }
        });
    }

    private void releaseDelay(long time) {
        Log.d(TAG, "release delay, status:" + mCurState);
        synchronized (this) {
            mDelayTimer = new Timer();
            mDelayTimer.schedule(new TimerTask() {
                @Override
                public void run() {
                    stop();
                    cancel();
                }
            }, time);
        }
    }

    private void cancelDelayTimer() {
        synchronized (this) {
            if (mDelayTimer != null) {
                mDelayTimer.cancel();
                mDelayTimer = null;
            }
        }
    }


    @Override
    protected void onStop() {
        SkillManager.getInstance().openSpeechAsrRecognize();
        cancelDelayTimer();
        mCharging = false;
        Log.d(TAG, "notify to CoreService onSetChargePileFinished");
        SystemApi.getInstance().onSetChargePileFinished();
        stopMyAction();
        mCurState = State.IDLE;

        mPreChargingEnvironment = CHARGING_ENVIRONMENT_INVALID_VALUE;
        mChargingEnvironment = CHARGING_ENVIRONMENT_INVALID_VALUE;
        isSetChargingEnvironment = false;
        release(mReqId, RESULT_OK, null);
        Log.d(TAG, "ChargeModule finish and release ==========");
    }

    private void stopMyAction() {
        if (mCurState == State.SET_PILE_ACTION) {
            SystemApi.getInstance().stopSetChargePileAction(mReqId);
            Log.d(TAG, "stopModule and stopSetChargePileAction");
        }
    }

    //------------------------- WeChat set charge pile section ---------------------- //

    /**
     * set WeChatChargingPile 设置充电桩
     */
    private void setWeChatChargingPile() {

        SystemApi.getInstance().setStartChargePoseAction(mReqId, 0, new ActionListener() {
            @Override
            public void onResult(int status, String responseString) throws RemoteException {
                super.onResult(status, responseString);
                Log.d(TAG, "status = " + status + ", resString = " + responseString);
                Log.d(TAG, "onWeChatSetChargePileFinished");
                if (Definition.RESULT_OK == status) {
                    Log.d(TAG, "setStartChargePoseAction success ");
                    RobotSettingApi.getInstance().setRobotInt(Definition.CHARGING_PILE_CONFIGURED,
                            Definition.ROBOT_SETTING_ENABLE);
                    voice(mContext.getString(R.string.charge_pile_success_wc));
                    SystemApi.getInstance().getPlace(mReqId, Definition.START_BACK_CHARGE_POSE,
                            new CommandListener() {

                                @Override
                                public void onResult(int result, String message) {
                                    super.onResult(result, message);
                                    Log.d(TAG, "get START_BACK_CHARGE_POSE = " + message);
                                    SystemApi.getInstance().remoteChargePile(mReqId, "finish", message,
                                            true, null);

                                }
                            });
                } else {
                    Log.d(TAG, "setStartChargePoseAction fail ");
                    voice(mContext.getString(R.string.charge_pile_fail));
                    // report to wx , set pile fail
                    SystemApi.getInstance().remoteChargePile(mReqId, "finish", responseString,
                            false, null);
                }
                stop();
            }

            @Override
            public void onStatusUpdate(int status, String data) throws RemoteException {
                super.onStatusUpdate(status, data);
            }

            @Override
            public void onError(int errorCode, String errorString) throws RemoteException {
                super.onError(errorCode, errorString);
                Log.e(TAG, "setStartChargePoseAction onError code = " + errorCode + " , " +
                        "errorStr = " + errorString);
                voice(mContext.getString(R.string.charge_pile_fail));
                SystemApi.getInstance().onWeChatSetChargePileFinished();
                SystemApi.getInstance().remoteChargePile(mReqId, "finish", errorString,
                        false, null);
                stop();
            }
        });
    }

    /**
     * get place pose and report to server the pose .
     */
    private void postSetPlaceToServer() {
        SystemApi.getInstance().postSetPlaceToServer(mReqId, Definition.START_CHARGE_PILE_POSE);
        SystemApi.getInstance().postSetPlaceToServer(mReqId, Definition.START_BACK_CHARGE_POSE);
    }

    //------------------------- Bi report -------------------------------- //

    private void chargeBi(String params) {
        if (TextUtils.isEmpty(params)) {
            return;
        }

        if (mCharging) {
            return;
        }
        Log.d(TAG, "chargeBi() params: " + params);
        try {
            JSONObject json = new JSONObject(params);
            String reason = json.has("reason") ? json.getString("reason") : "";
            if (!TextUtils.isEmpty(reason)) {
                mBiGbCharging.charging().occur(reason).report();
            }
        } catch (JSONException e) {
            Log.w(TAG, Log.getStackTraceString(e));
        }
    }
}
