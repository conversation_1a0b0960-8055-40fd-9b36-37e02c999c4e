package com.ainirobot.home.fallback;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;
import android.view.WindowManager;

import com.ainirobot.home.fallback.blackbug.BlackBugCheck;

import java.io.DataInputStream;
import java.io.IOException;
import java.io.InputStream;

public class Screencap {
    private static final String TAG = BlackBugCheck.class.getSimpleName();
    private int[] mColors = null;
    private int mScreenWidth;
    private int mScreenHeight;
    private int mDeepth = 0;

    public boolean init(final Context context) {
        boolean ret = false;
        try {
            WindowManager winManager = (WindowManager) context
                    .getSystemService(Context.WINDOW_SERVICE);
            DisplayMetrics dm = new DisplayMetrics();
            Display display = winManager.getDefaultDisplay();
            display.getRealMetrics(dm);
            mScreenWidth = dm.widthPixels;
            mScreenHeight = dm.heightPixels;
            int pixelformat = display.getPixelFormat();
            PixelFormat localPixelFormat1 = new PixelFormat();
            PixelFormat.getPixelFormatInfo(pixelformat, localPixelFormat1);
            mDeepth = localPixelFormat1.bytesPerPixel;
            mColors = new int[mScreenHeight * mScreenWidth];
            ret = true;

        } catch (Exception e) {
            Log.e(TAG, "init Exception: ", e);
        }

        return ret;
    }


    public Bitmap screencap() {
        Log.d(TAG, "screencap");
        InputStream inputStream = null;
        DataInputStream dataInputStream = null;
        Bitmap bitmap = null;
        Process process = null;
        try {
            process = Runtime.getRuntime().exec("screencap");
            inputStream = process.getInputStream();
            byte[] piex = Utils.readStream(inputStream);

            Log.d(TAG, "screencap complete");

            int r = 0, g = 0, b = 0, a = 0;
            for (int j = 0; j < mScreenWidth * mScreenHeight * mDeepth; j += mDeepth) {
                r = piex[j + 0] & 0xff;
                g = piex[j + 1] & 0xff;
                b = piex[j + 2] & 0xff;
                a = piex[j + 3] & 0xff;
                mColors[j / mDeepth] = Color.argb(a, r, g, b);
            }

            bitmap = Bitmap.createBitmap(mColors, mScreenWidth, mScreenHeight,
                    Bitmap.Config.ARGB_8888);

            return bitmap;
        } catch (IOException e) {
            e.printStackTrace();
            Log.d(TAG, e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, e.getMessage());
        } finally {
            try {
                if (inputStream != null)
                    inputStream.close();
                if (dataInputStream != null)
                    dataInputStream.close();
                Utils.destoryProcess(process);
            } catch (Exception e) {
                e.printStackTrace();
                Log.d(TAG, e.getMessage());
            } finally {

            }
            Log.d(TAG, "screencap covbitmap: " + bitmap);
        }
        return bitmap;
    }
}
