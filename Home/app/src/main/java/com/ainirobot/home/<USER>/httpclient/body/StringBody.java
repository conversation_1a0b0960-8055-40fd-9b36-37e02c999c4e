/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.home.ota.httpclient.body;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

public class StringBody implements RequestBody {
    private static final String CHARSET = "UTF-8";
    private final String mContentType;
    private final byte[] mContent;
    private String mCharset = CHARSET;

    public StringBody(String content, String contentType, String charset) {
        if (contentType != null) {
            this.mContentType = contentType + "; charset=" + charset;
        }else{
            this.mContentType = null;
        }

        content = (content == null) ? "" : content;
        this.mContent = content.getBytes();
        this.mCharset = charset;
    }

    public StringBody(String content, String contentType) {
        this(content, contentType, CHARSET);
    }

    public StringBody(String content) {
        this(content, null);
    }

    public String getContentType() {
        return mContentType;
    }

    @Override
    public long getContentLength() {
        return mContent.length;
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        final InputStream in = new ByteArrayInputStream(this.mContent);
        final byte[] buffer = new byte[4096];
        int l;
        while ((l = in.read(buffer)) != -1) {
            out.write(buffer, 0, l);
        }
        out.flush();
    }
}
