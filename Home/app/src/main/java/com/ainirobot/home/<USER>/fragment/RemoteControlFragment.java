package com.ainirobot.home.ui.fragment;

import android.os.Bundle;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.ainirobot.home.R;

public class RemoteControlFragment extends BaseFragment {

    private static final String TAG = "RemoteControl:Home";

    private TextView navigation_tip;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        Log.i(TAG, "on create view");
        View view = inflater.inflate(R.layout.fragment_remote_control, null);
        navigation_tip = (TextView) view.findViewById(R.id.navigation_tip);
        Bundle bundle = getArguments();
        if (bundle != null) {
            String positionName = bundle.getString("name");
            if (!TextUtils.isEmpty(positionName)) {
                String prefix = getString(R.string.remote_control_navigation);
                navigation_tip.setText(String.format("%s %s", prefix, positionName));
            }
        }
        return view;
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onStart() {
        super.onStart();
        Log.i(TAG, "onstart");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.d(TAG, "onDestroyView");
    }
}
