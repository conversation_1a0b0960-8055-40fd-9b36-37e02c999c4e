package com.ainirobot.home.control;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.Message;
import android.util.Log;

import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;

public class WakeUpWordManager {
    private static final String TAG = WakeUpWordManager.class.getSimpleName() + ":home";

    private WakeUpWordManager.WakeUpWordReceiver mBatteryReceiver;

    public void startReceiver() {
        IntentFilter batteryFilter = new IntentFilter(ModuleDef.CUSTOM_WAKEUP_WORD);
        mBatteryReceiver = new WakeUpWordManager.WakeUpWordReceiver();
        ApplicationWrapper.getContext().registerReceiver(mBatteryReceiver, batteryFilter);
    }

    public void unRegiesterReceiver() {
        if (mBatteryReceiver != null) {
            ApplicationWrapper.getContext().unregisterReceiver(mBatteryReceiver);
        }
    }

    static class WakeUpWordReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent == null || intent.getAction() == null) {
                return;
            }

            Log.d(TAG, " WakeUpWordReceiver " + intent.getAction());
            switch (intent.getAction()) {
                case ModuleDef.CUSTOM_WAKEUP_WORD:
                    Bundle bundle = new Bundle();
                    bundle.putString(ModuleDef.MSG_BUNDLE_INTENT, ModuleDef.INTENT_CUSTOM_WAKEUP_WORD);
                    bundle.putInt(ModuleDef.MSG_BUNDLE_ID, ModuleDef.FEATURE_SETTING_MODULE);
                    bundle.putString(ModuleDef.MSG_BUNDLE_TEXT, ModuleDef.INTENT_CUSTOM_WAKEUP_WORD);
                    bundle.putString(ModuleDef.MSG_BUNDLE_PARAM, "");
                    Message msg = Message.obtain();
                    msg.what = ModuleDef.MSG_NEW_REQUEST;
                    msg.setData(bundle);
                    ControlManager.getControlManager().getMainHandler().sendMessage(msg);
                    break;
            }
        }
    }
}
