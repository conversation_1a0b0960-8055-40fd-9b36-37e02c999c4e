package com.ainirobot.home.bi;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

/**
 * sdcardInfo bi report
 *
 * @version V1.0.0
 * @date 2019/3/5 18:23
 */
public class BiSdcardInfoReport extends BaseBiReport {
    private static final String TABLE_NAME = "gb_perf_storage";
    private static final String AVAILABLE = "available";
    private static final String TOTAL = "total";
    private static final String CTIME = "ctime";

    public BiSdcardInfoReport() {
        super(TABLE_NAME);
        initData();
    }

    private void initData() {
        addData(AVAILABLE, "");
        addData(TOTAL, "");
        addData(CTIME, "");
    }

    public void addAvailable(int available) {
        addData(AVAILABLE, available);
    }

    public void addTotal(int total) {
        addData(TOTAL, total);
    }

    public void addCTime() {
        addData(CTIME, System.currentTimeMillis());
    }

    @Override
    public void report() {
        addCTime();
        super.report();
    }
}
