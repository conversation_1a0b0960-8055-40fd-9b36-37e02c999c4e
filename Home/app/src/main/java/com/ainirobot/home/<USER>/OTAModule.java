/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.home.module;

import android.app.Dialog;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.support.v4.app.ActivityOptionsCompat;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.fragment.OTAResultFragment;
import com.ainirobot.home.ui.view.OTADialog;
import com.ainirobot.home.ui.view.OTAInspectFailDialog;
import com.ainirobot.home.ui.view.OTANetworkDialog;
import com.ainirobot.home.ui.view.OTAFactoryResetDialog;
import com.ainirobot.home.utils.ResUtil;
import com.ainirobot.home.utils.SystemUtils;
import com.ainirobot.home.utils.WifiUtils;
import com.ainirobot.home.utils.VersionUtils;
import com.ainirobot.home.ota.utils.Utils;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by Orion on 2018/1/2.
 */

public class OTAModule extends BaseModule {

    private static final String TAG = "OTAModule:Home";
    private static OTAModule otaModule = new OTAModule();

    private Context mContext;
    private String mVersionCode;
    private String mVersionDescription;
    private String mStartFrom;
    private final static long NORMAL_DELAY = 1000;
    private final static long BATTERY_LOWER_DELAY = 3000;
    private final static int WHAT_REQ_OTA_UPGRADE = 0;
    private final static int WHAT_STATUS_OTA_PROGRESS = 1;
    private final static int WHAT_REQ_OTA_FINISH = 2;
    private final static int WHAT_REQ_RELEASE = 3;
    private boolean mIsOtaDialogShow = false;
    private boolean mIsStartPowerInstall = false;

    private boolean mIsForce;
    private volatile boolean mNeedReboot;
    private boolean mNeedDownload = true;
    private boolean mCanStop = false;
    private boolean mIsInspect = false;
    private boolean mIsUpgrading = false;
    private boolean mIsBootCheck = true;
    private boolean mIsOtaSuccess = false;

    private boolean mShowAfterInspectIcon;
    private String mShowAfterInspectionMessage;
    private boolean mShowAfterInspectionRelease;

    private String mInspectionResult;
    private boolean mInspectionSuccess;
    private String mNaviError;

    private OTAModule() {
    }

    public static OTAModule getInstance() {
        if (otaModule == null) {
            otaModule = new OTAModule();
        }
        return otaModule;
    }

    public void init(Context context) {
        mContext = context;
    }

    private final Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            String params = (String) msg.obj;
            int reqId = msg.arg1;
            switch (msg.what) {
                case WHAT_REQ_OTA_UPGRADE:
                    mHandler.removeMessages(WHAT_REQ_RELEASE);
                    try {
                        JSONObject object = new JSONObject(params);
                        mIsForce = object.getBoolean("isForce");
                        mStartFrom = object.getString("startup");
                        JSONObject param = new JSONObject(object.getString("otaInfo"));
                        mNeedDownload = param.getBoolean(Definition.JSON_OTA_NEED_DOWNLOAD);
                        if (mNeedDownload) {
                            getVersionInfo();
                        } else {
                            handleVersion();
                        }
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    break;
                case WHAT_STATUS_OTA_PROGRESS:
                    Log.i(TAG, "ota params = " + params);
                    try {
                        JSONObject jsonObject = new JSONObject(params);
                        boolean isDownload = jsonObject.optBoolean(Definition.JSON_OTA_IS_DOWNLOAD_PROGRESS);
                        double progress = jsonObject.optDouble(Definition.JSON_OTA_INSTALL_PERCENT);
                        //download progress
                        if (isDownload) {
                            UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.PROGRESS_DOWNLOAD,
                                    (int) (100 * progress) + "");
                        } else {
                            //install progress
                            if (!mIsStartPowerInstall) {
                                startPowerListener();
                            }
                            UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.PROGRESS_INSTALL,
                                    (int) (100 * progress) + "");
                        }
                    } catch (JSONException e) {
                        e.printStackTrace();
                        Log.e(TAG, e.getMessage());
                    }
                    break;
                case WHAT_REQ_OTA_FINISH:
                    try {
                        JSONObject finishJsonObject = new JSONObject(params);
                        String result = finishJsonObject.optString(Definition.JSON_OTA_RESULT);
                        int code = finishJsonObject.optInt(Definition.JSON_OTA_CODE);
                        String updateType = finishJsonObject.optString(Definition.JSON_OTA_TYPE, "");
                        mNeedReboot = finishJsonObject.optBoolean(Definition.JSON_OTA_REBOOT, false);
                        Log.d(TAG, "analyse result=" + result + ", code=" + code +
                                ",type=" + updateType + ",reboot=" + mNeedReboot);
                        handleOtaFinish(result, updateType, code);
                        SystemApi.getInstance().finishModuleParser(reqId, true);
                    } catch (JSONException e) {
                        e.printStackTrace();
                        mNeedReboot = false;
                        SystemApi.getInstance().finishModuleParser(reqId, true);
                        releaseOtaModule();
                    }
                    break;
                case WHAT_REQ_RELEASE:
                    Log.i(TAG, "ota WHAT_REQ_RELEASE mNeedReboot = " + mNeedReboot);
                    if (mNeedReboot) {
                        realReboot();
                    } else {
                        releaseOtaModule();
                    }
                    break;
                default:
                    break;
            }

        }
    };

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.i(TAG, "onNewSemantics :" + "intent=" + intent + ",params =" + params);
        switch (intent) {
            case Definition.REQ_OTA_AUTH://start upgrade
                //user touch check upgrade
                SystemUtils.setThreeFinger(false);
                mIsOtaSuccess = false;
                mHandler.removeMessages(WHAT_REQ_RELEASE);
                Message upgradeMessage = mHandler.obtainMessage();
                upgradeMessage.what = WHAT_REQ_OTA_UPGRADE;
                upgradeMessage.arg1 = reqId;
                upgradeMessage.obj = params;
                mHandler.sendMessage(upgradeMessage);
                sendStatusReport(reqId, intent, text, true, "start");
                SystemApi.getInstance().finishModuleParser(reqId, true);
                mIsBootCheck = false;
                break;
            case Definition.REQ_OTA_RESULT:
                SystemUtils.setThreeFinger(false);
                stopPowerListener();
                Message finishMessage = mHandler.obtainMessage();
                finishMessage.what = WHAT_REQ_OTA_FINISH;
                finishMessage.arg1 = reqId;
                finishMessage.obj = params;
                mHandler.sendMessage(finishMessage);
                sendStatusReport(reqId, intent, text, true, "finish");
                break;
            case Definition.REQ_OTA_DOWNLOAD_FULL_PAC:
                releaseOtaModule();
                SystemApi.getInstance().finishModuleParser(reqId, true);
                break;
            case ModuleDef.REQ_STOP:
                if (mCanStop) {
                    releaseOtaModule();
                }
                break;
            case Definition.REQ_OTA_INSPECT:
                SystemUtils.setThreeFinger(false);
                initInspectionInfo();
                Log.d(TAG, "mInspectionSuccess : " + mInspectionSuccess
                        + ", mInspectionResult : " + mInspectionResult + ", mNaviError : " + mNaviError);
                if (!mInspectionSuccess) {
                    UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_INSPECT_OTA, null,
                            ActivityOptionsCompat.makeCustomAnimation(mContext, 0, 0).toBundle());
                }
                break;
            default:
                break;
        }
        return super.onNewSemantics(reqId, intent, text, params);
    }

    private void initInspectionInfo() {
        if (mIsBootCheck) {
            mIsInspect = true;
            mInspectionSuccess = ControlManager.getControlManager().getSystemStatusManager().isInspectSuccess();
            mInspectionResult = ControlManager.getControlManager().getSystemStatusManager().getInspectionResult();
            mNaviError = ControlManager.getControlManager().getSystemStatusManager().getNaviErrorId();
            mIsBootCheck = false;
        }
    }

    /**
     * get versionCode and versionDescription
     */
    private void getVersionInfo() {
        Log.i(TAG, "getVersionInfo");
        String upgradeStr = SystemApi.getInstance().getOtaUpgradeDescription();

        Log.i(TAG, "getOtaUpgradeDescription()");
        Log.i(TAG, "description=" + upgradeStr);
        try {
            JSONObject upgradeJson = new JSONObject(upgradeStr);
            mVersionCode = upgradeJson.optString(Definition.JSON_OTA_TARGET_VERSION);
            mVersionDescription = upgradeJson.optString(Definition.JSON_OTA_TARGET_DESCRITION);
            handleVersion();
        } catch (JSONException e) {
            e.printStackTrace();
            Log.e(TAG, "parse version json Error");
        }
    }

    private void handleVersion() {
        UIController.getInstance().setLastApp();
        //force upgrade
        if (mIsForce) {
            Log.i(TAG, "force upgrade");
            showOtaDialog();
            return;
        }

        if (mStartFrom == null) {
            Log.d(TAG, "null start type");
            return;
        }

        switch (mStartFrom) {
            case Definition.OTA_START_NIGHTTIME:
            case Definition.OTA_START_REMOTE:
                if (WifiUtils.isWifi(mContext)) {
                    processUpgrade(true, false);
                } else {
                    if (RobotSettingApi.getInstance().getRobotInt(Definition.VERSION_UPGRADE_ON_MOBILE)
                            == Definition.ROBOT_SETTING_ENABLE) {
                        processUpgrade(true, false);
                    } else {
                        releaseOtaModule();
                    }
                }
                break;
            case Definition.OTA_START_SETTING:
                if (mNeedDownload) {
                    Log.i(TAG, "isWifi " + WifiUtils.isWifi(mContext));
                    if (WifiUtils.isWifi(mContext)) {
                        showOtaDialog();
                    } else {
                        showNetworkDialog();
                    }
                } else {
                    processUpgrade(true, false);
                }
                break;
            case Definition.OTA_START_BOOT:
                sendStickyBroadcastToAboutRobot();
                releaseOtaModule();
                break;
            default:
                Log.d(TAG, "unknown start type");
        }
    }

    private void handleOtaFinish(String result, String updateType, int code) {
        mIsUpgrading = false;
        initInspectionInfo();
        switch (result) {
            case Definition.JSON_OTA_RESULT_SUCCESS:
                handleOtaSuccess(updateType);
                break;
            case Definition.JSON_OTA_RESULT_FAILED:
                handleOtaFailed(updateType, code);
                break;
            case Definition.JSON_OTA_RESULT_CONTINUE:
                showInspectFailDialog(updateType, mContext.getString(Definition.JSON_OTA_TYPE_NORMAL.equals(updateType) ?
                        R.string.ota_continue_install : R.string.ota_continue_rollback));
                break;
            default:
                if (mIsInspect && !mInspectionSuccess) {
                    showInspectionErrorFragment();
                }
                else {
//                    releaseOtaModule();
                    releaseDelay();
                }
                break;
        }
        mInspectionSuccess = true;
        mIsInspect = false;
        mInspectionResult = "";
        mNaviError = "";
    }

    private void handleOtaSuccess(String updateType) {
        mIsOtaSuccess = true;
        if (Definition.JSON_OTA_TYPE_ROLLBACK.equals(updateType)) {
            if (mIsInspect && !mInspectionSuccess) {
                showInspectionErrorFragment();
                mShowAfterInspectIcon = true;
                mShowAfterInspectionMessage = mContext.getString(R.string.ota_rollback_success);
                mShowAfterInspectionRelease = true;
            } else {
                showOtaResultFragment(true, mContext.getString(R.string.ota_rollback_success), true);
            }
        } else {
            // 检查是否需要强制恢复出厂设置
            if (shouldShowFactoryResetDialog()) {
                showFactoryResetFragment();
            } else if (mIsInspect && !mInspectionSuccess) {
                showInspectionErrorFragment();
                mShowAfterInspectIcon = true;
                mShowAfterInspectionMessage = mContext.getString(R.string.ota_success);
                mShowAfterInspectionRelease = true;
            } else {
                showOtaResultFragment(true, mContext.getString(R.string.ota_success), true);
            }
        }
    }

    private void handleOtaFailed(String updateType, int code) {
        if (Definition.JSON_OTA_TYPE_ROLLBACK.equals(updateType)) {
            if (mIsInspect && !mInspectionSuccess) {
                showInspectionErrorFragment();
                mShowAfterInspectIcon = false;
                mShowAfterInspectionMessage = mContext.getString(R.string.ota_rollback_fail);
                mShowAfterInspectionRelease = false;
            } else {
                showOtaResultFragment(false, mContext.getString(R.string.ota_rollback_fail), false);
            }
        } else {
            if (-4000 == code) {
                if (mIsInspect) {
                    showInspectFailDialog(Definition.JSON_OTA_TYPE_ROLLBACK, mContext.getString(R.string.ota_continue_fail));
                } else {
                    mNeedDownload = false;
                    processUpgrade(false, true);
                    SkillManager.getInstance().speechPlayText(mContext.getString(R.string.ota_fail_rollback));
                }
            } else {
                String message;
                if (-1003 == code) {
                    message = mContext.getString(R.string.ota_fail_download);
                } else {
                    message = mContext.getString(R.string.ota_fail_no_rollback);
                }
                if (mIsInspect && !mInspectionSuccess) {
                    showInspectionErrorFragment();
                    mShowAfterInspectIcon = false;
                    mShowAfterInspectionMessage = message;
                    mShowAfterInspectionRelease = true;
                } else {
                    showOtaResultFragment(false, message, true);
                }
            }
        }
    }

    private void showInspectFailDialog(final String updateType, String message) {
        mIsUpgrading = true;
        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_INSPECT_OTA, null,
                ActivityOptionsCompat.makeCustomAnimation(mContext, 0, 0).toBundle());
        new OTAInspectFailDialog(mContext, message,
                new OTAInspectFailDialog.DialogEvent() {
                    @Override
                    public void onConfirm() {
                        if (Definition.JSON_OTA_TYPE_ROLLBACK.equals(updateType)) {
                            mNeedDownload = false;
                            processUpgrade(true, true);
                        } else {
                            mNeedDownload = false;
                            processUpgrade(true, false);
                        }
                    }
                }).show();
    }

    private void showInspectionErrorFragment() {
        SkillManager.getInstance().speechPlayText(ResUtil.getString(R.string.inspect_err_speech_tips));
        mCanStop = true;
        Bundle bundle = new Bundle();
        bundle.putString(ModuleDef.INSPECT_ERROR_MESSAGE, mInspectionResult);
        bundle.putString(ModuleDef.INSPECT_NAVI_ERROR_ID, mNaviError);
        SystemUtils.setThreeFinger(false);
        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_INSPECT_ERROR, bundle, null);
    }

    private void showFactoryResetFragment() {
        Log.i(TAG, "showFactoryResetFragment: showing factory reset fragment");
        SkillManager.getInstance().speechPlayText(ResUtil.getString(R.string.ota_factory_reset_speech_tips));
        mCanStop = true;
        Bundle bundle = new Bundle();
        bundle.putBoolean(ModuleDef.FACTORY_RESET_MANDATORY, true);
        bundle.putString(ModuleDef.FACTORY_RESET_REASON, mContext.getString(R.string.ota_factory_reset_mandatory_content));
        SystemUtils.setThreeFinger(false);
        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_FACTORY_RESET, bundle, null);
    }

    private void showOtaResultFragment(boolean isOkIcon, String message, final boolean releaseAfterSpeech) {
        if (releaseAfterSpeech) {
            mHandler.sendEmptyMessageDelayed(WHAT_REQ_RELEASE, 10 * 1000);
        }
        SkillManager.getInstance().speechWithFinishCallBack(message, new SkillManager.SpeechFinishCallBack() {
            @Override
            public void finish() {
                if (releaseAfterSpeech) {
                    mHandler.removeMessages(WHAT_REQ_RELEASE);
                    mHandler.sendEmptyMessage(WHAT_REQ_RELEASE);
                }
            }
        });
        Bundle bundle = new Bundle();
        bundle.putBoolean(OTAResultFragment.SHOW_OK_ICON, isOkIcon);
        bundle.putString(OTAResultFragment.SHOW_MESSAGE, message);
        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_OTA_RESULT, bundle, null);
    }

    private void releaseOtaModule() {
        mIsInspect = false;
        mCanStop = false;
        mNeedDownload = true;
        mIsUpgrading = false;
        mHandler.removeMessages(WHAT_REQ_RELEASE);
        if (null != mShowAfterInspectionMessage) {
            showOtaResultFragment(mShowAfterInspectIcon, mShowAfterInspectionMessage, mShowAfterInspectionRelease);
            mShowAfterInspectionMessage = null;
        } else {
            Log.e(TAG, "release OTAModule mIsOtaSuccess: " + mIsOtaSuccess);
            SkillManager.getInstance().openSpeechAsrRecognize();
            if (mIsOtaSuccess) {
                SystemApi.getInstance().onOtaSuccess();
            } else {
                SystemApi.getInstance().onOtaFinished();
            }
            SystemUtils.setThreeFinger(true);
            SystemApi.getInstance().unregisterStatusListener(mProgressListener);
            release(0, 0, null);
        }
        mIsOtaSuccess = false;
    }

    private void showNetworkDialog() {
        boolean flag = RobotSettingApi.getInstance().getRobotInt(Definition.VERSION_UPGRADE_ON_MOBILE)
                == Definition.ROBOT_SETTING_ENABLE;
        if (flag) {
            Log.i(TAG, "users click no to remind,not OTANetworkDialog but OTADialog");
            showOtaDialog();
            return;
        }
        new OTANetworkDialog(mContext)
                .setDialogEvent(new OTANetworkDialog.DialogEvent() {
                    @Override
                    public void onConfirm(OTANetworkDialog dialog) {
                        showOtaDialog();
                        dialog.dismiss();
                    }

                    @Override
                    public void onCancel(OTANetworkDialog dialog) {
                        dialog.dismiss();
                        releaseOtaModule();
                    }
                }).show();
    }

    /**
     * Send a broadcast to the robot setting "about robot" activity.
     * StickBroadcast
     */
    private void sendStickyBroadcastToAboutRobot() {
        Log.i(TAG, "sendStickyBroadcastToAboutRobot");
        Intent intent = new Intent();
        intent.putExtra(Definition.JSON_OTA_TARGET_VERSION, mVersionCode);
        intent.putExtra(Definition.JSON_OTA_TARGET_DESCRITION, mVersionDescription);
        intent.setAction(ModuleDef.OTA_BROADCAST_ACTION_TO_ABOUT_ROBOT);
        mContext.sendStickyBroadcast(intent);
    }

    /**
     * send a broadcast to RemoteControl to kill OtaService
     */
    public void sendBroadcastToRemoteControl() {
        Log.i(TAG, "sendBroadcastToRemoteControl to kill OtaService");
        Intent intent = new Intent();
        intent.setAction(ModuleDef.OTA_BROADCAST_ACTION_TO_REMOTE_CONTROL);
        mContext.sendBroadcast(intent);
    }

    private void showOtaDialog() {
        Log.i(TAG, "showOtaDialog");
        if (mIsOtaDialogShow || mIsUpgrading) {
            return;
        }
        mIsOtaDialogShow = true;
        Dialog dialog = new OTADialog(mContext)
                .isForce(mIsForce)
                .setVersionText(mVersionCode)
                .setUpgradeContentText(mVersionDescription)
                .setOnUpdateListener(new OTADialog.OnUpdateListener() {
                    @Override
                    public void onUpdateNextTime(OTADialog dialog) {
                        mIsOtaDialogShow = false;
                        releaseOtaModule();
                    }

                    @Override
                    public void onUpdateNow(OTADialog dialog) {
                        mIsOtaDialogShow = false;
                        processUpgrade(true, false);
                    }
                });
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(false);
        dialog.show();
    }

    /**
     * dialog click upgrade,process upgrade
     */
    private void processUpgrade(boolean needInit, boolean isRollback) {
        mIsUpgrading = true;
        SystemApi.getInstance().startOtaUpgrade(mNeedDownload, isRollback);
        if (needInit) {
            SystemApi.getInstance().registerStatusListener(Definition.STATUS_OTA_PROGRESS, mProgressListener);
            //开始OTA升级
            Log.i(TAG, "startOtaUpgrade() running");
            try {
                SkillManager.getInstance().speechPlayText(
                        mContext.getString(isRollback ? R.string.ota_fail_rollback : R.string.ota_start),
                        new TextListener() {
                            @Override
                            public void onComplete() {
                                super.onComplete();
                                SkillManager.getInstance().closeSpeechAsrRecognize();
                            }
                        });
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }

        //startToMainActivity();
        Bundle bundle = new Bundle();
        bundle.putBoolean(Definition.JSON_OTA_FORCEUPATE, mIsForce);
        bundle.putBoolean(Definition.JSON_OTA_NEED_DOWNLOAD, mNeedDownload);
        bundle.putBoolean(ModuleDef.PARAM_IS_ROLLBACK, isRollback);
        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_OTA_PROGRESS, bundle,
                ActivityOptionsCompat.makeCustomAnimation(mContext, R.anim.ota_alpha_enter, R.anim.ota_alpha_out).toBundle());
    }

    private StatusListener mProgressListener = new StatusListener() {
        @Override
        public void onStatusUpdate(String type, String data) {
            Message message = mHandler.obtainMessage();
            message.arg1 = -1;
            message.obj = data;
            message.what = WHAT_STATUS_OTA_PROGRESS;
            mHandler.sendMessage(message);
        }
    };

    private void releaseDelay() {
        mHandler.removeMessages(WHAT_REQ_RELEASE);
        // 考虑到电量不足20% 时,CoreService会下发 ota_wait_update 的string,所以电量不足时适当延迟release module时间。等待tts播报.
        mHandler.sendEmptyMessageDelayed(WHAT_REQ_RELEASE,
                (SystemApi.getInstance().getBatteryLevel() <= 20 ? BATTERY_LOWER_DELAY : NORMAL_DELAY));
    }

    private void realReboot() {
        Log.i(TAG, "complete machine reboot");
        Intent intent = new Intent(Intent.ACTION_REBOOT);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        mContext.startActivity(intent);
    }

    private void pauseOta(boolean pause) {
        Log.i(TAG, pause ? "ota pause" : "ota continue");
        ComponentName component = new ComponentName("com.ainirobot.ota",
                "com.ainirobot.ota.service.UpdateService");
        Intent intent = new Intent();
        intent.putExtra("start_command", pause ? 112 : 113);
        intent.setComponent(component);
        mContext.startService(intent);
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
        switch (type) {
            case ModuleDef.LOCAL_MESSAGE_PAUSE_OTA:
                pauseOta(true);
                break;
            case ModuleDef.LOCAL_MESSAGE_CONTINUE_OTA:
                pauseOta(false);
                break;
            case ModuleDef.LOCAL_MESSAGE_CANCEL_OTA:
                SystemApi.getInstance().cancelOtaUpgradeDownload();
                releaseOtaModule();
                break;
            case ModuleDef.LOCAL_MESSAGE_STOP_INSPECTION:
                releaseOtaModule();
                break;
            default:
                break;
        }
    }

    private void startPowerListener() {
        Log.d(TAG, "start power listener");
        mIsStartPowerInstall = true;
        SystemApi.getInstance().registerStatusListener(Definition.STATUS_OTA_WARNING, statusListener);
    }

    private StatusListener statusListener = new StatusListener() {
        @Override
        public void onStatusUpdate(String type, String data) {
            Log.d(TAG, "type:" + type + " data:" + data);
            UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.OTA_WARNING, data);
        }
    };

    private void stopPowerListener() {
        SystemApi.getInstance().unregisterStatusListener(statusListener);
        mIsStartPowerInstall = false;
    }

    /**
     * 检查是否需要强制恢复出厂设置
     * 如果robot_aios_version不为空且<=1.3.0，则需要强制恢复出厂设置
     */
    private boolean shouldShowFactoryResetDialog() {
        Log.i(TAG, "shouldShowFactoryResetDialog: checking robot_aios_version");

        // 从RobotSettingApi获取robot_aios_version信息
        // 由于robot_profile是通过HTTP接口获取的，我们使用系统设置中存储的版本信息
        String robotAiosVersion = SettingsUtil.getString(mContext, RobotSettings.SETTINGS_GLOBAL_ROBOT_AIOS_VERSION);

        robotAiosVersion = "1.3.0.250616.C";

        Log.i(TAG, "robot_aios_version: " + robotAiosVersion);

        // 检查版本是否不为空且<=1.3.0
        boolean needFactoryReset = !robotAiosVersion.isEmpty() && VersionUtils.compareVersion(robotAiosVersion, "1.3.0") <= 0;

        if (needFactoryReset) {
            Log.i(TAG, "robot_aios_version " + robotAiosVersion + " <= 1.3.0, need to show factory reset dialog");
        } else {
            Log.i(TAG, "robot_aios_version " + robotAiosVersion + " > 1.3.0 or empty, no need to show factory reset dialog");
        }

        return needFactoryReset;
    }

    /**
     * 显示强制恢复出厂设置对话框
     * 此对话框会完全阻塞业务流程，用户必须选择恢复出厂设置才能继续
     */
    private void showFactoryResetDialog() {
        Log.i(TAG, "showFactoryResetDialog: showing mandatory factory reset dialog");

        OTAFactoryResetDialog dialog = new OTAFactoryResetDialog(mContext);
        dialog.setOnFactoryResetListener(new OTAFactoryResetDialog.OnFactoryResetListener() {
            @Override
            public void onConfirm() {
                Log.i(TAG, "User confirmed mandatory factory reset");
                performFactoryReset();
                // 恢复出厂设置后，系统会重启，不需要继续后续流程
            }

            @Override
            public void onCancel() {
                Log.i(TAG, "User tried to cancel mandatory factory reset - not allowed");
                // 强制恢复出厂设置，不允许取消
                // 重新显示对话框
                showFactoryResetDialog();
            }
        });

        // 设置为强制对话框，不允许任何形式的取消
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(false);
        dialog.setForceMandatory(true); // 设置为强制模式
        dialog.show();
    }

    private static final String ACTION_FACTORY_RESET = "android.intent.action.FACTORY_RESET";
    private static final String EXTRA_REASON = "android.intent.extra.REASON";
    private static final String EXTRA_WIPE_EXTERNAL_STORAGE = "android.intent.extra.WIPE_EXTERNAL_STORAGE";
    private static final String EXTRA_WIPE_ESIMS = "com.android.internal.intent.extra.WIPE_ESIMS";

    /**
     * 执行恢复出厂设置
     */
    private void performFactoryReset() {
        Log.i(TAG, "performFactoryReset: starting mandatory factory reset process");

        final Intent intent = new Intent(ACTION_FACTORY_RESET);
        intent.setPackage("android");
        intent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND);
        intent.putExtra(EXTRA_REASON, ApplicationWrapper.getContext().getPackageName() + " Master Clear");
        intent.putExtra(EXTRA_WIPE_EXTERNAL_STORAGE, true);
        intent.putExtra(EXTRA_WIPE_ESIMS, true);
        ApplicationWrapper.getContext().sendBroadcast(intent);
    }
}
