package com.ainirobot.home.fallback.blackbug;

import android.os.Environment;

import com.ainirobot.home.fallback.Utils;

import java.io.File;

public class PathConstants {
    public static String getExecptionDumpSubDir() {
        String dumpDir = getExecptionDumpDir();
        dumpDir = dumpDir + Utils.getStringTodayTime() + File.separator;
        File dir = new File(dumpDir);
        if (!dir.exists())
            dir.mkdirs();
        return dumpDir;
    }

    public static String getExecptionDumpDir() {
        String dumpDir = getBlackCheckDir();
        dumpDir = dumpDir + "execption" + File.separator;
        File dir = new File(dumpDir);
        if (!dir.exists())
            dir.mkdirs();

        return dumpDir;
    }

    public static String getPoweredDownDumpSubDir() {
        String dumpDir = getPoweredDownDumpDir();
        dumpDir = dumpDir + Utils.getStringTodayTime() + File.separator;
        File dir = new File(dumpDir);
        if (!dir.exists())
            dir.mkdirs();
        return dumpDir;
    }


    public static String getPoweredDownDumpDir() {
        String dumpDir = getBlackCheckDir();
        dumpDir = dumpDir + "powered" + File.separator;
        File dir = new File(dumpDir);
        if (!dir.exists())
            dir.mkdirs();
        return dumpDir;
    }

    public static String getBlackCheckDir() {
        return Environment.getExternalStorageDirectory() + File.separator + "blackcheck" + File.separator;
    }

}
