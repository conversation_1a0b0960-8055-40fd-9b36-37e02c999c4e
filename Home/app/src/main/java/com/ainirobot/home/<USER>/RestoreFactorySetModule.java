package com.ainirobot.home.module;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.bi.RestoreFactorySetReport;
import com.ainirobot.home.ui.view.LoadingDialog;
import com.ainirobot.home.ui.view.ResetErrorDoalog;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 恢复出厂设置
 */
public class RestoreFactorySetModule extends BaseModule {

    static final String TAG = "RestoreFactorySetModule_Home";

    private static final String ACTION_FACTORY_RESET = "android.intent.action.FACTORY_RESET";
    private static final String EXTRA_REASON = "android.intent.extra.REASON";
    private static final String EXTRA_WIPE_EXTERNAL_STORAGE = "android.intent.extra.WIPE_EXTERNAL_STORAGE";
    private static final String EXTRA_WIPE_ESIMS = "com.android.internal.intent.extra.WIPE_ESIMS";

    private Context mContext;
    private int mReqId = 0;
    private String mUserName;
    private String mPassword;
    private LoadingDialog mLoadingDialog;
    private ResetErrorDoalog mErrorDialog;
    private final static int SUCCESS = 0;
    private final static int FAIL_NUM = 2;
    private int fail_cycle_num;
    private RestoreFactorySetReport mReport;


    private RestoreFactorySetModule() {
    }

    private static class SingletonHolder {
        private static final RestoreFactorySetModule mInstance = new RestoreFactorySetModule();
    }

    public static RestoreFactorySetModule getInstance() {
        return RestoreFactorySetModule.SingletonHolder.mInstance;
    }

    public void init(Context context) {
        mContext = context;
    }

    @SuppressLint("LongLogTag")
    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {

        Log.i(TAG, "onNewSemantics: reqId = " + reqId + "  intent = " + intent + "  params = " + params);
        mReport = new RestoreFactorySetReport();
        switch (intent) {
            case Definition.REQ_SYSTEM_RECOVERY:
                try {
                    JSONObject json = new JSONObject(params);
                    mUserName = json.optString(Definition.JSON_REMOTE_USER_NAME);
                    mPassword = json.optString(Definition.JSON_REMOTE_PASSWORD);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                mReqId = reqId;
                mLoadingDialog = new LoadingDialog(ApplicationWrapper.getContext());
                mLoadingDialog.startLoading();
                startHeadRecovery();

                break;
        }

        return true;
    }

    /**
     * 删除TX1数据
     *
     * @return
     */
    @SuppressLint("LongLogTag")
    private void startHeadRecovery() {
        try {
            boolean headServiceEnable = SystemApi.getInstance().isHeadServiceEnable();
            if (!headServiceEnable){
                Log.d(TAG, "HeadService is disabled , start next NavigationRecovery");
                startNavigationRecovery();
                return;
            }

            SystemApi.getInstance().startHeadRecovery(mReqId, new CommandListener() {

                @Override
                public void onResult(int result, String message) {
                    Log.d(TAG, "  startHeadRecovery result = " + result + ",message = " + message);
                    String state = "";
                    try {
                        JSONObject jsonObject = new JSONObject(message);
                        state = (String) jsonObject.get(Definition.JSON_NAVI_POSITION_STATUS);
                    } catch (JSONException e) {
                        Log.d(TAG, "  startHeadRecovery json catch");
                        e.printStackTrace();
                    }

                    switch (state) {
                        case Definition.CMD_STATUS_OK:
                            Log.d(TAG, "  startHeadRecovery ok ");
                            startNavigationRecovery();
                            break;
                        default:
                            if (fail_cycle_num < FAIL_NUM) {
                                fail_cycle_num++;
                                startHeadRecovery();
                            }
                            Log.d(TAG, "  startHeadRecovery fail_cycle_num = " + fail_cycle_num);
                            if (fail_cycle_num == FAIL_NUM) {
                                initDialog("TX1：" + state);
                            }
                            break;
                    }

                }
            });
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "startHeadRecovery error :"+e.getLocalizedMessage());
            startNavigationRecovery();
        }

    }

    /**
     * 删除TK1数据
     */
    public void startNavigationRecovery() {

        try {
            boolean isNavigationEnable = SystemApi.getInstance().isNavigationServiceEnable();
            //导航服务被禁用，直接解除绑定
            if (!isNavigationEnable) {
                unBindRobot();
                return;
            }

            SystemApi.getInstance().startNavigationRecovery(mReqId, new CommandListener() {

                @SuppressLint("LongLogTag")
                @Override
                public void onResult(int result, String message) {
                    Log.d(TAG, "  startNavigationRecovery result = " + result + ",message = " + message);
                    switch (message) {

                        case Definition.SUCCEED:
                            unBindRobot();
                            break;
                        case Definition.CMD_STATUS_FAILED:
                        case Definition.JSON_HEAD_TIMEOUT:
                        default:
                            initDialog("TK1：" + message);
                            break;
                    }

                }
            });

        } catch (RemoteException e) {
            e.printStackTrace();

            unBindRobot();
        }
    }

    public void unBindRobot() {

        SystemApi.getInstance().unBindRobot(mReqId, mUserName, mPassword, new CommandListener() {

            @SuppressLint("LongLogTag")
            @Override
            public void onResult(int result, String message) {

                try {
                    JSONObject jsonObject = new JSONObject(message);
                    int code = (int) jsonObject.get("code");
                    switch (code) {
                        case SUCCESS:
                            if (Build.VERSION.SDK_INT < 28) {
                                ApplicationWrapper.getContext()
                                        .sendBroadcast(new Intent("android.intent.action.MASTER_CLEAR")
                                                .addFlags(Intent.FLAG_RECEIVER_FOREGROUND));
                            } else {
                                final Intent intent = new Intent(ACTION_FACTORY_RESET);
                                intent.setPackage("android");
                                intent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND);
                                intent.putExtra(EXTRA_REASON, ApplicationWrapper.getContext().getPackageName() + " Master Clear");
                                intent.putExtra(EXTRA_WIPE_EXTERNAL_STORAGE, true);
                                intent.putExtra(EXTRA_WIPE_ESIMS, true);
                                ApplicationWrapper.getContext().sendBroadcast(intent);
                            }
                            break;
                        default:
                            initDialog("unBind：" + message);
                            break;
                    }
                } catch (Exception e) {
                    initDialog("unBind：" + message);
                    e.printStackTrace();
                }
                Log.d(TAG, "  unBindRobot result = " + result + ",message = " + message);
            }
        });
    }

    public void initDialog(String failResone) {
        mReport.addFailedReason(failResone);
        mReport.report();
        if (null != mLoadingDialog && mLoadingDialog.isShowing()) {
            mLoadingDialog.stopLoading();
        }
        if (mErrorDialog == null) {
            mErrorDialog = new ResetErrorDoalog(ApplicationWrapper.getContext());
        }
        mErrorDialog.showResetErrorDialog();
        mErrorDialog.setFailReason(failResone);
    }

}
