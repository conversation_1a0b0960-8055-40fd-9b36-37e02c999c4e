package com.ainirobot.home.ota.service;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Bundle;
import android.os.Process;
import android.util.Log;

import com.ainirobot.coreservice.client.ApiListener;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.actionbean.CanOtaBean;
import com.ainirobot.coreservice.client.input.OtaApi;
import com.ainirobot.coreservice.config.ServiceConfig;
import com.ainirobot.home.ota.constants.OtaConstants;
import com.ainirobot.home.ota.utils.Utils;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * helper interface must be called in sub-thread
 */
public class OtaApiHelper implements IOtaApiHelper {
    private final static String TAG = OtaConstants.TAG_PREFIX  + OtaApiHelper.class.getSimpleName();

    private Context mContext;
    private CommandReply mOtaResult;

    @SuppressLint("StaticFieldLeak")
    public final static OtaApi mOtaApi = OtaApi.getInstance();
    private OtaCallbackApi mUpdateCallBackApi;

    public final static Object mConnectLock = new Object();
    public static boolean mCallBackRegisterd = false; //是否和CoreService注册完成callback

    private final static String KEY_HEAD_PRE_SCP_RESULT = "head_pre_scp_result";
    private final static String KEY_HEAD_UPDATE_RESULT = "head_udpate_result";
    private final static String KEY_HEAD_VERSION_RESULT = "head_version_result";
    private final static String KEY_HEAD_UPDATE_PARAMS_RESULT = "head_update_params_result";
    private final static String KEY_NAVIGATION_UPDATE_RESULT = "navi_update_result";
    private final static String KEY_NAVIGATION_VERSION_RESULT = "navi_version_result";

    private final static String KEY_CAN_OTA_START = "can_ota_start";
    private final static String KEY_CAN_GET_OTA_STATE = "can_get_ota_state";
    private final static String KEY_CAN_GET_BOARD_VERSION = "can_get_board_version";
    private final static String KEY_CAN_GET_MOTOR_H_VERSION = "can_get_motor_h_version";
    private final static String KEY_CAN_GET_MOTOR_V_VERSION = "can_get_motor_v_version";
    private final static String KEY_CAN_GET_PSB_VERSION = "can_get_psb_version";
    private final static String KEY_CAN_GET_AC_CLIENT_VERSION = "can_get_ac_client_version";
    private final static String KEY_CAN_GET_BMS_VERSION = "can_get_bms_version";
    private final static String KEY_CAN_GET_PSB_S_VERSION = "can_get_psb_s_version";

    private static final ConcurrentHashMap<String, String> mResult =
            new ConcurrentHashMap<>();

    private static final ConcurrentHashMap<String, Object> mKeyLockMap =
            new ConcurrentHashMap<>();

    static {
        mKeyLockMap.put(KEY_HEAD_PRE_SCP_RESULT, new Object());
        mKeyLockMap.put(KEY_HEAD_UPDATE_RESULT, new Object());
        mKeyLockMap.put(KEY_HEAD_VERSION_RESULT, new Object());
        mKeyLockMap.put(KEY_HEAD_UPDATE_PARAMS_RESULT, new Object());
        mKeyLockMap.put(KEY_NAVIGATION_UPDATE_RESULT, new Object());
        mKeyLockMap.put(KEY_NAVIGATION_VERSION_RESULT, new Object());

        mKeyLockMap.put(KEY_CAN_OTA_START, new Object());
        mKeyLockMap.put(KEY_CAN_GET_OTA_STATE, new Object());

        mKeyLockMap.put(KEY_CAN_GET_BOARD_VERSION, new Object());
        mKeyLockMap.put(KEY_CAN_GET_MOTOR_H_VERSION, new Object());
        mKeyLockMap.put(KEY_CAN_GET_MOTOR_V_VERSION, new Object());
        mKeyLockMap.put(KEY_CAN_GET_PSB_VERSION, new Object());
        mKeyLockMap.put(KEY_CAN_GET_AC_CLIENT_VERSION, new Object());
        mKeyLockMap.put(KEY_CAN_GET_BMS_VERSION, new Object());
        mKeyLockMap.put(KEY_CAN_GET_PSB_S_VERSION, new Object());
    }

    private final Gson mGson = new Gson();

    private OnOtaStartListener otaStartListener;

    private ApiListener mOtaApiListener = new ApiListener() {
        @Override
        public void handleApiDisabled() {
        }

        @Override
        public void handleApiConnected() {
            Log.d(TAG, "coreService connected! callback:" + mUpdateCallBackApi);

            String ret = Utils.findPidName(Process.myPid());
            String pidName = null;
            pidName = ret.trim();
            Log.d(TAG, "pid:" + Process.myPid() + " name:" + pidName);
            if (pidName.contains("com.ainirobot.ota:filedownloader")) {
                Log.e(TAG, "this is download process. won't register callback");
                return;
            }

            Log.d(TAG, "registerCallBack. " + mUpdateCallBackApi);
            mOtaApi.registerCallBack(mUpdateCallBackApi);

            if (otaStartListener != null) {
                otaStartListener.OnOtaServiceConfig(mOtaApi.getConfig());
            }
            synchronized (mConnectLock) {
                mConnectLock.notifyAll();
                mCallBackRegisterd = true;
            }
            Log.d(TAG, "handleApiConnected done.");
        }

        @Override
        public void handleApiDisconnected() {
            Log.e(TAG, "handleApiDisconnected. reConnect coreService");
            mOtaApi.connectApi();
            mCallBackRegisterd = false;
        }
    };

    public OtaApiHelper(Context mContext, OnOtaStartListener listener) {
        this.mContext = mContext;

        otaStartListener = listener;
        mOtaResult = new CommandReply();
        mUpdateCallBackApi = new OtaCallbackApi(mOtaResult);
        mOtaApi.addApiEventListener(mOtaApiListener);
        mOtaApi.connectApi(mContext);
    }

    @Override
    public boolean isCoreConnected() {
        return mOtaApi.isApiConnectedService();
    }

    @Override
    public void isHeadConntected() {
        if (!mOtaApi.isApiConnectedService()) {
            Log.e(TAG, "OtaApi is not connected");
            return;
        }

        if (mOtaApi.isHeadConnected() == Definition.CMD_SEND_ERROR_UNKNOWN) {
            Log.e(TAG, "send isHeadConntected error. got -1");
            return;
        }
    }

    @Override
    public boolean registerStatusListener(String type, StatusListener listener) {
        try {
            checkAndWaitForServiceConnect(200);
            if (!mOtaApi.isApiConnectedService()) {
                Log.e(TAG, "OtaApi is not connected after " + 200 + "ms. return");
                return false;
            }
            String ret = mOtaApi.registerStatusListener(type, listener);
            Log.d(TAG, "registerStatusListener. type:" + type
                    + " listener:" + listener
                    + " ret:" + ret);
            return ret != null;
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public boolean unregisterStatusListener(StatusListener listener) {
        try {
            checkAndWaitForServiceConnect(200);
            if (!mOtaApi.isApiConnectedService()) {
                Log.e(TAG, "OtaApi is not connected after " + 200 + "ms. return");
                return false;
            }
            boolean ret = mOtaApi.unregisterStatusListener(listener);
            Log.d(TAG, "unregisterStatusListener. listener:" + listener
                    + " ret:" + ret);
            return ret;
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public String getHeadVersion(int millSecondTimeout) {
        String ret = null;
        try {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_HEAD_VERSION_RESULT))) {
                checkAndWaitForServiceConnect(millSecondTimeout);
                if (!mOtaApi.isApiConnectedService()) {
                    Log.e(TAG, "OtaApi is not connected after " + millSecondTimeout + "ms. return");
                    return ret;
                } else if (mOtaApi.getOtaHeadVersion() != Definition.CMD_SEND_ERROR_UNKNOWN) {
                    checkAndLockWait(mKeyLockMap.get(KEY_HEAD_VERSION_RESULT), millSecondTimeout);
                    ret = mResult.remove(KEY_HEAD_VERSION_RESULT);
                    Log.d(TAG, "getOtaHeadVersion get=" + ret);
                }

                if (ret == null || ret.isEmpty() || ret.contains(OtaConstants.CMD_TIMEOUT) || ret.contains("invalid command")) {
                    if (mOtaApi.getHeadVersion() == Definition.CMD_SEND_ERROR_UNKNOWN) {
                        Log.e(TAG, "send headVersion error. got -1");
                        return ret;
                    }

                    checkAndLockWait(mKeyLockMap.get(KEY_HEAD_VERSION_RESULT), millSecondTimeout);
                    ret = mResult.remove(KEY_HEAD_VERSION_RESULT);
                    Log.d(TAG, "getHeadVersion get=" + ret);
                }
            }
        } catch (InterruptedException e) {
            ret = null;
            e.printStackTrace();
        }

        return ret;
    }

    @Override
    public String startHeadScp(int millSecondTimeout) {
        String ret = null;

        try {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_HEAD_PRE_SCP_RESULT))) {
                checkAndWaitForServiceConnect(millSecondTimeout);
                if (!mOtaApi.isApiConnectedService()) {
                    Log.e(TAG, "OtaApi is not connected after " + millSecondTimeout + "ms. return");
                    return ret;
                } else if (mOtaApi.startHeadScp(null) == Definition.CMD_SEND_ERROR_UNKNOWN) {
                    Log.e(TAG, "send start head scp error. got -1");
                    return ret;
                }

                checkAndLockWait(mKeyLockMap.get(KEY_HEAD_PRE_SCP_RESULT), millSecondTimeout);
                ret = mResult.remove(KEY_HEAD_PRE_SCP_RESULT);
                Log.d(TAG, "startHeadUpdate get=" + ret);
            }
        } catch (InterruptedException e) {
            ret = null;
            e.printStackTrace();
        }

        return ret;
    }

    @Override
    public String startHeadUpdate(String params, int millSecondTimeout) {
        String ret = null;
        try {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_HEAD_UPDATE_RESULT))) {
                checkAndWaitForServiceConnect(millSecondTimeout);
                if (!mOtaApi.isApiConnectedService()) {
                    Log.e(TAG, "OtaApi is not connected after " + millSecondTimeout + "ms. return");
                    return ret;
                } else if (mOtaApi.startHeadUpdate(params) == Definition.CMD_SEND_ERROR_UNKNOWN) {
                    Log.e(TAG, "send start head update error. got -1");
                    return ret;
                }

                checkAndLockWait(mKeyLockMap.get(KEY_HEAD_UPDATE_RESULT), millSecondTimeout);
                ret = mResult.remove(KEY_HEAD_UPDATE_RESULT);
                Log.d(TAG, "startHeadUpdate get=" + ret);
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        return ret;
    }

    @Override
    public String getHeadUpdateParams(int millSecondTimeout) {
        String ret = null;
        try {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_HEAD_UPDATE_PARAMS_RESULT))) {
                checkAndWaitForServiceConnect(millSecondTimeout);
                if (!mOtaApi.isApiConnectedService()) {
                    Log.e(TAG, "OtaApi is not connected after " + millSecondTimeout + "ms. return");
                    return ret;
                } else if (mOtaApi.getHeadUpdateParams() == Definition.CMD_SEND_ERROR_UNKNOWN) {
                    Log.e(TAG, "send getHeadUpdateParams error. got -1");
                    return ret;
                }

                checkAndLockWait(mKeyLockMap.get(KEY_HEAD_UPDATE_PARAMS_RESULT), millSecondTimeout);
                ret = mResult.remove(KEY_HEAD_UPDATE_PARAMS_RESULT);
                Log.d(TAG, "getHeadUpdateParams get=" + ret);
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        return ret;
    }

    @Override
    public String startNavigationUpdate(String params, int millSecondTimeout) {
        String ret = null;
        try {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_NAVIGATION_UPDATE_RESULT))) {
                checkAndWaitForServiceConnect(millSecondTimeout);
                if (!mOtaApi.isApiConnectedService()) {
                    Log.e(TAG, "OtaApi is not connected after " + millSecondTimeout + "ms. return");
                    return ret;
                } else if (mOtaApi.startNavigationUpdate(params) == Definition.CMD_SEND_ERROR_UNKNOWN) {
                    Log.e(TAG, "send start tk1 update error. got -1");
                    return ret;
                }

                checkAndLockWait(mKeyLockMap.get(KEY_NAVIGATION_UPDATE_RESULT), millSecondTimeout);
                ret = mResult.remove(KEY_NAVIGATION_UPDATE_RESULT);
                Log.d(TAG, "startNavigationUpdate get=" + ret);
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        return ret;
    }

    @Override
    public String getNavigationVersion(int millSecondTimeout) {
        String ret = null;
        try {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_NAVIGATION_VERSION_RESULT))) {
                checkAndWaitForServiceConnect(millSecondTimeout);
                if (!mOtaApi.isApiConnectedService()) {
                    Log.e(TAG, "OtaApi is not connected after " + millSecondTimeout + "ms. return");
                    return ret;
                } else if (mOtaApi.getNavigationVersion() == Definition.CMD_SEND_ERROR_UNKNOWN) {
                    Log.e(TAG, "send get tk1 version error. got -1");
                    return ret;
                }

                checkAndLockWait(mKeyLockMap.get(KEY_NAVIGATION_VERSION_RESULT), millSecondTimeout);
                ret = mResult.remove(KEY_NAVIGATION_VERSION_RESULT);
                Log.d(TAG, "getNavigationVersion get=" + ret);
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        return ret;
    }

    @Override
    public String startCanUpdate(String params, int millSecondTimeout) {
        String ret = null;
        try {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_OTA_START))) {
                checkAndWaitForServiceConnect(millSecondTimeout);
                if (!mOtaApi.isApiConnectedService()) {
                    Log.e(TAG, "OtaApi is not connected after " + millSecondTimeout + "ms. return");
                    return ret;
                } else if (mOtaApi.startCanUpdate(params) == Definition.CMD_SEND_ERROR_UNKNOWN) {
                    Log.e(TAG, "send start can update error. got -1");
                    return ret;
                }
                checkAndLockWait(mKeyLockMap.get(KEY_CAN_OTA_START), millSecondTimeout);
                ret = mResult.remove(KEY_CAN_OTA_START);
                Log.d(TAG, "startCanUpdate ret=" + ret);
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return ret;
    }

    @Override
    public String getCanOtaState(int millSecondTimeout) {
        String ret = null;
        try {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_OTA_STATE))) {
                checkAndWaitForServiceConnect(millSecondTimeout);
                if (!mOtaApi.isApiConnectedService()) {
                    Log.e(TAG, "OtaApi is not connected after " + millSecondTimeout + "ms. return");
                    return ret;
                } else if (mOtaApi.getCanOtaState() == Definition.CMD_SEND_ERROR_UNKNOWN) {
                    Log.e(TAG, "send start can update error. got -1");
                    return ret;
                }
                checkAndLockWait(mKeyLockMap.get(KEY_CAN_GET_OTA_STATE), millSecondTimeout);
                ret = mResult.remove(KEY_CAN_GET_OTA_STATE);
                Log.d(TAG, "getCanOtaState ret=" + ret);
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return ret;
    }

    @Override
    public String getCanBoardVersion(String params, int millSecondTimeout) {
        String ret = null;
        try {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_BOARD_VERSION))) {
                checkAndWaitForServiceConnect(millSecondTimeout);
                if (!mOtaApi.isApiConnectedService()) {
                    Log.e(TAG, "OtaApi is not connected after " + millSecondTimeout + "ms. return");
                    return ret;
                } else if (mOtaApi.getCanBoardVersion(params) == Definition.CMD_SEND_ERROR_UNKNOWN) {
                    Log.e(TAG, "send get can board_version error. got -1");
                    return ret;
                }
                checkAndLockWait(mKeyLockMap.get(KEY_CAN_GET_BOARD_VERSION), millSecondTimeout);
                ret = mResult.remove(KEY_CAN_GET_BOARD_VERSION);
                Log.d(TAG, "getCanBoardVersion ret=" + ret);
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return ret;
    }

    @Override
    public String getCanMotorHVersion(int millSecondTimeout) {
        String ret = null;
        try {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_MOTOR_H_VERSION))) {
                checkAndWaitForServiceConnect(millSecondTimeout);
                if (!mOtaApi.isApiConnectedService()) {
                    Log.e(TAG, "OtaApi is not connected after " + millSecondTimeout + "ms. return");
                    return ret;
                } else if (mOtaApi.getCanMotorHorizontalVersion() == Definition.CMD_SEND_ERROR_UNKNOWN) {
                    Log.e(TAG, "send get can board_version error. got -1");
                    return ret;
                }
                checkAndLockWait(mKeyLockMap.get(KEY_CAN_GET_MOTOR_H_VERSION), millSecondTimeout);
                ret = mResult.remove(KEY_CAN_GET_MOTOR_H_VERSION);
                Log.d(TAG, "getCanMotorHVersion ret=" + ret);
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return ret;
    }

    @Override
    public String getCanMotorVVersion(int millSecondTimeout) {
        String ret = null;
        try {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_MOTOR_V_VERSION))) {
                checkAndWaitForServiceConnect(millSecondTimeout);
                if (!mOtaApi.isApiConnectedService()) {
                    Log.e(TAG, "OtaApi is not connected after " + millSecondTimeout + "ms. return");
                    return ret;
                } else if (mOtaApi.getCanMotorVerticalVersion() == Definition.CMD_SEND_ERROR_UNKNOWN) {
                    Log.e(TAG, "send get can board_version error. got -1");
                    return ret;
                }
                checkAndLockWait(mKeyLockMap.get(KEY_CAN_GET_MOTOR_V_VERSION), millSecondTimeout);
                ret = mResult.remove(KEY_CAN_GET_MOTOR_V_VERSION);
                Log.d(TAG, "getCanMotorVVersion ret=" + ret);
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return ret;
    }

    @Override
    public String getCanPsbVersion(int millSecondTimeout) {
        String ret = null;
        try {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_PSB_VERSION))) {
                checkAndWaitForServiceConnect(millSecondTimeout);
                if (!mOtaApi.isApiConnectedService()) {
                    Log.e(TAG, "OtaApi is not connected after " + millSecondTimeout + "ms. return");
                    return ret;
                } else if (mOtaApi.getCanPsbVersion() == Definition.CMD_SEND_ERROR_UNKNOWN) {
                    Log.e(TAG, "send get can board_version error. got -1");
                    return ret;
                }
                checkAndLockWait(mKeyLockMap.get(KEY_CAN_GET_PSB_VERSION), millSecondTimeout);
                ret = mResult.remove(KEY_CAN_GET_PSB_VERSION);
                Log.d(TAG, "getCanMotorPsbVersion ret=" + ret);
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return ret;
    }

    @Override
    public String getCanAcClientVersion(int millSecondTimeout) {
        String ret = null;
        try {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_AC_CLIENT_VERSION))) {
                checkAndWaitForServiceConnect(millSecondTimeout);
                if (!mOtaApi.isApiConnectedService()) {
                    Log.e(TAG, "OtaApi is not connected after " + millSecondTimeout + "ms. return");
                    return ret;
                } else if (mOtaApi.getCanAutoChargeVersion() == Definition.CMD_SEND_ERROR_UNKNOWN) {
                    Log.e(TAG, "send get can board_version error. got -1");
                    return ret;
                }
                checkAndLockWait(mKeyLockMap.get(KEY_CAN_GET_AC_CLIENT_VERSION), millSecondTimeout);
                ret = mResult.remove(KEY_CAN_GET_AC_CLIENT_VERSION);
                Log.d(TAG, "getCanAcClientVersion ret=" + ret);
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return ret;
    }

    @Override
    public String getCanBmsVersion(int millSecondTimeout) {
        String ret = null;
        try {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_BMS_VERSION))) {
                checkAndWaitForServiceConnect(millSecondTimeout);
                if (!mOtaApi.isApiConnectedService()) {
                    Log.e(TAG, "OtaApi is not connected after " + millSecondTimeout + "ms. return");
                    return ret;
                } else if (mOtaApi.getCanBatteryVersion() == Definition.CMD_SEND_ERROR_UNKNOWN) {
                    Log.e(TAG, "send get can board_version error. got -1");
                    return ret;
                }
                checkAndLockWait(mKeyLockMap.get(KEY_CAN_GET_BMS_VERSION), millSecondTimeout);
                ret = mResult.remove(KEY_CAN_GET_BMS_VERSION);
                Log.d(TAG, "getCanBmsVersion ret=" + ret);
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return ret;
    }

    @Override
    public String getCanPsbSVersion(int millSecondTimeout) {
        String ret = null;
        try {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_PSB_S_VERSION))) {
                checkAndWaitForServiceConnect(millSecondTimeout);
                if (!mOtaApi.isApiConnectedService()) {
                    Log.e(TAG, "getCanPsbSVersion:: OtaApi is not connected after " + millSecondTimeout + "ms. return");
                    return ret;
                } else if (mOtaApi.getCanPsbSVersion() == Definition.CMD_SEND_ERROR_UNKNOWN) {
                    Log.e(TAG, "getCanPsbSVersion:: send get can board_version error. got -1");
                    return ret;
                }
                checkAndLockWait(mKeyLockMap.get(KEY_CAN_GET_PSB_S_VERSION), millSecondTimeout);
                ret = mResult.remove(KEY_CAN_GET_PSB_S_VERSION);
                Log.d(TAG, "getCanPsbSVersion:: ret=" + ret);
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return ret;
    }

    @Override
    public int sendOtaEvent(boolean isForce, String params) {
        int ret = -1;
        try {
            Log.d(TAG, "sendOtaEvent. params:" + params);
            Log.d(TAG, "sendOtaEvent. isApiConnectedService:" + mOtaApi.isApiConnectedService());
            Log.d(TAG, "sendOtaEvent. mCallBackRegister:" + mCallBackRegisterd);
            if (!mOtaApi.isApiConnectedService() || !mCallBackRegisterd) {
                checkAndWaitForServiceConnect(200);
            }

            synchronized (mConnectLock){
                if (mOtaApi.isApiConnectedService() && mCallBackRegisterd) {
                    Log.d(TAG, "real sendOtaEvent. params:" + params);
                    ret = mOtaApi.sendOtaEvent(isForce, params);
                }
            }
        } catch (InterruptedException e) {
            Log.d(TAG, "sendOtaEvent. error:" + e.getMessage());
        }
        return ret;
    }

    @Override
    public int sendDowngradeOtaEvent(String params) {
        int ret = -1;
        try {
            Log.d(TAG, "sendDowngradeOtaEvent. params:" + params);
            Log.d(TAG, "sendDowngradeOtaEvent. isApiConnectedService:" + mOtaApi.isApiConnectedService());
            Log.d(TAG, "sendDowngradeOtaEvent. mCallBackRegister:" + mCallBackRegisterd);
            if (!mOtaApi.isApiConnectedService() || !mCallBackRegisterd) {
                checkAndWaitForServiceConnect(200);
            }

            synchronized (mConnectLock){
                if (mOtaApi.isApiConnectedService() && mCallBackRegisterd) {
                    Log.d(TAG, "real sendDowngradeOtaEvent. params:" + params);
                    ret = mOtaApi.sendDowngradeOtaEvent(params);
                }
            }
        } catch (InterruptedException e) {
            Log.d(TAG, "sendDowngradeOtaEvent. error:" + e.getMessage());
        }
        return ret;
    }

    @Override
    public int sendOtaDownloadSuccess(String params) {
        int ret = -1;
        synchronized (mConnectLock){
            if (mOtaApi.isApiConnectedService() && mCallBackRegisterd) {
                Log.d(TAG, "real sendOtaDownloadSuccess. params:" + params);
                ret = mOtaApi.sendOtaDownloadSuccess(params);
            }
        }
        return ret;
    }

    @Override
    public int finishOtaUpgrade(String params) {
        int ret = -1;
        try {
            Log.d(TAG, "finishOtaUpgrade. params:" + params);
            while (!mOtaApi.isApiConnectedService()) {
                checkAndWaitForServiceConnect(2000);
            }

            if (mOtaApi.isApiConnectedService()) {
                ret = mOtaApi.finishOtaUpgrade(params);
            }
        } catch (InterruptedException e) {
            Log.d(TAG, "finishOtaUpgrade. error:" + e.getMessage());
        }

        return ret;
    }

    @Override
    public void updateProgress(String params) {
        try {
            Log.d(TAG, "updateProgress. params:" + params);
            if (!mOtaApi.isApiConnectedService()) {
                checkAndWaitForServiceConnect(200);
            }

            if (mOtaApi.isApiConnectedService()) {
                mOtaApi.updateProgress(params);
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public static void checkAndWaitForServiceConnect(int millSecondTimeout) throws InterruptedException {
        synchronized (mConnectLock) {
            if (!mOtaApi.isApiConnectedService()) {
                mOtaApi.connectApi();
                Log.d(TAG, "checkAndWaitForServiceConnect." + millSecondTimeout + " ms timeout.");

                if (millSecondTimeout > 0) {
                    mConnectLock.wait(millSecondTimeout);
                } else {
                    mConnectLock.wait();
                }
            }

            if(!mCallBackRegisterd){
                Log.d(TAG, "checkAndWaitForRegisterCallBack." + millSecondTimeout + " ms timeout.");
                if (millSecondTimeout > 0) {
                    mConnectLock.wait(millSecondTimeout);
                } else {
                    mConnectLock.wait();
                }
            }
        }
    }

    private void checkAndLockWait(Object lock, int millSecondTimeout) throws InterruptedException {
        if (millSecondTimeout > 0) {
            Log.d(TAG, "ota request wait " + millSecondTimeout / 1000 + "s ");
            lock.wait(millSecondTimeout);
        } else {
            lock.wait();
        }
    }

    public interface OnOtaStartListener {
        boolean onOtaStart(boolean needDownload);
        boolean onOtaRollbackStart();
        String onOtaGetDescription();
        boolean onOtaCancelDownload();
        void installPatch(Bundle bundle);
        void updateHeadConnectStatus(boolean isConnected);
        void onOtaInterrupted(String reason);
        void OnOtaServiceConfig(List<ServiceConfig> serviceConfigs);
    }

    private class CommandReply implements IOtaCallbackInterface {

        @Override
        public boolean onOtaUpgradeStart(boolean needDownload) {
            boolean ret = false;
            if (otaStartListener != null) {
                ret = otaStartListener.onOtaStart(needDownload);
            }
            return ret;
        }

        @Override
        public boolean onOtaRollbackStart() {
            boolean ret = false;
            if (otaStartListener != null) {
                ret = otaStartListener.onOtaRollbackStart();
            }
            return ret;
        }

        @Override
        public String onOtaGetDescription() {
            String ret = null;
            if (otaStartListener != null) {
                ret = otaStartListener.onOtaGetDescription();
            }
            Log.d(TAG, "ret=" + ret);
            return ret;
        }

        @Override
        public boolean onOtaCancelDownload() {
            if (otaStartListener != null) {
                return otaStartListener.onOtaCancelDownload();
            }
            return false;
        }

        @Override
        public void installPatch(Bundle bundle) {
            if (otaStartListener != null) {
                otaStartListener.installPatch(bundle);
            }
        }

        @Override
        public void onOtaInterrupted(String reason) {
            if (otaStartListener != null) {
                otaStartListener.onOtaInterrupted(reason);
            }
        }

        @Override
        public void updateHeadConnectStatus(String result) {
            if (otaStartListener != null) {
                otaStartListener.updateHeadConnectStatus(Definition.CMD_STATUS_OK.equals(result));
            }
        }

        @Override
        public void headScpResult(String result) {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_HEAD_PRE_SCP_RESULT))) {
                Log.d(TAG, "put head_pre_scp result=" + result);
                mResult.put(KEY_HEAD_PRE_SCP_RESULT, result);
                mKeyLockMap.get(KEY_HEAD_PRE_SCP_RESULT).notify();
            }
        }

        @Override
        public void headUpdateResult(String result) {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_HEAD_UPDATE_RESULT))) {
                try {
                    //skip ongoing status.
                    JSONObject params = new JSONObject(result);
                    String status = (String) params.get(OtaConstants.PARAM_STATUS);
                    if (OtaConstants.ONGOING.equals(status)) {
                        Log.d(TAG, "skip ongoing result status.");
                        return;
                    }
                } catch (JSONException e) {
                    Log.e(TAG, "parse params result failed. result=" + result);
                    e.printStackTrace();
                    return;
                }
                Log.d(TAG, "put head_update result=" + result);
                mResult.put(KEY_HEAD_UPDATE_RESULT, result);
                mKeyLockMap.get(KEY_HEAD_UPDATE_RESULT).notify();
            }
        }

        @Override
        public void headVersionResult(String result) {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_HEAD_VERSION_RESULT))) {
                Log.d(TAG, "put head_version result=" + result);
                mResult.put(KEY_HEAD_VERSION_RESULT, result);
                mKeyLockMap.get(KEY_HEAD_VERSION_RESULT).notify();
            }
        }

        @Override
        public void headUpdateParamsResult(String result) {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_HEAD_UPDATE_PARAMS_RESULT))) {
                Log.d(TAG, "put head_update_params result=" + result);
                mResult.put(KEY_HEAD_UPDATE_PARAMS_RESULT, result);
                mKeyLockMap.get(KEY_HEAD_UPDATE_PARAMS_RESULT).notify();
            }
        }

        @Override
        public void navigationUpdateResult(String result) {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_NAVIGATION_UPDATE_RESULT))) {
                Log.d(TAG, "put nav_update result=" + result);
                mResult.put(KEY_NAVIGATION_UPDATE_RESULT, result);
                mKeyLockMap.get(KEY_NAVIGATION_UPDATE_RESULT).notify();
            }
        }

        @Override
        public void navigationVersionResult(String result) {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_NAVIGATION_VERSION_RESULT))) {
                Log.d(TAG, "put nav_version result=" + result);
                mResult.put(KEY_NAVIGATION_VERSION_RESULT, result);
                mKeyLockMap.get(KEY_NAVIGATION_VERSION_RESULT).notify();
            }
        }

        @Override
        public void canOtaStartResult(String result) {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_OTA_START))) {
                Log.d(TAG, "put can_ota_start result=" + result);
                mResult.put(KEY_CAN_OTA_START, result);
                mKeyLockMap.get(KEY_CAN_OTA_START).notify();
            }
        }

        @Override
        public void canOtaGetStateResult(String result) {
            // all internal state is skipped
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_OTA_STATE))) {
                Log.d(TAG, "put can_get_ota_state result=" + result);

                if (OtaConstants.CMD_TIMEOUT.equals(result)) {
                    mResult.put(KEY_CAN_GET_OTA_STATE, result);
                    Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_OTA_STATE)).notify();
                    return;
                }

                CanOtaBean bean = mGson.fromJson(result, CanOtaBean.class);
                switch (bean.getStatus()) {
                    case OtaConstants.OTA_UPDATE_STATUS_COMPLIETE: {
                        Log.d(TAG,  "board: " + bean.getBoard() + " update success.");
                        mResult.put(KEY_CAN_GET_OTA_STATE, result);
                        Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_OTA_STATE)).notify();
                        break;
                    }

                    case OtaConstants.OTA_UPDATE_STATUS_ABNORMAL: {
                        Log.d(TAG, "board: " + bean.getBoard() + " update failed. " +
                                ", failReason:" + bean.getFailReason());
                        mResult.put(KEY_CAN_GET_OTA_STATE, result);
                        Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_OTA_STATE)).notify();
                        break;
                    }

                    default: {
                        Log.d(TAG, "canOtaGetState internal message:" + bean);
                    }
                }
            }
        }

        @Override
        public void canGetBoardVersionResult(String result) {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_BOARD_VERSION))) {
                Log.d(TAG, "put board_version result=" + result);
                mResult.put(KEY_CAN_GET_BOARD_VERSION, result);
                Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_BOARD_VERSION)).notify();
            }
        }

        @Override
        public void canGetPsbVersionResult(String result) {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_PSB_VERSION))) {
                Log.d(TAG, "put psb_version result=" + result);
                mResult.put(KEY_CAN_GET_PSB_VERSION, result);
                Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_PSB_VERSION)).notify();
            }
        }

        @Override
        public void canGetMotorHVersionResult(String result) {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_MOTOR_H_VERSION))) {
                Log.d(TAG, "put motor_h_version result=" + result);
                mResult.put(KEY_CAN_GET_MOTOR_H_VERSION, result);
                Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_MOTOR_H_VERSION)).notify();
            }
        }

        @Override
        public void canGetMotorVVersionResult(String result) {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_MOTOR_V_VERSION))) {
                Log.d(TAG, "put motor_v_version result=" + result);
                mResult.put(KEY_CAN_GET_MOTOR_V_VERSION, result);
                Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_MOTOR_V_VERSION)).notify();
            }
        }

        @Override
        public void canGetAcClientVersionResult(String result) {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_AC_CLIENT_VERSION))) {
                Log.d(TAG, "put ac_client_version result=" + result);
                mResult.put(KEY_CAN_GET_AC_CLIENT_VERSION, result);
                Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_AC_CLIENT_VERSION)).notify();
            }
        }

        @Override
        public void canGetBmsVersionResult(String result) {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_BMS_VERSION))) {
                Log.d(TAG, "put bms_version result=" + result);
                mResult.put(KEY_CAN_GET_BMS_VERSION, result);
                Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_BMS_VERSION)).notify();
            }
        }

        @Override
        public void canGetPsbSVersionResult(String result) {
            synchronized (Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_PSB_S_VERSION))) {
                Log.d(TAG, "put psb_s_version result=" + result);
                mResult.put(KEY_CAN_GET_PSB_S_VERSION, result);
                Objects.requireNonNull(mKeyLockMap.get(KEY_CAN_GET_PSB_S_VERSION)).notify();
            }
        }
    }
}
