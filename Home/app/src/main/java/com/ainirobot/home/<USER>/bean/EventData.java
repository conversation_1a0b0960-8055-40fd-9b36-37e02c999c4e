package com.ainirobot.home.ota.bean;

/**
 * OTA 执行事件通知接口数据类 事件携带的数据
 * example: {"from_version":"V10.3.X","to_version_id":"123","to_version":"V10.1.X"}
 */
public class EventData {
    /**
     *  当前版本号
     */
    private String fromVersion; // android.os.SystemProperties.get(PROPERTY_VERSION, "")
    /**
     * 服务端下发的目标版本 version_id
     */
    private String toVersionId;
    /**
     * 目标版本号
     */
    private String toVersion;

    public EventData(String fromVersion, String toVersionId, String toVersion) {
        this.fromVersion = fromVersion;
        this.toVersionId = toVersionId;
        this.toVersion = toVersion;
    }

    public String getFromVersion() {
        return fromVersion;
    }

    public void setFromVersion(String fromVersion) {
        this.fromVersion = fromVersion;
    }

    public String getToVersionId() {
        return toVersionId;
    }

    public void setToVersionId(String toVersionId) {
        this.toVersionId = toVersionId;
    }

    public String getToVersion() {
        return toVersion;
    }

    public void setToVersion(String toVersion) {
        this.toVersion = toVersion;
    }

    @Override
    public String toString() {
        return "EventData{" +
                "fromVersion='" + fromVersion + '\'' +
                ", toVersionId='" + toVersionId + '\'' +
                ", toVersion='" + toVersion + '\'' +
                '}';
    }
}
