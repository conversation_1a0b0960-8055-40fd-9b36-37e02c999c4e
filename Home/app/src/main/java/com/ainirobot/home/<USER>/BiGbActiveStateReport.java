package com.ainirobot.home.bi;


import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.utils.SystemUtils;

/**
 * active state bi report
 *
 * @version V1.0.0
 * @date 2019/1/3 19:53
 */
public class BiGbActiveStateReport extends BaseBiReport {

    private static final String TABLE_NAME = "gb_active";
    private static final String TABLE_COLUMN_STATE = "state";
    private static final String TABLE_COLUMN_CTIME = "ctime";
    private static final String TABLE_COLUMN_IMEI = "imei";

    public static final int NORMAL_STATE = 0x01;
    public static final int CHARGING_STATE = 0x02;
    public static final int EMERGENCY_STATE = 0x03;
    public static final int POWER_ON_STATE = 0x04;
    public static final int DATE_CHANGE_STATE = 0x05;
    /**
     * when receive power off broadcast,bi socket was released,so unable to bi report
     */
    public static final int POWER_OFF_STATE = 0x06;

    public static final int TIME_REPORT = 0x07;

    public BiGbActiveStateReport() {
        super(TABLE_NAME);
    }


    public BiGbActiveStateReport addState(int state) {
        addActiveData(state, System.currentTimeMillis());
        return this;
    }

    public BiGbActiveStateReport addState(int state, long timestamp) {
        addActiveData(state, timestamp);
        return this;
    }

    private void addActiveData(int state, long timestamp) {
        clearReportDatas();
        addData(TABLE_COLUMN_CTIME, timestamp);
        addData(TABLE_COLUMN_STATE, state);
        addData(TABLE_COLUMN_IMEI, SystemUtils.getSystemImei());
    }
}
