package com.ainirobot.home.module;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.RadarManager;
import com.ainirobot.home.control.RadarManager.RadarListener;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.utils.MapUtils;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Timer;
import java.util.TimerTask;


/**
 * Semi-managed mode completes the function of remote positioning, which can not only obtain
 * infrared information and relocate by remote control. Positioning can also be done directly
 * by human operation.
 *
 * <p>The remote relocation mode maintains a { REMOTE_REPOSITION } system state with priority 45 in
 * the { com.ainirobot.coreservice#SystemStatus } .</p>
 *
 * <p>In order to avoid displaying the charging status after exit, it is necessary to actively clear
 * the Battery status when remote relocation exits. Complete the cleanup in
 * { com.ainirobot.coreservice#SystemManager }</p>
 * <p>
 * Created by Orion on 2019/2/18.
 */
public class RemoteRepositionModule extends BaseModule {
    private static final String TAG = "RemoteRepoModule:Home";
    private static final float GO_FORWARD_SPEED = 0.2f;
    private static final float GO_FORWARD_DISTANCE = ProductInfo.isSaiphXdOrBigScreen()
            || ProductInfo.isSaiphChargeIr() || ProductInfo.isSaiphPro()
            || ProductInfo.isAlnilamPro() || ProductInfo.isMeissaPlus() ? 0.25f : 0.1f;

    private static final int DEFAULT_ANGULAR_SPEED = 20;
    private static final int MIDDLE_ANGULAR_SPEED = 80;
    private static final int MSG_LOCATION_TIMER = 0x01;
    private static final int MSG_CHARGING_STATUS_CHECK = 0x02;
    private static final long LOCATION_TIME_OUT = 90 * 1000;
    private static final long CHARGE_STATUS_CHECK_INTERVAL = 2 * 1000;

    private static RemoteRepositionModule sInstance;
    private Context mContext;
    private Handler mHandler = null;
    private State mState = State.IDLE;
    private ExitMode mExitMode = ExitMode.MODULE_CHANGE;
    private int mReqId = 0;
    private boolean mFindChargePile = false;
    private Timer mRelocateTimer;

    private enum State {
        IDLE,
        GET_INFRARED_INFO,
        LOCATION_CHARGE,
        POSE_ESTIMATE,
        GO_FORWARD,
    }

    private enum Result {
        SUCCESS(1),
        NO_CHARGE_PILE(2),
        CHARGE_LOCATION_START(3),
        CHARGE_LOCATION_FAIL(4),
        ESTIMATE_FAIL(5),
        TIME_OUT(6);

        private int value;

        Result(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    private enum ExitMode {
        SUCCESS,
        FAILURE,
        REMOTE_CLOSE,
        UI_CANCEL,
        MODULE_CHANGE
    }

    public static RemoteRepositionModule getInstance() {
        if (null == sInstance) {
            sInstance = new RemoteRepositionModule();
        }
        return sInstance;
    }

    private RemoteRepositionModule() {
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.i(TAG, "onNewSemantics : " + reqId + ", " + intent + ", " + text + ", " + params);
        Log.i(TAG, "onNewSemantics : mState= " + mState);
        switch (intent) {
            case ModuleDef.REQ_REMOTE_RELOCATE:
                if (mState == State.IDLE) {
                    mReqId = parseSwitchOpenReqId(reqId, params);
                    Log.i(TAG, "onNewSemantics : mReqId= " + mReqId);
                    startGetInfraredInfoDelay(mReqId);
                    sendCheckChargeStatusMsg();
                    sendRemoteResponse(mReqId, Definition.RELOCATE_SWITCH_OPEN, 0, "");
                    UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_REMOTE_REPOSITION, null, null);
                }
                break;

            case Definition.RELOCATE_ACTION:
                if (mState != State.IDLE) {
                    sendRemoteResponse(reqId, Definition.RELOCATE_ACTION, 0, "");
                    startLocationChargeDelay(reqId);
                } else {
                    release(reqId, RESULT_FAILURE, null);
                }
                break;

            case Definition.RELOCATE_SWITCH_CLOSE:
                if (mState != State.IDLE) {
                    sendRemoteResponse(reqId, Definition.RELOCATE_SWITCH_CLOSE, 0, "");
                    stopWithMode(ExitMode.REMOTE_CLOSE);
                } else {
                    release(reqId, RESULT_FAILURE, null);
                }
                break;

            default:
                break;
        }

        SystemApi.getInstance().finishModuleParser(reqId, true);
        return super.onNewSemantics(reqId, intent, text, params);
    }

    private int parseSwitchOpenReqId(int reqId, String param) {
        Log.i(TAG, "parseSwitchOpenReqId, reqId= " + mState + ", param= " + param);
        if (TextUtils.isEmpty(param)) {
            return reqId;
        }
        try {
            JSONObject object = new JSONObject(param);
            if (object.has(Definition.REQ_SWITCH_OPEN_ID)) {
                return object.optInt(Definition.REQ_SWITCH_OPEN_ID);
            } else {
                return reqId;
            }
        } catch (JSONException e) {
            Log.w(TAG, Log.getStackTraceString(e));
        }
        return reqId;
    }

    @Override
    protected void onStop() {
        Log.i(TAG, "onStop, state= " + mState + ", mode= " + mExitMode);
        sendCloseEventReport(mExitMode.toString());
        cancelDelayLocationTimer();
        mExitMode = ExitMode.MODULE_CHANGE;
        mState = State.IDLE;
        mFindChargePile = false;
        removeLocationTimeOutMsg();
        removeCheckChargeStatusMsg();
        SystemApi.getInstance().setVerticalMaxLimitAngle(mReqId, 0, null);
        SystemApi.getInstance().resetHead(mReqId, null);
        SystemApi.getInstance().stopGetCanInfraredInfo(mReqId);
        stopLocationCharge(mReqId);

        SystemApi.getInstance().onRemoteRepositionFinished();
        super.onStop();
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
        Log.i(TAG, "onMessageFromLocal, " + type);
        switch (type) {
            case ModuleDef.LOCAL_MESSAGE_REPOSITION_CANCEL:
                stopWithMode(ExitMode.UI_CANCEL);
                break;
            case ModuleDef.LOCAL_MESSAGE_CHARGE_START:
                sendChargeStateToFragment(true);
                break;
            case ModuleDef.LOCAL_MESSAGE_CHARGE_END:
                sendChargeStateToFragment(false);
                break;
        }
    }

    private void sendChargeStateToFragment(boolean charging) {
        if (mState == State.GET_INFRARED_INFO || mState == State.LOCATION_CHARGE) {
            UIController.getInstance().sendMessageToFragment(
                    UIController.MESSAGE_TYPE.MSG_REMOTE_CHARGE_STATUS, String.valueOf(charging));
        }
    }

    public void init(Context context) {
        this.mContext = context;
        if (mHandler == null) {
            initHandler();
        }
    }

    @SuppressLint("HandlerLeak")
    private void initHandler() {
        this.mHandler = new Handler() {
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);
                Log.i(TAG, "handleMessage : " + msg.what + ", state= " + mState);
                switch (msg.what) {
                    case MSG_LOCATION_TIMER:
                        if (mState != State.LOCATION_CHARGE) {
                            removeLocationTimeOutMsg();
                            return;
                        }
                        estimateTimeOut();
                        break;
                    case MSG_CHARGING_STATUS_CHECK:
                        getChargeStatus();
                        mHandler.sendEmptyMessageDelayed(MSG_CHARGING_STATUS_CHECK, CHARGE_STATUS_CHECK_INTERVAL);
                        break;
                    default:
                        break;
                }
            }
        };
    }

    private void setHeadMaxLimitAngle(final int reqId) {
        Log.i(TAG, "setHeadMaxLimitAngle");
        SystemApi.getInstance().setVerticalMaxLimitAngle(reqId, 1, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.i(TAG, "setHeadMaxLimitAngle onResult, " + result + ", " + message);
                moveHeadDown(reqId);
            }

            @Override
            public void onError(int errorCode, String errorString) {
                Log.i(TAG, "setHeadMaxLimitAngle onResult, " + errorCode + ", " + errorString);
                moveHeadDown(reqId);
            }
        });
    }

    private void moveHeadDown(int reqId) {
        Log.i(TAG, "moveHeadDown");
        SystemApi.getInstance().moveHead(reqId, "absolute", "absolute",
                0, 90, MIDDLE_ANGULAR_SPEED, DEFAULT_ANGULAR_SPEED,
                new CommandListener() {
                    @Override
                    public void onStatusUpdate(int status, String data) {
                        Log.i(TAG, "moveHeadDown onStatusUpdate, " + status + ", " + data);
                    }

                    @Override
                    public void onResult(int result, String message) {
                        Log.i(TAG, "moveHeadDown onResult, " + result + ", " + message);
                    }
                });
    }

    private void startGetInfraredInfoDelay(final int reqId) {
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                startGetInfraredInfo(reqId);
            }
        }, 200);
    }

    private void startGetInfraredInfo(final int reqId) {
        Log.i(TAG, "startGetInfraredInfo");
        setHeadMaxLimitAngle(reqId);

        mState = State.GET_INFRARED_INFO;
        SystemApi.getInstance().getCanInfraredInfo(reqId, new ActionListener() {
            @Override
            public void onResult(int status, String responseString) {
                Log.i(TAG, "startGetInfraredInfo onResult, " + status + ", " + responseString);
            }

            @Override
            public void onStatusUpdate(int status, String data) {
                Log.i(TAG, "startGetInfraredInfo onStatusUpdate, " + status + ", " + data);
                if (!TextUtils.isEmpty(data)) {
                    sendInfraredEventReport(data);
                }
            }

            @Override
            public void onError(int errorCode, String errorString) {
                Log.i(TAG, "startGetInfraredInfo onError, " + errorCode + ", " + errorString);
            }
        });
    }

    /**
     * When the infrared signal is acquired,
     * the PSB infrared module will filter out the first 3s signal,
     * so it needs to delay 3s before starting the repositioning and recharge.
     */
    private void startLocationChargeDelay(final int reqId) {
        Log.i(TAG, "startLocationChargeDelay");
        mState = State.LOCATION_CHARGE;
        sendRelocateEventReport(Result.CHARGE_LOCATION_START);
        SystemApi.getInstance().stopGetCanInfraredInfo(reqId);
        SystemApi.getInstance().switchChargeMode(reqId);

        cancelDelayLocationTimer();
        mRelocateTimer = new Timer();
        mRelocateTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                Log.i(TAG, "startLocationChargeDelay timer run, mState= " + mState);
                if (mState == State.LOCATION_CHARGE) {
                    startLocationCharge(reqId);
                }
            }
        }, 3 * 1000);
        sendLocationTimeOutMsg();
    }

    private void cancelDelayLocationTimer() {
        if (mRelocateTimer != null) {
            mRelocateTimer.cancel();
            mRelocateTimer = null;
        }
    }

    /**
     * Try to move to charge pile without estimate.
     * Need to stop get can infrared info and surrender the controller of navigation first.
     */
    private void startLocationCharge(int reqId) {
        Log.i(TAG, "startLocationCharge");
        SystemApi.getInstance().startLocationCharge(reqId, 1, new CommandListener() {
            @Override
            public void onResult(int status, String responseString) {
                Log.i(TAG, "startLocationCharge onResult, " + status + ", " + responseString);
            }

            @Override
            public void onStatusUpdate(int status, String data) {
                Log.i(TAG, "startLocationCharge onStatusUpdate, " + status + ", " + data);
            }

            @Override
            public void onError(int errorCode, String errorString) {
                Log.i(TAG, "startLocationCharge onError, " + errorCode + ", " + errorString);
            }
        });
    }

    private void stopLocationCharge(int reqId) {
        Log.i(TAG, "stopLocationCharge");
        SystemApi.getInstance().startLocationCharge(reqId, 0, null);
    }

    private void sendLocationTimeOutMsg() {
        Log.i(TAG, "sendLocationTimeOutMsg");
        removeLocationTimeOutMsg();
        if (mState == State.LOCATION_CHARGE) {
            mHandler.sendEmptyMessageDelayed(MSG_LOCATION_TIMER, LOCATION_TIME_OUT);
        }
    }

    private void removeLocationTimeOutMsg() {
        mHandler.removeMessages(MSG_LOCATION_TIMER);
    }

    private void sendCheckChargeStatusMsg() {
        Log.i(TAG, "sendCheckChargeStatusMsg");
        removeCheckChargeStatusMsg();
        mHandler.sendEmptyMessageDelayed(MSG_CHARGING_STATUS_CHECK, CHARGE_STATUS_CHECK_INTERVAL);
    }

    private void removeCheckChargeStatusMsg() {
        mHandler.removeMessages(MSG_CHARGING_STATUS_CHECK);
    }

    private void getChargeStatus() {
        SystemApi.getInstance().getChargeStatus(mReqId, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.i(TAG, "getChargeStatus onResult, " + result + ", " + message);
                if (TextUtils.isEmpty(message)) {
                    return;
                }
                int status = Integer.valueOf(message);
                if (status == Definition.AUTOCHARGE_STATUS_DONE) {
                    Log.i(TAG, "location charge success, state= " + mState);
                    removeCheckChargeStatusMsg();
                    removeLocationTimeOutMsg();
                    stopLocationCharge(mReqId);
                    SystemApi.getInstance().stopGetCanInfraredInfo(mReqId);
                    SystemApi.getInstance().resetHead(mReqId, null);
                    UIController.getInstance().sendMessageToFragment(
                            UIController.MESSAGE_TYPE.MSG_REMOTE_REPO_ESTIMATE, "");

                    checkIsEstimate();
                } else if (status == Definition.AUTOCHARGE_STATUS_ONGOING) {
                    mFindChargePile = true;
                }
            }
        });
    }

    /**
     * Start pose estimate. Check current estimate state first.
     */
    private void checkIsEstimate() {
        mState = State.POSE_ESTIMATE;
        SystemApi.getInstance().isRobotEstimate(mReqId, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, "checkIsEstimate: " + result + ", " + message);
                if (result == Definition.RESULT_OK
                        && !TextUtils.isEmpty(message)
                        && "true".equals(message)) {
                    estimateSuccess();
                } else {
                    RadarManager.openRadar(new RadarListener() {
                        @Override
                        public boolean onSucceed() {
                            getChargePileLocation();
                            return true;
                        }
                    });
                }
            }
        });

    }

    private void getChargePileLocation() {
        SystemApi.getInstance().getLocation(mReqId, Definition.CHARGING_POLE_TYPE, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, "getChargePileLocation: " + result + ", " + message);
                if (result == Definition.RESULT_OK) {
                    try {
                        JSONObject jsonObject = new JSONObject(message);
                        Pose chargingPose = new Pose();
                        boolean state = jsonObject.optBoolean(Definition.JSON_NAVI_SITE_EXIST, false);
                        if (state) {
                            chargingPose.setX((float) jsonObject.optDouble(Definition.JSON_NAVI_POSITION_X, 0.0));
                            chargingPose.setY((float) jsonObject.optDouble(Definition.JSON_NAVI_POSITION_Y, 0.0));
                            chargingPose.setTheta((float) jsonObject.optDouble(Definition.JSON_NAVI_POSITION_THETA, 0.0));
                            setPoseEstimate(chargingPose);
                        } else {
                            estimateFailure();
                        }
                    } catch (JSONException e) {
                        e.printStackTrace();
                        estimateFailure();
                    }
                } else {
                    estimateFailure();
                }
            }
        });
    }

    private void setPoseEstimate(Pose pose) {
        Log.d(TAG, "setPoseEstimate");
        SystemApi.getInstance().setPoseEstimate(mReqId, new Gson().toJson(pose), new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, "setPoseEstimate: " + result + ", " + message);
                if (result == Definition.RESULT_OK &&
                        !TextUtils.isEmpty(message) && message.equals("succeed")) {
                    estimateSuccess();
                } else {
                    estimateFailure();
                }
            }
        });
    }

    private void estimateSuccess() {
        Log.i(TAG, "estimateSuccess");
        sendRelocateEventReport(Result.SUCCESS);
        UIController.getInstance().sendMessageToFragment(
                UIController.MESSAGE_TYPE.MSG_REMOTE_REPO_SUCCESS, "");
        SkillManager.getInstance().speechWithFinishCallBack(
                mContext.getString(R.string.reposition_remote_estimate_success),
                new SkillManager.SpeechFinishCallBack() {
                    @Override
                    public void finish() {
                        goForward(true);
                    }
                });
    }

    private void estimateFailure() {
        Log.i(TAG, "estimateFailure");
        sendRelocateEventReport(Result.ESTIMATE_FAIL);
        UIController.getInstance().sendMessageToFragment(
                UIController.MESSAGE_TYPE.MSG_REMOTE_REPO_FAILURE, "");
        SkillManager.getInstance().speechWithFinishCallBack(
                mContext.getString(R.string.reposition_remote_estimate_failure),
                new SkillManager.SpeechFinishCallBack() {
                    @Override
                    public void finish() {
                        goForward(false);
                    }
                });
    }

    private void estimateTimeOut() {
        if (mFindChargePile) {
            sendRelocateEventReport(Result.CHARGE_LOCATION_FAIL);
        } else {
            sendRelocateEventReport(Result.NO_CHARGE_PILE);
        }
        UIController.getInstance().sendMessageToFragment(
                UIController.MESSAGE_TYPE.MSG_REMOTE_REPO_FAILURE, "");
        SkillManager.getInstance().speechWithFinishCallBack(
                mContext.getString(R.string.reposition_remote_estimate_time_out),
                new SkillManager.SpeechFinishCallBack() {
                    @Override
                    public void finish() {
                        stopWithMode(ExitMode.FAILURE);
                    }
                });
    }

    private void goForward(final boolean success) {
        Log.i(TAG, "goForward");
        mState = State.GO_FORWARD;
        float distance = GO_FORWARD_DISTANCE;
        distance += MapUtils.getStopChargeMoveOffset();
        SystemApi.getInstance().goForward(mReqId, GO_FORWARD_SPEED, distance,
                new CommandListener() {
                    @Override
                    public void onResult(int result, String message) {
                        super.onResult(result, message);
                        Log.d(TAG, "goForward onResult, " + result + " , " + message);
                        stopDelay(success);
                    }
                });
    }

    /**
     * In order to avoid the display of charging status after stop, the
     * delay is 1.5s before stop module.
     * The PSB board report battery status is not timely.
     */
    private void stopDelay(final boolean success) {
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                Log.i(TAG, "stopDelay, state= " + mState);
                if (mState != State.IDLE) {
                    stopWithMode(success ? ExitMode.SUCCESS : ExitMode.FAILURE);
                }
            }
        }, (long) (1.5 * 1000));
    }

    private void stopWithMode(ExitMode mode) {
        mExitMode = mode;
        stop();
    }

    private void sendRemoteResponse(int reqId, String type, int result, String msg) {
        Log.i(TAG, "sendRemoteResponse : " + reqId + ", " + type + ", " + result + ", " + msg);
        JsonObject obj = new JsonObject();
        obj.addProperty("reqId", reqId);
        obj.addProperty("type", type);
        obj.addProperty("result", result);
        obj.addProperty("errmsg", msg);
        SystemApi.getInstance().sendStatusReport(Definition.STATUS_PROCESS_STATE,
                new Gson().toJson(obj));
    }

    private void sendInfraredEventReport(String value) {
        Log.i(TAG, "sendInfraredEventReport : " + value);
        JsonObject obj = new JsonObject();
        obj.addProperty("event", Definition.REMOTE_EVENT_INFRARED);
        obj.addProperty("value", value);
        SystemApi.getInstance().sendStatusReport(Definition.STATUS_RELOCATE_EVENT,
                new Gson().toJson(obj));
    }

    private void sendRelocateEventReport(Result result) {
        Log.i(TAG, "sendRelocateEventReport : " + result + "-" + result.getValue());
        JsonObject obj = new JsonObject();
        obj.addProperty("event", Definition.REMOTE_EVENT_RELOCATE);
        obj.addProperty("value", result.getValue());
        SystemApi.getInstance().sendStatusReport(Definition.STATUS_RELOCATE_EVENT,
                new Gson().toJson(obj));
    }

    private void sendCloseEventReport(String result) {
        Log.i(TAG, "sendCloseEventReport : " + result);
        JsonObject obj = new JsonObject();
        obj.addProperty("event", Definition.REMOTE_EVENT_CLOSE);
        obj.addProperty("value", result);
        SystemApi.getInstance().sendStatusReport(Definition.STATUS_RELOCATE_EVENT,
                new Gson().toJson(obj));
    }

}
