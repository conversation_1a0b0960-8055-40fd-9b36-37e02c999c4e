package com.ainirobot.home.ota.utils;

import static com.ainirobot.home.ota.parser.PackData.CONFIG_CHECK;

import android.content.Context;
import android.content.SharedPreferences;

import com.ainirobot.home.ota.network.NetDefine;

public class Preferences {
    /**
     * The upgrade flag whether OTA is upgrading or not.
     */
    private static final String PREFS_OTA_IS_UPGRADING = "ota_is_upgrading";
    /**
     * 升级包大小
     */
    private static final String PREFS_DOWNLOAD_SIZE = "download_size";

    /**
     * 升级包下载地址
     */
    private static final String PREFS_DIFF_DOWNLOAD_URL = "download_URL";

    /**
     * 全量备份包下载地址
     */
    private static final String PREFS_FULL_DOWNLOAD_URL = "full_package_download_URL";

    /**
     * 全量备份进行中，用于续传
     */
    private static final String PREFS_FULL_PACKAGE_DOWNLOADING = "full_package_downloading";

    /**
     * 升级描述
     */
    private static final String PREFS_DIFF_PACKAGE_DESCRIPTOR = "package_descriptor";

    /**
     * 差分升级包MD5
     */
    private static final String PREFS_DIFF_PACKAGE_MD5 = "package_md5";

    /**
     * 全量升级包MD5
     */
    private static final String PREFS_FULL_PACKAGE_MD5 = "full_package_md5";

    /**
     * 差分包类型。 1: 全量包 0: 差分包
     */
    private static final String PREFS_DIFF_PACKAGE_TYPE = "diff_package_type";

    /**
     * 强制升级
     */
    private static final String PREFS_PACKAGE_FORCE_UPDATE = "forced_update";

    /**
     * 下位机升级包scp位置
     */
    private static final String PREFS_TX1_PACKAGE_PATH = "tx1_package";

    /**
     * 服务器返回版本结果，用于重启升级成功判断
     */
    private static final String PREFS_TARGET_VERSION = "target_version";

    /**
     * 升级前的版本
     */
    private static final String PREFS_BASE_VERSION = "base_version";

    /**
     * 升级id
     */
    private static final String PREFS_VERSION_ID = "version_id";

    /**
     * OTA包下载开始时间
     */
    private static final String PREFS_DOWNLOAD_START_TIME = "download_start_time";

    /**
     * OTA包下载结束时间
     */
    private static final String PREFS_DOWNLOAD_END_TIME = "download_end_time";

    /**
     * OTA升级开始时间
     */
    private static final String PREFS_OTA_INSTALL_START_TIME = "install_start_time";

    /**
     * OTA升级结束时间
     */
    private static final String PREFS_OTA_INSTALL_END_TIME = "install_end_time";

    /**
     * OTA升级结束时间
     */
    private static final String PREFS_OTA_SILENT_UPDATE = "silent_update";

    /**
     * 最近一次下载URL
     */
    private static final String PREFS_LASTEST_DOWNLOAD_URL = "lastest_download_URL";

    /**
     * Host升级结果，本地升级时显示
     */
    private static final String PREFS_HOST_UPDATE_RESULT = "host_update_result";

    private static final String PREFS_ROLLBACK = "rollback";

    private static final String PREFS_UPGRADE_FAILED_REASON = "upgrade_failed_reason";

    private static final String PREFS_STARTING_MODE = "starting_mode";

    private static final String  PREFS_ROLLBACK_FAILED_NEED_BI_REPORT = "rollback_failed_need_bi_report";

    /**
     * ota版本类型
     */
    private static final String PREFS_COMPONENT_TYPE = "component_type";

    /**
     * 设置ota版本类型
     * @param type
     */
    public void setComponentType(String type){
        setString(PREFS_COMPONENT_TYPE, type);
    }

    /**
     * 获取ota版本类型
     * @return
     */
    public String getComponentType(){
        return mPrefs.getString(PREFS_COMPONENT_TYPE, "");
    }

    public void setPrefsRollback(boolean rollback) {
        setBoolean(PREFS_ROLLBACK, rollback);
    }

    public boolean getPrefsRollback() {
        return mPrefs.getBoolean(PREFS_ROLLBACK, false);
    }

    private static SharedPreferences mPrefs;

    private static Preferences instance;

    private Preferences(Context context) {
        mPrefs = context.getSharedPreferences("home_ota_sp", Context.MODE_PRIVATE);
    }

    public static synchronized void initialize(Context context) {
        if (instance == null) {
            instance = new Preferences(context);
        } else {
            throw new RuntimeException("Preferences has initialized!");
        }
    }

    public static synchronized Preferences getInstance() {
        if (instance == null) {
            throw new RuntimeException("Preferences is null! need intialize first.");
        }

        return instance;
    }

    private void setString(String key, String Str) {
        SharedPreferences.Editor mEditor = mPrefs.edit();
        mEditor.putString(key, Str);
        mEditor.commit();
    }

    private void setInt(String key, int Int) {
        SharedPreferences.Editor mEditor = mPrefs.edit();
        mEditor.putInt(key, Int);
        mEditor.commit();
    }

    private void setLong(String key, long Long) {
        SharedPreferences.Editor mEditor = mPrefs.edit();
        mEditor.putLong(key, Long);
        mEditor.commit();
    }

    private void setBoolean(String key, Boolean bool) {
        SharedPreferences.Editor mEditor = mPrefs.edit();
        mEditor.putBoolean(key, bool);
        mEditor.commit();
    }

    public void setOtaIsUpgrading(boolean isUpgrading) {
        setBoolean(PREFS_OTA_IS_UPGRADING, isUpgrading);
    }

    public boolean getOtaIsUpgrading() {
        return mPrefs.getBoolean(PREFS_OTA_IS_UPGRADING, false);
    }

    public void setDownloadSize(long size) {
        setLong(PREFS_DOWNLOAD_SIZE, size);
    }

    /**
     * 升级包大小
     *
     * @return
     */
    public long getDownloadSize() {
        return mPrefs.getLong(PREFS_DOWNLOAD_SIZE, 0);
    }


    /**
     * 差分升级包md5
     * @param md5
     */
    public void setDiffUpdatePackageMd5(String md5) {
        setString(PREFS_DIFF_PACKAGE_MD5, md5);
    }

    /**
     * 差分包校验值
     *
     * @return
     */
    public String getDiffUpdatePackageMd5() {
        return mPrefs.getString(PREFS_DIFF_PACKAGE_MD5, "");
    }

    /**
     * 全量升级包md5
     * @param md5
     */
    public void setFullUpdatePackageMd5(String md5) {
        setString(PREFS_FULL_PACKAGE_MD5, md5);
    }

    /**
     * 全量包校验值
     * @return
     */
    public String getFullUpdatePackageMd5() {
        return mPrefs.getString(PREFS_FULL_PACKAGE_MD5, "");
    }

    /**
     * 设置强制升级标志位
     *
     * @param force
     */
    public void setForceUpdateSign(boolean force) {
        setBoolean(PREFS_PACKAGE_FORCE_UPDATE, force);
    }

    /**
     * 返回强制升级标志
     *
     * @return true，需要强制升级
     */
    public boolean getForceUpdateSign() {
        return mPrefs.getBoolean(PREFS_PACKAGE_FORCE_UPDATE, false);
    }

    /**
     * 保存从服务器获取的目标版本号，软件大版本号
     *
     * @param ver
     */
    public void setServerTargetVersion(String ver) {
        setString(PREFS_TARGET_VERSION, ver);
    }

    /**
     * 服务器返回的目标版本
     *
     * @return
     */
    public String getServerTargetVersion() {
        return mPrefs.getString(PREFS_TARGET_VERSION, null);
    }

    /**
     * 设置升级前的版本
     * @param baseVersion
     */
    public void setBaseVersion(String baseVersion) {
        setString(PREFS_BASE_VERSION, baseVersion);
    }


    /**
     * 升级前的版本获取
     * @return
     */
    public String getBaseVersion() {
        return mPrefs.getString(PREFS_BASE_VERSION, null);
    }

    /**
     * 设置版本id
     * @param versionId
     */
    public void setVersionId(String versionId) {
        setString(PREFS_VERSION_ID, versionId);
    }

    /**
     * 获取版本id
     * @return
     */
    public String getVersionId() {
        return mPrefs.getString(PREFS_VERSION_ID, null);
    }
    /**
     * 差分升级包，升级描述
     *
     * @param str
     */
    public void setPackageDescriptor(String str) {
        setString(PREFS_DIFF_PACKAGE_DESCRIPTOR, str);
    }

    /**
     * 升级描述
     *
     */
    public String getPackageDescriptor() {
        return mPrefs.getString(PREFS_DIFF_PACKAGE_DESCRIPTOR, null);
    }

    /**
     * 升级包下载地址
     *
     * @param URL
     */
    public void setDiffUpdatePackageURL(String URL) {
        setString(PREFS_DIFF_DOWNLOAD_URL, URL);
    }

    /**
     * 下载地址
     *
     * @return
     */
    public String getDiffUpdatePackageURL() {
        return mPrefs.getString(PREFS_DIFF_DOWNLOAD_URL, null);
    }

    /**
     * 全量升级包下载地址
     *
     * @param URL
     */
    public void setFullUpdatePakcageURL(String URL) {
        setString(PREFS_FULL_DOWNLOAD_URL, URL);
    }

    /**
     * 全量升级包下载地址
     * @return
     */
    public String getFullUpdatePackageURL() {
        return mPrefs.getString(PREFS_FULL_DOWNLOAD_URL, null);
    }

    /**
     * 全量升级包下载进行中，标志存在时，开始升级检测时继续下载全量升级包
     *
     * @param needContinue
     */
    public void setFullPackageDownloading(boolean needContinue) {
        setBoolean(PREFS_FULL_PACKAGE_DOWNLOADING, needContinue);
    }

    /**
     * 全量升级包下载进行中，标志存在时，开始升级检测时继续下载全量升级包
     *
     * @return
     */
    public boolean getFullPackageDownloading() {
        return mPrefs.getBoolean(PREFS_FULL_PACKAGE_DOWNLOADING, false);
    }

    /**
     * 设置host升级结果
     *
     * @param result
     */
    public void setPrefsHostUpdateResult(String result) {
        setString(PREFS_HOST_UPDATE_RESULT, result);
    }

    /**
     * 获取host升级结果
     *
     * @return
     */
    public String getPrefsHostUpdateResult() {
        return mPrefs.getString(PREFS_HOST_UPDATE_RESULT, "no_update");
    }

    /**
     * 设置差分包类型
     *
     * @param type 1：全量包，升级成功后不再另行下载全量包； 0：差分包
     */
    public void setDiffUpdatePackageType(int type) {
        setInt(PREFS_DIFF_PACKAGE_TYPE, type);
    }

    /**
     * 获取差分包类型
     *
     * @return
     */
    public int getDiffUpdatePackageType() {
        return mPrefs.getInt(PREFS_DIFF_PACKAGE_TYPE, NetDefine.DIFF_PACKAGE);
    }

    /**
     * 设置各系统升级包路径
     *
     * @param osName
     * @param packPage
     */
    public void setOsPackagePath(String osName, String packPage) {
        setString(osName, packPage);
    }

    /**
     * 获取各系统升级包解压后的路径
     *
     * @param osName
     * @return
     */
    public String getOsPackagePath(String osName) {
        return mPrefs.getString(osName, null);
    }

    /**
     * 各升级包md5
     * @param osPackMd5 HOST_pack_md5
     * @param md5
     */
    public void setOsPackageMD5(String osPackMd5, String md5) {
        setString(osPackMd5, md5);
    }

    public String getOsPackageMD5(String osPackMd5) {
        return mPrefs.getString(osPackMd5, "");
    }


    /**
     * 设置下位机升级包scp存放路径
     *
     * @param path
     */
    public void setTx1PackagePath(String path) {
        setString(PREFS_TX1_PACKAGE_PATH, path);
    }

    public String getTx1packagePath(String defaultVal) {
        return mPrefs.getString(PREFS_TX1_PACKAGE_PATH, defaultVal);
    }

    /**
     * 设置OTA包下载开始时间
     * @param startTime
     */
    public void setPrefsDownloadStartTime(String startTime) {
        setString(PREFS_DOWNLOAD_START_TIME, startTime);
    }

    /**
     * 获取OTA包下载开始时间
     * @return
     */
    public String getPrefsDownloadStartTime() {
        return mPrefs.getString(PREFS_DOWNLOAD_START_TIME, null);
    }

    /**
     * 设置OTA包下载结束时间
     * @param endTime
     */
    public void setPrefsDownloadEndTime(String  endTime) {
        setString(PREFS_DOWNLOAD_END_TIME, endTime);
    }

    /**
     * 获取OTA包下载结束时间
     * @return
     */
    public String getPrefsDownloadEndTime() {
        return mPrefs.getString(PREFS_DOWNLOAD_END_TIME, null);
    }

    /**
     * 设置OTA升级开始时间
     * @param startTime
     */
    public void setPrefsOtaInstallStartTime(String startTime) {
        setString(PREFS_OTA_INSTALL_START_TIME, startTime);
    }

    /**
     * 获取OTA升级开始时间
     * @return
     */
    public String getPrefsOtaInstallStartTime() {
        return mPrefs.getString(PREFS_OTA_INSTALL_START_TIME, null);
    }

    /**
     * 设置OTA升级结束时间
     * @param endTime
     */
    public void setPrefsOtaInstallEndTime(String endTime) {
        setString(PREFS_OTA_INSTALL_END_TIME, endTime);
    }

    /**
     * 获取OTA升级结束时间
     * @return
     */
    public String getPrefsOtaInstallEndTime() {
        return mPrefs.getString(PREFS_OTA_INSTALL_END_TIME, null);
    }

    /**
     * 设置OTA升级结束时间
     * @param silentUpdate
     */
    public void setPrefsSilentUpdate(boolean silentUpdate) {
        setBoolean(PREFS_OTA_SILENT_UPDATE, silentUpdate);
    }

    /**
     * 获取OTA升级结束时间
     * @return
     */
    public boolean getPrefsSilentUpdate() {
        return mPrefs.getBoolean(PREFS_OTA_SILENT_UPDATE, false);
    }

    /**
     * 保存最近一次下载URL
     * @param lastestDownloadUrl
     */
    public void setPrefsLastestDownloadUrl(String lastestDownloadUrl) {
        setString(PREFS_LASTEST_DOWNLOAD_URL, lastestDownloadUrl);
    }

    /**
     * 获取最近一次下载URL
     * @return
     */
    public String getPrefsLastestDownloadUrl() {
        return mPrefs.getString(PREFS_LASTEST_DOWNLOAD_URL, null);
    }

    /**
     * 设置是否检测update/config.xml中该OS升级信息
     *
     * @param osName such as host, and the check file name is host_config_check
     * @param needCheck
     */
    public void setOsCheckConfig(String osName, boolean needCheck) {
        setBoolean(osName + CONFIG_CHECK, needCheck);
    }

    public boolean getOsCheckConfig(String osName) {
        return mPrefs.getBoolean(osName + CONFIG_CHECK, true);
    }

    /**
     * 升级失败原因
     *
     * @param reason the failed reason of the ota upgrade.
     *
     */
    public void setUpgradeFailedReason(String reason) {
        setString(PREFS_UPGRADE_FAILED_REASON, reason);
    }

    public String getUpgradeFailedReason() {
        return mPrefs.getString(PREFS_UPGRADE_FAILED_REASON, null);
    }

    /**
     * OTA启动方式
     *
     * @param startingMode OTA starting mode.
     *
     */
    public void setPrefsStartingMode(String startingMode) {
        setString(PREFS_STARTING_MODE, startingMode);
    }

    public String getPrefsStartingMode() {
        return mPrefs.getString(PREFS_STARTING_MODE, null);
    }

    /**
     * 回滚失败后，重启机器会重新检测升级状态
     * 设置标志，回滚失败是否需要发送埋点数据
     * True：需要发送埋点
     * False：不需要发送埋点
     *
     * @param needReport
     */
    public void setRollbackFailedBiReport(boolean needReport) {
        setBoolean(PREFS_ROLLBACK_FAILED_NEED_BI_REPORT, needReport);
    }

    /**
     * 回滚失败后，重启机器会重新检测升级状态
     * 设置标志，回滚失败是否需要发送埋点数据
     *
     * @return
     * True：需要发送埋点
     * False：不需要发送埋点
     *
     */
    public boolean getRollbackFailedBiReport() {
        return mPrefs.getBoolean(PREFS_ROLLBACK_FAILED_NEED_BI_REPORT, true);
    }

    public boolean clearPreference() {
        setDiffUpdatePackageURL(null);
        setDiffUpdatePackageMd5(null);
        setForceUpdateSign(false);
        setServerTargetVersion(null);
        setVersionId(null);
        setPrefsStartingMode(null);
        setUpgradeFailedReason(null);
        setPrefsRollback(false);
        setOtaIsUpgrading(false);
        setRollbackFailedBiReport(true);
        setPrefsSilentUpdate(false);
        setPrefsDownloadStartTime("00:00");
        setPrefsDownloadEndTime("00:00");
        setPrefsOtaInstallStartTime("00:00");
        setPrefsOtaInstallEndTime("00:00");
        setPrefsLastestDownloadUrl(null);
        // won't clear full setFullPakcageDownloadURL

        return true;
    }
}
