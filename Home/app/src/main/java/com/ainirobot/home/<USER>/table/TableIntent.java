package com.ainirobot.home.data.table;

import static com.ainirobot.home.bean.Semantics.DBFiled.FILED_FEATURE;
import static com.ainirobot.home.bean.Semantics.DBFiled.FILED_INTENT;
import static com.ainirobot.home.bean.Semantics.DBFiled.FILED_PATTERN;
import static com.ainirobot.home.bean.Semantics.DBFiled.FILED_TYPE;

import com.ainirobot.home.bean.Semantics;

public class TableIntent {

    public static final String INTENT_DROP = "DROP TABLE IF EXISTS " + Semantics.DBFiled.TABLE_NAME;
    public static final String INTENT_DELETE = "DELETE FROM " + Semantics.DBFiled.TABLE_NAME;
    public static final String INTENT_DELETE_SEQUENCE = "DELETE FROM sqlite_sequence WHERE name =  '" + Semantics.DBFiled.TABLE_NAME + "'";

    public static final String INTENT_INSERT = "INSERT INTO " + Semantics.DBFiled.TABLE_NAME + " (" +
            FILED_INTENT + "," + FILED_FEATURE + "," + FILED_PATTERN + "," + FILED_TYPE + ") VALUES " +
            "('req_ota_auth',                  17,      'req_ota_auth',                   1)," +
            "('req_ota_result',                17,      'req_ota_result',                 1)," +
            "('req_ota_inspect',               17,      'req_ota_inspect',                1)," +
            "('start_inspection',              19,      'start_inspection',               1)," +
            "('stop_inspection',               19,      'stop_inspection',                1)," +
            "('open_radar_failed',             19,      'open_radar_failed',              1)," +
            "('req_bind_failed',               60,      'req_bind_failed',                1)," +
            "('type_robot_info',               1000,    'type_robot_info',                1)," +
            "('req_standby_start',             20,      'req_standby_start',              1)," +
            "('req_standby_stop',              20,      'req_standby_stop',               1)," +
            "('req_standby_finish',            20,      'req_standby_finish',             1)," +
            "('wheel_motor_blocked',           21,      'wheel_motor_blocked',            1)," +
            "('req_emergency_press',           21,      'req_emergency_press',            1)," +
            "('req_emergency_release',         21,      'req_emergency_release',          1)," +
            "('req_set_charge_pile',           27,      'req_set_charge_pile',            1)," +
            "('req_start_charge',              26,      'req_start_charge',               1)," +
            "('req_battery_low',               26,      'req_battery_low',                1)," +
            "('WeChat_set_charging_pile',      27,      'WeChat_set_charging_pile',       1)," +
            "('WeChat_first_recharging',       26,      'WeChat_first_recharging',        1)," +
            "('req_battery_charging',          28,      'req_battery_charging',           1)," +
            "('req_stop_charging_by_app',      28,      'req_stop_charging_by_app',       1)," +
            "('req_stop_charging_by_charging_slow',      28,      'req_stop_charging_by_charging_slow',       1)," +
            "('req_battery_full',              28,      'req_battery_full',               1)," +
            "('req_normal',                    28,      'req_normal',                     1)," +
            "('req_hw_e70',                    222,      'req_hw_e70',                     1)," +
            "('req_hw_e70_recovery',           222,      'req_hw_e70_recovery',            1)," +
            "('req_hw_malfunction',            29,      'req_hw_malfunction',             1)," +
            "('req_hw_recovery',               29,      'req_hw_recovery',                1)," +
            "('req_reposition',                25,      'req_reposition',                 1)," +
            "('e70_restart_succ_reposition',   25,      'e70_restart_succ_reposition',    1)," +
            "('req_remote_relocate',           208,     'req_remote_relocate',            1)," +
            "('relocate_switch_close',         1000,    'relocate_switch_close',          1)," +
            "('relocate_action',               1000,    'relocate_action',                1)," +
            "('req_stop_charging',             28,      'req_stop_charging',              1)," +
            "('req_speech_wakeup',             1000,    'req_speech_wakeup',              1)," +
            "('req_speech_sleep',              1000,    'req_speech_sleep',               1)," +
            "('req_stop',                      1000,    '^停止任务$',                      2)," +
            "('req_stop',                      1000,    'req_stop',                       1)," +
            "('req_stop',                      1000,    'general_command&cancel',         1)," +
            "('req_stop',                      1000,    'general_command&power_off',      1)," +
            "('req_stop',                      1000,    'general_command&stop',           1)," +
            "('req_speech_text',               1001,    'req_speech_text',                1)," +
            "('req_set_light',                 1001,    'req_set_light',                  1)," +
            "('play_tts',                      1001,    'play_tts',                       1)," +
            "('audio_up',                      112,     'general_command&up_command',     1)," +
            "('audio_set',                     112,     'general_command&set_command',    1)," +
            "('audio_down',                    112,     'general_command&down_command',   1)," +
            "('audio_max',                     112,     'general_command&max_command',    1)," +
            "('audio_min',                     112,     'general_command&min_command',    1)," +
            "('req_wakeup_word',               112,     'req_wakeup_word',                1)," +
            "('req_open_radar',                209,     'req_open_radar',                 1)," +
            "('req_system_recovery',           210,     'req_system_recovery',            1)," +
            "('req_open_radar_succeed',        1000,    'req_open_radar_succeed',         1)," +
            "('req_start_dormancy',            211,     'req_start_dormancy',             1)," +
            "('system_remote_go_position',     30,      'system_remote_go_position',      1)," +
            "('system_remote_go_pose',         30,      'system_remote_go_pose',          1)," +
            "('system_remote_stop_navigation', 30,      'system_remote_stop_navigation',  1)," +
            "('remote_push_map_need_switch',   32,      'remote_push_map_need_switch',    1)," +
            "('remote_push_map_no_switch',     114,     'remote_push_map_no_switch',      1)," +
            "('system_remote_lock_dialog',     113,     'system_remote_lock_dialog',      1)," +
            "('system_remote_lock_speech',     113,     'system_remote_lock_speech',      1)," +
            "('system_remote_lock_lite_exit',  113,     'system_remote_lock_lite_exit',   1)," +
            "('system_remote_lock_full',       31,      'system_remote_lock_full',        1)," +
            "('system_remote_lock_full_exit',  31,      'system_remote_lock_full_exit',   1)," +
            "('req_map_drift',                 45,      'req_map_drift',                  1)," +
            "('req_map_outside',               46,      'req_map_outside',                1)," +
            "('req_map_outside_release',       46,      'req_map_outside_release',        1)," +
            "('req_system_time_warning_start', 47,      'req_system_time_warning_start',  1)," +
            "('req_system_time_warning_stop',  47,      'req_system_time_warning_stop',   1)," +
            "('req_robot_being_pushed',        48,      'req_robot_being_pushed',         1)," +
            "('req_robot_being_pushed_release',48,      'req_robot_being_pushed_release', 1)," +
            "('req_d430_calibration_start',    49,      'req_d430_calibration_start',     1)," +
            "('req_ble_signal_near',           24,      'req_ble_signal_near',            1)," +
            "('req_ble_signal_far',            24,      'req_ble_signal_far',             1)," +
            "('req_wheel_over_danger',         50,      'req_wheel_over_danger',          1)," +
            "('req_wheel_over_normal',         50,      'req_wheel_over_normal',          1)," +
            "('req_chassis_sensor_normal',     223,     'req_chassis_sensor_normal',      1)," +
            "('req_chassis_sensor_error',      223,     'req_chassis_sensor_error',       1)," +
            "('req_multi_robot_error',         51,      'req_multi_robot_error',          1)," +
            "('req_system_shutdown_timer',     115,     'req_system_shutdown_timer',      1)," +
            "('req_enable_target_custom',      116,     'req_enable_target_custom',       1)," +
            "('req_system_stop_charging',      117,     'req_system_stop_charging',       1)," +
            "('req_system_stop_charging_status',    117,     'req_system_stop_charging_status',     1),"+
            "('req_load_map',                  52,      'req_load_map',                   1)," +
            "('system_import_map_begin',       224,     'system_import_map_begin',        1)," +
            "('req_stop_charging_confirm',     53,     'req_stop_charging_confirm',       1)," +
            "('req_ota_downgrade',             13,     'req_ota_downgrade',               1)," +
            "('req_ota_auth_downgrade',        13,       'req_ota_auth_downgrade',        1)," +
            "('req_ota_download_success',      13,       'req_ota_download_success',      1)," +
            "('req_ota_downgrade_result',      13,       'req_ota_downgrade_result',      1)," +
            "('req_ota_downgrade_inspect',     13,       'req_ota_downgrade_inspect',     1)," +
            "('req_stop_charging_confirm',     53,      'req_stop_charging_confirm',      1)," +
            "('req_leave_pile_go_point',       54,      'req_leave_pile_go_point',        1)," +
            "('req_stop_charging_by_voice',    28,      'general_command&end_charging',   1)," +
            "('req_stop_charging_by_voice_new',     28, 'general_command_refactoringv5&end_charging_refactoringv5',      1);";
}
