package com.ainirobot.home.bean;

import com.google.gson.annotations.SerializedName;

public class PushMapBean {
    @SerializedName("map_name")
    String mapName;
    @SerializedName("map_uuid")
    String mapUuid;
    @SerializedName("map_url")
    String mapUrl;
    @SerializedName("task_id")
    String taskId;
    @SerializedName("version")
    String version;
    @SerializedName("pgm_md5")
    String pgmMd5;
    @SerializedName("extra_file_id")
    String extraFileId;
    @SerializedName("extra_url")
    String extraUrl;
    @SerializedName("extra_md5")
    String extraMd5;
    @SerializedName("cmpt_version")
    int mapVersion;
    @SerializedName("cmpt_type")
    String mapSupportType;

    String state;

    private boolean naviMap;

    public boolean isNaviMap() {
        return naviMap;
    }

    public void setNaviMap(boolean naviMap) {
        this.naviMap = naviMap;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getMapName() {
        return mapName;
    }

    public void setMapName(String mapName) {
        this.mapName = mapName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getPgmMd5() {
        return pgmMd5;
    }

    public void setPgmMd5(String pgmMd5) {
        this.pgmMd5 = pgmMd5;
    }

    public String getMapUuid() {
        return mapUuid;
    }

    public void setMapUuid(String mapUuid) {
        this.mapUuid = mapUuid;
    }

    public String getExtraFileId() {
        return extraFileId;
    }

    public void setExtraFileId(String extraFileId) {
        this.extraFileId = extraFileId;
    }

    public String getExtraUrl() {
        return extraUrl;
    }

    public void setExtraUrl(String extraUrl) {
        this.extraUrl = extraUrl;
    }

    public String getExtraMd5() {
        return extraMd5;
    }

    public void setExtraMd5(String extraMd5) {
        this.extraMd5 = extraMd5;
    }

    public String getMapUrl() {
        return mapUrl;
    }

    public void setMapUrl(String mapUrl) {
        this.mapUrl = mapUrl;
    }

    public int getMapVersion() {
        return mapVersion;
    }

    public String getMapSupportType() {
        return mapSupportType;
    }

    @Override
    public String toString() {
        return "PushMapBean{" +
                "mapName='" + mapName + '\'' +
                ", mapUuid='" + mapUuid + '\'' +
                ", mapUrl='" + mapUrl + '\'' +
                ", taskId='" + taskId + '\'' +
                ", version='" + version + '\'' +
                ", extraFileId='" + extraFileId + '\'' +
                ", extraUrl='" + extraUrl + '\'' +
                ", extraMd5='" + extraMd5 + '\'' +
                ", mapVersion='" + mapVersion + '\'' +
                ", mapSupportType='" + mapSupportType + '\'' +
                '}';
    }
}
