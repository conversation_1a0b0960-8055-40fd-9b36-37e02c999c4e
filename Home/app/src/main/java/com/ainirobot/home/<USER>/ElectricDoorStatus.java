package com.ainirobot.home.bean;

import com.ainirobot.coreservice.client.Definition;

/**
 * 电门状态
 * 数据：{"bootReason":0,"door1":68,"door2":68,"door3":68,"door4":68,"doorstatus":0,"down_status":0,"propType":7,"runMode":0,"state":0,"up_status":0}
 */
public class ElectricDoorStatus {
    private int bootReason;
    private int door1;
    private int door2;
    private int door3;
    private int door4;
    private int doorstatus;
    private int down_status;
    private int propType;
    private int runMode;
    private int state;
    private int up_status;

    public boolean isAllDoorClosed() {
        return door1 == Definition.CAN_DOOR_STATUS_CLOSE
                && door2 == Definition.CAN_DOOR_STATUS_CLOSE
                && door3 == Definition.CAN_DOOR_STATUS_CLOSE
                && door4 == Definition.CAN_DOOR_STATUS_CLOSE;
    }

    public boolean isAllDoorOpen() {
        return door1 == Definition.CAN_DOOR_STATUS_OPEN
                && door2 == Definition.CAN_DOOR_STATUS_OPEN
                && door3 == Definition.CAN_DOOR_STATUS_OPEN
                && door4 == Definition.CAN_DOOR_STATUS_OPEN;
    }

    public int getBootReason() {
        return bootReason;
    }

    public void setBootReason(int bootReason) {
        this.bootReason = bootReason;
    }

    public int getDoor1() {
        return door1;
    }

    public void setDoor1(int door1) {
        this.door1 = door1;
    }

    public int getDoor2() {
        return door2;
    }

    public void setDoor2(int door2) {
        this.door2 = door2;
    }

    public int getDoor3() {
        return door3;
    }

    public void setDoor3(int door3) {
        this.door3 = door3;
    }

    public int getDoor4() {
        return door4;
    }

    public void setDoor4(int door4) {
        this.door4 = door4;
    }

    public int getDoorstatus() {
        return doorstatus;
    }

    public void setDoorstatus(int doorstatus) {
        this.doorstatus = doorstatus;
    }

    public int getDown_status() {
        return down_status;
    }

    public void setDown_status(int down_status) {
        this.down_status = down_status;
    }

    public int getPropType() {
        return propType;
    }

    public void setPropType(int propType) {
        this.propType = propType;
    }

    public int getRunMode() {
        return runMode;
    }

    public void setRunMode(int runMode) {
        this.runMode = runMode;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getUp_status() {
        return up_status;
    }

    public void setUp_status(int up_status) {
        this.up_status = up_status;
    }

    @Override
    public String toString() {
        return "ElectricDoorStatus{" +
                "bootReason=" + bootReason +
                ", door1=" + door1 +
                ", door2=" + door2 +
                ", door3=" + door3 +
                ", door4=" + door4 +
                ", doorstatus=" + doorstatus +
                ", down_status=" + down_status +
                ", propType=" + propType +
                ", runMode=" + runMode +
                ", state=" + state +
                ", up_status=" + up_status +
                '}';
    }
}
