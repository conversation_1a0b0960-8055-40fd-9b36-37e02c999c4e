package com.ainirobot.home.module;

import android.content.Context;
import android.media.AudioManager;
import android.os.Handler;
import android.os.Looper;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.coreservice.listener.IMusicListener;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.utils.ResUtil;
import com.ainirobot.home.utils.SystemUtils;
import com.ainirobot.home.utils.ToastUtil;

public class WheelOverControlModule extends BaseModule {

    private static final String TAG = "WheelOverModule:Home";

    private static final long TTS_INTERVAL = 10 * Definition.SECOND;
    public static final Handler mHandler = new Handler(Looper.getMainLooper());
    private static WheelOverControlModule sInstance = null;
    private Context mContext;
    private volatile boolean mIsWheelOverDanger = false; // 是否过流5次，锁死轮子
    private AudioManager audioManager;


    public static WheelOverControlModule getInstance() {
        if (sInstance == null) {
            sInstance = new WheelOverControlModule();
        }
        return sInstance;
    }

    private WheelOverControlModule() {}

    public void init(Context context) {
        mContext = context;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.d(TAG, "reqId=" + reqId + "; intent=" + intent + "; params=" + params);
        switch (intent) {
            case Definition.REQ_WHEEL_OVER_DANGER:
                if (!mIsWheelOverDanger){
                    mIsWheelOverDanger = true;
                    SystemApi.getInstance().resetHead(0, null);
                    SystemUtils.setThreeFinger(false);// 禁止三指下拉状态栏
//                    playWarningAudio();
                    UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_WHEEL_OVER_CONTROL, null, null);
                }else {
                    Log.d(TAG, "already in  WheelOverControlModule , handling REQ_WHEEL_OVER_DANGER");
                }
                break;
            case Definition.REQ_WHEEL_OVER_NORMAL:
                stop();
                break;
        }
        SystemApi.getInstance().finishModuleParser(reqId, true);
        return false;
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
        switch (type) {
            case ModuleDef.LOCAL_MESSAGE_WHEEL_OVER_UNLOCK:
                exitWheelOverLock();
            default:
                break;
        }
    }

    private void exitWheelOverLock() {
        Log.d(TAG, "exitWheelOverLock is in danger :"+ mIsWheelOverDanger);
        if (mIsWheelOverDanger){
            stop();
        }else {
            ToastUtil.showToast(mContext,ResUtil.getString(R.string.wheel_over_again));
        }
    }

    private void playWarningAudio() {
        Log.d(TAG, "start to play warning audio ");
        audioManager = (AudioManager) ApplicationWrapper.getContext()
                .getSystemService(Context.AUDIO_SERVICE);
        ControlManager.getControlManager().setLastVolumeIndex(audioManager.getStreamVolume(AudioManager.STREAM_MUSIC));
        int musicVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
        audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, musicVolume, 0);

        playMusic(R.raw.ble_danger_warning);
    }

    private void playMusic(int rawResourceId) {
        try {
            SkillManager.getInstance().playMusicByLocalPath(rawResourceId, true, true, new IMusicListener.Stub() {
                @Override
                public void onStart() throws RemoteException {
//                    Log.d(TAG, "playWarningAudio onStart, playTTS ");
//                    mHandler.post(playTextRunnable);
                }

                @Override
                public void onResume() throws RemoteException {

                }

                @Override
                public void onPause() throws RemoteException {

                }

                @Override
                public void onStop() throws RemoteException {

                }

                @Override
                public void onError() throws RemoteException {

                }

                @Override
                public void onComplete() throws RemoteException {

                }
            });
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }


    Runnable playTextRunnable = new Runnable() {
        @Override
        public void run() {
            mHandler.removeCallbacks(playTextRunnable);
            playText();
        }
    };


    private void playText() {
//        if (!mIsInClose){
//            Log.d(TAG, "playText , current is not Danger");
//            return;
//        }
        String text = ResUtil.getString(R.string.ble_robot_dangerous_tts);
        try {
            SkillManager.getInstance().speechPlayText(text, new TextListener() {
                @Override
                public void onComplete() {
                    super.onComplete();
                    mHandler.postDelayed(playTextRunnable, TTS_INTERVAL);
                }

                @Override
                public void onError() {
                    super.onError();
                    mHandler.postDelayed(playTextRunnable, TTS_INTERVAL);
                }

                @Override
                public void onStop() {
                    super.onStop();
                    mHandler.postDelayed(playTextRunnable, TTS_INTERVAL);
                }
            });
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }


    /**
     * 播报功能
     * 不需要区分机器人类型时调用
     */
    private void voice(String tts) {
        Log.d(TAG, "voice : " + tts);
        SkillManager.getInstance().speechPlayText(tts);
    }

    @Override
    protected void onStop() {
        super.onStop();
        mIsWheelOverDanger = false;
        SystemUtils.setThreeFinger(true);// 恢复三指下拉
        SystemApi.getInstance().onWheelOverDangerFinish();//通知CoreService SystemStatus状态清理
    }
}


