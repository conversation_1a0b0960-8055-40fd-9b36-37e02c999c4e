package com.ainirobot.home.ota.task;

import static com.ainirobot.home.ota.constants.OtaConstants.DOWNLOAD_PATH;

import android.content.Context;
import android.os.Handler;
import android.os.SystemClock;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.home.ota.config.OtaConfig;
import com.ainirobot.home.ota.database.DataBaseManager;
import com.ainirobot.home.ota.executor.ThreadPoolManager;
import com.ainirobot.home.ota.network.NetHelper;
import com.ainirobot.home.ota.parser.IParserInterface;
import com.ainirobot.home.ota.service.OtaApiHelper;
import com.ainirobot.home.ota.utils.IOUtils;
import com.ainirobot.home.ota.utils.Preferences;
import com.ainirobot.home.ota.utils.Scp;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Enumeration;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

public class FileCheckTask extends AbstractOtaTask implements Runnable {

    public static final String TAG = "FileCheckTask:Home";
    private String mPath = DOWNLOAD_PATH;

    public FileCheckTask(Context context, Preferences preferences, NetHelper netHelper, OtaApiHelper otaApiHelper, DataBaseManager dataBaseManager, Scp scpHelper, Handler mainHandler, IParserInterface iParserInterface, OtaTaskFactory otaTaskFactory, OtaConfig otaConfig, ThreadPoolManager threadPoolManager) {
        super(context, preferences, netHelper, otaApiHelper, dataBaseManager, scpHelper, mainHandler, iParserInterface, otaTaskFactory, otaConfig, threadPoolManager);
    }
    @Override
    public void run() {
        Log.d(TAG, "file checking thread is running.");
        boolean result = false;
        if (checkZipFile(mPath)) {
            clearInstallFile();
            if (mPath.equals(DOWNLOAD_PATH)) {
                JSONObject jsonOtaEvent = new JSONObject();
                try {
                    jsonOtaEvent.put(Definition.JSON_OTA_FORCEUPATE, false);
                    jsonOtaEvent.put(Definition.JSON_OTA_IS_USER_TOUCH, true);
                    jsonOtaEvent.put(Definition.JSON_OTA_NEED_DOWNLOAD, false);
                    jsonOtaEvent.put(Definition.JSON_OTA_UPTIME, SystemClock.uptimeMillis());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                Log.d(TAG, "file checking thread sendDowngradeOtaEvent: " + jsonOtaEvent.toString());
                mOtaApiHelper.sendDowngradeOtaEvent(jsonOtaEvent.toString());
                result = true;
            }
        } else if (DOWNLOAD_PATH.equals(mPath)) {
            clearDownloadFile();
        }
        Log.d(TAG, "file checking thread finish. result=" + result);
    }

    private boolean checkZipFile(String file) {
        Log.i(TAG, "checkZipFile");
        try {
            ZipFile zipFile = new ZipFile(file);
            Enumeration<ZipEntry> entries = (Enumeration<ZipEntry>) zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry zipElement = entries.nextElement();
                String fileName = zipElement.getName();
                if ("config.xml".endsWith(zipElement.getName())) {
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
