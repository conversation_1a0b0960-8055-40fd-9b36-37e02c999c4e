package com.ainirobot.home.ota.network;

import static com.ainirobot.home.ota.network.NetDefine.API_POST_STATUS;
import static com.ainirobot.home.ota.network.NetDefine.API_UPDATE_CHECK;
import static com.ainirobot.home.ota.network.NetDefine.OTA_BASE_VERSION;
import static com.ainirobot.home.ota.network.NetDefine.OTA_DESC;
import static com.ainirobot.home.ota.network.NetDefine.OTA_PROGRESS;
import static com.ainirobot.home.ota.network.NetDefine.OTA_STATUS;
import static com.ainirobot.home.ota.network.NetDefine.OTA_TYPE;
import static com.ainirobot.home.ota.network.NetDefine.OTA_VERSION;
import static com.ainirobot.home.ota.network.NetDefine.OTA_VERSION_ID;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;
import android.util.Log;

import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ota.constants.OtaConstants;
import com.ainirobot.home.ota.bean.DowngradeParams;
import com.ainirobot.home.ota.httpclient.HttpClient;
import com.ainirobot.home.ota.httpclient.Request;
import com.ainirobot.home.ota.httpclient.RequestListener;
import com.ainirobot.home.ota.httpclient.body.MultipartBody;
import com.ainirobot.home.ota.httpclient.body.RequestBody;
import com.ainirobot.home.ota.httpclient.body.StringBody;
import com.ainirobot.home.ota.service.OtaApiHelper;
import com.ainirobot.home.ota.utils.SystemUtils;

import java.util.Map;


/**
 * OTA network api helper
 */
public class NetHelper {
    private final static String TAG = OtaConstants.TAG_PREFIX + NetHelper.class.getSimpleName();

    private HttpClient httpClient;
    private ConnectivityManager mConnectivityManager;
    private ConnectivityManager.NetworkCallback mNetworkCallback = new ConnectivityManager.NetworkCallback() {
        @Override
        public void onAvailable(Network network) {
            Log.e(TAG, "Network onAvailable");
            super.onAvailable(network);
            try {
                httpClient.onAvailable(true);
            } catch (RuntimeException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onLost(Network network) {
            Log.e(TAG, "Network onLost");
            super.onLost(network);
            try {
                httpClient.onAvailable(false);
            } catch (RuntimeException e) {
                e.printStackTrace();
            }
        }
    };

    private static NetHelper mInstance;

    private NetHelper() {
        httpClient = new HttpClient();
        if (ApplicationWrapper.getContext() != null) {
            mConnectivityManager = (ConnectivityManager) ApplicationWrapper
                    .getContext()
                    .getSystemService(Context.CONNECTIVITY_SERVICE);
            mConnectivityManager.requestNetwork(
                    new NetworkRequest
                        .Builder()
                        .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                        .addTransportType(NetworkCapabilities.TRANSPORT_CELLULAR)
                        .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
                        .build(),
                    mNetworkCallback);
        }
    }

    public static NetHelper getInstance() {
        synchronized (NetHelper.class) {
            if (mInstance == null) {
                mInstance = new NetHelper();
            }
        }
        return mInstance;
    }

    public void checkNewVersion(DowngradeParams params, RequestListener listener) {

        try {
            if (!OtaApiHelper.mOtaApi.isApiConnectedService() || !OtaApiHelper.mCallBackRegisterd) {
                Log.d(TAG,"checkNewVersion WaitForServiceConnect");
                OtaApiHelper.checkAndWaitForServiceConnect(200);
            }

            synchronized (OtaApiHelper.mConnectLock) {
                if (OtaApiHelper.mOtaApi.isApiConnectedService() && OtaApiHelper.mCallBackRegisterd) {
                    Log.d(TAG, "real checkNewVersion. do http request");
                    String url = SystemUtils.getDomain() + API_UPDATE_CHECK;

                    Log.d(TAG, "checkNewVersion. params:" + params + " url:" + url);

                    Request.Builder builder = new Request.Builder().url(url);
                    Map<String, String> keyMap = params.getHashMap();
                    for (Map.Entry<String, String> entry : keyMap.entrySet()) {
                        builder.urlParam(entry.getKey(), entry.getValue());
                    }

                    Request request = builder.build();
                    httpClient.execute(request, listener, false);
                }
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public void uploadUpdateStatus(DowngradeParams params, OTA_TYPE type, String otaVersion,
                                   String baseVersion, String versionId, OTA_STATUS status,
                                   String desc, NetDefine.OTA_INSTALL_PROGRESS progress,
                                   RequestListener listener) {
        Log.d(TAG, "uploadUpdateStatus:type=" + type + ",ota_version = " + otaVersion +
                ",base_version=" + baseVersion + ",version_id=" + versionId +
                ",status=" + status + ",desc=" + desc + ",progress=" + progress);
        MultipartBody body = new MultipartBody();
        body.addPart(OTA_TYPE, new StringBody(Integer.valueOf(type.getValue()).toString()));
        body.addPart(OTA_VERSION, new StringBody(otaVersion));
        body.addPart(OTA_BASE_VERSION, new StringBody(baseVersion));
        body.addPart(OTA_VERSION_ID, new StringBody(versionId));
        body.addPart(OTA_STATUS, new StringBody(Integer.valueOf(status.getValue()).toString()));
        body.addPart(OTA_DESC, new StringBody(desc));
        body.addPart(OTA_PROGRESS, new StringBody(progress.getValue()));

        //add post params
        Map<String, String> keyMap = params.getHashMap();
        for (Map.Entry<String, String> entry : keyMap.entrySet()) {
            body.addPart(entry.getKey(), new StringBody(entry.getValue()));
        }

        uploadUpdateStatus(params, body, listener);
    }

    private void uploadUpdateStatus(DowngradeParams params, RequestBody body, RequestListener
            listener) {
        String url = SystemUtils.getDomain() + API_POST_STATUS;

        Request.Builder builder = new Request.Builder().url(url).post(body);

        Request request = builder.build();
        httpClient.execute(request, listener);
    }
}
