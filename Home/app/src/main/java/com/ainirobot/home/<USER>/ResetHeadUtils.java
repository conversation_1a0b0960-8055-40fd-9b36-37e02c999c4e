package com.ainirobot.home.utils;

import android.os.Handler;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.client.subtype.DeviceSubType;

public class ResetHeadUtils {
    //重置头部位置重试次数
    private static final int RESET_HEAD_RETRY_COUNT = 3;
    private static final int RESET_HEAD_RETRY_INTERVAL = 500;
    private int mResetHeadRetryCount = 0;
    private final String TAG;
    private Handler mHandler;
    private int mReqId;
    private ResetHeadListener mResetHeadListener;
    private volatile boolean isFinish;

    public ResetHeadUtils(String tag, Handler handler) {
        TAG = tag;
        mHandler = handler;
    }

    public void checkResetHeadState(int reqId, ResetHeadListener resetHeadListener) {
        isFinish = false;
        this.mReqId = reqId;
        this.mResetHeadListener = resetHeadListener;
        //mini或者小秘 2 机型（支持移动脑袋）
        //当前机型支持双通
        //且当前机器支持视觉导航，当前地图是视觉导航地图
        //则需要先重置脑袋到默认位置，再移动，否则直接移动
        //注意：修改此处逻辑时，需要同时修改Core 中NavigationResetHeadUtils的checkResetHeadStaten逻辑
        //硬件二开，屏蔽头部运动开关，打开时不reset head
        boolean isDisableHeadMoving = RobotSettingApi.getInstance().
                getRobotInt(Definition.ROBOT_SETTING_DISABLE_HEAD_MOVING) == 1;

        boolean isSupportProduct = ProductInfo.isMiniProduct() || ProductInfo.isMeissa2();
        if (isSupportProduct && !isDisableHeadMoving && DeviceSubType.getInstance().hasTopMono()) {
            Log.d(TAG, "checkResetHeadState: check map has vision");
            SystemApi.getInstance().isCurrentMapHasVision(reqId, new CommandListener() {
                @Override
                public void onResult(int result, String message, String extraData) {
                    Log.d(TAG, "isMapHasVision onResult: result=" + result + " message=" + message);
                    if (isFinish) {
                        Log.d(TAG, "isMapHasVision: not running and return");
                        return;
                    }
                    if (result == Definition.RESULT_OK && "true".equals(message)) {
                        startResetHead();
                    } else {
                        onResetHeadSuccess();
                    }
                }
            });
        } else {
            Log.d(TAG, "checkResetHeadState: not need reset head isSupportProduct=" + isSupportProduct
                    + " isDisableHeadMoving=" + isDisableHeadMoving
                    + " hasTopMono=" + DeviceSubType.getInstance().hasTopMono());
            onResetHeadSuccess();
        }
    }

    public void stop() {
        isFinish = true;
        mHandler.removeCallbacks(mResetHeadRunnable);
        mResetHeadListener = null;
        mResetHeadRetryCount = 0;
    }

    private void startResetHead() {
        SystemApi.getInstance().resetHead(mReqId, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                Log.d(TAG, "resetHead onResult: result=" + result + " message=" + message);
                if (isFinish) {
                    Log.d(TAG, "resetHead: not running and return");
                    return;
                }
                if (result == Definition.RESULT_TURN_HEAD_SUCCESS || result == Definition.RESULT_TURN_HEAD_UN_SUPPORT) {
                    onResetHeadSuccess();
                } else {
                    //重试
                    if (mResetHeadRetryCount < RESET_HEAD_RETRY_COUNT) {
                        mResetHeadRetryCount++;
                        Log.d(TAG, "onResult: reset head retry count=" + mResetHeadRetryCount);
                        mHandler.postDelayed(mResetHeadRunnable, RESET_HEAD_RETRY_INTERVAL);
                    } else {
                        onResetHeadFailed();
                    }
                }
            }
        });
    }

    private Runnable mResetHeadRunnable = new Runnable() {
        @Override
        public void run() {
            startResetHead();
        }
    };

    private void onResetHeadSuccess() {
        if (mResetHeadListener != null && !isFinish) {
            mResetHeadListener.onResetHeadSuccess();
        }
    }

    private void onResetHeadFailed() {
        if (mResetHeadListener != null && !isFinish) {
            mResetHeadListener.onResetHeadFailed();
        }
    }

    public interface ResetHeadListener {
        void onResetHeadSuccess();

        void onResetHeadFailed();
    }
}
