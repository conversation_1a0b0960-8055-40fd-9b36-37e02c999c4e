package com.ainirobot.home.module;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.control.RadarManager;
import com.ainirobot.home.ui.UIController;

import java.util.Timer;

public class StopChargeConfirmModule extends BaseModule {
    private static final String TAG = StopChargeConfirmModule.class.getSimpleName();

    private static class SingletonHolder {
        private static final StopChargeConfirmModule mInstance = new StopChargeConfirmModule();
    }

    public static StopChargeConfirmModule getInstance() {
        return StopChargeConfirmModule.SingletonHolder.mInstance;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.i(TAG, "reqId=" + reqId + "; intent=" + intent + "; params=" + params + " " + getState());
        switch (intent) {
            case Definition.REQ_STOP_CHARGING_CONFIRM:
                showLoadingView();
                break;
        }
        return super.onNewSemantics(reqId, intent, text, params);
    }

    private void showLoadingView() {
        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_STOP_CHARGE_CONFIRM, null, null);
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
        Log.i(TAG, "type =" + type);
        if (type == ModuleDef.LOCAL_MESSAGE_CONFIRM_STOP_CHARGE) {
            stop();
        }
    }

    @Override
    protected void onStop() {
        SystemApi.getInstance().onStopChargeConfirmFinished();
        super.onStop();
    }
}
