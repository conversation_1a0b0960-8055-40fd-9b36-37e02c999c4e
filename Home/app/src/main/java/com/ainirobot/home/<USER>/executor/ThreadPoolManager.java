package com.ainirobot.home.ota.executor;

import android.support.annotation.NonNull;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;

public class ThreadPoolManager {
    private ExecutorService cachedThreadPool;
    private final ThreadFactory threadFactory;

    private ThreadPoolManager() {
        threadFactory = new ThreadFactory() {
            final AtomicInteger integer = new AtomicInteger();

            @Override
            public Thread newThread(@NonNull Runnable r) {
                Thread thread = new Thread(r);
                thread.setName("Thread #" + integer.getAndIncrement());
                return thread;
            }
        };
        cachedThreadPool = Executors.newCachedThreadPool(threadFactory);
    }

    private static class Holder {
        private static final ThreadPoolManager INSTANCE = new ThreadPoolManager();
    }

    public static ThreadPoolManager getInstance() {
        return Holder.INSTANCE;
    }

    public ExecutorService getCachedThreadPool() {
        if (cachedThreadPool.isShutdown() || cachedThreadPool.isTerminated()) {
            cachedThreadPool = Executors.newCachedThreadPool(threadFactory);
        }
        return cachedThreadPool;
    }

    public void submitCachedTask(Runnable task) {
        getCachedThreadPool().submit(task);
    }

    public void shutdown() {
        if (cachedThreadPool != null) {
            cachedThreadPool.shutdownNow();
        }
    }
}

