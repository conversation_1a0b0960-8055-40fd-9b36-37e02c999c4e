/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.home.ota.httpclient;

import android.text.TextUtils;

import com.ainirobot.home.ota.utils.IOUtils;

import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

public class Response implements Closeable {

    private final int statusCode;
    private final InputStream content;
    private final long contentLength;
    private Map<String, List<String>> headers;
    private String contentType;
    private String contentEncoding;
    private String responseMessage;

    public Response(int statusCode, InputStream content, long contentLength) {
        this.statusCode = statusCode;
        this.content = content;
        this.contentLength = contentLength;
        this.contentEncoding = "UTF-8";
    }

    public int getStatusCode() {
        return statusCode;
    }

    public Map<String, List<String>> getHeaders() {
        return headers;
    }

    public String getHeaderField(String key) {
        List<String> sessionList = headers.get(key);
        return sessionList != null ? sessionList.get(0) : null;
    }

    public InputStream getContent() {
        return content;
    }

    public String getContentString() {
        if (content == null) {
            return null;
        }
        byte[] buffer = new byte[4096];
        int len;
        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            while ((len = content.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
            return new String(out.toByteArray(), contentEncoding);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            IOUtils.close(content);
        }
        return null;
    }

    public long getContentLength() {
        return contentLength;
    }

    public String getContentType() {
        return contentType;
    }

    public String getContentEncoding() {
        return contentEncoding;
    }

    public String getResponseMessage() {
        return responseMessage;
    }

    public void setResponseMessage(String responseMessage) {
        this.responseMessage = responseMessage;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public void setContentEncoding(String contentEncoding) {
        if (TextUtils.isEmpty(contentEncoding)) {
            return;
        }
        this.contentEncoding = contentEncoding;
    }

    public void setHeaders(Map<String, List<String>> headers) {
        this.headers = headers;
    }

    @Override
    public void close() throws IOException {
        IOUtils.close(content);
    }

    @Override
    public String toString() {
        return "Response{" +
                "statusCode=" + statusCode +
                ", content=" + content +
                ", contentLength=" + contentLength +
                ", headers=" + headers +
                ", contentType='" + contentType + '\'' +
                ", contentEncoding='" + contentEncoding + '\'' +
                ", responseMessage='" + responseMessage + '\'' +
                '}';
    }
}
