package com.ainirobot.home.module;

import android.content.Context;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.ui.UIController;

public class CalibrationModule extends BaseModule {

    private static final String TAG = "CalibrationModule:Home";

    private static CalibrationModule sInstance = null;
    private Context mContext;
    private int mReqId;

    public static CalibrationModule getInstance() {
        if (sInstance == null) {
            sInstance = new CalibrationModule();
        }
        return sInstance;
    }

    private CalibrationModule() {
    }

    public void init(Context context) {
        mContext = context;
        mReqId = 0;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.d(TAG, "reqId=" + reqId + "; intent=" + intent + "; params=" + params);
        switch (intent) {
            case Definition.REQ_D430_CALIBRATION_START:
                SkillManager.getInstance().closeSpeechAsrRecognize();
//                SkillManager.getInstance().cancleAudioOperation(); //该Module只在OTA升级成功后触发,这里无需打断“升级成功”的播报.
                UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_CALIBRATION, null, null);
                startCalibration();
                break;
            default:
                break;
        }
        SystemApi.getInstance().finishModuleParser(reqId, true);
        return false;
    }

    private void startCalibration() {
        SystemApi.getInstance().startD430Calibration(mReqId, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                Log.d(TAG, "startCalibration result: " + result
                        + ", message: " + message + ", isRunning: " + isRunning());
                if (isRunning()) {
                    release(0, 0, null);
                }
            }
        });
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
    }

    @Override
    protected void onFinish() {
        SkillManager.getInstance().openSpeechAsrRecognize();
        SystemApi.getInstance().stopD430Calibration(mReqId);
        SystemApi.getInstance().onCalibrationFinished();
    }
}
