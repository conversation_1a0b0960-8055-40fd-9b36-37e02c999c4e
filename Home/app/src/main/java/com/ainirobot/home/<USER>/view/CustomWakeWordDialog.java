package com.ainirobot.home.ui.view;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.home.R;
import com.ainirobot.home.utils.InputCorrectionUtil;

public class CustomWakeWordDialog extends Dialog implements View.OnClickListener {
    private static final int CHINESE_INDEX = 0;
    private static final int PINYIN_INDEX = 1;
    private Context mContext;
    private TextView mConfirm;
    private TextView mCancel;
    private ImageView btnClear;
    private EditText mEtInput;
    private TextView mTvSpell;
    private String mPreWakeWords;
    private String mSpells;
    private TextView btnDelete;

    public CustomWakeWordDialog(@NonNull Context context) {
        super(context, R.style.Wake_Word_Dialog);
        this.mContext = context;
        if (!(mContext instanceof Activity)) {
            Window window = getWindow();
            if (window != null) {
                window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_DIALOG);
            }
        }
    }

    private ClickListener mClickListener;

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.confirm:
                if (mClickListener != null) {
                    if (mClickListener.onConfirmClick(mEtInput.getText().toString())) {
                        clearText();
                        dismiss();
                    }
                }
                break;

            case R.id.cancel:
                clearText();
                dismiss();
                break;

            case R.id.btn_clear:
                clearText();
                break;

            case R.id.btn_delete:
                if (mClickListener != null) {
                    if (mClickListener.onDeleteClick(mEtInput)) {
                        if (mEtInput != null && mTvSpell != null && btnDelete != null) {
                            mEtInput.setText("");
                            mPreWakeWords = "";
                            mSpells = "";
                            mEtInput.setFocusableInTouchMode(true);
                            mEtInput.setFocusable(true);
                            mEtInput.requestFocus();
                            mTvSpell.setText("");
                            btnDelete.setVisibility(View.INVISIBLE);
                        }
                    }
                }
                break;
        }
    }

    private void clearText() {
        if (mEtInput != null && mTvSpell != null) {
            mPreWakeWords = "";
            mEtInput.setText("");
            mEtInput.setFocusableInTouchMode(true);
            mEtInput.setFocusable(true);
            mEtInput.requestFocus();
            mTvSpell.setText("");
        }
    }

    public interface ClickListener {
        boolean onConfirmClick(String inputWakeWord);

        boolean onDeleteClick(EditText editText);
    }

    public CustomWakeWordDialog setDialogClickListener(ClickListener listener) {
        this.mClickListener = listener;
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.custom_wakeword_dialog);
        initView();
    }

    private void initView() {
        mConfirm = (TextView) findViewById(R.id.confirm);
        mCancel = (TextView) findViewById(R.id.cancel);
        btnClear = (ImageView) findViewById(R.id.btn_clear);
        btnDelete = (TextView) findViewById(R.id.btn_delete);
        mEtInput = (EditText) findViewById(R.id.input_text);
        mTvSpell = (TextView) findViewById(R.id.spell);
        mEtInput.addTextChangedListener(new LimitTextWatcher());
        mConfirm.setOnClickListener(this);
        mCancel.setOnClickListener(this);
        btnClear.setOnClickListener(this);
        btnDelete.setOnClickListener(this);
        mEtInput.setFocusable(TextUtils.isEmpty(mPreWakeWords));
    }

    @Override
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        fillTextForView();
    }

    private class LimitTextWatcher implements TextWatcher {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            if (TextUtils.isEmpty(s.toString())) {
                btnClear.setVisibility(View.INVISIBLE);
            } else if (btnClear.getVisibility() == View.INVISIBLE) {
                btnClear.setVisibility(View.VISIBLE);
                if (btnDelete.getVisibility() == View.VISIBLE) {
                    btnDelete.setVisibility(View.INVISIBLE);
                    mTvSpell.setText("");
                }
            }
        }

        @Override
        public void afterTextChanged(Editable s) {
            String currentInput = s.toString();
            String inputStr = InputCorrectionUtil.getInstance().filterInvalidStr(currentInput);

            mEtInput.removeTextChangedListener(this);
            s.replace(0, s.length(), inputStr.trim());
            mEtInput.addTextChangedListener(this);

            InputCorrectionUtil.getInstance().processLongText(mEtInput);
        }
    }

    private void fillTextForView() {
        mEtInput.setText(mPreWakeWords);
        mEtInput.setFocusable(TextUtils.isEmpty(mPreWakeWords));
        mTvSpell.setText(mSpells);
        btnClear.setVisibility(View.INVISIBLE);
        if (TextUtils.isEmpty(mPreWakeWords)) {
            btnDelete.setVisibility(View.INVISIBLE);
        } else {
            btnDelete.setVisibility(View.VISIBLE);
        }
    }

    public CustomWakeWordDialog setPreviousWakeWords(String previousWakeWords) {
        if (previousWakeWords.contains("(") && previousWakeWords.contains(")")) {
            StringBuilder wakeWord = new StringBuilder();
            StringBuilder spell = new StringBuilder();
            String[] split = previousWakeWords.split("\\)");
            for (String s : split) {
                String[] combine = s.split("\\(");
                wakeWord.append(combine[CHINESE_INDEX]);
                spell.append(combine[PINYIN_INDEX]);
            }

            this.mPreWakeWords = wakeWord.toString();
            this.mSpells = spell.toString();
        } else {
            this.mPreWakeWords = "";
            this.mSpells = "";
        }
        return this;
    }
}
