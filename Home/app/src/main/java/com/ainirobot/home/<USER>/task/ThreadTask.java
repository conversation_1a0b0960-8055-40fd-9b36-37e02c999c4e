package com.ainirobot.home.ota.task;

import android.util.Log;

import com.ainirobot.home.ota.constants.OtaConstants;

public abstract class ThreadTask implements Runnable {

    public static final String TAG = OtaConstants.TAG_PREFIX  + ThreadTask.class.getSimpleName();
    public static final int NO_ERROR = 0;
    protected int mErrorCode = NO_ERROR;
    protected Thread mThread = null;
    protected Object mResult;

    public enum STATUS {
        STATUS_UNSTART(0),
        STATUS_RUNNING(1),
        STATUS_FINISH(3);
        private int status;
        STATUS(int status) {
            this.status = status;
        }
    }
    protected volatile STATUS mRunningStatus = STATUS.STATUS_UNSTART;

    @Override
    public void run() {
        onRun();
    }

    public void start() {
        if (mRunningStatus != STATUS.STATUS_RUNNING) {
            mRunningStatus = STATUS.STATUS_RUNNING;
            if (mThread == null || !mThread.isAlive() || mThread.isInterrupted()) {
                Log.d(TAG, "mThread is null or dead. start new thread");
                mThread = new Thread(this);
                mThread.start();
            } else {
                Log.d(TAG, "mThread is running");
            }
        } else {
            Log.d(TAG, "thread#" + mThread + " already start");
        }
    }

    public void stop() {
        if (mRunningStatus != STATUS.STATUS_UNSTART) {
            mRunningStatus = STATUS.STATUS_FINISH;
            onStop();
        }
    }

    protected void onRun() {
    }

    protected void onStop() {
    }


    public boolean isRunning() {
        return mRunningStatus == STATUS.STATUS_RUNNING;
    }
}
