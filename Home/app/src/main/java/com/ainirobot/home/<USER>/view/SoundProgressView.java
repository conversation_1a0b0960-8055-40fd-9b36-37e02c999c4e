package com.ainirobot.home.ui.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.drawable.Drawable;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;

import com.ainirobot.home.R;


public class SoundProgressView extends View {
    private final String TAG = "SoundProgressView:Home";
    private int progress = 10;//可配置
    private int mMaxProgress = 10;
    private int lineWidth = 10;//可配置
    private int startAngle = -90;

    public int getProgress() {
        return progress;
    }

    public void setProgress(int progress) {
        this.progress = progress;
        invalidate();
    }

    public int getLineWidth() {
        return lineWidth;
    }

    public void setLineWidth(int lineWidth) {
        this.lineWidth = lineWidth;
        invalidate();
    }

    public SoundProgressView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public SoundProgressView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    public SoundProgressView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        /*TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.SoundProgressView);
        progress = a.getInt(R.styleable.SoundProgressView_progress, 0);
        lineWidth = a.getInt(R.styleable.SoundProgressView_lineWidth, 20);*/
    }

    private void printLayoutParam() {
        ViewGroup.LayoutParams lp =  getLayoutParams();
        Log.d(TAG, "height = "  + lp.height);
        Log.d(TAG, "width = " + lp.width);
        Log.d(TAG, "getHeight = " + getHeight());
        Log.d(TAG, "getWidth = " + getWidth());
    }

    private void drawProgressLine(Canvas canvas, int radius, int progress) {
        Paint paint = new Paint();
        paint.setStyle(Paint.Style.STROKE); // 画线模式
        paint.setStrokeWidth(lineWidth);
        paint.setAntiAlias(true);
        paint.setColor(Color.WHITE);
        Path path = new Path();
        float sweepAngle = 360*progress/mMaxProgress;
        path.addArc(lineWidth/2, lineWidth/2, 2*radius - lineWidth/2, 2*radius -lineWidth/2, startAngle, sweepAngle);
        canvas.save();
        canvas.drawPath(path, paint);
        canvas.restore();
    }

    private void drawSpeaker(Canvas canvas, int speakerWidth, int radius, int progress) {
        Drawable d = getResources().getDrawable(R.drawable.sound2);
        if (progress > 5) {
            d = getResources().getDrawable(R.drawable.sound5);
        } else if (progress == 0) {
            d = getResources().getDrawable(R.drawable.sound0);
        }
        d.setBounds(0,0,speakerWidth,speakerWidth);

        canvas.save();
        canvas.translate(radius - speakerWidth/2, radius -speakerWidth/2);
        d.draw(canvas);
        canvas.restore();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        ViewGroup.LayoutParams lp =  getLayoutParams();
        printLayoutParam();
        final int min = Math.min(lp.height, lp.width);
        setMeasuredDimension(min,min);
    }



    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        Log.d(TAG, "onLayout after");
        printLayoutParam();
    }


    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        drawProgressLine(canvas, getHeight()/2, progress);
        drawSpeaker(canvas, getHeight()/3, getHeight()/2, progress);
    }
}
