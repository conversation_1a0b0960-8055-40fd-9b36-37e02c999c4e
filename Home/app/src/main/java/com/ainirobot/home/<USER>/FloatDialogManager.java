package com.ainirobot.home.floatdialog;

import android.graphics.PixelFormat;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.TextView;

import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.R;
import com.ainirobot.home.bi.BIRobotLockReport;

import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static android.content.Context.WINDOW_SERVICE;
import static android.view.ViewGroup.LayoutParams.WRAP_CONTENT;

public class FloatDialogManager {

    private static final String TAG = "FloatDialogManager";

    private View mFloatView;
    private View mLiteLockView;
    private View mPushMapDialog;
    private ScheduledThreadPoolExecutor mScheduled;
    private ScheduledFuture mFuture;

    public enum FloatDialogType{
        TYPE_RESPONSE_LITE
    }


    private static FloatDialogManager mInstance = new FloatDialogManager();
    public static FloatDialogManager getInstance(){
        if(mInstance == null){
            mInstance = new FloatDialogManager();
        }
        return mInstance;
    }

    public void showFloatDialog(FloatDialogType type, String content){

        Log.i(TAG, "show float dialog, float dialog type: " + type);

        switch (type) {

            case TYPE_RESPONSE_LITE:
                showLightResponseDialog(content);
                break;

            default:
                break;
        }

    }

    public void showLightResponseDialog(String content){
        removeAll();
        initLightResponseView(content);
        WindowManager.LayoutParams params = getLightResponseParams();
        WindowManager manager = (WindowManager) ApplicationWrapper.getContext().getSystemService(WINDOW_SERVICE);
        if (manager == null) {
            Log.i(TAG, "window manager get error, return!!!");
            return;
        }
        manager.addView(mFloatView, params);
        startScheduledExec(5000l);
    }

    public void showRemoteControlDailog(String content) {
        removeAll();
        initLightResponseView(content);
        WindowManager.LayoutParams params = getRemoteControlParams();
        WindowManager manager = (WindowManager) ApplicationWrapper.getContext().getSystemService(WINDOW_SERVICE);
        if (manager == null) {
            Log.i(TAG, "window manager get error, return!!!");
            return;
        }
        manager.addView(mFloatView, params);
    }

    public void showLiteLockDialog(final String msg){
        removeLiteLockView();
        initLiteLockView(msg);
        WindowManager.LayoutParams params = getLiteLockParams();
        WindowManager manager = (WindowManager) ApplicationWrapper.getContext().getSystemService(WINDOW_SERVICE);
        if (manager == null) {
            Log.i(TAG, "window manager get error, return!!!");
            return;
        }
        manager.addView(mLiteLockView, params);
    }

    public void removeAll(){
        Log.i(TAG, "remove all float dialog, current view" + mFloatView);
        if(mFloatView == null){
            return;
        }
        WindowManager manager = (WindowManager) ApplicationWrapper.getContext().getSystemService(WINDOW_SERVICE);
        if (manager == null) {
            Log.i(TAG, "window manager get error, return!!!");
            return;
        }
        manager.removeView(mFloatView);
        mFloatView = null;
        shutDownFuture();
    }

    public void removeLiteLockView(){
        Log.i(TAG, "remove lite lock view");
        if(mLiteLockView == null){
            return;
        }
        WindowManager manager = (WindowManager) ApplicationWrapper.getContext().getSystemService(WINDOW_SERVICE);
        if (manager == null) {
            Log.i(TAG, "window manager get error, return!!!");
            return;
        }
        manager.removeView(mLiteLockView);
        mLiteLockView = null;
    }

    public void showPushMapDialog(){
        removePushMapDialog();
        initPushMapDialog();
        WindowManager.LayoutParams params = getPushMapDialogParams();
        WindowManager manager = (WindowManager) ApplicationWrapper.getContext().getSystemService(WINDOW_SERVICE);
        if (manager == null) {
            Log.i(TAG, "window manager get error, return!!!");
            return;
        }
        manager.addView(mPushMapDialog, params);
    }

    private WindowManager.LayoutParams getPushMapDialogParams(){
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
        layoutParams.type = WindowManager.LayoutParams.TYPE_PHONE;
        layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
        layoutParams.format = PixelFormat.RGBA_8888;
        layoutParams.width = 940;
        layoutParams.height = 500;
        //layoutParams.windowAnimations = android.R.style.Animation_Translucent;
//        layoutParams.windowAnimations = R.style.WindowAnim;
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL | Gravity.CENTER_VERTICAL;
        return layoutParams;
    }

    public void removePushMapDialog(){
        Log.i(TAG, "remove lite lock view");
        if(mPushMapDialog == null){
            return;
        }
        WindowManager manager = (WindowManager) ApplicationWrapper.getContext().getSystemService(WINDOW_SERVICE);
        if (manager == null) {
            Log.i(TAG, "window manager get error, return!!!");
            return;
        }
        manager.removeView(mPushMapDialog);
        mPushMapDialog = null;
    }

    private void initPushMapDialog(){
        mPushMapDialog = LayoutInflater.from(ApplicationWrapper.getContext()).inflate(R.layout.push_map_dialog, null);
        TextView bt_close = (TextView) mPushMapDialog.findViewById(R.id.bt_close);
        bt_close.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                removePushMapDialog();
                // TODO: 2020/5/25 bireport
            }
        });
    }


    private void initLightResponseView(String content){
        mFloatView = LayoutInflater.from(ApplicationWrapper.getContext()).inflate(R.layout.float_dialog_window, null);
        TextView tv_text = (TextView) mFloatView.findViewById(R.id.tv_content);
        tv_text.setText(TextUtils.isEmpty(content) ? "" : content);
    }

    private void initLiteLockView(final String msg){
        mLiteLockView = LayoutInflater.from(ApplicationWrapper.getContext()).inflate(R.layout.lite_lock_window, null);
        if (!TextUtils.isEmpty(msg)) {
            TextView tvMsg = (TextView) mLiteLockView.findViewById(R.id.tv_msg);
            tvMsg.setText(msg);
        }
        TextView bt_close = (TextView) mLiteLockView.findViewById(R.id.bt_close);
        bt_close.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                removeLiteLockView();
                BIRobotLockReport exitReport = new BIRobotLockReport();
                exitReport.addEndTime(System.currentTimeMillis());
                exitReport.addLockType(0);
                exitReport.report();
            }
        });
    }

    private WindowManager.LayoutParams getLightResponseParams(){
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
        layoutParams.type = WindowManager.LayoutParams.TYPE_PHONE;
        layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
        layoutParams.format = PixelFormat.RGBA_8888;
        layoutParams.width = WRAP_CONTENT;
        layoutParams.height = WRAP_CONTENT;
        //layoutParams.windowAnimations = android.R.style.Animation_Translucent;
        layoutParams.windowAnimations = R.style.WindowAnim;
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        layoutParams.y = 700;
        return layoutParams;

    }

    private WindowManager.LayoutParams getRemoteControlParams(){
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
        layoutParams.type = WindowManager.LayoutParams.TYPE_PHONE;
        layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
        layoutParams.format = PixelFormat.RGBA_8888;
        layoutParams.width = WRAP_CONTENT;
        layoutParams.height = WRAP_CONTENT;
        layoutParams.windowAnimations = R.style.WindowAnim;
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        layoutParams.y = -900;
        return layoutParams;
    }

    private WindowManager.LayoutParams getLiteLockParams(){
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
        layoutParams.type = WindowManager.LayoutParams.TYPE_PHONE;
        layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
        layoutParams.format = PixelFormat.RGBA_8888;
        layoutParams.width = 940;
        layoutParams.height = 565;
        //layoutParams.windowAnimations = android.R.style.Animation_Translucent;
//        layoutParams.windowAnimations = R.style.WindowAnim;
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL | Gravity.CENTER_VERTICAL;
        return layoutParams;
    }

    private void startScheduledExec(Long timeDelay){
        Log.i(TAG, "start scheduled executor, time delay: " + timeDelay);
        shutDownFuture();
        if(mScheduled == null){
            mScheduled = new ScheduledThreadPoolExecutor(1);
        }
        mFuture = mScheduled.schedule(new Runnable() {
            @Override
            public void run() {
                Log.i(TAG, "future is comming");
                removeAll();
            }
        }, timeDelay, TimeUnit.MILLISECONDS);
    }

    private void shutDownFuture(){
        Log.i(TAG, "shut down future");
        if (!(mFuture == null || mFuture.isCancelled() || mFuture.isDone())){
            mFuture.cancel(true);
        }
        mFuture = null;
    }

    public boolean floatDialogIsShow() {
        return mFloatView != null;
    }
}
