package com.ainirobot.home.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;

import com.ainirobot.home.ApplicationWrapper;

public class SharedPrefUtil {

    private final SharedPreferences sharedPreferences;

    private SharedPrefUtil() {
        sharedPreferences = ApplicationWrapper.getContext().getSharedPreferences("home_sp", Context.MODE_PRIVATE);
    }

    private static final class InstanceHolder {
        private static final SharedPrefUtil instance = new SharedPrefUtil();
    }

    public static SharedPrefUtil getInstance(){
        return InstanceHolder.instance;
    }

    private static final String WEIXIN_QRCODE_URL = "weixin_qrcode_url";

    public void putString(String key , String value ) {
        if (TextUtils.isEmpty(key)) {
            return;
        }
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putString(key,value).apply();
    }

    public String getString(String key,String defaultValue) {
        if (TextUtils.isEmpty(key)) {
            return "";
        }
        return sharedPreferences.getString(key,defaultValue);
    }

    public void putInt(String key, int value) {
        if (TextUtils.isEmpty(key)) {
            return;
        }
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putInt(key, value).apply();
    }

    public int getInt(String key) {
        if (TextUtils.isEmpty(key)) {
            return 0;
        }
        return sharedPreferences.getInt(key, 0);
    }

    public void remove(String key) {
        if (TextUtils.isEmpty(key)) {
            return;
        }
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.remove(key).apply();
    }
}