/*
 *   Copyright (C) 2017 OrionStar Technology Project
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.ainirobot.home.report;

import android.support.annotation.NonNull;
import android.util.Log;

import com.ainirobot.coreservice.bean.Task;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.TaskProxy;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.control.ControlManager;
import com.google.gson.Gson;

/**
 * 在此写用途
 *
 * @FileName: com.ainirobot.home.report.TaskReport.java
 * @author: Orion
 * @date: 2019-03-08 10:38
 */
public class TaskReport {
    private static final String TAG = TaskReport.class.getSimpleName();

    //TODO delete
    private Constant.TaskInfo mTaskInfo = new Constant.TaskInfo();


    private TaskProxy mCurrentTask = new TaskProxy();

    private Gson mGson = new Gson();

    private static TaskReport sInstance = new TaskReport();

    public static TaskReport getInstance() {
        return sInstance;
    }

    public synchronized void setTaskResult(@NonNull Constant.TaskEvent event) {
        //TODO delete
        mTaskInfo.setCurrentResult(event);


        switch (event){
            case task_start:
                mCurrentTask.setStatus(Task.TASK_STATUS_START);
                break;

            case task_fail:
                mCurrentTask.setStatus(Task.TASK_STATUS_FAIL);
                break;

            case task_cancel:
                mCurrentTask.setStatus(Task.TASK_STATUS_CANCEL);
                break;

            case task_finish:
                mCurrentTask.setStatus(Task.TASK_STATUS_FINISH);
                break;

            default:
                break;
        }
    }

    public synchronized void reportModuleChange(int module) {
        int taskId = 0;
        if ((ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus())
                && module == ModuleDef.FEATURE_AUTO_CHARGE) {
            taskId = ModuleDef.FEATURE_POWER_LOW;
        } else {
            taskId = module;
        }
        Log.d(TAG, "reportModuleChange module : " + taskId);
        Constant.TaskBaseInfo nextTask = Constant.TASKBASEINFO.get(taskId);
        if (null == nextTask) {
            Log.w(TAG, "TASKBASEINFO have no feature: " + taskId);
            return;
        }
        reportTask_old(nextTask);
        reportTask_new(nextTask);
    }

    //TODO delete
    private void reportTask_old(Constant.TaskBaseInfo nextTask){
        mTaskInfo.setNextTask(nextTask);
        Log.d(TAG, "reportModuleChange taskChange: " + mGson.toJson(mTaskInfo));
        SystemApi.getInstance().sendStatusReport(Definition.STATUS_MODULE_CHANGE, mGson.toJson(mTaskInfo));

        mTaskInfo.setCurrentResult(Constant.TaskEvent.task_finish);
    }

    private void reportTask_new(Constant.TaskBaseInfo nextTask){
        if(mCurrentTask.getStatus() != Task.TASK_STATUS_START) {
            SystemApi.getInstance().reportTask(mCurrentTask);
        }

        mCurrentTask.setName(nextTask.getName());
        mCurrentTask.setType(nextTask.getFeatureType());
        mCurrentTask.setStatus(Task.TASK_STATUS_START);
        Log.d(TAG, "reportModuleChange taskChange: " + mGson.toJson(mTaskInfo));
        SystemApi.getInstance().reportTask(mCurrentTask);
    }

    private synchronized void reportUnknownPackageChange(String packagename) {
        //TODO delete
        Constant.TaskBaseInfo nextTask = new Constant.TaskBaseInfo(ModuleDef.FEATURE_NONE, packagename,
                packagename, "");
        mTaskInfo.setNextTask(nextTask);
        SystemApi.getInstance().sendStatusReport(Definition.STATUS_MODULE_CHANGE, mGson.toJson(mTaskInfo));
        mTaskInfo.setCurrentResult(Constant.TaskEvent.task_finish);


        mCurrentTask.setType(packagename);
        mCurrentTask.setName(packagename);
        mCurrentTask.setStatus(Task.TASK_STATUS_START);
        Log.d(TAG, "reportUnknownPackageChange taskChange: " + mGson.toJson(mTaskInfo));
        SystemApi.getInstance().reportTask(mCurrentTask);

    }

    public synchronized void reportPackageChange(String packagename) {
        Log.d(TAG, "reportPackageChange packageName : " + packagename);
        Constant.TaskBaseInfo nextTask = Constant.ACTIVIEYBASEINFO.get(packagename);
        if (null == nextTask) {
            Log.w(TAG, "ACTIVIEYBASEINFO have no packagename: " + packagename);
            if (!Constant.WHITE_LIST_PACKAGE.contains(packagename)) {
                Log.d(TAG, "report unknown pkg:" + packagename);
                reportUnknownPackageChange(packagename);
            }
            //这个特殊的判断解决当按下急停的时候，点击设置然后返回，Home并没有任务切换，导致后台任务显示出错。
            if (ApplicationWrapper.getContext().getPackageName().equals(packagename) &&
                    ControlManager.getControlManager().getCurrentModule() == ModuleDef.FEATURE_EMERGENCY) {
                reportModuleChange(ModuleDef.FEATURE_EMERGENCY);
            }
            return;
        }

        //TODO delete
        mTaskInfo.setNextTask(nextTask);
        SystemApi.getInstance().sendStatusReport(Definition.STATUS_MODULE_CHANGE, mGson.toJson(mTaskInfo));
        mTaskInfo.setCurrentResult(Constant.TaskEvent.task_finish);


        mCurrentTask.setStatus(Task.TASK_STATUS_START);
        mCurrentTask.setName(nextTask.getName());
        mCurrentTask.setType(nextTask.getFeatureType());
        Log.d(TAG, "reportPackageChange taskChange: " + mGson.toJson(mTaskInfo));
        SystemApi.getInstance().reportTask(mCurrentTask);
    }

    public void warningReport(@NonNull String value) {
        SystemApi.getInstance().sendStatusReport(Definition.STATUS_WARNING_REPORT, value);
    }
}
