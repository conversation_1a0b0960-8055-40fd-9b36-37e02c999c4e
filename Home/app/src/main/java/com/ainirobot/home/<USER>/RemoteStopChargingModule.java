package com.ainirobot.home.module;

import static com.ainirobot.home.ModuleDef.MSG_REMOTE_STOP_CHARGING_EXIT;

import android.content.Context;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.utils.LocationUtil;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import org.json.JSONObject;

import java.util.Timer;
import java.util.TimerTask;


/**
 * 远程结束充电
 */
public class RemoteStopChargingModule extends BaseModule {
    private static final String TAG = "RemoteStopChargingModule:Home";

    /**
     *   0 成功
     *  -1 正在执行离桩操作
     *  -2 失败
     *  -3 非充电状态
     *  -4 非桩充
     *  -5 避障
     *  -6 雷达启动失败
     */

    public static int STOPING_SUCCESS = 0;
    public static int STOPING_START = -1;
    public static int STOPING_FAILED = -2;
    public static int STOPING_FAILED_NOT_CHARGING = -3;
    public static int STOPING_FAILED_NO_PILE = -4;
    public static int STOPING_FAILED_AVOID = -5;
    public static int STOPING_FAILED_RADAR_FAIL = -6;

    private static final int DEFAULT_TIME_OUT_SECOND = 60;

    private int mReqId = 0;
    private String taskId = null;
    private int mCurState = 0;
    private String errorMsg = null;
    private Context mContext;
    private volatile boolean mCharging = false;

    private Timer mTimeOutTimer;
    private Timer delayTimeOutTimer;
    private int mTimeOut = DEFAULT_TIME_OUT_SECOND;

    private RemoteStopChargingModule() {
    }

    private static class SingletonHolder {
        private static final RemoteStopChargingModule mInstance = new RemoteStopChargingModule();
    }

    public static RemoteStopChargingModule getInstance() {
        return RemoteStopChargingModule.SingletonHolder.mInstance;
    }


    public void init(Context context) {
        mContext = context;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.d(TAG, "onNewSemantics reqId= " + reqId + ", intent = " + intent +
                ", text" + text + ", params = " + params);
        this.initReqId(params);
        switch (intent) {
            case Definition.REQ_SYSTEM_STOP_CHARGING:
                //解析task_id
                this.initParams(params);
                this.remoteStopCharging();
                break;
            case Definition.REQ_SYSTEM_STOP_CHARGING_STATUS:
                this.remoteStopChargingStatus();
                break;
            default:
                break;
        }
        return true;
    }

    /**
     * 远程离桩
     */
    private void remoteStopCharging(){
        Log.d(TAG, "remoteStopCharging begin");
        //判断是否正在执行
        if(this.mCurState == STOPING_START){
            sendStopResponse();
            return ;
        }
        //重置默认状态
        mCurState = STOPING_SUCCESS;
        //开始离桩，设置默认超时时间
        handlerTimerOut(DEFAULT_TIME_OUT_SECOND);

        //判断充电状态
        mCharging = ControlManager.getControlManager().getSystemStatusManager().getIsCharging();
        if (!mCharging) {
            Log.d(TAG, "remoteStopCharging not charging");
            mCurState = STOPING_FAILED_NOT_CHARGING;
            finishStopCharging();
            sendStopResponse();
            return;
        }
        //判断是否桩充
        if (!LocationUtil.getInstance().isChargingTypePile()){
            Log.d(TAG, "remoteStopCharging is not ChargingTypePile ");
            this.mCurState = STOPING_FAILED_NO_PILE;
            finishStopCharging();
            sendStopResponse();
            return;
        }
        sendStopResponse();
        mCurState = STOPING_START;
        handleStop();
    }

    private void handleStop(){
        UIController.getInstance().showFragment(
                UIController.FRAGMENT_TYPE.FRAGMENT_REMOTE_STOP_CHARGING, null, null);

        float speed = 0.2f;
        float distance = ProductInfo.isSaiphXdOrBigScreen()
                || ProductInfo.isSaiphChargeIr() || ProductInfo.isSaiphPro()
                || ProductInfo.isAlnilamPro() || ProductInfo.isMeissaPlus() ? 0.25f : 0.1f;
        SystemApi.getInstance().leaveChargingPile(mReqId,speed,distance, new CommandListener() {
            @Override
            public void onStatusUpdate(int status, String data, String extraData){
                Log.d(TAG, "leaveChargingPile onStatusUpdate: "+ status + " data:"+ data + " extraData:"+ extraData);
            }
            @Override
            public void onError(int errorCode, String errorString, String extraData){
                Log.d(TAG, "leaveChargingPile errorCode: "+ errorCode + " errorString:"+ errorString + " extraData:"+ extraData);
                errorMsg = errorString;
                switch (errorCode){
                    case Definition.RESULT_FAILURE_MOTION_AVOID_STOP://避障
                        mCurState = STOPING_FAILED_AVOID;
                        UIController.getInstance().sendMessageToFragment(
                                UIController.MESSAGE_TYPE.REMOTE_STOP_CHARGING_AVOID_FAILED, null);
                        break;
                    case Definition.RESULT_FAILURE_TIMEOUT:
                        mCurState = STOPING_FAILED;
                        UIController.getInstance().sendMessageToFragment(
                                UIController.MESSAGE_TYPE.REMOTE_STOP_CHARGING_UNKNOWN_FAILED, null);
                        break;
                    case Definition.STATUS_LEAVE_PILE_OPEN_RADAR_FAILURE://雷达启动失败
                        mCurState = STOPING_FAILED_RADAR_FAIL;
                        UIController.getInstance().sendMessageToFragment(
                                UIController.MESSAGE_TYPE.REMOTE_STOP_CHARGING_RADAR_FAILED, null);
                        break;
                    default:
                        mCurState = STOPING_FAILED;
                        UIController.getInstance().sendMessageToFragment(
                                UIController.MESSAGE_TYPE.REMOTE_STOP_CHARGING_UNKNOWN_FAILED, null);
                        break;
                }
            }
            @Override
            public void onResult(int result, String message, String extraData){
                Log.d(TAG, "leaveChargingPile onResult: "+ result + " message:"+ message);
                errorMsg = message;
                switch (result){
                    case Definition.RESULT_OK://离桩成功
                        //如果此处想判断充电状态 注意充电状态更新会有一定时间
                        mCurState = STOPING_SUCCESS;
                        UIController.getInstance().sendMessageToFragment(
                                UIController.MESSAGE_TYPE.REMOTE_STOP_CHARGING_SUCCEED, null);
                        setLeaveSuccessTimeOut();
                        break;
                    default:
                        mCurState = STOPING_FAILED;
                        break;
                }
            }
        });
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
        if (type == MSG_REMOTE_STOP_CHARGING_EXIT) {
            Log.d(TAG, "onMessageFromLocal MSG_REMOTE_STOP_CHARGING_EXIT");
            stop();
        } else if (type == ModuleDef.LOCAL_MESSAGE_CHARGE_END) {
            mCurState = STOPING_SUCCESS;
        }
    }

    @Override
    protected void onStop() {
        Log.d(TAG, "onStop==========");
        clearTimeOut();
        clearDelayTimeOut();
        SystemApi.getInstance().onRemoteStopChargingFinished();
        super.onStop();
    }

    private void setLeaveSuccessTimeOut() {
        if (mTimeOut == DEFAULT_TIME_OUT_SECOND) {
            Log.d(TAG, "setLeaveSuccessTimeOut is default, not set time out");
            return;
        }
        handlerTimerOut(mTimeOut);
    }

    private void handlerTimerOut(int delaySecond){
        try{
            this.clearTimeOut();
            mTimeOutTimer = new Timer();
            mTimeOutTimer.schedule(new TimerTask() {
                @Override
                public void run() {
                    Log.d(TAG, "mTimeOutTimer  ========== delaySecond:" + delaySecond);
                    mCurState = STOPING_FAILED;
                    errorMsg = "time out";
                    cancel();
                    mTimeOutTimer = null;
                    finishStopCharging();
                }
            }, delaySecond * 1000L);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
    private void clearTimeOut(){
        try{
            if(mTimeOutTimer != null){
                mTimeOutTimer.cancel();
                mTimeOutTimer = null;
            }
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    private void initReqId(String data){
        Log.d(TAG, "initReqId  data:"+ data);
        try{
            JSONObject jsonObject = new JSONObject(data);
            mReqId = jsonObject.optInt("reqId");
            Log.d(TAG, "initReqId  mReqId:"+ mReqId);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    private void initParams(String data){
        Log.d(TAG, "initTaskId  data:"+ data);
        try{
            JSONObject jsonObject = new JSONObject(data);
            String param_1 = jsonObject.optString("param");
            JSONObject jsonObject_data1 = new JSONObject(param_1);
            taskId = jsonObject_data1.optString("task_id");
            mTimeOut = jsonObject_data1.optInt("result_timeout", DEFAULT_TIME_OUT_SECOND);
            //最大超时时间遵循之前逻辑，最大超时时间为60s
            if (mTimeOut > DEFAULT_TIME_OUT_SECOND) {
                mTimeOut = DEFAULT_TIME_OUT_SECOND;
            }else if (mTimeOut <= 0) {
                mTimeOut = 1;//测试中发现由于电量返回延迟，如果超时时间为0导致立即更新状态，会造成重新进入充电页面
            }
            Log.d(TAG, "initTaskId  task_id:" + taskId + " result_timeout:" + mTimeOut);
        }catch (Exception e){
            e.printStackTrace();
        }
    }


    /**
     * 远程离桩返回
     */
    private void sendStopResponse() {
        Log.i(TAG, "sendRemoteResponse : mReqId: " +mReqId + " result: "+ mCurState + ",type " + Definition.REQ_REMOTE_STOP_CHARGING);
        JsonObject obj = new JsonObject();
        obj.addProperty("reqId", mReqId);
        obj.addProperty("type", Definition.REQ_REMOTE_STOP_CHARGING);
        obj.addProperty("result", mCurState );
        obj.addProperty("errmsg", errorMsg);
        SystemApi.getInstance().sendStatusReport(Definition.REQ_REMOTE_STOP_CHARGING,
                new Gson().toJson(obj));
    }



    /**
     * 远程离桩状态返回
     *
     *
     */
    private void remoteStopChargingStatus(){
        Log.d(TAG, "remoteStopChargingStatus : mReqId: " +mReqId + " result: "+ mCurState + ",type " + Definition.REQ_REMOTE_STOP_CHARGING_STATUS);

        String status = mCurState == STOPING_START ? "start" : "end";

        JsonObject dataobj = new JsonObject();
        dataobj.addProperty("is_charging", mCharging ? 1 : 0);
        dataobj.addProperty("task_id", taskId);
        dataobj.addProperty("status", status);
        dataobj.addProperty("result", mCurState == STOPING_START ? STOPING_SUCCESS : mCurState);//执行中返回0
        dataobj.addProperty("errmsg", errorMsg);

        JsonObject obj = new JsonObject();
        obj.addProperty("reqId", mReqId);
        obj.addProperty("type", Definition.REQ_REMOTE_STOP_CHARGING_STATUS);
        obj.addProperty("result", STOPING_SUCCESS);
        obj.addProperty("errmsg", errorMsg);
        obj.addProperty("data", new Gson().toJson(dataobj));
        SystemApi.getInstance().sendStatusReport(Definition.REQ_REMOTE_STOP_CHARGING,
                new Gson().toJson(obj));

        if ("end".equals(status) && (mCurState == STOPING_SUCCESS || mCurState == STOPING_FAILED_NOT_CHARGING || mCurState == STOPING_FAILED_NO_PILE)) {
            Log.d(TAG, "remoteStopChargingStatus : mReqId: " + mReqId + " on Finish ");
            finishStopCharging();
        }
    }

    private void finishStopCharging(){
        Log.d(TAG, " finishStopCharging ");
        //由于充电状态上报会有一定时间 所以延迟退出
        delayTimeOutTimer = new Timer();
        delayTimeOutTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                stop();
            }
        }, 1100);
    }

    private void clearDelayTimeOut(){
        try{
            if(delayTimeOutTimer != null){
                delayTimeOutTimer.cancel();
                delayTimeOutTimer = null;
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

}
