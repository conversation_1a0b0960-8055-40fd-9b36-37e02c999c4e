# Home国际化注意事项

## 1.暂不修改文件

### 1.1 Constant.java
机器端和服务端通信部分字段数据。该数据服务端根据简体中文强匹配

## 2.注意事项
### 2.1 
string.xml文件中简体中文和繁体中文（台湾）一一对应。
理论上只允许从尾部新增string，不允许私自改动已有字符串文本信息，如需改动参考2.3注意事项。
### 2.2
繁体中文切勿自己百度翻译直接添加，会有翻译团队翻译后添加
### 2.3
如需改动已有字符串,用于TTS播放或UI展示,请在ChangedString.md文件中添加修改前后字符串id，行数和内容。
### 2.4
每个工程内会建一个UnchangeableString.java文件，此文件用于存储无需翻译的字符串。
例如：定位和充电需要用到的“回充点”，“接待点”，由于需要坐标强匹配，简体和繁体属于不同两个地点，
此类字符需要定义在UnchangeableString.java内。该文件只对单个Project生效。



