package com.ainirobot.home.ota.database;

import static com.ainirobot.home.ota.constants.OtaConstants.OS_LIST;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.util.Log;

import com.ainirobot.home.ota.bean.InstallData;
import com.ainirobot.home.ota.bean.VersionData;
import com.ainirobot.home.ota.constants.OtaConstants;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class DataBaseManager implements IDataBaseInterface {
    private final static String TAG = OtaConstants.TAG_PREFIX + DataBaseManager.class.getSimpleName();
    private final Context mContext;

    private final Uri uri = VersionData.CONTENT_URI;

    public DataBaseManager(Context context) {
        mContext = context;
    }

    @Override
    public synchronized boolean insert(VersionData dataBean) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(VersionData.DB.name, dataBean.getName());
        contentValues.put(VersionData.DB.status, dataBean.getStatus().ordinal());
        Uri ret = mContext.getContentResolver().insert(uri, contentValues);
        if (ret == null) {
            Log.e(TAG, "insert failed");
            return false;
        } else {
            return true;
        }
    }

    @Override
    public synchronized int update(VersionData dataBean) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(VersionData.DB.name, dataBean.getName());
        contentValues.put(VersionData.DB.status, dataBean.getStatus().ordinal());
        contentValues.put(VersionData.DB.currentVersion, dataBean.getCurrentVersion());
        contentValues.put(VersionData.DB.targetVersion, dataBean.getTargetVersion());

        String[] args = {dataBean.getName()};
        String select = "name=" + "'" + args[0] + "'";

        int ret = mContext.getContentResolver().update(uri, contentValues, select, null);
        return ret;
    }

    @Override
    public VersionData getOsData(String osName) {
        if (osName == null) {
            return null;
        }

        VersionData data;
        String[] args = {osName};
        data = query(args);

        return data;
    }

    @Override
    public List<VersionData> getOsData(String[] os) {
        if (os == null || os.length == 0) {
            return null;
        }

        List<VersionData> retList = new ArrayList<VersionData>();

        for (int i = 0; i < os.length; i++) {
            if(Arrays.asList(OS_LIST).contains(os[i])) {
                VersionData data = getOsData(os[i]);
                retList.add(data);
            }
        }

        return retList;
    }

    @Override
    public VersionData getOsData(InstallData os) {
        if (os == null || os.getOsName() == null) {
            return null;
        }

        return getOsData(os.getOsName());
    }

    @Override
    public List<VersionData> getOsData(InstallData[] os) {
        if (os == null || os.length == 0) {
            return null;
        }

        String[] osData = new String[os.length];
        for (int i = 0; i < osData.length; i++) {
            osData[i] = os[i].getOsName();
        }

        return getOsData(osData);
    }

    @Override
    public synchronized List<VersionData> getAllVersion() {
        List<VersionData> list = new ArrayList<>();
        VersionData data;
        Cursor cursor = null;
        try {
            cursor = mContext.getContentResolver().query(uri, null, null, null,
                    null);
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    data = new VersionData();
                    data.setId(cursor.getInt(
                            cursor.getColumnIndexOrThrow(VersionData.DB.id)));
                    data.setName(cursor.getString(
                            cursor.getColumnIndexOrThrow(VersionData.DB.name)));
                    data.setStatus(cursor.getInt(
                            cursor.getColumnIndexOrThrow(VersionData.DB.status)));
                    data.setCurrentVersion(cursor.getString(
                            cursor.getColumnIndexOrThrow(VersionData.DB.currentVersion)));
                    data.setTargetVersion(cursor.getString(
                            cursor.getColumnIndexOrThrow(VersionData.DB.targetVersion)));
                    list.add(data);
                }
                cursor.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (cursor != null)
                cursor.close();
        }
        return list;
    }

    private VersionData query(String[] args) {
        VersionData data = new VersionData();
        String[] columns = {"_id", "name", "currentVersion", "targetVersion", "status"};
        String select = "name=" + "'" + args[0] + "'";

        Cursor cursor = null;
        try {
            cursor = mContext.getContentResolver().query(uri, columns, select, null, null);
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    data.setId(cursor.getInt(
                            cursor.getColumnIndexOrThrow(VersionData.DB.id)));
                    data.setName(cursor.getString(
                            cursor.getColumnIndexOrThrow(VersionData.DB.name)));
                    data.setStatus(cursor.getInt(
                            cursor.getColumnIndexOrThrow(VersionData.DB.status)));
                    data.setCurrentVersion(cursor.getString(
                            cursor.getColumnIndexOrThrow(VersionData.DB.currentVersion)));
                    data.setTargetVersion(cursor.getString(
                            cursor.getColumnIndexOrThrow(VersionData.DB.targetVersion)));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (cursor != null)
                cursor.close();
        }

        return data;
    }
}
