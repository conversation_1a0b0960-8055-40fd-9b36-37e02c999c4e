package com.ainirobot.home.module;

import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.R;
import com.ainirobot.home.bean.PushMapBean;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.utils.MapUtils;
import com.ainirobot.home.utils.SystemUtils;
import com.ainirobot.home.utils.ToastUtil;
import com.google.gson.JsonSyntaxException;

import org.json.JSONException;
import org.json.JSONObject;

public class PushMapNeedSwitchModule extends BaseModule {
    private static final String TAG = PushMapNeedSwitchModule.class.getSimpleName();
    private static PushMapNeedSwitchModule mInstance = new PushMapNeedSwitchModule();

    public static PushMapNeedSwitchModule getInstance() {
        return mInstance;
    }

    private PushMapBean pushMapInfo;


    //params 参考 http://172.16.0.34/#api-RobotPush-MapPublish
    //{
    //    "map_url": "http://xxx",
    //    "map_name": "xxx",
    //    "map_uuid": "xxxx",
    //    "task_id": "task_id",
    //    "version": 123456
    //}

    //状态上报，产品设计和服务端要求，结果导致很乱，放在这里，方便后来人读懂。
//    0 --- 等待机器人拉取地图      -1  ---- 地图更新失败
//    1---  地图定位中
//    2 ---  切图定位成功（定位成功）
//    3 --- 仅需更新地点，无需切图，定位正常（定位成功）
//    4 --- 切图失败； 不是导航图不需要切图（需要切图定位）
//    5 --- 定位失败  （需要定位）
//    6 --- 仅需要更新地点，无需切图，本身无定位  （需要定位）
//    7 --- 需要切图，因为本身无定位，无需定位  （需要定位）


    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, final String params) {
        Log.i(TAG, "push_map handle semantics, intent: " + intent + ", params = " + params);
        switch (intent) {
            case Definition.REMOTE_PUSH_MAP_NEED_SWITCH:
                try {
                    if (pushMapInfo == null) {
                        pushMapInfo = mGson.fromJson(params, PushMapBean.class);
                        Log.d(TAG, "onNewSemantics: pushMapInfo = " + pushMapInfo.toString());
                    } else {
                        Log.d(TAG, "onNewSemantics: push map is Executing, do nothing");
                        return false;
                    }
                } catch (JsonSyntaxException e) {
                    e.printStackTrace();
                }

                SkillManager.getInstance().closeSpeechAsrRecognize();
                SystemApi.getInstance().resetHead(0, null);
                SystemUtils.setThreeFinger(false);
                SystemApi.getInstance().stopMove(reqId, null);
//                FloatDialogManager.getInstance().showPushMapDialog();
                UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_PUSH_MAP, null, null);
                if (pushMapInfo != null) {
                    int mapCompatibleVersion = Settings.Global.getInt(ApplicationWrapper.getContext().getContentResolver(),
                            Definition.ROBOT_MAP_COMPATIBLE_VERSION, 0);
                    Log.d(TAG, "mapCompatibleVersion : " + mapCompatibleVersion);
                    if (pushMapInfo.getMapVersion() <= mapCompatibleVersion) {
                        updateStatus(Definition.PUSH_MAP_WAIT_TO_PULL);
                        Log.d(TAG, "download map");
                        downLoadPkg();
                    } else {
                        Log.e(TAG, "Robot support version is smaller than map version");
                        ToastUtil.showToast(ApplicationWrapper.getContext(), ApplicationWrapper.getContext().getString(R.string.map_not_compatible));
                        stopAndUpdateStatus("", Definition.PUSH_MAP_DOWNLOAD_FAIL, false);
                    }
                } else {
                    stopAndUpdateStatus("", Definition.PUSH_MAP_DOWNLOAD_FAIL, false);
                }
                break;

            default:
                break;
        }
        return super.onNewSemantics(reqId, intent, text, params);
    }

    private void updateStatus(String state) {
        Log.d(TAG, "updateStatus: state = " + state);
        if (pushMapInfo == null) {
            Log.e(TAG, "updateStatus: error ," + "no Executing push map task");
            return;
        }
        pushMapInfo.setState(state);
        pushMapInfo.setNaviMap(true);
        SystemApi.getInstance().sendStatusReport(Definition.STATUS_PUSH_MAP_PROCESS, mGson.toJson(pushMapInfo));
    }

    private Pose curPose;

    private void downLoadPkg() {
        final String mapName = pushMapInfo.getMapName();
        MapUtils.downLoadWholeMapPkg(pushMapInfo, mGson, new CommandListener(){
            @Override
            public void onResult(int result, String message, String extraData) {
                Log.d(TAG, "downLoadPkg:downLoadMapPkg:onResult: result=" + result + ", message=" + message);
                if (message.equals(Definition.SUCCEED)) {
                    startRelocate(mapName);
                } else {
                    stopAndUpdateStatus(mapName, Definition.PUSH_MAP_DOWNLOAD_FAIL, false);
                }
            }
        });
    }

    private void stopAndUpdateStatus(String mapName, String pushMapDownloadFail, boolean b) {
        updateStatus(pushMapDownloadFail);
        showPushMapResultToast(b, mapName);
        stop();
    }

    private void startRelocate(final String mapName) {
        updateStatus(Definition.PUSH_MAP_WAIT_TO_RELOCATE);
        SystemApi.getInstance().isRobotEstimate(0, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, "isRobotEstimate onResult: " + message);
                if (result == Definition.RESULT_OK && "true".equals(message)) {
                    SystemApi.getInstance().getPosition(0, new CommandListener() {
                        @Override
                        public void onResult(int result, String message) {
                            Log.d(TAG, "getPosition message " + message);
                            if (result == Definition.RESULT_OK && !TextUtils.isEmpty(message)) {
                                JSONObject object = null;
                                try {
                                    object = new JSONObject(message);
                                    float px = (float) object.optDouble("px");
                                    float py = (float) object.optDouble("py");
                                    float theta = (float) object.optDouble("theta");
                                    curPose = new Pose();
                                    curPose.setX(px);
                                    curPose.setY(py);
                                    curPose.setTheta(theta);
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                    Log.d(TAG, "onResult: " + "pose parse fail");
                                }
                            }
                            switchMapProcess(mapName);
                        }
                    });

                } else {
                    switchMapProcess(mapName);
                }
            }
        });
    }

    private void switchMapProcess(final String mapName) {
        SystemApi.getInstance().switchMap(0, mapName, new CommandListener() {
            @Override
            public void onResult(int result, String msg) {
                if (msg.equals(Definition.SUCCEED)) {

                    SystemApi.getInstance().uploadSwitchInfo(0, mapName, new CommandListener() {
                        @Override
                        public void onResult(int result, String message) {
                            Log.d(TAG, "uploadSwitchInfo onResult: " + message);
                            if (!TextUtils.isEmpty(message) &&
                                    message.equals(Definition.SUCCEED)) {

                            } else {

                            }
                        }
                    });


                    if (curPose != null) {
                        Log.d(TAG, "setForceEstimate: " + curPose.toString());
                        String poseJson = mGson.toJson(curPose);
                        curPose = null;
                        SystemApi.getInstance().setForceEstimate(0, poseJson, new CommandListener() {
                            @Override
                            public void onResult(int result, String message) {
                                Log.d(TAG, "setForceEstimate onResult: " + message);
                                if (result == Definition.RESULT_OK &&
                                        !TextUtils.isEmpty(message) && message.equals(Definition.SUCCEED)) {
                                    stopAndUpdateStatus(mapName, Definition.PUSH_MAP_SWITCH_AND_RELOCATE_SUC, true);
                                } else {
                                    stopAndUpdateStatus(mapName, Definition.PUSH_MAP_RELOCATE_FAIL, true);
                                }
                            }
                        });
                    } else {
                        stopAndUpdateStatus(mapName, Definition.PUSH_MAP_NEED_SWITCH_NO_RELOCATE, true);
                    }
                } else {
                    updateStatus(Definition.PUSH_MAP_SWITCH_FAIL_OR_NO_SWITCH);
                    showPushMapResultToast(true, mapName);
                    SystemApi.getInstance().uploadSwitchInfo(0, mapName, new CommandListener() {
                        @Override
                        public void onResult(int result, String message) {
                            Log.d(TAG, "uploadSwitchInfo onResult: " + message);
                            stop();
                        }
                    });
                }
            }
        });
    }

    private void showPushMapResultToast(final boolean isSuc, final String mapName){
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                if (isSuc) {
                    Toast.makeText(ApplicationWrapper.getContext(),
                            ApplicationWrapper.getContext().getString(R.string.push_map_success)
                            , Toast.LENGTH_LONG).show();
                    Log.d(TAG, "showPushMapResultToast: pushMapIntent");
                    Intent pushMapIntent = new Intent();
                    pushMapIntent.setAction("status_pushed_map_success");
                    pushMapIntent.putExtra("mapName", mapName);
                    ApplicationWrapper.getContext().sendBroadcast(pushMapIntent);
                } else {
                    Toast.makeText(ApplicationWrapper.getContext(),
                            ApplicationWrapper.getContext().getString(R.string.push_map_fail)
                            , Toast.LENGTH_LONG).show();
                }
            }
        });
    }

    @Override
    protected void onStop() {
        Log.i(TAG, "on stop");
        pushMapInfo = null;
        curPose = null;
        SystemUtils.setThreeFinger(true);
        SkillManager.getInstance().openSpeechAsrRecognize();
//        FloatDialogManager.getInstance().removePushMapDialog();
        UIController.getInstance().moveToBack();
        SystemApi.getInstance().onPushMapNeedSwitchModuleFinish();
        super.onStop();
    }
}
