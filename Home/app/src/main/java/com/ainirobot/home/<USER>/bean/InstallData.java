package com.ainirobot.home.ota.bean;

import static com.ainirobot.home.ota.constants.OtaConstants.OS_AC_CLIENT;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_BMS;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_HOST;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_MOTOR_HORIZON;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_MOTOR_LEFT;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_MOTOR_RIGHT;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_MOTOR_VERTICAL;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_PSB;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_PSB_S;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_TK1;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_TX1;

public class InstallData {

    public enum TYPE {
        HOST("host"),
        CAN("can"),
        NAVI("navi"),
        HEAD("head");

        private String val;

        TYPE(String str) {
            this.val = str;
        }
    }

    private String mOsName;
    private TYPE type;

    public InstallData(String mOsName) {
        this.mOsName = mOsName;
        updateType();
    }

    public String getOsName() {
        return mOsName;
    }

    public TYPE getType() {
        return type;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof InstallData && mOsName != null) {
            InstallData data = (InstallData) obj;
            return (mOsName.equals(data.getOsName()));
        } else if (obj instanceof String && mOsName != null) {
            String data = (String) obj;
            return (mOsName.equals(data));
        }

        return super.equals(obj);
    }

    @Override
    public int hashCode() {
        if (mOsName == null) {
            return super.hashCode();
        }

        return mOsName.hashCode();
    }

    @Override
    public String toString() {
        return "InstallData {" +
                "mOsName='" + mOsName + '\'' +
                ", type='" + type + '\'' +
                '}';
    }

    private void updateType() {
        switch (mOsName) {
            case OS_HOST:
                this.type = TYPE.HOST;
                break;

            case OS_TK1:
            case OS_MOTOR_LEFT:
            case OS_MOTOR_RIGHT:
                this.type = TYPE.NAVI;
                break;

            case OS_PSB:
            case OS_MOTOR_HORIZON:
            case OS_MOTOR_VERTICAL:
            case OS_AC_CLIENT:
            case OS_BMS:
            case OS_PSB_S:
                this.type = TYPE.CAN;
                break;

            case OS_TX1:
                this.type = TYPE.HEAD;
                break;
        }
    }
}
