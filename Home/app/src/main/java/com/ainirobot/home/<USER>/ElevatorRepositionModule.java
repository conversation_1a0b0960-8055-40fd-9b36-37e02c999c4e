/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.home.module;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.ainirobot.coreservice.bean.MultiFloorInfo;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.coreservice.utils.ZipUtils;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.bi.BiRelocationFailReport;
import com.ainirobot.home.control.RadarManager;
import com.ainirobot.home.control.RadarManager.RadarListener;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.report.Constant;
import com.ainirobot.home.report.TaskReport;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.UIController.FRAGMENT_TYPE;
import com.ainirobot.home.utils.LocationUtil;
import com.ainirobot.home.utils.ResUtil;
import com.ainirobot.home.utils.ResetHeadUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 如果梯控开关开启,则使用当前Module的定位逻辑。
 * 包括梯控地图中的定位点定位和充电桩定位的
 */
public class ElevatorRepositionModule extends BaseModule {

    private static final String TAG = ElevatorRepositionModule.class.getSimpleName();
    private static final int CIRCLE_VISION_CNT = 9;
    private static final int INTERVAL_CIRCLE_TIME = 3 * 1000;
    private static final int ESTIMATE_POSE_CHARGE = 1;
    private static final int ESTIMATE_POSE_OTHER = 2;

    private static ElevatorRepositionModule sInstance;
    private Context mContext = ApplicationWrapper.getContext();
    private Gson mGson = new Gson();
    protected Handler mHandler = null;
    private RepositionState mRepositeState = RepositionState.IDLE;
    private int mReqId = 0;
    private ArrayList<Pose> mPoseList = new ArrayList<>();
    private Boolean mIsRepositing = false;
    private AtomicInteger mVisionLocateRetry = new AtomicInteger(0);
    private boolean mIsVisionReposition = false;
    private boolean mIsChargePileReposition = false;
    private Object mStateLock = new Object();
    private Pose mChargingPilePose = null;
    /**
     * 当机器人弹出手动重定位界面后，是否有人手动操作机器
     */
    private volatile boolean isMenualClick = false;
    private ArrayList<MultiFloorInfo> mFloorList = new ArrayList<>();
    private ResetHeadUtils mResetHeadUtils;

    /**
     * 重定位流程  加载地图-->视觉定位首次尝试-->转圈尝试-->选择重试模式
     */
    private enum RepositionState {
        IDLE,
        CHECK_ESTIMATE,
        SWITCH_MAP_START,
        SWITCH_MAP_SUCCESS,
        CHARGING_CHECK,
        CHARGE_LOCATE_START,
        CHARGE_LOCATING,
        CHARGE_LOCATE_END,
        END
    }

    private ElevatorRepositionModule() {
    }

    public static ElevatorRepositionModule getInstance() {
        if (null == sInstance) {
            sInstance = new ElevatorRepositionModule();
        }
        return sInstance;
    }

    public void init(Context context) {
        this.mContext = context;
        if (mHandler == null) {
            initHandler();
        }
        mResetHeadUtils = new ResetHeadUtils(TAG, mHandler);
    }

    /**
     * Module类的内部使用
     */
    private void initHandler() {
        this.mHandler = new Handler(Looper.myLooper()) {
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);
                final Bundle bundle = msg.getData();
                Log.d(TAG, "Reposition========msg:" + msg.what);
                switch (msg.what) {
                    case ModuleDef.MSG_REPOSITION_SUCCESS_TIMER:
                        stop();
                        break;
                    default:
                        break;
                }
            }

        };
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.i(TAG, "reqId=" + reqId + "; intent=" + intent + "; params=" + params + " " + getState());
        switch (intent) {
            case Definition.REQ_SPEECH_WAKEUP:
                SystemApi.getInstance().finishModuleParser(reqId, false);
                break;

            case Definition.REQ_REPOSITION:
                analyseParam(reqId, params);
                queryFloorList();
//                checkEstimateStatus(reqId, params);
                break;
            case ModuleDef.REQ_STOP:
                TaskReport.getInstance().setTaskResult(Constant.TaskEvent.task_cancel);
//                if (mRepositeState == RepositionState.MENUAL_END) {
//                    SystemApi.getInstance().finishModuleParser(reqId, false);
//                    return true;
//                }
                updateRepositionState(RepositionState.END);
                stop();
                break;
            default:
                break;
        }

        return super.onNewSemantics(reqId, intent, text, params);
    }

    /**
     * 先选择当前楼层
     */
    private void queryFloorList() {
        updateRepositionState(RepositionState.SWITCH_MAP_START);
        SystemApi.getInstance().queryMultiFloorConfig(0, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                Log.d(TAG, "queryMultiFloorConfig onResult msg : " + message + ", extraData : " + extraData);
                if (Definition.RESULT_OK == result && !TextUtils.isEmpty(message)) {

                    TypeToken<ArrayList<MultiFloorInfo>> token = new TypeToken<ArrayList<MultiFloorInfo>>() {
                    };
                    mFloorList = mGson.fromJson(message, token.getType());
                    if (mFloorList == null || mFloorList.size() == 0) {
                        showToast(ResUtil.getString(R.string.elevator_reposition_no_navi_map));
                        stop();
                        return;
                    }
                    showUi(message);

                } else {
                    showToast(ResUtil.getString(R.string.elevator_reposition_no_navi_map));
                    stop();
                }
            }
        });
    }

    private void showUi(final String floorInfoList) {
        SystemApi.getInstance().getMapName(mReqId, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                Log.d(TAG, "getMapName onResult message : " + message + ", extraData : " + extraData);
                if (mRepositeState != RepositionState.SWITCH_MAP_START){
                    Log.d(TAG, "getMapName mRepositeState : "+ mRepositeState);
                    return;
                }
                if(!ProductInfo.isAlnilamPro()){
                    if (!TextUtils.isEmpty(message)){
                        if (!isMapInFloorList(message)){
                            Log.d(TAG, "Current map is not in FloorList, Stop ElevatorRepositionModule.");
                            showToast(ResUtil.getString(R.string.elevator_reposition_not_find_locate_map));
                            stop();
                            return;
                        }
                    }
                }
                Bundle bundle = new Bundle();
                bundle.putString(ModuleDef.FLOOR_LIST, floorInfoList);
                if(ProductInfo.isAlnilamPro()){
                    bundle.putBoolean(ModuleDef.IS_MAP_IN_FLOOR_LIST, isMapInFloorList(message));
                }
                bundle.putString(ModuleDef.CURRENT_MAP_NAME, message);
                UIController.getInstance().showFragment(FRAGMENT_TYPE.FRAGMENT_REPOSITION, bundle, null);
            }
        });
    }

    /**
     * 当前用作导航的地图是否在多楼层地图列表中
     * @param mapName
     * @return
     */
    private boolean isMapInFloorList(String mapName) {
        for (MultiFloorInfo info : mFloorList){
            if (info.getMapName().equals(mapName)){
                return true;
            }
        }
        return false;
    }

    private void analyseParam(int reqId, String params) {
//        LocationUtil.getInstance().reloadChargePilePose();
        SkillManager.getInstance().closeSpeechAsrRecognize();
        if (mRepositeState != RepositionState.IDLE) {
            finishInviladAction(reqId);
            return;
        }
        mReqId = reqId;
        if (!TextUtils.isEmpty(params)) {
            try {
                JSONObject jsonObject = new JSONObject(params);
                mIsVisionReposition = jsonObject.getBoolean(Definition.REPOSITION_VISION);
            } catch (JSONException e) {
                e.printStackTrace();
                mIsVisionReposition = false;
            }
        } else {
            mIsVisionReposition = false;
        }
    }


    @Override
    public void onMessageFromLocal(int type) {
        Log.d(TAG, "onMessageFromLocal ========type:" + type);
        Log.d(TAG, "mRepositeState: " + mRepositeState);
        switch (type) {

            case ModuleDef.LOCAL_MESSAGE_CHARGE_START:
                if (mRepositeState == RepositionState.CHARGING_CHECK) {
                    UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.ELEVATOR_CHARGE_PILE_LOCATE_START, true + "");
                }
                break;
            case ModuleDef.LOCAL_MESSAGE_CHARGE_END:
                if (mRepositeState == RepositionState.CHARGING_CHECK
                    || mRepositeState == RepositionState.CHARGE_LOCATE_START) {
                    UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.ELEVATOR_CHARGE_PILE_LOCATE_START, false + "");
                    if (mRepositeState == RepositionState.CHARGE_LOCATE_START) {
                        updateRepositionState(RepositionState.CHARGING_CHECK); // 第一次定位失败， 重新打开定位页面会，需重置重定位状态
                    }
                }
                break;

            case ModuleDef.LOCAL_MESSAGE_REPOSITION_CANCEL:
                TaskReport.getInstance().setTaskResult(Constant.TaskEvent.task_cancel);
                stop();
                break;
            case ModuleDef.LOCAL_MESSAGE_ELEVATOR_BACK_TO_CHOOSE_FLOOR:
                updateRepositionState(RepositionState.SWITCH_MAP_START);
                break;
            default:
                break;
        }
    }

    @Override
    public void onMessageFromLocal(int type, Object param) {
        Log.d(TAG, "onMessageFromLocal ========type:" + type + ", param :" + param);
        switch (type) {
            case ModuleDef.LOCAL_MESSAGE_ELEVATOR_CONFIRM_FLOOR:
                if (param instanceof MultiFloorInfo) {
                    handleSwitchMapMessage((MultiFloorInfo) param, false);
                } else {
                    showSwitchMapResultToast(false);
                }
                break;

            case ModuleDef.LOCAL_MESSAGE_ELEVATOR_POINT_LOCATE:
                if (mRepositeState != RepositionState.SWITCH_MAP_SUCCESS) {
                    return;
                }
                if (param instanceof Integer) {
                    int poseIndex = (int) param;
                    Pose pose = mPoseList.get(poseIndex);
                    if (pose != null) {
                        Log.d(TAG, "start point locate : " + pose.toString());
                        checkResetHeadState(pose);
                    }
                }
                break;

            case ModuleDef.LOCAL_MESSAGE_ELEVATOR_CHARGE_LOCATE:
                if (param instanceof MultiFloorInfo) {
                    handleSwitchMapMessage((MultiFloorInfo) param, true);
                } else {
                    showSwitchMapResultToast(false);
                }
                break;
            case ModuleDef.LOCAL_MESSAGE_ELEVATOR_CHARGE_LOCATING:
                mRepositeState = RepositionState.CHARGE_LOCATE_START;
                queryChargePilePose();
                break;
            default:
                break;
        }
    }

    private void checkResetHeadState(Pose pose) {
        mResetHeadUtils.checkResetHeadState(mReqId, new ResetHeadUtils.ResetHeadListener() {
            @Override
            public void onResetHeadSuccess() {
                startFixLocate(pose);
            }

            @Override
            public void onResetHeadFailed() {
                Log.d(TAG, "onResetHeadFailed");
                speakResultTTS(ResUtil.getString(R.string.reset_head_failed));
            }
        });
    }


    private void handleSwitchMapMessage(MultiFloorInfo floorInfo, boolean isChargePileLocate) {
        if (floorInfo != null) {
            mIsChargePileReposition = isChargePileLocate;
            switchMapProcess(floorInfo);
        } else {
            // TODO 对应楼层地图切换失败
            showSwitchMapResultToast(false);
        }
    }

    /**
     * 通过当前楼层值， 获取对应的MultiFloorInfo
     * @return
     */
    private List<Pose> queryPoseListByMapId(int floorIndex, ArrayList<MultiFloorInfo> floorList) {
        for (MultiFloorInfo bean : floorList) {
            if (bean.getFloorIndex() == floorIndex) {
                return bean.getPoseList();
            }
        }
        return new ArrayList<>();
    }

    private void switchMapProcess(@NonNull final MultiFloorInfo floorInfo) {
        String mapName = floorInfo.getMapName();
        Log.d(TAG, "switchMapProcess mapName : " + mapName);
        SystemApi.getInstance().switchMap(0, mapName, new CommandListener() {
            @Override
            public void onResult(int result, String msg) {
                if (Definition.SUCCEED.equals(msg)) {
                    Log.d(TAG, "switchMapProcess succeed : "+ mIsChargePileReposition);
                    getSpecialPoseList(floorInfo, mIsChargePileReposition);
                    showSwitchMapResultToast(true);
                } else {
                    showSwitchMapResultToast(false);
                }
            }
        });
    }

    private void getSpecialPoseList(final MultiFloorInfo curFloorInfo, final boolean mIsChargePileReposition) {
        Log.d(TAG, "getSpecialPoseList: ");
        SystemApi.getInstance().getMultiFloorConfigAndPose(mReqId, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                Log.d(TAG, "onResult message: " + message);
                if (Definition.RESULT_OK == result && !TextUtils.isEmpty(message)) {
                    TypeToken<ArrayList<MultiFloorInfo>> token = new TypeToken<ArrayList<MultiFloorInfo>>() {
                    };
                    message = ZipUtils.unzipMapData(mGson, message);
                    ArrayList<MultiFloorInfo> floorList = mGson.fromJson(message, token.getType());
                    if (floorList == null || floorList.size() == 0) {
                        showToast(ResUtil.getString(R.string.elevator_reposition_not_find_locate_points));
                        return;
                    }
                    List<Pose> poseList = queryPoseListByMapId(curFloorInfo.getFloorIndex(), floorList);
                    mPoseList.clear();
                    mPoseList.addAll(poseList);
                    Log.d(TAG, "getSpecialPoseList mPoseList : " + mPoseList.toString());
                    if (mIsChargePileReposition){
                        //更新fragment使用原有的充电桩页面定位
                        updateRepositionState(RepositionState.CHARGING_CHECK);
                        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.ELEVATOR_CHARGE_PILE_LOCATE_START, "false");
                    } else {
                        // 更新fragment状态和特殊点位列表
                        updateRepositionState(RepositionState.SWITCH_MAP_SUCCESS);
                        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.ELEVATOR_SWITCH_MAP_SUCCEED,
                                mGson.toJson(mPoseList, new TypeToken<ArrayList<Pose>>() {
                                }.getType()));
                    }
                } else {
                    showToast(ResUtil.getString(R.string.elevator_reposition_not_find_locate_points));
                }

            }

            @Override
            public void onError(int errorCode, String errorString, String extraData) throws RemoteException {
                super.onError(errorCode, errorString, extraData);
                Log.d(TAG, "onError errorCode : " + errorCode + ",errorString :" + errorString + ", extraData : " + extraData);
                showToast(ResUtil.getString(R.string.elevator_reposition_not_find_locate_points));
            }

        });
    }

    private void startFixLocate(Pose pose) {
        if (mRepositeState != RepositionState.SWITCH_MAP_SUCCESS) {
            Log.d(TAG, "mRepositeState is not SWITCH_MAP_SUCCESS");
            return;
        }
        Log.d(TAG, "startFixLocate curState : " + mRepositeState + ", pose : " + pose.toString());
        SystemApi.getInstance().setFixedEstimate(mReqId, mGson.toJson(pose), new LocationCommandListener(mReqId, ESTIMATE_POSE_OTHER));
    }

    private void showToast(final String text) {
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                Toast.makeText(ApplicationWrapper.getContext(),
                        text,
                        Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void showSwitchMapResultToast(final boolean isSuc) {
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                if (isSuc) {
                    Toast.makeText(ApplicationWrapper.getContext(),
                            ApplicationWrapper.getContext().getString(R.string.elevator_reposition_switch_map_success)
                            , Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(ApplicationWrapper.getContext(),
                            ApplicationWrapper.getContext().getString(R.string.elevator_reposition_switch_map_fail)
                            , Toast.LENGTH_SHORT).show();
                }
            }
        });
    }

    private void showLocateResultToast(final boolean isSuccess) {
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                if (isSuccess) {
                    Toast.makeText(ApplicationWrapper.getContext(),
                            ApplicationWrapper.getContext().getString(R.string.elevator_reposition_success)
                            , Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(ApplicationWrapper.getContext(),
                            ApplicationWrapper.getContext().getString(R.string.elevator_reposition_fail)
                            , Toast.LENGTH_SHORT).show();
                }
            }
        });
    }


    class LocationCommandListener extends CommandListener {

        int reqId;
        int mode;

        LocationCommandListener(int reqId, int mode) {
            this.reqId = reqId;
            this.mode = mode;
        }

        @Override
        public void onResult(int result, String message) {
            Log.d(TAG, "LocationCommandListener result: " + result + " , message = " + message + " , isRunning: " + isRunning());
            if (!isRunning()) {
                return;
            }

            if (result == Definition.RESULT_OK &&
                    !TextUtils.isEmpty(message) && message.equals("succeed")) {
                repositionSuccess(false, "");
            } else {
                repositionFail(message);
            }
        }
    }

    private void repositionFail(String message) {
        speakResultTTS(mContext.getString(R.string.elevator_reposition_fail));
        showLocateResultToast(false);
    }


    private void updateRepositionState(RepositionState newState) {
        synchronized (mStateLock) {
            Log.d(TAG, "oldstate:" + mRepositeState + ",newstate:" + newState);
            mRepositeState = newState;
        }
    }

    private RepositionState getReceptionState() {
        synchronized (mStateLock) {
            return mRepositeState;
        }
    }

    private Pose queryChargePilePose() {
        SystemApi.getInstance().getLocation(0,Definition.CHARGING_POLE_TYPE,new CommandListener(){
            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                Log.i(TAG, "Get charging pile result:" + result + " message:" + message + "  " + isRunning());
                if (!isRunning()) {
                    return;
                }
                try {
                    JSONObject jsonObject = new JSONObject(message);
                    if (null == mChargingPilePose) {
                        mChargingPilePose = new Pose();
                    }
                    boolean state = jsonObject.optBoolean(Definition.JSON_NAVI_SITE_EXIST, false);
                    if (state) {
                        float x = (float) jsonObject.getDouble(Definition.JSON_NAVI_POSITION_X);
                        float y = (float) jsonObject.getDouble(Definition.JSON_NAVI_POSITION_Y);
                        float theta = (float) jsonObject.getDouble(Definition.JSON_NAVI_POSITION_THETA);
                        mChargingPilePose.setX(x);
                        mChargingPilePose.setY(y);
                        mChargingPilePose.setTheta(theta);
                    }
                    chargePileLocating(mChargingPilePose);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onError(int errorCode, String errorString, String extraData) throws RemoteException {
                super.onError(errorCode, errorString, extraData);
            }
        });
        return null;
    }


    private void chargePileLocating(final Pose chargingPilePose) {
        if (!isRunning() || chargingPilePose == null) {
            Log.d(TAG, "not running || mchargingPose is null");
            TaskReport.getInstance().setTaskResult(Constant.TaskEvent.task_fail);
            speakResultTTS(ResUtil.getString(R.string.elevator_reposition_charge_pile_point_not_exist));
            relocationFailed(ResUtil.getString(R.string.elevator_reposition_charge_pile_point_not_exist));
            return;
        }

        RadarManager.openRadar(new RadarListener() {
            @Override
            public boolean onSucceed() {
//                clearRepositionFailReason();
                startGetChargingPosition(chargingPilePose);
                return true;
            }
        });
    }


    private void startGetChargingPosition(Pose chargePilePose) {
        Log.i(TAG, "Start get charging pile mRepositeState:" + mRepositeState);
        setFixedEstimate(chargePilePose, 1);
    }

    private void setFixedEstimate(final Pose pose, final int retryCount) {

        Log.i(TAG, "retry resetFixedEstimate count:" + retryCount);
        SystemApi.getInstance().setFixedEstimate(0, mGson.toJson(pose), new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, "pose estimate:result=" + result + ",message=" + message + "  " + isRunning());
                if (!isRunning()) {
                    return;
                }

                super.onResult(result, message);
                mIsRepositing = false;
                switch (result) {
                    case Definition.RESULT_OK:
                        if ("succeed".equals(message)) {
                            mRepositeState = RepositionState.CHARGE_LOCATE_END;
                            String poseName = "";
                            if (TextUtils.isEmpty(pose.getName())){
                                Pose chargePilePose = SystemApi.getInstance().getSpecialPose(Definition.START_CHARGE_PILE_POSE);
                                poseName = chargePilePose.getName();
                            }
                            Log.d(TAG, "poseName : "+ poseName);
                            repositionSuccess(false, poseName);
                        } else {
                            if (retryCount > 2) {
                                relocationFailed(message);
                                return;
                            }
                            setFixedEstimate(pose, retryCount + 1);
                        }
                        break;
                    case Definition.RESULT_FAILURE:
                    case Definition.RESULT_STOP:
                        if (retryCount > 2) {
                            relocationFailed(message);
                            return;
                        }
                        setFixedEstimate(pose, retryCount + 1);
                        break;
                    default:
                        break;
                }
            }
        });

    }

    private void finishInviladAction(int reqId) {
        Log.d(TAG, "finish invilad action id:" + reqId);
        SystemApi.getInstance().finishModuleParser(reqId, true);
    }

    private void relocationFailed(String msg) {
        Log.d(TAG, "Reposition failed");
        naviCmdTimeOutReport();
        speakResultTTS(mContext.getString(R.string.reposition_reset_failed));
        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.ELEVATOR_CHARGE_PILE_REPOSITION_FAIL, msg);
    }

    private void clearRepositionFailReason() {
        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.REPOSITION_CLEAR_REASON, "");
    }

    private void speakResultTTS(String playTTS) {
        Log.i(TAG, "speakResultTTS:" + playTTS);
        SkillManager.getInstance().speechPlayText(playTTS);
    }

    private void readyForExit(final String playTTS) {
        Log.i(TAG, "Speech for exit :" + playTTS);
        try {
            SkillManager.getInstance().speechPlayText(playTTS, new TextListener() {
                @Override
                public void onStop() {
                    Log.i(TAG, "Speech on stop : " + playTTS);
                }

                @Override
                public void onError() {
                    Log.i(TAG, "Speech on error : " + playTTS);
                }

                @Override
                public void onComplete() {
                    Log.i(TAG, "Speech on complete : " + playTTS);
                }
            });
        } catch (RemoteException e) {
            Log.i(TAG, "Speech exception : " + playTTS);
            e.printStackTrace();
        }
        TaskReport.getInstance().setTaskResult(Constant.TaskEvent.task_fail);
        stop();
    }

    private void skipReposition() {
        Log.i(TAG, "skip reposition");
        TaskReport.getInstance().setTaskResult(Constant.TaskEvent.task_cancel);
        stop();
    }


    private void repositionSuccess(boolean isByVision, String name) {
        Log.i(TAG, "ElevatorReposition success isByVision:" + isByVision + ", poseName : " + name);
        if (isByVision) {
            UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.REPOSITION_VISION_SUCCESS, "");
            speakResultTTS(mContext.getString(R.string.reposition_vision_relocate_success));
        } else {
            UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.ELEVATOR_REPOSITION_SUCCEED, name);
            speakResultTTS(mContext.getString(R.string.elevator_reposition_success));
        }


        Message msg = Message.obtain();
        msg.what = ModuleDef.MSG_REPOSITION_SUCCESS_TIMER;
        if (mHandler != null) {
            mHandler.sendMessageDelayed(msg, 2000);
        }
    }

    /**
     * 产品定义埋点
     * 当机器人弹出请把我推到充电桩上的界面时表示机器人进入手动重定位流程
     * 如果进入手动重定位流程60秒无交互，则上报定位失败埋点，表明机器人处于无人操作中（大概率是定位漂移后进入的重定位）
     * 有人操作的情况下不上报此埋点
     */
    private void delayReportRelocationFailureMsg() {
        Log.i(TAG, "delayReportRelocationFailureMsg");
        isMenualClick = false;
        Message msg = Message.obtain();
        msg.what = ModuleDef.MSG_REPOSITION_SHOW_MENUAL;
        if (mHandler != null) {
            mHandler.sendMessageDelayed(msg, 60 * 1000);
        }
    }

    private void removeRelocationFailureMsg() {
        isMenualClick = true;
        if (mHandler != null) {
            mHandler.removeMessages(ModuleDef.MSG_REPOSITION_SHOW_MENUAL);
        }
    }

    private void startReportRelocationFailure() {
        if (isMenualClick) {
            return;
        }
        BiRelocationFailReport failReport = new BiRelocationFailReport();
        failReport.addRelocationFailure(1)
                .addStatusCode(BiRelocationFailReport.STATUS_CODE_RELOCATION_FAIL)
                .report();
    }

    public Handler getRepositionHandler() {
        return mHandler;
    }

    private void naviCmdTimeOutReport() {
        Log.d(TAG, "nviCmdTimeOutReport");
        long timestamp = System.currentTimeMillis();
        String cacheId = RobotSettings.getSystemSn() + "-" + timestamp;
        SystemApi.getInstance().naviTimeOutCmdReport(0, timestamp, cacheId,
                Definition.TYPE_ACTION_CHARGE_PILE_ESTIMATE_FAILUE, "ChargePileEstimateFail", new CommandListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.i(TAG, "nviCmdTimeOutReport: " + result + " message:" + message);
                    }
                });
    }

    private String getSpeckResultTts() {
        return mContext.getString(LocationUtil.getInstance().isChargingTypeWire() ?
                R.string.reposition_reset_remind_msg_locate :
                R.string.reposition_reset_remind_msg);
    }

    private String getLocationErrorTts() {
        return mContext.getString(LocationUtil.getInstance().isChargingTypeWire() ?
                R.string.reposition_noset_locate_position :
                R.string.reposition_noset_charge_position);
    }

    @Override
    protected void onStop() {
        Log.i(TAG, "onStop: exit ElevatorRepositionModule notify ");
        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.REPOSITION_EXIT, "");
        updateRepositionState(RepositionState.IDLE);
        SkillManager.getInstance().openSpeechAsrRecognize();
        Log.d(TAG, "onStop: mRepositeState " + mRepositeState);
        SystemApi.getInstance().onRepositionFinished();
        release(mReqId, RESULT_OK, null);
    }

    @Override
    protected void release(int reqId, int result, Bundle data) {
        Log.i(TAG, "release reqId:" + reqId);
        updateRepositionState(RepositionState.IDLE);
        mChargingPilePose = null;
        mPoseList.clear();
        if (mHandler != null) {
            mHandler.removeMessages(ModuleDef.MSG_REPOSITION_SUCCESS_TIMER);
            mHandler.removeMessages(ModuleDef.MSG_REPOSITION_CIRCLE_TIMER);
            mHandler.removeMessages(ModuleDef.MSG_REPOSITION_SHOW_MENUAL);
        }
        mIsRepositing = false;
        isMenualClick = false;
        SystemApi.getInstance().finishModuleParser(reqId, RESULT_FAILURE != result);
        super.release(reqId, result, data);
    }

}
