package com.ainirobot.home.control;

import android.app.ActivityManager;
import android.content.Context;
import android.os.Debug;
import android.util.Log;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.bi.BiRunningErrorReport;
import com.ainirobot.home.utils.LocationUtil;
import com.ainirobot.home.utils.SystemHookUtils;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Timer;
import java.util.TimerTask;

/**
 * 导航内存泄漏后处理逻辑
 */
public class LxcMemoryManager {
    private static final String TAG = "LxcMemoryManager";

    private volatile static LxcMemoryManager mInstance;
    private static final String PROCESS_NAME_REMOTE="exe_chassis_remote";
    private static final String PROCESS_NAME_SERVICES="exe_chassis_service";
    private static final String CMD_DUMPSYS_MEM = "dumpsys meminfo";
    private static final String CMD_PS_PRO_1 = "ps -ef | grep ";
    private static final String CMD_PS_PRO_2 = " | grep -v grep | awk '{print $2}' ";
    private Timer mMemTimer;
    private Timer mCheckLxcTimer;
    private int mTimerNum = 10 * 60 * 1000;//每10分钟判断一次
    private double maxMemTotal = 2.5 * 1024 * 1024;//最大内存2.5G

    public static LxcMemoryManager getInstance() {
        if (mInstance == null) {
            synchronized (LxcMemoryManager.class) {
                if (mInstance == null) {
                    mInstance = new LxcMemoryManager();
                }
            }
        }
        return mInstance;
    }
    private LxcMemoryManager(){}

    /**
     * 开始内存检测
     */
    public void startCheckTimer(){
        Log.v(TAG,"startCheckTimer");
        cancelMemTimer();
        mMemTimer=new Timer();
        mMemTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                checkMem();
            }
        },5000,mTimerNum);
    }

    /**
     * 结束内存检测
     */
    public void cancelMemTimer() {
        Log.v(TAG,"cancelMemTimer");
        if (mMemTimer != null) {
            mMemTimer.cancel();
            mMemTimer=null;
        }
    }

    /**
     * 内存检测
     */
    public void checkMem(){
        int lxc_pid = this.getProcessIdByName(PROCESS_NAME_REMOTE);
        Log.v(TAG,"checkMem pid: "+ lxc_pid);
        if(lxc_pid != -1){
            int mexTotal = this.getMemoryInfoByPid(lxc_pid);
            if(mexTotal > maxMemTotal){
                LocationUtil.getInstance().startRecordPose();
                stopOrionlxc();
                new BiRunningErrorReport().addError(BiRunningErrorReport.NAVIGATION_TYPE, "Lxc内存泄露").report();
            }
        }
    }

    /**
     * 根据名称获取进程号
     *  ps -ef | grep exe_chassis_remote | grep -v grep | awk '{print $2}'
     * 此方法获取内容为空
     * @return
     */
    private int getProcessIdByName(String name){
        BufferedReader reader = null;
        InputStreamReader streamReader = null;
        InputStream inputStream = null;
        try{
            String cmdStr = CMD_PS_PRO_1 + name + CMD_PS_PRO_2;
            Log.d(TAG, "getProcessIdByName  cmd: " + cmdStr);
            Process process = Runtime.getRuntime().exec(new String[]{"/bin/sh", "-c", cmdStr});
            int wn = process.waitFor();
            Log.d(TAG, "getProcessIdByName  wait: " + wn);
            inputStream = process.getInputStream();
            streamReader = new InputStreamReader(inputStream);
            reader = new BufferedReader(streamReader);
            String line  = reader.readLine();
            Log.d(TAG, "getProcessIdByName  pid: " + line);
            if (null != line && line.length() > 0) {
                return Integer.parseInt(line);
            }
        } catch (Throwable e) {
            Log.e(TAG, "getProcessIdByName Exception" + e.getMessage());
        } finally {
            closeStream(reader, inputStream, streamReader);
        }
        return -1;
    }

//    /**
//     * 获取exe_chassis_remote进程号
//     * @return
//     */
//    private int getProcessIdByName(String name){
//        BufferedReader reader = null;
//        InputStreamReader streamReader = null;
//        InputStream inputStream = null;
//        try{
//            Process process = Runtime.getRuntime().exec(CMD_DUMPSYS_MEM);
//            inputStream = process.getInputStream();
//            streamReader = new InputStreamReader(inputStream);
//            reader = new BufferedReader(streamReader);
//
//            for (String line = reader.readLine(); line!=null; line = reader.readLine()){
////                Log.d(TAG, "getProcessIdByName  processInfo: " + line);
//                if (line.contains(PROCESS_NAME_REMOTE) && line.contains("pid")) {
//                    String[] temp_Pro = line.split(":");
//                    String memtemp = temp_Pro[0].trim();
//                    String pid = temp_Pro[1].replace(PROCESS_NAME_REMOTE+" (pid ", "").replace(")","").trim();
//                    Log.d(TAG, "getProcessIdByName :  mem: " +memtemp+ "  pid:" + pid);
//                    return Integer.parseInt(pid);
//                }
//            }
//        } catch (Throwable e) {
//            Log.e(TAG, "getProcessIdByName Exception" + e.getMessage());
//        } finally {
//            closeStream(reader, inputStream, streamReader);
//        }
//        return -1;
//    }

    //    /**
//     * 获取exe_chassis_remote进程号
//     * 此方法获取不到获取exe_chassis_remote进程信息
//     * @return
//     */
//    public int getProcessIdByName() {
//        ActivityManager mActivityManager = (ActivityManager) ApplicationWrapper.getContext().getSystemService(Context.ACTIVITY_SERVICE);
//        for (ActivityManager.RunningAppProcessInfo appProcess : mActivityManager
//                .getRunningAppProcesses()) {
//            Log.v(TAG,"getProcessIdByName : "+ appProcess.processName);
//
//            if (appProcess.processName.equals(PROCESS_NAME_REMOTE)) {
//                return appProcess.pid;
//            }
//        }
//        return -1;
//    }


    private void closeStream(BufferedReader reader, InputStream inputStream, InputStreamReader streamReader) {
        try {
            if (reader != null) {
                reader.close();
            }
            if (inputStream != null) {
                inputStream.close();
            }
            if (streamReader != null) {
                streamReader.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据进程号获取内存信息
     * @param pid
     */
    public int getMemoryInfoByPid(int pid){
        ActivityManager mActivityManager = (ActivityManager) ApplicationWrapper.getContext().getSystemService(Context.ACTIVITY_SERVICE);
        Debug.MemoryInfo[] memInfo = mActivityManager.getProcessMemoryInfo(new int[]{pid});
        if(memInfo.length > 0){
            Log.d(TAG,"getMemoryInfoByPid memTotal: "+ memInfo[0].getTotalPss());
            return memInfo[0].getTotalPss();
        }
        return -1;
    }

//    /**
//     * 判断lxc进程是否启动
//     */
//    public boolean checkIsExistProcess(){
//        BufferedReader reader = null;
//        InputStreamReader streamReader = null;
//        InputStream inputStream = null;
//        try{
//            Process process = Runtime.getRuntime().exec(CMD_DUMPSYS_MEM);
//            inputStream = process.getInputStream();
//            streamReader = new InputStreamReader(inputStream);
//            reader = new BufferedReader(streamReader);
//
//            for (String line = reader.readLine(); line!=null; line = reader.readLine()){
//                if (line.contains(PROCESS_NAME_REMOTE) || line.contains(PROCESS_NAME_SERVICES) ) {
//                    Log.d(TAG, "checkIsExistProcess : " + line);
//                    return true;
//                }
//            }
//        } catch (Throwable e) {
//            Log.e(TAG, "checkIsExistProcess Exception" + e.getMessage());
//        } finally {
//            closeStream(reader, inputStream, streamReader);
//        }
//        return false;
//    }


    /**
     * 判断lxc进程是否启动
     */
    public boolean checkIsExistProcess(){
        int lxc_remote_pid = this.getProcessIdByName(PROCESS_NAME_REMOTE);
        Log.v(TAG,"checkIsExistProcess "+PROCESS_NAME_REMOTE+" pid: "+ lxc_remote_pid);
        if(lxc_remote_pid != -1){
            return true;
        }
        int lxc_service_pid = this.getProcessIdByName(PROCESS_NAME_SERVICES);
        Log.v(TAG,"checkIsExistProcess "+PROCESS_NAME_SERVICES+" pid: "+ lxc_service_pid);
        if(lxc_service_pid != -1){
            return true;
        }
        return false;
    }

    /**
     * 判断lxc内存是否启动，未启动手动启动
     * 延迟判断 否则exe_chassis_service存在
     */
    public void checkLxcMem(){
        Log.d(TAG, " checkLxcMem");
        mCheckLxcTimer = new Timer();
        TimerTask timerTask = new TimerTask() {
            @Override
            public void run() {
                Log.d(TAG, " checkLxcMem  TimerTask");
                boolean re = LxcMemoryManager.getInstance().checkIsExistProcess();
                Log.d(TAG, "checkLxcMem : " + re);
                if(!re){
                    LxcMemoryManager.getInstance().statOrionlxc();
                }
            }
        };
        mCheckLxcTimer.schedule(timerTask, 5000);
    }

    public void clearCheckLxcTimer(){
        Log.d(TAG, " clearCheckLxcTimer");
        if (null != mCheckLxcTimer){
            mCheckLxcTimer.cancel();
        }
    }

    /**
     * stop orionlxc
     * 注意：stop orionlxc 命令未生效
     */
    public void stopOrionlxc() {
        Log.d(TAG, "stopOrionlxc begin");
        try{
            SystemHookUtils.setSystemProperties("vendor.sys.orion_lpm", "1");
        } catch (Throwable e) {
            Log.e(TAG, "stopOrionlxc Exception" + e.getMessage());
        }
        Log.d(TAG, "stopOrionlxc end");
    }

    /**
     * start orionlxc
     * 设置属性
     */
    public void statOrionlxc() {
        Log.d(TAG, "statOrionlxc");
        try{
            SystemHookUtils.setSystemProperties("vendor.sys.orion_lpm", "0");
        } catch (Throwable e) {
            Log.e(TAG, "statOrionlxc Exception" + e.getMessage());
        }

        Log.d(TAG, "statOrionlxc end");
    }



}
