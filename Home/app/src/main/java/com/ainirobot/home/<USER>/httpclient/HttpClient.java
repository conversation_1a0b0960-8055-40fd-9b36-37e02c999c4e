package com.ainirobot.home.ota.httpclient;

import android.util.Log;

public class HttpClient {
    private static final String TAG = HttpClient.class.getSimpleName();
    private long connectTimeout = 30000;
    private long readTimeout = 30000;
    private final Dispatcher mDispatcher;

    public HttpClient() {
        this.mDispatcher = new Dispatcher();
    }

    public void setConnectTimeout(long connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(long readTimeout) {
        this.readTimeout = readTimeout;
    }

    public long getConnectTimeout() {
        return connectTimeout;
    }

    public long getReadTimeout() {
        return readTimeout;
    }

    public Dispatcher getDispatcher(){
        return mDispatcher;
    }

    public void setMaxRequests(int maxRequests){
        mDispatcher.setMaxRequests(maxRequests);
    }

    public void execute(Request request){
        execute(request,null);
    }

    public void execute(Request request, RequestListener listener, boolean isPendingMsg) {
        NetworkRunnable runnable = new NetworkRunnable(this,request, listener);
        mDispatcher.submit(runnable, isPendingMsg);
    }

    public void execute(Request request, RequestListener listener){
        execute(request, listener, true);
    }

    public void cancel(Object tag){
        Log.d(TAG, "tag:" + tag);
        mDispatcher.cancel(tag);
    }

    public void onAvailable(boolean status) {
        mDispatcher.onAvailable(status);
    }
}

