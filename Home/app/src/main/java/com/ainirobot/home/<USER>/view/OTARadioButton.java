/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.home.ui.view;

import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;

import com.ainirobot.home.R;

@SuppressLint("AppCompatCustomView")
public class OTARadioButton extends View {
    private Paint mPaintStroke;
    private Paint mPaintCircle;
    private boolean mIsShowCircle;
    private OnCheckedChangeListener mCheckedChangeListener;
    private ObjectAnimator mAnimator ;


    public OTARadioButton(Context context) {
        super(context);
        init();
    }

    public OTARadioButton(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public OTARadioButton(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int widthSpecMode = MeasureSpec.getMode(widthMeasureSpec);
        int widthSpecSize = MeasureSpec.getSize(widthMeasureSpec);
        if(widthSpecMode == MeasureSpec.AT_MOST){
            widthSpecSize = 50;
        }
        int heightSpecMode = MeasureSpec.getMode(heightMeasureSpec);
        int heightSpecSize = MeasureSpec.getSize(heightMeasureSpec);
        if(heightSpecMode == MeasureSpec.AT_MOST){
            heightSpecSize = 50;
        }
        setMeasuredDimension(widthSpecSize,heightSpecSize);
    }

    private void alphaAnimator(){
        if(mAnimator!=null && mAnimator.isRunning()){
            mAnimator.cancel();
        }
        mAnimator.setDuration(170);
        mAnimator.start();
    }


    private void init() {
        mPaintStroke = new Paint();
        mPaintStroke.setStyle(Paint.Style.STROKE);
        mPaintStroke.setStrokeWidth(1);

        mPaintCircle = new Paint();
        mPaintCircle.setColor(getResources().getColor(R.color.ota_network_radio_circle));
        mPaintCircle.setStyle(Paint.Style.FILL);

        setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                mIsShowCircle = !mIsShowCircle;
                invalidate();
                if(mCheckedChangeListener!=null){
                    mCheckedChangeListener.onCheckedChanged(mIsShowCircle);
                }
                alphaAnimator();
            }
        });

        mAnimator = ObjectAnimator.ofFloat(OTARadioButton.this,"alpha",0,1);
    }

    public void setChecked(boolean checked){
        mIsShowCircle = checked;
        invalidate();
        alphaAnimator();
    }

    public boolean isChecked(){
        return mIsShowCircle;
    }

    @Override
    protected void onDraw(Canvas canvas) {

        if(mIsShowCircle){
            mPaintStroke.setColor(getResources().getColor(R.color.ota_network_radio_circle));
            canvas.drawCircle(50/2,50/2,20/2,mPaintCircle);
        }else{
            mPaintStroke.setColor(getResources().getColor(R.color.ota_network_radio_stoke));
        }
        canvas.drawCircle(50/2,50/2,50/2,mPaintStroke);
    }

    public void setOnCheckedChangeListener(OnCheckedChangeListener mCheckedChangeListener) {
        this.mCheckedChangeListener = mCheckedChangeListener;
    }

    public interface OnCheckedChangeListener {
        void onCheckedChanged(boolean isChecked);
    }
}
