package com.ainirobot.home.ui.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Movie;
import android.util.AttributeSet;
import android.view.View;

public class GifView extends View {

    private static final int PAUSE_AFTER_MOVIE = 2000;
    private Movie movie;
    private long mStart = 0;
    private int mDuration;
    private int mLeft;
    private int mTop;
    private float mScale;

    public GifView(Context context, AttributeSet attrs) {
        super(context, attrs);
        setLayerType(View.LAYER_TYPE_SOFTWARE, null);
    }

    public void setMovieResource(int movieResId) {
        movie = Movie.decodeStream(getResources().openRawResource(movieResId));
        mDuration = movie.duration();
        requestLayout();
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        if (movie == null) {
            return;
        }
        super.onLayout(changed, left, top, right, bottom);
        int movieWidth = movie.width();
        int movieHeight = movie.height();
        int layoutWidth = right - left;
        int layoutHeight = bottom - top;
        if (movieWidth > layoutWidth || movieHeight > layoutHeight) {
            mScale = Math.min(layoutHeight * 1.0f / movieHeight, layoutWidth * 1.0f / movieWidth);
        } else {
            mScale = 1;
        }
        mLeft = (int)(right - left - movie.width() * mScale) / 2;
        mTop = (int)(bottom - top - movie.height() * mScale) / 2;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        if (movie == null) {
            return;
        }
        long now = android.os.SystemClock.uptimeMillis();
        if (mStart == 0) {
            mStart = now;
        }
        int time = (int)((now - mStart) % (mDuration + PAUSE_AFTER_MOVIE));
        if (time - mDuration >= PAUSE_AFTER_MOVIE) {
            time = mDuration;
        }
        movie.setTime(time);
        canvas.scale(mScale, mScale);
        movie.draw(canvas, mLeft, mTop);
        postInvalidateOnAnimation();
    }
}