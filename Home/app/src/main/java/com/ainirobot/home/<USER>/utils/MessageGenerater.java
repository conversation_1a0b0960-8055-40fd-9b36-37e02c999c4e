package com.ainirobot.home.ota.utils;

import static com.ainirobot.coreservice.client.Definition.JSON_OTA_DOWNLOAD_FULL;
import static com.ainirobot.coreservice.client.Definition.JSON_OTA_INSTALL_PERCENT;
import static com.ainirobot.coreservice.client.Definition.JSON_OTA_IS_DOWNLOAD_PROGRESS;
import static com.ainirobot.home.ota.bean.VersionData.STATUS.FAILED;
import static com.ainirobot.home.ota.bean.VersionData.STATUS.SUCCESS;
import static com.ainirobot.home.ota.constants.OtaConstants.Error.ERR_UPDATE_FAILED;
import static com.ainirobot.home.ota.constants.OtaConstants.Error.NO_ERR;
import static com.ainirobot.home.ota.constants.OtaConstants.PARAM_BOARD;
import static com.ainirobot.home.ota.constants.OtaConstants.PARAM_FILE_LENGTH;
import static com.ainirobot.home.ota.constants.OtaConstants.PARAM_MD5;
import static com.ainirobot.home.ota.constants.OtaConstants.PARAM_NAME;
import static com.ainirobot.home.ota.constants.OtaConstants.PARAM_TARGET_VERSION;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.home.ota.bean.InstallData;
import com.ainirobot.home.ota.bean.OtaMessage;
import com.ainirobot.home.ota.bean.VersionData;
import com.ainirobot.home.ota.database.IDataBaseInterface;
import com.ainirobot.home.ota.parser.PackData;
import com.ainirobot.home.ota.service.DowngradeManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.HashSet;
import java.util.List;

/**
 * Generate reply message to CoreService
 *
 */
public class MessageGenerater {
    private final static String TAG = MessageGenerater.class.getSimpleName();

    private static JSONObject generateUpdateParams(String type, String name, String md5) {
        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject();
            jsonObject.put(PARAM_BOARD, type);
            jsonObject.put(PARAM_NAME, name);
            jsonObject.put(PARAM_MD5, md5);
        } catch (JSONException e) {
            Log.e(TAG, "generateUpdateParams got exception " + e.getMessage());
            jsonObject = null;
            e.printStackTrace();
        }

        return jsonObject;
    }

    private static JSONObject generateUpdateParams(String type, String name, String md5
            , String filelength, String targetVersion) {
        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject();
            jsonObject.put(PARAM_BOARD, type);
            jsonObject.put(PARAM_NAME, name);
            jsonObject.put(PARAM_MD5, md5);
            jsonObject.put(PARAM_FILE_LENGTH, filelength);
            jsonObject.put(PARAM_TARGET_VERSION, targetVersion);
        } catch (JSONException e) {
            Log.e(TAG, "generateUpdateParams got exception " + e.getMessage());
            jsonObject = null;
            e.printStackTrace();
        }

        return jsonObject;
    }

    /**
     * Generate start update command through can bus.
     *
     * @param board
     * @param filePath
     * @return
     */
    public static JSONObject generateCanUpdateMessage(int board, String filePath) {
        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject();
            jsonObject.put(Definition.JSON_CAN_BOARD, board);
            jsonObject.put(Definition.JSON_CAN_FILE_PATH, filePath);
        } catch (JSONException e) {
            Log.e(TAG, "generateCanUpdateMessage got exception " + e.getMessage());
            jsonObject = null;
            e.printStackTrace();
        }

        return jsonObject;
    }

    public static JSONObject generateCanBoardVersionMessage(int board) {
        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject();
            jsonObject.put(Definition.JSON_CAN_BOARD, board);
        } catch (JSONException e) {
            Log.e(TAG, "generateCanBoardVersionMessage got exception " + e.getMessage());
            jsonObject = null;
            e.printStackTrace();
        }

        return jsonObject;
    }

    /**
     * Generate start update command.
     *
     * @param dataBaseManager
     * @param mPreference
     * @param osSet naviHashSet, contains wheel_left/right and navigation board.
     * @param osName
     * @return
     */
    public static JSONObject generateUpdateJsonObjectS(IDataBaseInterface dataBaseManager,
                                                       Preferences mPreference,
                                                       HashSet<String> osSet,
                                                       String osName) {
        String filePath = null;
        String[] files = null;
        String fileName = null;
        String md5 = null;
        JSONObject jsonObject = null;

        filePath = mPreference.getOsPackagePath(osName);
        files = filePath.split(File.separator);
        fileName = files[files.length - 1];
        md5 = mPreference.getOsPackageMD5(osName + PackData.MD5);

        if (osSet.contains(osName)) {
            VersionData vData = dataBaseManager.getOsData(osName);
            File file = new File(filePath);
            long fileL = file.length();
            String fileLength = Long.toString(fileL);
            String targetVersion = vData.getTargetVersion();
            jsonObject = generateUpdateParams(osName, filePath, md5, fileLength, targetVersion);
        } else {
            jsonObject = generateUpdateParams(osName, fileName, md5);
        }

        return jsonObject;
    }

    /**
     * Generate start update command.
     *
     * @param dataBaseManager
     * @param mPreference
     * @param osSet naviHashSet, contains wheel_left/right and navigation board.
     * @param osName
     * @return
     */
    public static JSONObject generateUpdateJsonObject(IDataBaseInterface dataBaseManager,
                                                      Preferences mPreference,
                                                      List<InstallData> osSet,
                                                      String osName) {
        String filePath = null;
        String[] files = null;
        String fileName = null;
        String md5 = null;
        JSONObject jsonObject = null;

        filePath = mPreference.getOsPackagePath(osName);
        files = filePath.split(File.separator);
        fileName = files[files.length - 1];
        md5 = mPreference.getOsPackageMD5(osName + PackData.MD5);

        if (osSet.contains(new InstallData(osName))) {
            VersionData vData = dataBaseManager.getOsData(osName);
            File file = new File(filePath);
            long fileL = file.length();
            String fileLength = Long.toString(fileL);
            String targetVersion = vData.getTargetVersion();
            jsonObject = generateUpdateParams(osName, filePath, md5, fileLength, targetVersion);
        } else {
            jsonObject = generateUpdateParams(osName, fileName, md5);
        }

        return jsonObject;
    }

    /**
     * Generate update install progress message.
     *
     * @param inc
     * @param total
     * @return
     */
    public static String generateProgressMessage(int inc, int total) {
        JSONObject jsonProgress = new JSONObject();
        try {
            jsonProgress.put(JSON_OTA_IS_DOWNLOAD_PROGRESS, false);
            jsonProgress.put(JSON_OTA_INSTALL_PERCENT,
                    String.valueOf((float) inc / (float) total));
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return jsonProgress.toString();
    }

    /**
     * Genergate download event message about full package.
     *
     * @param msg
     * @return
     */
    public static String generateDownloadFullPackEventMessage(String msg) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(JSON_OTA_DOWNLOAD_FULL, msg);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObject.toString();
    }

    /**
     * Generate update finish message.
     * need call generateOtaFinishMessage before clearInstallMessage
     *
     * @param dataBaseManager
     * @param isRollBack
     * @return
     */
    public static OtaMessage generateOtaFinishMessage(
            IDataBaseInterface dataBaseManager, boolean isRollBack) {
        JSONObject jsonObject = new JSONObject();
        String result;
        String updateType;
        String message;
        String extra = null;
        boolean hasFailed = false;
        boolean needClearData = true;
        List<VersionData> listData = dataBaseManager.getAllVersion();

        for (VersionData data : listData) {
            try {
                if (data.getStatus() == FAILED) {
                    jsonObject.put(data.getName(), Definition.JSON_OTA_RESULT_FAILED);
                    hasFailed = true;
                } else if (data.getStatus() == SUCCESS) {
                    jsonObject.put(data.getName(), Definition.JSON_OTA_RESULT_SUCCESS);
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        result = hasFailed ? Definition.JSON_OTA_RESULT_FAILED : Definition.JSON_OTA_RESULT_SUCCESS;
        updateType = isRollBack ? Definition.JSON_OTA_TYPE_ROLLBACK
                : Definition.JSON_OTA_TYPE_NORMAL;
        message = hasFailed ? ERR_UPDATE_FAILED : NO_ERR;
        if (hasFailed) {
            extra = jsonObject.toString();
            needClearData = false;
        }

        return new OtaMessage(result, false, updateType, message, extra, needClearData);
    }
}
