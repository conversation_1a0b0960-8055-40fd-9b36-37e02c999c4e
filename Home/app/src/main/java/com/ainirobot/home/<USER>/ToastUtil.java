/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.home.utils;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import com.ainirobot.home.R;
import com.ainirobot.home.ui.view.SoundProgressView;

public class ToastUtil {

    private static ToastUtil mInstance = null;
    private Toast mToast;
    private TextView mTextView;

    public static ToastUtil getInstance() {
        if (mInstance == null) {
            mInstance = new ToastUtil();
        }
        return mInstance;
    }

    public ToastUtil setDuration(int duration) {
        if (mToast != null) {
            mToast.setDuration(duration);
        }
        return this;
    }

    public ToastUtil setText(CharSequence text) {
        if (mTextView != null) {
            mTextView.setText(text);
        }
        return this;
    }

    public void show() {
        if (mToast != null) {
            mToast.show();
        }
    }


    private static Toast soundToast = null;
    private static View layout = null;
    private static SoundProgressView sView = null;

    public static void show(Context context, int progress) {
        cancelSoundToast();
        if (soundToast == null) {
            soundToast = new Toast(context);
            soundToast.setGravity(Gravity.CENTER, 0, 0);
            layout = LayoutInflater.from(context).inflate(R.layout.toast_view, null);
            sView = (SoundProgressView) layout.findViewById(R.id.sView);
            soundToast.setView(layout);
            soundToast.setDuration(Toast.LENGTH_SHORT);
        }
        sView.setProgress(progress);
        soundToast.show();
    }

    public static void cancelSoundToast() {
        if (soundToast != null) {
            soundToast.cancel();
            soundToast = null;
            layout = null;
            sView = null;
        }
    }

    public static void showToast(final Context context, final String text) {

        if (isMainThread()) {
            Toast.makeText(context, text, Toast.LENGTH_SHORT).show();
        } else {
            Handler handler = new Handler(Looper.getMainLooper());
            handler.post(new Runnable() {
                @Override
                public void run() {
                    Toast.makeText(context, text, Toast.LENGTH_LONG).show();
                }
            });
        }
    }

    private static boolean isMainThread() {
        return Looper.getMainLooper() == Looper.myLooper();
    }

}
