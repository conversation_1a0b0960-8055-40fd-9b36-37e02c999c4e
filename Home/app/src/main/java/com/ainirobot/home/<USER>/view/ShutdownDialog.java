/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.home.ui.view;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Outline;
import android.net.Uri;
import android.os.CountDownTimer;
import android.provider.Settings;
import android.support.annotation.NonNull;
import android.text.Html;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewOutlineProvider;
import android.view.WindowManager;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.R;


public class ShutdownDialog extends Dialog {
    private final String TAG = "ShutdownDialog:Home";

    /**
     * 超时时间
     */
    private final long TIMEOUT = 61 * 1000;

    private DialogEvent mDialogEvent;
    private View mContentView;
    private TextView mTips;
    private CountDownTimer mCountDownTimer;

    public ShutdownDialog setDialogEvent(DialogEvent mDialogEvent) {
        this.mDialogEvent = mDialogEvent;
        return this;
    }

    public ShutdownDialog(@NonNull Context context) {
        super(context, R.style.OTADialog);
        //configuration
        initView(context);
        setContentView(mContentView);
        WindowManager.LayoutParams p = this.getWindow().getAttributes();
        p.width = 950;
        p.height = WindowManager.LayoutParams.WRAP_CONTENT;
        this.getWindow().setAttributes(p);
        setCanceledOnTouchOutside(false);


        if (!(getContext() instanceof Activity)) {
            getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_DIALOG);
            getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
        }
    }

    private void initView(Context context) {
        mContentView = LayoutInflater.from(context).inflate(R.layout.shutdown_dialog, null);

        mContentView.findViewById(R.id.shutdown_cancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mDialogEvent != null) {
                    cancelCountDownTimer();
                    mDialogEvent.onCancel();
                }
            }
        });
        mContentView.findViewById(R.id.shutdown_now).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mDialogEvent != null) {
                    cancelCountDownTimer();
                    mDialogEvent.onConfirm();
                }
            }
        });

        mTips = mContentView.findViewById(R.id.shutdown_tips);
        mTips.setText(Html.fromHtml(context.getString(R.string.shutdown_tips, TIMEOUT / 1000)));
        ViewOutlineProvider viewOutlineProvider = new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                view.setClipToOutline(true);
                outline.setRoundRect(0, 0, view.getWidth(), view.getHeight(), 30);
            }
        };
        mContentView.setOutlineProvider(viewOutlineProvider);
    }

    private void startCountDownTimer() {
        mCountDownTimer = new CountDownTimer(TIMEOUT, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                Log.d(TAG, "On tick : " + millisUntilFinished);
                int second = (int) (millisUntilFinished / 1000);
                mTips.setText(Html.fromHtml(getContext().getString(R.string.shutdown_tips, second)));
                if (mDialogEvent != null) {
                    mDialogEvent.onTick(second);
                }
            }

            @Override
            public void onFinish() {
                if (mDialogEvent != null) {
                    mDialogEvent.onTimeout();
                }
            }
        };
        mCountDownTimer.start();
    }

    private void cancelCountDownTimer() {
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
        }
    }

    public interface DialogEvent {
        void onConfirm();

        void onCancel();

        void onTick(int secondUntilFinished);

        void onTimeout();
    }

    @Override
    public void show() {
        permission();
        ObjectAnimator.ofFloat(mContentView, "alpha", 0, 1)
                .setDuration(170)
                .start();

        startCountDownTimer();
    }

    @Override
    public void dismiss() {
        ObjectAnimator animator = ObjectAnimator
                .ofFloat(mContentView, "alpha", 1, 0)
                .setDuration(170);
        animator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                cancelCountDownTimer();
                ShutdownDialog.super.dismiss();
            }
        });
        animator.start();
    }


    private void permission() {
        if (!Settings.canDrawOverlays(getContext())) {
            Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:" + getContext().getPackageName()));
            getContext().startActivity(intent);
        } else {
            // Already hold the SYSTEM_ALERT_WINDOW permission,
            Log.e(TAG, "has ACTION_MANAGE_OVERLAY_PERMISSION");
            super.show();
        }
    }
}
