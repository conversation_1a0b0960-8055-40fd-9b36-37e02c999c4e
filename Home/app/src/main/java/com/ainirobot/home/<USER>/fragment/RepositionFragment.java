package com.ainirobot.home.ui.fragment;

import static com.ainirobot.home.utils.CommonUtils.getPriority;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.annotation.TargetApi;
import android.content.Context;
import android.os.Build;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.view.GifView;
import com.ainirobot.home.ui.view.MarkView;
import com.ainirobot.home.ui.view.RepositionCancelDialog;
import com.ainirobot.home.ui.view.RepositionSolutionDialog;
import com.ainirobot.home.utils.LocationUtil;
import com.ainirobot.home.utils.ResType;
import com.ainirobot.home.utils.ResUtil;


public class RepositionFragment extends BaseFragment
        implements View.OnClickListener {

    private static final String TAG = "RepositionFragment";
    private Context mContext;

    private View mVisionShowV;
    private View mMenualGuideV;
    private View mMenualSearchV;
    private View mSuccessView;
    private View mVisionFailV;
    private View mMenualFailureV;
    private ImageView mSearchImageV;
    private ImageView mSearchShadowIV;
    private MarkView mMarkView;
    private TextView mSuccessTitleView;
    private Button mGuideConfirmBtn;
    private Button mFailureConfirmBtn;
    private Button mFailureSeeSolution;
    private Button mFailRetryBtn;
    private Button mFailMenualBtn;
    private boolean mAnimateStopped = false;
    private ObjectAnimator mUpAnimator;
    private ObjectAnimator mDownAnimator;
    private AnimatorSet mResetAnimatorSet;
    private AnimatorSet mZoomAnimatorSet;
    private CountDownTimer mCountDownTimer;
    private RepositionCancelDialog mDialog;
    private LinearLayout mVisionFailCancel;
    private String failReason;
    private int mOrientation;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        Log.i(TAG, "on create");
        super.onCreate(savedInstanceState);
        mAnimateStopped = false;
        mOrientation = ApplicationWrapper.getContext().getResources().getConfiguration().orientation;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        Log.i(TAG, "on create view");
        View view = initSubViews(inflater);

        Bundle bundle = getArguments();
        String message = "false";
        boolean isFromVision = false;
        boolean isFailure = false;
        if (bundle != null) {
            message = bundle.getString("isCharging", "false");
            isFromVision = bundle.getBoolean(Definition.REPOSITION_VISION, false);
            isFailure = bundle.getBoolean("isFailure", false);
        }

        if(isFailure){
            recordFailReason(Definition.RESULT_ESTIMATE_FAIL_CHECK_ERR);
            showFailureView();
        } else if (isFromVision){
            showVisionCircleView();
            resumeFailReason();
        }else {
            showGuideView(message);
            resumeFailReason();
        }
        return view;
    }

    private View initSubViews(LayoutInflater inflater) {
        View view = inflater.inflate(R.layout.fragment_reposition, null);
        mVisionShowV = (View)view.findViewById(R.id.locate_vision_show);
        mMenualGuideV = (View) view.findViewById(R.id.charge_locate_guide);
        mMenualSearchV = (View) view.findViewById(R.id.locate_search);
        mSuccessView = (View) view.findViewById(R.id.locate_success);
        mMenualFailureV = (View) view.findViewById(R.id.locate_failure);
        mVisionFailV = (View)view.findViewById(R.id.locate_vision_failure);
        mVisionFailCancel = (LinearLayout)view.findViewById(R.id.reposition_cancel_layout);
        mSearchImageV = (ImageView) view.findViewById(R.id.locate_search_img);
        mSearchShadowIV = (ImageView) view.findViewById(R.id.locate_search_shadow);
        mMarkView = (MarkView) view.findViewById(R.id.locate_success_mark_view);
        mSuccessTitleView = (TextView)view.findViewById(R.id.locate_success_title);
        mFailureConfirmBtn = (Button) view.findViewById(R.id.locate_failure_confirmBtn);
        mFailureSeeSolution = (Button) view.findViewById(R.id.locate_failure_see_solution);
        mGuideConfirmBtn = (Button) view.findViewById(R.id.locate_guide_confirmBtn);
        mFailRetryBtn = (Button)view.findViewById(R.id.locate_failure_vision_retry_btn);
        mFailMenualBtn = (Button)view.findViewById(R.id.locate_failure_menual_btn);
        mGuideConfirmBtn.setOnClickListener(this);
        mVisionFailCancel.setOnClickListener(this);
        mFailureConfirmBtn.setOnClickListener(this);
        mFailureSeeSolution.setOnClickListener(this);
        mFailMenualBtn.setOnClickListener(this);
        mFailRetryBtn.setOnClickListener(this);
        mMenualGuideV.findViewById(R.id.reposition_cancel_text).setOnClickListener(this);
        mMenualGuideV.findViewById(R.id.reposition_cancel).setOnClickListener(this);
        mMenualFailureV.findViewById(R.id.reposition_cancel_text).setOnClickListener(this);
        mMenualFailureV.findViewById(R.id.reposition_cancel).setOnClickListener(this);
        if (LocationUtil.getInstance().isChargingTypeWire()) {
            mFailMenualBtn.setText(ResUtil.getString(R.string.locate_pile_reposition));
            mFailureConfirmBtn.setText(ResUtil.getString(R.string.reposition_reposition));
        }
        return view;
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        mContext = context;
        Log.d(TAG, "onAttach");
    }

    @Override
    public void onStart() {
        super.onStart();
        Log.i(TAG, "onstart");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.d(TAG, "onDestroyView");
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
            mCountDownTimer = null;
        }
        if (mDialog != null) {
            mDialog.dismiss();
        }
        resumeFailReason();
    }

    private void destroyAllAnimationAndView() {
        stopRepositionAnimate();
        mAnimateStopped = false;
        if (mMarkView != null) {
            mMarkView.post(new Runnable() {
                @Override
                public void run() {
                    mMarkView.stopAllAnimate();
                }
            });
        }
    }

    private void showGuideView(final String message) {
        mMenualGuideV.post(new Runnable() {
            @Override
            public void run() {
                mMenualGuideV.setVisibility(View.VISIBLE);
                mMenualSearchV.setVisibility(View.GONE);
                mMenualFailureV.setVisibility(View.GONE);
                mSuccessView.setVisibility(View.GONE);
                mVisionShowV.setVisibility(View.GONE);
                mVisionFailV.setVisibility(View.GONE);
                TextView textView = (TextView) mMenualGuideV.findViewById(R.id.locate_guide_subtitle);
                GifView gifView = (GifView) mMenualGuideV.findViewById(R.id.locate_guide_bg);
                if ("false".equals(message)) {
                    if (mCountDownTimer != null) {
                        mCountDownTimer.cancel();
                        mCountDownTimer = null;
                    }
                    textView.setText(getLocateGuideSubtitle());
                    Log.d(TAG, "showGuideView: mIsWireCharging=" +
                            LocationUtil.getInstance().isChargingTypeWire());
                    gifView.setMovieResource(getLocateGuideGif());
                    if(LocationUtil.getInstance().isChargingTypeWire()){
                        mGuideConfirmBtn.setVisibility(View.VISIBLE);
                        mGuideConfirmBtn.setText(ResUtil.getString(R.string.dialog_confirm));
                    }else {
                        mGuideConfirmBtn.setVisibility(View.INVISIBLE);
                    }
                } else {
                    clickFailureConfirmBtn();
                }
            }
        });
    }

    private void showSearchView() {
        mMenualGuideV.post(new Runnable() {
            @Override
            public void run() {
                mAnimateStopped = false;
                mMenualGuideV.setVisibility(View.GONE);
                mMenualSearchV.setVisibility(View.VISIBLE);
                mMenualFailureV.setVisibility(View.GONE);
                mSuccessView.setVisibility(View.GONE);
                mVisionShowV.setVisibility(View.GONE);
                mVisionFailV.setVisibility(View.GONE);
            }
        });
    }

    private void showSuccessView() {
        mMenualGuideV.post(new Runnable() {
            @Override
            public void run() {
                mMenualGuideV.setVisibility(View.GONE);
                mMenualSearchV.setVisibility(View.GONE);
                mMenualFailureV.setVisibility(View.GONE);
                mSuccessView.setVisibility(View.VISIBLE);
                mVisionShowV.setVisibility(View.GONE);
                mVisionFailV.setVisibility(View.GONE);
                if (mMarkView.getStatus() != MarkView.Status.LoadSuccess) {
                    mMarkView.setStatus(MarkView.Status.LoadSuccess);
                    mMarkView.startAnima();
                }
            }
        });
    }

    private void showFailureView() {
        mMenualSearchV.post(new Runnable() {
            @Override
            public void run() {
                mMenualGuideV.setVisibility(View.GONE);
                mMenualSearchV.setVisibility(View.GONE);
                mMenualFailureV.setVisibility(View.VISIBLE);
                mSuccessView.setVisibility(View.GONE);
                mVisionShowV.setVisibility(View.GONE);
                mVisionFailV.setVisibility(View.GONE);
            }
        });
    }

    private void showVisionCircleView(){
        mMenualSearchV.post(new Runnable() {
            @Override
            public void run() {
                mMenualGuideV.setVisibility(View.GONE);
                mMenualSearchV.setVisibility(View.GONE);
                mMenualFailureV.setVisibility(View.GONE);
                mSuccessView.setVisibility(View.GONE);
                mVisionShowV.setVisibility(View.VISIBLE);
                mVisionFailV.setVisibility(View.GONE);
            }
        });
    }

    private void showVisionFailedView(){
        mMenualSearchV.post(new Runnable() {
            @Override
            public void run() {
                mMenualGuideV.setVisibility(View.GONE);
                mMenualSearchV.setVisibility(View.GONE);
                mMenualFailureV.setVisibility(View.GONE);
                mSuccessView.setVisibility(View.GONE);
                mVisionShowV.setVisibility(View.GONE);
                mVisionFailV.setVisibility(View.VISIBLE);
            }
        });
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.locate_failure_confirmBtn:
                mAnimateStopped = false;
                clickFailureConfirmBtn();
                break;
            case R.id.locate_failure_see_solution:
//                clickFailureSkipBtn();
                showSolutionDialog();
                break;
            case R.id.locate_guide_confirmBtn:
                clickGuideConfirmBtn();
                break;
            case R.id.locate_failure_vision_retry_btn:
                clickVisionFailRetryBtn();
                break;
            case R.id.locate_failure_menual_btn:
                clickVisionFailMenualBtn();
                break;
            case R.id.reposition_cancel_layout:
            case R.id.reposition_cancel:
            case R.id.reposition_cancel_text:
                clickCancel();
                break;
            default:
                break;
        }
    }

    private void showSolutionDialog() {
        RepositionSolutionDialog repositionSolutionDialog = new RepositionSolutionDialog(mContext, R.style.OTADialog);
        repositionSolutionDialog.setReason1(getReason1());
        repositionSolutionDialog.setReason2(getReason2());
        repositionSolutionDialog.show();
    }

    private String getReason1() {
        if (!TextUtils.isEmpty(failReason)
                && failReason.equals(Definition.RESULT_ESTIMATE_FAIL_MATCH_ERR)) {
            return ResUtil.getString(R.string.reason_loss_error_large);
        } else if (!TextUtils.isEmpty(failReason)
                && failReason.equals(Definition.RESULT_ESTIMATE_FAIL_CHECK_ERR)) {
            return ResUtil.getString(R.string.reason_chargepile_moved);
        } else {
            return ResUtil.getString(R.string.reason_reboot);
        }
    }

    private String getReason2() {
        if (!TextUtils.isEmpty(failReason)
                && failReason.equals(Definition.RESULT_ESTIMATE_FAIL_MATCH_ERR)) {
            return ResUtil.getString(R.string.reason_map_wrong);
        } else if (!TextUtils.isEmpty(failReason)
                && failReason.equals(Definition.RESULT_ESTIMATE_FAIL_CHECK_ERR)) {
            return ResUtil.getString(R.string.reason_multi_chargepile);
        } else {
            return "";
        }
    }

    private void clickFailureConfirmBtn() {
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.MSG_REPOSITION_FAILURE_RETRY);
    }

    private void clickFailureSkipBtn() {
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.MSG_REPOSITION_FAILURE_SKIP);
    }

    private void clickGuideConfirmBtn() {
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.MSG_REPOSITION_GUIDE_CONFIRM);
    }

    private void clickVisionFailRetryBtn(){
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.MSG_REPOSITION_VISION_RETRY);
    }

    private void clickVisionFailMenualBtn() {
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.MSG_REPOSITION_VISION_MENUAL);
    }

    @TargetApi(Build.VERSION_CODES.M)
    private void clickCancel() {
        if (mDialog == null) {
            mDialog = new RepositionCancelDialog(getContext(), R.style.OTADialog)
                    .setDialogClickListener(new RepositionCancelDialog.ClickListener() {
                        @Override
                        public void onConfirmClick() {
                            mDialog = null;
                        }

                        @Override
                        public void onCancelClick() {
                            ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_REPOSITION_CANCEL);
                            mDialog = null;
                        }
                    });
            mDialog.show();
        }
    }

    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
        switch (type) {
            case REPOSITION_CHARGING_CHECK:
                showGuideView(message);
                break;
            case REPOSITION_START://正在定位中
                if (mCountDownTimer != null) {
                    mCountDownTimer.cancel();
                    mCountDownTimer = null;
                }
                showSearchView();
                upThrowAnimator();
                zoomImageViewScale();
                break;
            case REPOSITION_SUCCEED://定位成功
                mVisionShowV.post(new Runnable() {
                    @Override
                    public void run() {
                        mSuccessTitleView.setText(ResUtil.getString(R.string.reposition_manual_success));
                    }
                });
                stopRepositionAnimate();
                showSuccessView();
                break;
            case REPOSITION_FAILURE://定位失败
                stopRepositionAnimate();
                showFailureView();
                recordFailReason(message);
                break;
            case REPOSITION_EXIT://退出定位
                destroyAllAnimationAndView();
                break;
            case REPOSITION_VISION_CIRCLE:
                showVisionCircleView();
                break;
            case REPOSITION_VISION_FAILED:
                showVisionFailedView();
                break;
            case REPOSITION_MENUAL_GUIDE:
                showGuideView(message);
                break;
            case REPOSITION_VISION_SUCCESS:
                mVisionShowV.post(new Runnable() {
                    @Override
                    public void run() {
                        mSuccessTitleView.setText(ResUtil.getString(R.string.reposition_vision_success));
                    }
                });
                stopRepositionAnimate();
                showSuccessView();
                break;
            case REPOSITION_CLEAR_REASON:
                resumeFailReason();
                break;
            case DOEMANCY_START:
                if (mCountDownTimer != null) {
                    mCountDownTimer.cancel();
                    mCountDownTimer = null;
                }
                break;
            default:
                break;
        }
    }

    private void resumeFailReason() {
        Log.d(TAG, "reset FailReason empty");
        failReason = "";
    }

    private void recordFailReason(String message) {
        Log.d(TAG, "recordFailReason = " + message);
        if (getPriority(message) < getPriority(failReason)) {
            // only record high priority reason, smaller is higher
            failReason = message;
        }
        Log.d(TAG, "finaly fail reason: "+failReason);
    }

    private void zoomImageViewScale() {
        if (mAnimateStopped) {
            return;
        }
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(mSearchShadowIV, "scaleX", 1.0f, 0.7f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(mSearchShadowIV, "scaleY", 1.0f, 0.7f);
        mZoomAnimatorSet = new AnimatorSet();
        mZoomAnimatorSet.playTogether(scaleX, scaleY);
        mZoomAnimatorSet.setDuration(500);
        mZoomAnimatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (!mAnimateStopped) {
                    resetImageViewScale();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        mZoomAnimatorSet.start();
    }

    private void resetImageViewScale() {
        if (mAnimateStopped) {
            return;
        }
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(mSearchShadowIV, "scaleX", 0.7f, 1.0f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(mSearchShadowIV, "scaleY", 0.7f, 1.0f);
        mResetAnimatorSet = new AnimatorSet();
        mResetAnimatorSet.playTogether(scaleX, scaleY);
        mResetAnimatorSet.setDuration(500);
        mResetAnimatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (!mAnimateStopped) {
                    zoomImageViewScale();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        mResetAnimatorSet.start();
    }

    private void upThrowAnimator() {
        if (mAnimateStopped) {
            return;
        }
        mUpAnimator = ObjectAnimator.ofFloat(mSearchImageV, "translationY", 0, -160);
        mUpAnimator.setDuration(500);
        mUpAnimator.setInterpolator(new DecelerateInterpolator(1.2f));
        mUpAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (!mAnimateStopped) {
                    freeFall();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        mUpAnimator.start();
    }

    private void freeFall() {
        if (mAnimateStopped) {
            return;
        }
        mDownAnimator = ObjectAnimator.ofFloat(mSearchImageV, "translationY", -160, 0);
        mDownAnimator.setDuration(500);
        mDownAnimator.setInterpolator(new AccelerateInterpolator(1.2f));
        mDownAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (mAnimateStopped == false) {
                    upThrowAnimator();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        mDownAnimator.start();
    }

    private void stopRepositionAnimate() {
        Log.i(TAG, "stopRepositionAnimate mAnimateStoppen:" + mAnimateStopped);
        if (mAnimateStopped) {
            return;
        }
        if(mMenualGuideV == null){
            Log.i(TAG, "stopRepositionAnimate mMenualGuideV is null");
            return;
        }
        mMenualGuideV.post(new Runnable() {
            @Override
            public void run() {
                mAnimateStopped = true;
                if (mUpAnimator != null && mUpAnimator.isRunning()) {
                    mUpAnimator.end();
                    mUpAnimator = null;
                }
                if (mDownAnimator != null && mDownAnimator.isRunning()) {
                    mDownAnimator.end();
                    mDownAnimator = null;
                }
                if (mResetAnimatorSet != null && mResetAnimatorSet.isRunning()) {
                    mResetAnimatorSet.end();
                    mResetAnimatorSet = null;
                }
                if (mZoomAnimatorSet != null && mZoomAnimatorSet.isRunning()) {
                    mZoomAnimatorSet.end();
                    mZoomAnimatorSet = null;
                }
            }
        });

    }

    private String getLocateGuideSubtitle() {
        return ResUtil.getString(LocationUtil.getInstance().isChargingTypeWire() ?
                R.string.reposition_reset_remind_msg_locate :
                R.string.reposition_reset_remind_msg);
    }

    private int getLocateGuideGif() {
        return LocationUtil.getInstance().isChargingTypeWire() ?
                ResType.GIF_WIRE_CHARGE_TIP.getResIdByType() :
                ResType.GIF_PUSH_TO_CHARGE_PILE.getResIdByType();
    }

}
