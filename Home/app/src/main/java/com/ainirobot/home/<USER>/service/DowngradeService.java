package com.ainirobot.home.ota.service;

import static android.support.v4.app.NotificationCompat.PRIORITY_MIN;

import static com.ainirobot.home.ota.constants.DowngradeConstants.START_COMMAND_CHECKING;
import static com.ainirobot.home.ota.constants.DowngradeConstants.START_COMMAND_DOWNLOAD;
import static com.ainirobot.home.ota.constants.DowngradeConstants.START_COMMAND_DOWNLOAD_CONTINUE;
import static com.ainirobot.home.ota.constants.DowngradeConstants.START_COMMAND_DOWNLOAD_PAUSE;
import static com.ainirobot.home.ota.constants.DowngradeConstants.START_COMMAND_FILE_CHECKING;
import static com.ainirobot.home.ota.constants.DowngradeConstants.START_COMMAND_FILE_CHECKING_STOP;
import static com.ainirobot.home.ota.constants.DowngradeConstants.START_COMMAND_INSTALL;
import static com.ainirobot.home.ota.constants.DowngradeConstants.START_COMMAND_NEW_VERSION_CHECKING;
import static com.ainirobot.home.ota.constants.DowngradeConstants.START_COMMAND_REAL_INSTALL_ROM;
import static com.ainirobot.home.ota.constants.DowngradeConstants.START_COMMAND_SILENT_INSTALL;
import static com.ainirobot.home.ota.constants.DowngradeConstants.START_COMMAND_STOP_SERVICE;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.os.BatteryManager;
import android.os.Build;
import android.os.IBinder;
import android.support.annotation.Nullable;
import android.support.annotation.RequiresApi;
import android.support.v4.app.NotificationCompat;
import android.util.Log;

import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.R;
import com.ainirobot.home.ota.constants.DowngradeConstants;
import com.ainirobot.home.ota.constants.OtaConstants;

public class DowngradeService extends Service {
    private static final String TAG = OtaConstants.TAG_PREFIX + DowngradeService.class.getSimpleName();

    private DowngradeManager downgradeManager;
    private BatteryReceiver mBatteryReceiver;

    @Override
    public void onCreate() {
        Log.d(TAG, "onCreate");
        super.onCreate();
        startForeground();
        downgradeManager = DowngradeManager.getInstance(ApplicationWrapper.getContext());
        mBatteryReceiver = new BatteryReceiver();
        IntentFilter batteryFilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
        getApplicationContext().registerReceiver(mBatteryReceiver, batteryFilter);
    }

    /**
     * 启动前台服务
     */
    private void startForeground() {
        String channelId = null;
        // 8.0 以上需要特殊处理
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            channelId = createNotificationChannel("DowngradeService", "ForegroundService");
        } else {
            channelId = "";
        }
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, channelId);
        Notification notification = builder.setOngoing(true)
                .setSmallIcon(R.drawable.orion_home_icon)
                .setPriority(PRIORITY_MIN)
                .setCategory(Notification.CATEGORY_SERVICE)
                .build();
        startForeground(2002, notification);
    }

    /**
     * 创建通知通道
     * @param channelId
     * @param channelName
     * @return
     */
    @RequiresApi(Build.VERSION_CODES.O)
    private String createNotificationChannel(String channelId, String channelName){
        NotificationChannel chan = new NotificationChannel(channelId,
                channelName, NotificationManager.IMPORTANCE_NONE);
        chan.setLightColor(Color.BLUE);
        chan.setLockscreenVisibility(Notification.VISIBILITY_PRIVATE);
        NotificationManager service = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        service.createNotificationChannel(chan);
        return channelId;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        int command = intent.getIntExtra(DowngradeConstants.KEY_START_COMMAND, -1);
        boolean isUserDowngrade = intent.getBooleanExtra(DowngradeConstants.KEY_START_COMMAND_PARAMS, false);
        String startingMode = intent.getStringExtra(DowngradeConstants.KEY_START_MODE);
        Log.d(TAG, "onStartCommand command = " + command + "(" + getCommandString(command) +
            "), isUserDowngrade = " + isUserDowngrade + ", startingMode = " + startingMode);

        switch (command) {
            case START_COMMAND_CHECKING:
                Log.d(TAG, "start check");
                downgradeManager.startCheck(isUserDowngrade);
                break;

            case START_COMMAND_NEW_VERSION_CHECKING:
                Log.d(TAG, "start new version check");
                downgradeManager.startCheckNewVersion(startingMode);
                break;
            case START_COMMAND_DOWNLOAD:
                Log.d(TAG, "start download");
                // 1期：目前只存在用户主动降级
                String downloadType = OtaConstants.DOWNLOAD_FILE_TYPE_NORMAL;
                downgradeManager.startComponentDownload(downloadType);
                break;
            case START_COMMAND_INSTALL:
                Log.d(TAG, "start install");
                downgradeManager.startInstall();
                break;
            case START_COMMAND_DOWNLOAD_PAUSE:
                Log.d(TAG, "pause download");
                String stopDownloadType = intent.getStringExtra(OtaConstants.OTA_EXTRA_DOWNLOAD_TYPE);

                if (stopDownloadType == null || stopDownloadType.isEmpty()) {
                    stopDownloadType = OtaConstants.DOWNLOAD_FILE_TYPE_NORMAL;
                }
                downgradeManager.pauseDownload(stopDownloadType);
                break;
            case START_COMMAND_DOWNLOAD_CONTINUE:
                Log.d(TAG, "continue download");
                String continueDownloadType = intent.getStringExtra(OtaConstants.OTA_EXTRA_DOWNLOAD_TYPE);

                if (continueDownloadType == null || continueDownloadType.isEmpty()) {
                    continueDownloadType = OtaConstants.DOWNLOAD_FILE_TYPE_NORMAL;
                }
                downgradeManager.continueDownload(continueDownloadType);
                break;
            case START_COMMAND_STOP_SERVICE:
                Log.d(TAG, "stop downgrade service.");
                downgradeManager.stopOtaService();
                break;
            case START_COMMAND_REAL_INSTALL_ROM:
                Log.d(TAG, "start real install rom.");
                downgradeManager.startInstallRom();
                break;
            default:
                break;
        }
        return START_NOT_STICKY;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        getApplicationContext().unregisterReceiver(mBatteryReceiver);
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    private String getCommandString(int command) {
        String ret = null;
        switch (command) {
            case START_COMMAND_CHECKING:
                ret = "START_COMMAND_CHECKING";
                break;
            case START_COMMAND_NEW_VERSION_CHECKING:
                ret = "START_COMMAND_NEW_VERSION_CHECKING";
                break;
            case START_COMMAND_DOWNLOAD:
                ret = "START_COMMAND_DOWNLOAD";
                break;
            case START_COMMAND_DOWNLOAD_PAUSE:
                ret = "START_COMMAND_DOWNLOAD_PAUSE";
                break;
            case START_COMMAND_DOWNLOAD_CONTINUE:
                ret = "START_COMMAND_DOWNLOAD_CONTINUE";
                break;
            case START_COMMAND_INSTALL:
                ret = "START_COMMAND_INSTALL";
                break;
            case START_COMMAND_STOP_SERVICE:
                ret = "START_COMMAND_STOP_SERVICE";
                break;
            case START_COMMAND_FILE_CHECKING:
                ret = "START_COMMAND_FILE_CHECKING";
                break;
            case START_COMMAND_FILE_CHECKING_STOP:
                ret = "START_COMMAND_FILE_CHECKING_STOP";
                break;
            case START_COMMAND_SILENT_INSTALL:
                ret = "START_COMMAND_SILENT_INSTALL";
                break;
            case START_COMMAND_REAL_INSTALL_ROM:
                ret = "START_COMMAND_REAL_INSTALL_ROM";
                break;
            default:
                break;
        }
        return ret;
    }

    public class BatteryReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            downgradeManager.setBatteryLevel(intent.getIntExtra(BatteryManager.EXTRA_LEVEL, 0));
        }
    }
}
