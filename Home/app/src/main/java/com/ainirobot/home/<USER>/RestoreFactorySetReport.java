package com.ainirobot.home.bi;

import android.util.Log;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

public class RestoreFactorySetReport extends BaseBiReport {

    private static final String TABLE_NAME = "gb_recovery_error";
    private static final String ERROR_CODE = "errorCode";
    private static final String FAILED_REASON = "failed_reason";
    private static final String CTIME = "ctime";

    public RestoreFactorySetReport() {
        super(TABLE_NAME);
        initData();
    }

    private void initData() {
        addData(ERROR_CODE, "");
        addData(FAILED_REASON, "");
        addData(CTIME, "");
    }

    public void addErrorCode(String errorCode) {
        addData(ERROR_CODE, errorCode);

    }

    public void addFailedReason(String failedReason) {
        addData(FAILED_REASON, failedReason);

    }

    private void addCTime() {
        addData(CTIME, System.currentTimeMillis());
    }

    @Override
    public void report() {
        addCTime();
        super.report();
    }
}
