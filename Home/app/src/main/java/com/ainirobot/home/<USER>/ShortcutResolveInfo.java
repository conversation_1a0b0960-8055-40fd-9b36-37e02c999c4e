package com.ainirobot.home.bean;

import android.content.pm.ResolveInfo;
import android.graphics.drawable.Drawable;

/**
 * author : hu<PERSON><PERSON><PERSON><PERSON>
 * create date : 5/23/23
 * description :
 */
public class ShortcutResolveInfo extends ResolveInfo {

    private String shortcutId = "";
    private Drawable shortcutIcon ;
    private String label = "";
    private String targetPackage = "";
    private String targetClass = "";
    private String action = "";

    public ShortcutResolveInfo() {
    }

    public ShortcutResolveInfo(String shortcutId, Drawable icon, String label, String targetPackage, String targetClass, String action) {
        this.shortcutId = shortcutId;
        this.shortcutIcon = icon;
        this.label = label;
        this.targetPackage = targetPackage;
        this.targetClass = targetClass;
        this.action = action;
    }

    public String getShortcutId() {
        return shortcutId;
    }

    public void setShortcutId(String shortcutId) {
        this.shortcutId = shortcutId;
    }

    public Drawable getShortcutIcon() {
        return shortcutIcon;
    }

    public void setShortcutIcon(Drawable shortcutIcon) {
        this.shortcutIcon = shortcutIcon;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getTargetPackage() {
        return targetPackage;
    }

    public void setTargetPackage(String targetPackage) {
        this.targetPackage = targetPackage;
    }

    public String getTargetClass() {
        return targetClass;
    }

    public void setTargetClass(String targetClass) {
        this.targetClass = targetClass;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    @Override
    public String toString() {
        return "ShortcutResolveInfo{" +
                "shortcutId='" + shortcutId + '\'' +
                ", shortcutIcon=" + shortcutIcon +
                ", label='" + label + '\'' +
                ", targetPackage='" + targetPackage + '\'' +
                ", targetClass='" + targetClass + '\'' +
                ", action='" + action + '\'' +
                '}';
    }
}
