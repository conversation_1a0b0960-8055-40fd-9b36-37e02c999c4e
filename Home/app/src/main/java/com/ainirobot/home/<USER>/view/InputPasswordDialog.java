/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.home.ui.view;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.text.Editable;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.home.R;
import com.ainirobot.home.utils.Blur;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class InputPasswordDialog extends Dialog implements View.OnClickListener {
    private static final String TAG = InputPasswordDialog.class.getSimpleName();

    private static final int TIME_TO_DISMISS = 15 * 1000;

    private Bitmap mBlurImage;
    private Context mContext;
    private EditText mEditTextPassword;
    private TextView mTimeClock;
    private ViewGroup mLayoutPasswordDisplay;
    private Handler mUIHandler = new Handler();
    private DialogCallback mDialogCallback;
    private boolean mUseMd5;

    private final Runnable mAutoDismissRunnable = new Runnable() {
        @Override
        public void run() {
            dismiss();
        }
    };

    public InputPasswordDialog(Context context, int themeResId, Bitmap bitmap) {
        super(context, themeResId);
        mContext = context;
        mBlurImage = bitmap;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        View view = LayoutInflater.from(mContext).inflate(R.layout.restore_password_dialog, null);
        setContentView(view);

        if (getWindow() != null) {
            getWindow().setBackgroundDrawableResource(R.color.transparent);
            BitmapDrawable bitmapDrawable = new BitmapDrawable(getContext().getResources(),
                    mBlurImage.copy(mBlurImage.getConfig(), true));
            getWindow().setBackgroundDrawable(bitmapDrawable);
            getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
        }
        mTimeClock = (TextView)findViewById(R.id.time_clock);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm");
        String currentDate = dateFormat.format(new Date(System.currentTimeMillis()));
        mTimeClock.setText(currentDate);
        mTimeClock.setVisibility(mUseMd5 ? View.VISIBLE : View.INVISIBLE);
        mEditTextPassword = (EditText) findViewById(R.id.input_password_edit_text);
        (findViewById(R.id.cancel_input_password)).setOnClickListener(this);

        mLayoutPasswordDisplay = (ViewGroup) findViewById(R.id.layout_password_display);

        ((CheckBox) findViewById(R.id.password_show_hide_checkbox))
                .setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                    @Override
                    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                        for (int i = 0; i < mLayoutPasswordDisplay.getChildCount(); i++) {
                            ((TextView) mLayoutPasswordDisplay.getChildAt(i))
                                    .setInputType(isChecked ? (InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD)
                                            : (InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_PASSWORD));
                        }
                    }
                });
        mEditTextPassword.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                final int childCount = mLayoutPasswordDisplay.getChildCount();
                for (int i = 0; i < childCount; i++) {
                    if (i < s.length()) {
                        ((TextView) mLayoutPasswordDisplay.getChildAt(i)).setText(s.subSequence(i, i + 1));
                    } else {
                        ((TextView) mLayoutPasswordDisplay.getChildAt(i)).setText(null);
                    }
                }

                if (s.length() == childCount) {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHH");
                    String passWord = RobotSettings.getSystemSn() + InputPasswordDialog.getCurrentDate(dateFormat);
                    final String md5PassWord = InputPasswordDialog.md5(passWord);
                    if (mUseMd5 && md5PassWord.substring(Math.max(0, md5PassWord.length() - childCount)).equalsIgnoreCase(s.toString())) {
                        if (mDialogCallback != null) {
                            mDialogCallback.confirm();
                        }
                        dismiss();
                    } else if (!mUseMd5 && RobotSettings.getSystemSn().endsWith(s.toString().toUpperCase())) {
                        if (mDialogCallback != null) {
                            mDialogCallback.confirm();
                        }
                        dismiss();
                    } else {
                        Toast toast = new Toast(mContext);

                        toast.setView(getLayoutInflater().inflate(
                                R.layout.restore_password_err_toast, null, false));
                        toast.setDuration(Toast.LENGTH_SHORT);
                        toast.setGravity(Gravity.CENTER, 0, 0);
                        toast.show();

                        mUIHandler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                mEditTextPassword.setText("");
                            }
                        }, 500);
                    }
                }

                resetAutoDismissTimer();
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
    }

    private void resetAutoDismissTimer() {
        mUIHandler.removeCallbacks(mAutoDismissRunnable);
        mUIHandler.postDelayed(mAutoDismissRunnable, TIME_TO_DISMISS);
    }

    @Override
    public void show() {
        resetAutoDismissTimer();
        super.show();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.cancel_input_password:
                dismiss();
                break;
            default:
                Log.d(TAG, "no process");
                break;
        }
    }

    public void setUseMd5(boolean useMd5) {
        mUseMd5 = useMd5;
    }

    public interface DialogCallback {
        void confirm();
    }

    public void setCallback(DialogCallback callback) {
        this.mDialogCallback = callback;
    }

    public static String md5(String text) {
        byte[] secretBytes = null;
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            //对字符串进行加密
            md.update(text.getBytes());
            //获得加密后的数据
            secretBytes = md.digest();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5 does not exist");
        }
        //将加密后的数据转换为16进制数字
        String md5code = new BigInteger(1, secretBytes).toString(16);
        // 如果生成数字未满32位，需要前面补0
        for (int i = 0; i < 32 - md5code.length(); i++) {
            md5code = "0" + md5code;
        }
        return md5code;
    }

    public static String getCurrentDate(SimpleDateFormat dateFormat){
        long l = System.currentTimeMillis();
        Date date = new Date(l);
        String currentDate = dateFormat.format(date);

        return currentDate;
    }
}
