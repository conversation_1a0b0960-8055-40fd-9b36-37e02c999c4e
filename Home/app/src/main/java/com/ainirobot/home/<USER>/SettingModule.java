/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.home.module;

import android.app.Service;
import android.content.Context;
import android.media.AudioManager;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.utils.AudioCalUtils;
import com.ainirobot.home.utils.WakeUpWordsUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;


public class SettingModule extends BaseBackGroundModule {
    private final static String TAG = SettingModule.class.getSimpleName();
    private static final int MESSAGE_CUSTOM_WAKEUP_WORD = 1;

    private Context mContext;
    private static SettingModule mSettingModule;
    private final static String VOLUME = "Volume";

    private Handler mMainHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MESSAGE_CUSTOM_WAKEUP_WORD:
                    WakeUpWordsUtils.getInstance().showWakeUpWordSettingDialog(mContext);
                    break;
                default:
                    break;
            }
        }
    };

    public SettingModule() {
        super("SettingModuleThread");
    }

    public static SettingModule getInstance() {
        if (mSettingModule == null) {
            mSettingModule = new SettingModule();
        }
        return mSettingModule;
    }

    public void init(Context mContext) {
        this.mContext = mContext;
    }


    @Override
    protected void onThreadNewSemantics(int reqId, String intent, String text, String params) {
        Log.i(TAG, "handle semantics reqId:" + reqId + " intent:" + intent + " text:" + text + " " +
                "params:" + params);
        Log.d(TAG, "reqId=" + reqId + "; intent=" + intent + "; params=" + params);

        if (!ModuleDef.INTENT_CUSTOM_WAKEUP_WORD.equals(intent)
            && paramsForAudio(params)
            && !getVolumeControlValue()) {
            Log.i(TAG, "volume control disabled, so ignore");
            SkillManager.getInstance().speechPlayText(mContext.getString(R.string.volume_set_ignore_hint));
            stop();
            return;
        }

        if (!ModuleDef.INTENT_CUSTOM_WAKEUP_WORD.equals(intent) && !paramsForAudio(params)) {
            return;
        }

        AudioManager mAudioManager = (AudioManager) mContext.getSystemService(Service.AUDIO_SERVICE);
        switch (intent) {
            case ModuleDef.AUDIO_UP_MSG:
                if (mAudioManager.getStreamVolume(AudioManager.STREAM_MUSIC) >=
                        mAudioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)) {
                    SkillManager.getInstance().speechPlayText(mContext.getString(R.string.volume_hight));
                } else {
                    int curGradeIndex = AudioCalUtils.getCurAudioIndex(
                            mAudioManager.getStreamVolume(AudioManager.STREAM_MUSIC));
                    int value = AudioCalUtils.getProperAudioIndex(curGradeIndex + 1);
                    AudioCalUtils.setAudioLevelNum(mAudioManager, AudioCalUtils.soundGrade[value]
                            , AudioManager.STREAM_MUSIC);
                    // SkillManager.getInstance().speechPlayText(mContext.getString(R.string.volume_up_done));
                }
                stop();
                break;

            case ModuleDef.AUDIO_DOWN_MSG:
                if (mAudioManager.getStreamVolume(AudioManager.STREAM_MUSIC) <=
                        AudioCalUtils.MIN_AUDIO_NUM) {
                    SkillManager.getInstance().speechPlayText(mContext.getString(R.string.volume_low));
                } else {
                    int curGradeIndex = AudioCalUtils.getCurAudioIndex(
                            mAudioManager.getStreamVolume(AudioManager.STREAM_MUSIC));
                    int value = AudioCalUtils.getProperAudioIndex(curGradeIndex - 1);
                    AudioCalUtils.setAudioLevelNum(mAudioManager,
                            AudioCalUtils.soundGrade[value], AudioManager.STREAM_MUSIC);
                    // SkillManager.getInstance().speechPlayText(mContext.getString(R.string.volume_down_done));
                }
                stop();
                break;
            case ModuleDef.AUDIO_SET_NUM_MSG:
                int audioType = AudioManager.STREAM_MUSIC;
                int curGradeIndex = AudioCalUtils.getCurAudioIndex(
                        mAudioManager.getStreamVolume(AudioManager.STREAM_MUSIC));
                int value = AudioCalUtils.getValueFromParams(params, mAudioManager, audioType);
                AudioCalUtils.setAudioLevelNum(mAudioManager, AudioCalUtils.soundGrade[value], audioType);
                if (curGradeIndex == value) {
                    if (value == AudioCalUtils.MIN_AUDIO_NUM) {
                        SkillManager.getInstance().speechPlayText(mContext.getString(R.string.volume_low));
                    } else if (value == AudioCalUtils.MAX_AUDIO_NUM) {
                        SkillManager.getInstance().speechPlayText(mContext.getString(R.string.volume_hight));
                    } else {
                        SkillManager.getInstance().speechPlayText(mContext.getString(R.string.volume_set_done));
                    }
                } else if (value > curGradeIndex) {
                    // SkillManager.getInstance().speechPlayText(mContext.getString(R.string.volume_up_done));
                } else if (value < curGradeIndex) {
                    // SkillManager.getInstance().speechPlayText(mContext.getString(R.string.volume_down_done));
                }
                stop();
                break;
            case ModuleDef.AUDIO_MAX:
                if (mAudioManager.getStreamVolume(AudioManager.STREAM_MUSIC) >=
                        mAudioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)) {
                    SkillManager.getInstance().speechPlayText(mContext.getString(R.string.volume_hight));
                } else {
                    AudioCalUtils.setAudioLevelNum(mAudioManager, AudioCalUtils.soundGrade[10],
                            AudioManager.STREAM_MUSIC);
                    SkillManager.getInstance().speechPlayText(mContext.getString(R.string.volume_set_done));
                }
                stop();
                break;
            case ModuleDef.AUDIO_MIN:
                if (mAudioManager.getStreamVolume(AudioManager.STREAM_MUSIC) <=
                        AudioCalUtils.MIN_AUDIO_NUM) {
                    SkillManager.getInstance().speechPlayText(mContext.getString(R.string.volume_low));
                } else {
                    AudioCalUtils.setAudioLevelNum(mAudioManager, AudioCalUtils.soundGrade[1],
                            AudioManager.STREAM_MUSIC);
                    SkillManager.getInstance().speechPlayText(mContext.getString(R.string.volume_set_done));
                }
                break;
            case ModuleDef.INTENT_CUSTOM_WAKEUP_WORD:
                mMainHandler.sendEmptyMessage(MESSAGE_CUSTOM_WAKEUP_WORD);
                break;
            default:
                break;
        }
    }

    private boolean paramsForAudio(String params) {
        if (ProductInfo.isDeliveryOverSea()){
            return true;
        }
        try {
            JSONObject object = new JSONObject(params);
            JSONObject jsonSecond = new JSONObject(object.getString("slots"));
            if(!jsonSecond.has("command_param")){
                return true;
            }
            JSONArray arr = new JSONArray(jsonSecond.getString("command_param"));
            String valueStr = arr.getJSONObject(0).getString("value");
            Log.d(TAG, "params for audio, valueStr = " + valueStr);
            if (!TextUtils.isEmpty(valueStr) && VOLUME.equals(valueStr)) {
                return true;
            } else {
                return false;
            }
        } catch (JSONException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * get volume control from setting(default:true)
     * control audio up or down
     *
     * @return true: valid false:invalid
     */
    private boolean getVolumeControlValue() {
        int value = RobotSettingApi.getInstance().
                getRobotInt(Definition.ROBOT_SETTING_DISABLE_VOICE_CONTROL_VOLUME);
        Log.i(TAG, "volume control value = " + value);
        return value == 0;
    }


    @Override
    protected void onStop() {
        super.onStop();
    }
}
