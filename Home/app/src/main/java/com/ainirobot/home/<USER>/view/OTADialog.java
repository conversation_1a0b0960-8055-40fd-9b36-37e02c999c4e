/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.home.ui.view;

import android.animation.ValueAnimator;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Outline;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.support.annotation.NonNull;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LinearInterpolator;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.R;
import com.ainirobot.home.utils.ResUtil;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * global dialog
 * need permission
 * upgrade conditions:50% power
 * showDialog and dismissDialog alpha animation
 */
public class OTADialog extends Dialog {

    private TextView mVersion;
    private TextView mOtaUpgradeContent;
    private TextView mOtaUpgradeComplete;
    private TextView mUpdateNextTime;
    private TextView mUpdateNow;
    private TextView mTitle;
    private View mVerticalLine;
    private OnUpdateListener mOnUpdateListener;
    private static final String TAG = "OTADialog:Home";
    private final View mOtaDialogView;
    private boolean mFirstPowerBroadcast = true;
    private ValueAnimator mValueAnimator;
    private boolean mIsForce;
    private boolean isOta = true;
    private String mOtaType = Definition.OTA_TARGET_TYPE_UPDATE; // 默认为升级
    private String mVersionCode;

    public OTADialog setOnUpdateListener(OnUpdateListener mOnUpdateListener) {
        this.mOnUpdateListener = mOnUpdateListener;
        return this;
    }

    /**
     * update version
     */
    public OTADialog setVersionText(String text) {
        this.mVersionCode = text;
        this.mVersion.setText(text);
        return this;
    }

    /**
     * update version description
     */
    public OTADialog setUpgradeContentText(String text) {
        if (TextUtils.isEmpty(text)) {
            mOtaUpgradeContent.setVisibility(View.GONE);
        } else {
            if (mOtaType.equals(Definition.OTA_TARGET_TYPE_ROLL_BACK)) {
                mTitle.setText(getContext().getResources().getString(R.string.find_history_version));
                mOtaUpgradeContent.setText(ResUtil.getString(R.string.ota_downgrade_content, text));
            } else {
                setParagraphSpace(mOtaUpgradeContent, text, 7);
            }
            mOtaUpgradeContent.setVisibility(View.VISIBLE);
        }
        return this;
    }

    /**
     * update ota type
     */
    public OTADialog setOtaType(String otaType) {
        this.mOtaType = otaType;
        return this;
    }

    private void setParagraphSpace(TextView textView, String text, int size) {
        String formattedText = text.replaceAll("\n", "\n\n");
        SpannableString spannableString = new SpannableString(formattedText);

        Matcher matcher = Pattern.compile("\n\n").matcher(formattedText);
        while (matcher.find()) {
            spannableString.setSpan(new AbsoluteSizeSpan(size, true), matcher.start() + 1, matcher.end(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        textView.setText(spannableString);
    }

    public OTADialog isForce(boolean mIsForce) {
        if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) {
            //TODO OTA优化
            return this;
        }
        this.mIsForce = mIsForce;
        if (mIsForce) {
            mUpdateNextTime.setVisibility(View.GONE);
            mVerticalLine.setVisibility(View.GONE);
            ViewGroup.LayoutParams layoutParams = mUpdateNow.getLayoutParams();
            layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT;
            mUpdateNow.setLayoutParams(layoutParams);
        }
        return this;
    }

    public OTADialog(@NonNull Context context) {
        this(context, true);
    }

    public OTADialog(@NonNull Context context, boolean isOta) {
        super(context, R.style.OTADialog);
        this.isOta = isOta;
        mOtaDialogView = LayoutInflater.from(context).inflate(R.layout.ota_dialog, null);
        setContentView(mOtaDialogView);
        Window window = getWindow();
        Log.i(TAG, "height = " + window.getWindowManager().getDefaultDisplay().getHeight());
        if (window == null) return;
        WindowManager.LayoutParams p = window.getAttributes();  //获取对话框当前的参数值
        p.width = 950;
        int mOrientation = ApplicationWrapper.getContext().getResources().getConfiguration().orientation;
        if (mOrientation == Configuration.ORIENTATION_LANDSCAPE){
            p.height = 800;
        }else {
            p.height = WindowManager.LayoutParams.WRAP_CONTENT;
        }
        window.setAttributes(p);
        setCanceledOnTouchOutside(false);

        initView();
        initListener();
        upgradeCondition();
    }


    private void startAnimation(int resAnimation, final boolean animationEndDismiss) {
        Animation animation = AnimationUtils.loadAnimation(getContext(), resAnimation);
        mOtaDialogView.clearAnimation();
        mOtaDialogView.setAnimation(animation);
        animation.start();
        animation.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                if (animationEndDismiss) {
                    OTADialog.super.dismiss();
                }
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
    }

    private void permission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(getContext())) {
                Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:" + getContext().getPackageName()));
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                getContext().startActivity(intent);
                onStop();
            } else {
                // Already hold the SYSTEM_ALERT_WINDOW permission,
                Log.e(TAG, "has ACTION_MANAGE_OVERLAY_PERMISSION");
                super.show();
            }
        }
    }


    @Override
    public void show() {
        startAnimation(R.anim.ota_alpha_enter, false);
        if (!(getContext() instanceof Activity)) {
            Window window = getWindow();
            if (window != null) {
                window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_DIALOG);
                window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
            }
        }
        permission();
    }


    @Override
    public void dismiss() {
        startAnimation(R.anim.ota_alpha_out, true);
    }

    @Override
    protected void onStop() {
        super.onStop();
        mFirstPowerBroadcast = false;
        if (mValueAnimator != null && mValueAnimator.isRunning()) {
            Log.w(TAG, "count down cancel");
            mValueAnimator.cancel();
        }
        Log.i(TAG, "OTADialog onDismiss");
    }

    private void initListener() {
        mUpdateNextTime.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (mOnUpdateListener != null) {
                    mOnUpdateListener.onUpdateNextTime(OTADialog.this);
                }
            }
        });
        mUpdateNow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (mOnUpdateListener != null) {
                    mOnUpdateListener.onUpdateNow(OTADialog.this);
                }
            }
        });
    }

    private void initView() {
        mVersion = (TextView) mOtaDialogView.findViewById(R.id.version);
        mOtaUpgradeContent = (TextView) mOtaDialogView.findViewById(R.id.ota_upgrade_content);
        mOtaUpgradeComplete = (TextView) mOtaDialogView.findViewById(R.id.ota_upgrade_complete);
        mUpdateNextTime = (TextView) mOtaDialogView.findViewById(R.id.upgrade_next_time);
        mUpdateNow = (TextView) mOtaDialogView.findViewById(R.id.upgrade_now);
        mVerticalLine = mOtaDialogView.findViewById(R.id.vertical_view_line);
        mTitle = mOtaDialogView.findViewById(R.id.find_new_version);
        //clipping to round rect for background
        View view = findViewById(R.id.view_root);
        ViewOutlineProvider viewOutlineProvider = new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                view.setClipToOutline(true);
                outline.setRoundRect(0, 0, view.getWidth(), view.getHeight(), 30);
            }
        };
        view.setOutlineProvider(viewOutlineProvider);
    }

    private void upgradeCondition() {
        if (!mFirstPowerBroadcast) {
            return;
        }

        if (isOta) {
            mOtaUpgradeComplete.setText(getContext().getResources().getString(R.string.ota_upgrade_complete));
            mOtaUpgradeComplete.setTextColor(getContext().getResources().getColor(R.color.ota_upgrade_complete));
            mUpdateNow.setTextColor(getContext().getResources().getColor(R.color.ota_upgrade_now));
            mUpdateNow.setEnabled(true);
            if (mIsForce) {
                mOtaUpgradeComplete.setText(R.string.ota_can_not_use);
            }
        } else {
            if (ProductInfo.isMiniProduct()) {
                mOtaUpgradeComplete.setText(getContext().getResources().getString(R.string.ota_upgrade_no_complete_mini));
            } else {
                mOtaUpgradeComplete.setText(getContext().getResources().getString(R.string.ota_upgrade_no_complete));
            }
            mOtaUpgradeComplete.setTextColor(getContext().getResources().getColor(R.color.ota_upgrade_no_complete));
            mUpdateNow.setTextColor(getContext().getResources().getColor(R.color.ota_upgrade_now));
            mUpdateNow.setEnabled(true);
            //OTAModule.getInstance().sendBroadcastToRemoteControl();
            if (mIsForce) {
                mOtaUpgradeComplete.setText(R.string.ota_log_battery);
            }
            if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) {
                mOtaUpgradeComplete.setText(
                        ProductInfo.isDeliveryProduct() ?
                                getContext().getResources().getString(R.string.ota_upgrade_no_complete_saiph) :
                                getContext().getResources().getString(R.string.ota_upgrade_no_complete_mini));
                mUpdateNow.setVisibility(View.GONE);
                mVerticalLine.setVisibility(View.GONE);
                ViewGroup.LayoutParams layoutParams = mUpdateNextTime.getLayoutParams();
                layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT;
                mUpdateNextTime.setLayoutParams(layoutParams);
                mFirstPowerBroadcast = false;
                return;
            }
        }

        mFirstPowerBroadcast = false;
        //60s count down
        mValueAnimator = ValueAnimator.ofInt(60, 0);
        mValueAnimator.setDuration(60 * 1000);
        mValueAnimator.setInterpolator(new LinearInterpolator());
        mValueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                String orgStrUpgrade;
                if (mIsForce) {
                    orgStrUpgrade = getContext().getResources().getString(R.string.force_upgrade_now);
                } else {
                    orgStrUpgrade = getContext().getResources().getString(R.string.upgrade_now);
                }
                String strUpgrade;
                int value = (int) animation.getAnimatedValue();
                if (isOta) {
                    strUpgrade = String.format(orgStrUpgrade, getContext().getString(R.string.upgrade), value);
                } else {
                    strUpgrade = String.format(orgStrUpgrade, getContext().getString(R.string.charge), value);
                }
                mUpdateNow.setText(strUpgrade);
                if (value == 0) {
                    if (mOnUpdateListener != null) {
                        mOnUpdateListener.onUpdateNow(OTADialog.this);
                        //count down end
                        Log.i(TAG, "60S count down end");
                    }
                    dismiss();
                }
            }
        });
        mValueAnimator.start();
    }


    public interface OnUpdateListener {
        void onUpdateNextTime(OTADialog dialog);

        void onUpdateNow(OTADialog dialog);
    }



}
