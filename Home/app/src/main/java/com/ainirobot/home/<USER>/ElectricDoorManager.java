package com.ainirobot.home.control;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.home.bean.ElectricDoorStatus;
import com.google.gson.Gson;

/**
 * 电门管理类
 * <p>
 * 已知的电门操作异常情况：
 * 1.开的过程中触发关闭，或者关闭的过程中触发开启，四个电门的操作是不一致的。所以这里只允许四个门统一开关。
 * 2.电门关闭过程中被卡住，会自动打开，这个时候需要重新关闭。
 * 3.电门打开过程中被卡住，会卡在当前位置，这个时候需要重新打开。
 * </>
 */
public class ElectricDoorManager {
    private static final String TAG = "ElectricDoorManager:Home";

    private volatile ElectricDoorStatusListener mElectricDoorStatusListener;
    private volatile DoorStatus mDoorStatus = DoorStatus.CLOSE;
    private volatile boolean mStopGetStatus = false;
    private long mLastTime = 0;

    public enum DoorStatus {
        OPEN, CLOSE, OPENING, CLOSING,
    }

    //单例
    private static ElectricDoorManager sInstance = null;

    public static ElectricDoorManager getInstance() {
        if (sInstance == null) {
            sInstance = new ElectricDoorManager();
        }
        return sInstance;
    }

    public void init(ElectricDoorStatusListener electricDoorStatusListener) {
        Log.d(TAG, "init: ----------------------> ");
        mStopGetStatus = false;
        registerElectricDoorStatusListener(electricDoorStatusListener);
        //启动子线程，每隔1s获取一次电门状态
        new Thread(new Runnable() {
            @Override
            public void run() {
                while (!mStopGetStatus) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    getElectricDoorStatus();
                }
            }
        }).start();//启动线程
        getElectricDoorStatus();
    }

    public void release() {
        Log.d(TAG, "release:---------------------> ");
        mStopGetStatus = true;
        setDoorStatus(DoorStatus.CLOSE);//release,将电门状态设置为关闭
        unregisterElectricDoorStatusListener();
    }

    public void switchDoorStatus() {
        Log.d(TAG, "switchDoorStatus: current=" + mDoorStatus);
        if (mDoorStatus == DoorStatus.OPEN) {
            closeAll();
        } else if (mDoorStatus == DoorStatus.CLOSE) {
            openAll();
        }
    }

    public boolean isAllOpen() {
        return mDoorStatus == DoorStatus.OPEN;
    }

    public boolean isAllClose() {
        return mDoorStatus == DoorStatus.CLOSE;
    }

    private void openAll() {
        Log.d(TAG, "openAll: " + mDoorStatus);
        if (mDoorStatus == DoorStatus.CLOSE || mDoorStatus == DoorStatus.CLOSING) {
            setDoorStatus(DoorStatus.OPENING);//openAll,设置为正在打开
            openAllElectricDoor();
        }
    }

    private void closeAll() {
        Log.d(TAG, "closeAll: " + mDoorStatus);
        if (mDoorStatus == DoorStatus.OPEN || mDoorStatus == DoorStatus.OPENING) {
            setDoorStatus(DoorStatus.CLOSING);//closeAll,设置为正在关闭
            closeAllElectricDoor();
        }
    }

    private void setDoorStatus(DoorStatus status) {
        Log.d(TAG, "setDoorStatus: current=" + mDoorStatus + " new=" + status);
        if (status != mDoorStatus) {
            mDoorStatus = status;
            onElectricDoorStatusChanged(status);
            if (mDoorStatus == DoorStatus.OPENING || mDoorStatus == DoorStatus.CLOSING) {
                mLastTime = System.currentTimeMillis();
            }
        }
    }

    private synchronized void getElectricDoorStatus() {
        SystemApi.getInstance().getElectricDoorStatus(0, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                Log.d(TAG, "getElectricDoorStatus: result=" + result + "; message=" + message + "; extraData=" + extraData);
                if (result == 1 && !TextUtils.isEmpty(message)) {
                    ElectricDoorStatus status;
                    try {
                        status = new Gson().fromJson(message, ElectricDoorStatus.class);
                        if (status != null) {
                            if (status.isAllDoorOpen()) {
                                setDoorStatus(DoorStatus.OPEN);//监听状态，已全部打开
                            } else if (status.isAllDoorClosed()) {
                                setDoorStatus(DoorStatus.CLOSE);//监听状态，已全部关闭
                            } else {
                                //异常状态处理，开关过程中，每隔10s检查并重置状态
                                if (mDoorStatus == DoorStatus.OPENING || mDoorStatus == DoorStatus.CLOSING) {
                                    if (System.currentTimeMillis() - mLastTime > 10000) {
                                        Log.d(TAG, "getElectricDoorStatus: isDoorOpenAndClose，处理状态不一致情况！");
                                        setDoorStatus(status.isAllDoorOpen() ? DoorStatus.OPEN : DoorStatus.CLOSE);
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        });
    }

    private void openAllElectricDoor() {
        Log.d(TAG, "openAllElectricDoor: ");
        SystemApi.getInstance().setElectricDoorCtrl(0, Definition.CAN_DOOR_ALL_OPEN, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                Log.d(TAG, "openAllElectricDoor: result=" + result + "; message=" + message + "; extraData=" + extraData);
            }
        });
    }

    private void closeAllElectricDoor() {
        Log.d(TAG, "closeAllElectricDoor: ");
        SystemApi.getInstance().setElectricDoorCtrl(0, Definition.CAN_DOOR_ALL_CLOSE, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                Log.d(TAG, "closeAllElectricDoor: result=" + result + "; message=" + message + "; extraData=" + extraData);
            }
        });
    }

    /**
     * 电门状态变化回调
     */
    public interface ElectricDoorStatusListener {
        void onElectricDoorStatusChanged(DoorStatus status);
    }

    /**
     * 注册电门状态变化监听
     */
    private void registerElectricDoorStatusListener(ElectricDoorStatusListener electricDoorStatusListener) {
        mElectricDoorStatusListener = electricDoorStatusListener;
    }

    /**
     * 取消注册电门状态变化监听
     */
    private void unregisterElectricDoorStatusListener() {
        mElectricDoorStatusListener = null;
    }

    /**
     * 电门状态变化回调
     */
    private void onElectricDoorStatusChanged(DoorStatus doorStatus) {
        if (mElectricDoorStatusListener != null) {
            mElectricDoorStatusListener.onElectricDoorStatusChanged(doorStatus);
        }
    }

}
