package com.ainirobot.home.control;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.IntDef;
import android.util.Log;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.R;
import com.ainirobot.home.utils.ResUtil;

public class LowBatteryManager {

    public void startReceiver() {
        IntentFilter batteryFilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
        ApplicationWrapper.getContext().registerReceiver(new LowBatteryReceiver(), batteryFilter);
    }

    public static class LowBatteryReceiver extends BroadcastReceiver {

        private static final String TAG = "LowBatteryReceiver";

        private static final int LOW_BATTERY_LEVEL = 5;

        private static final int TIPS_INTERVAL = 30000;

        private Handler mHandler;

        @BatteryStatus
        private int mBatteryStatus;

        private Runnable mTTSRunnable = new Runnable() {
            @Override
            public void run() {
                if (BatteryStatus.Low == mBatteryStatus) {
                    if (!ProductInfo.isDeliveryProduct() && !ProductInfo.isMeissaPlus()) {
                        SkillManager.getInstance().speechPlayText(ResUtil.getString(R.string.charge_low_tips));
                        lowBatteryTips(TIPS_INTERVAL);
                    }
                }
            }
        };

        public LowBatteryReceiver() {
            mHandler = new Handler(Looper.getMainLooper());
            mBatteryStatus = BatteryStatus.Normal;
        }

        @Override
        public void onReceive(final Context context, final Intent intent) {
            int level = intent.getIntExtra(android.os.BatteryManager.EXTRA_LEVEL, 0);
            int scale = intent.getIntExtra(android.os.BatteryManager.EXTRA_SCALE, 100);
            int plugged = intent.getIntExtra(android.os.BatteryManager.EXTRA_PLUGGED, -1);
            int batteryPct = level * 100 / scale;

            int batteryStatus;
            if (plugged != 0) {
                batteryStatus = BatteryStatus.Charging;
            } else {
                if (batteryPct <= LOW_BATTERY_LEVEL) {
                    batteryStatus = BatteryStatus.Low;
                } else {
                    batteryStatus = BatteryStatus.Normal;
                }
            }

            if (batteryStatus != mBatteryStatus) {
                Log.d(TAG, String.format("onReceive: battery status change, level = %d, scale = %d ,batteryPct = %d, plugged = %d, batteryStatus = %d",
                        level, scale, batteryPct, plugged, batteryStatus));
                mBatteryStatus = batteryStatus;
                lowBatteryTips(0);
            }
        }

        private void lowBatteryTips(final int interval) {
            if (BatteryStatus.Low == mBatteryStatus) {
                mHandler.postDelayed(mTTSRunnable, interval);
            } else {
                mHandler.removeCallbacks(mTTSRunnable);
            }
        }
    }

    @IntDef({BatteryStatus.Charging, BatteryStatus.Normal, BatteryStatus.Low})
    private @interface BatteryStatus {
        int Charging = 0;
        int Normal = 1;
        int Low = 2;
    }
}
