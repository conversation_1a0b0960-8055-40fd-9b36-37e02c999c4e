package com.ainirobot.home.ui.fragment;

import android.os.Bundle;
import android.support.annotation.BoolRes;
import android.support.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.ui.UIController;

public class MapDriftFragment extends BaseFragment {
    private final String TAG = "MapDriftFragment:Home";

    private View mLayoutTip;
    private View mLayoutLoading;
    private TextView mTvLoadingTip;
    private TextView mBtnStartReposition;


    public MapDriftFragment() {

    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        Log.d(TAG, "on create view");
        View view = inflater.inflate(R.layout.fragment_map_drift, container, false);
        initView(view);
        return view;
    }

    private void initView(View view) {
        mLayoutTip = view.findViewById(R.id.layout_tip);
        mLayoutLoading = view.findViewById(R.id.layout_loading);
        mTvLoadingTip = view.findViewById(R.id.tv_loading_tip);
        mBtnStartReposition = view.findViewById(R.id.btn_start_reposition);

        mBtnStartReposition.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mLayoutTip.setVisibility(View.GONE);
                mLayoutLoading.setVisibility(View.VISIBLE);
                ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_MAP_DRIFT_START_DROP_POSITION);
            }
        });

        mLayoutTip.setVisibility(View.VISIBLE);
//        mLayoutLoading.setVisibility(View.GONE);

    }

    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
        Log.d(TAG, "type: " + type + ", message: " + message);
        switch (type) {
            case MAP_DRIFT_STATUS_CHECK_RADAR:
                mTvLoadingTip.setText(R.string.map_drift_check_radar_status);
                break;

            case MAP_DRIFT_STATUS_CHECK_ESTIMATE:
                mTvLoadingTip.setText(R.string.map_drift_check_estimate_status);
                break;

            case MAP_DRIFT_STATUS_RELOAD_CURRENT_MAP:
                mTvLoadingTip.setText(R.string.map_drift_reload_map);
                break;

            case MAP_DRIFT_STATUS_START_REPOSITION:
                mTvLoadingTip.setText(R.string.map_drift_start_reposition);
                break;

            case MAP_DRIFT_STATUS_RELOAD_MAP_FAIL:
                mTvLoadingTip.setText(R.string.map_drift_reload_map_fial);
                break;

            default:
                break;
        }
    }
}
