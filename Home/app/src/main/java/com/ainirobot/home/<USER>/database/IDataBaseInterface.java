package com.ainirobot.home.ota.database;


import com.ainirobot.home.ota.bean.InstallData;
import com.ainirobot.home.ota.bean.VersionData;

import java.util.List;

public interface IDataBaseInterface {
   boolean insert(VersionData dataBean);

    int update(VersionData dataBean);

    VersionData getOsData(String os);

    List<VersionData> getOsData(String[] os);

    List<VersionData> getAllVersion();

    VersionData getOsData(InstallData os);

    List<VersionData> getOsData(InstallData[] os);
}
