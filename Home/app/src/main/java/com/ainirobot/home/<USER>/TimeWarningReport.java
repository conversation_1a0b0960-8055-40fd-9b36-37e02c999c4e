package com.ainirobot.home.bi;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

public class TimeWarningReport extends BaseBiReport {

    public static final int ACTION_SHOW_VIEW = 2;
    public static final int ACTION_SET_NETWORK = 3;
    public static final int ACTION_SKIP = 4;
    public static final int ACTION_SKIP_ALWAYS = 5;

    private static final String TYPE = "type";
    private static final String ACTION = "action";
    private static final String CTIME = "ctime";

    public TimeWarningReport() {
        super("base_robot_check_time");
    }

    public TimeWarningReport addAction(int action) {
        addData(ACTION, action);
        return this;
    }

    @Override
    public void report() {
        addData(TYPE, 0);
        addData(CTIME, System.currentTimeMillis());
        super.report();
    }
}
