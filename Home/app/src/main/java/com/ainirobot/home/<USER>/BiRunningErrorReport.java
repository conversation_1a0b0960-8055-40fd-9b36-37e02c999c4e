package com.ainirobot.home.bi;

import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

public class BiRunningErrorReport extends BaseBiReport {
    private static final String TABLE_NAME = "gb_running_error";
    public static final String CHARGE_TYPE = "充电异常";
    public static final String NAVIGATION_TYPE = "导航异常";

    private static final String CTIME = "ctime";
    private static final String ERROR_TYPE = "error_type";
    private static final String ERROR_MSG = "error_msg";
    private static final String ROM_VERSION = "rom_version";
    private static final String LXC_VERSION = "lxc_version";

    public BiRunningErrorReport() {
        super(TABLE_NAME);
    }

    public BiRunningErrorReport addError(String errorType, String errorMsg) {
        addData(ERROR_TYPE, errorType);
        addData(ERROR_MSG, errorMsg);
        return this;
    }

    @Override
    public void report() {
        addData(CTIME, System.currentTimeMillis());
        addData(ROM_VERSION, RobotSettings.getVersion());
        addData(LXC_VERSION, SystemApi.getInstance().getLxcVersion());
        super.report();
    }
}
