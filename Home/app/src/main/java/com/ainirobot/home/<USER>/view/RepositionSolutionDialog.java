package com.ainirobot.home.ui.view;

import android.animation.ObjectAnimator;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Outline;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.constraint.ConstraintLayout;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewOutlineProvider;
import android.view.WindowManager;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.ainirobot.home.R;

public class RepositionSolutionDialog extends Dialog implements View.OnClickListener{
    private TextView mConfirm;
    private ConstraintLayout mLayout;

    private TextView textViewReason1;
    private String reason1 = "";
    private TextView textViewReason2;
    private String reason2 = "";
    private RelativeLayout layoutReason1;
    private RelativeLayout layoutReason2;

    public RepositionSolutionDialog(@NonNull Context context) {
        super(context);
    }

    public RepositionSolutionDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected RepositionSolutionDialog(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_dialog_reposition_solution);
        WindowManager.LayoutParams p = this.getWindow().getAttributes();
        this.getWindow().setAttributes(p);
        setCanceledOnTouchOutside(false);
        initView();
    }

    private void initView() {
        mLayout = (ConstraintLayout)findViewById(R.id.dialog_reposition_solution);
        mConfirm = (TextView) findViewById(R.id.confirm);
        mConfirm.setOnClickListener(this);

        layoutReason1 = (RelativeLayout) mLayout.findViewById(R.id.layout_reason1);
        if (TextUtils.isEmpty(reason1)) {
            layoutReason1.setVisibility(View.INVISIBLE);
        } else {
            layoutReason1.setVisibility(View.VISIBLE);
            textViewReason1 = (TextView)mLayout.findViewById(R.id.content_reason_1);
            textViewReason1.setText(reason1);
        }
        layoutReason2 = (RelativeLayout) mLayout.findViewById(R.id.layout_reason2);
        if (TextUtils.isEmpty(reason2)) {
            layoutReason2.setVisibility(View.INVISIBLE);
        } else {
            layoutReason2.setVisibility(View.VISIBLE);
            textViewReason2 = (TextView)mLayout.findViewById(R.id.content_reason_2);
            textViewReason2.setText(reason2);
        }
    }

    @Override
    public void show() {
        ObjectAnimator.ofFloat(mLayout,"alpha",0,1)
                .setDuration(170)
                .start();
        super.show();
    }

    @Override
    public void onClick(View v) {
        int vId = v.getId();
        switch (vId){
            case R.id.confirm:
                dismiss();
                break;
            default:
                break;
        }
    }

    public void setReason1(String reason1) {
        this.reason1 = reason1;
    }

    public void setReason2(String reason2) {
        this.reason2 = reason2;
    }

}
