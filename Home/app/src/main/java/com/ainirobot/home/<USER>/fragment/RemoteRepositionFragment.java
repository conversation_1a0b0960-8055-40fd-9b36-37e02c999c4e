package com.ainirobot.home.ui.fragment;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.annotation.TargetApi;
import android.content.Context;
import android.os.Build;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.view.GifView;
import com.ainirobot.home.ui.view.MarkView;
import com.ainirobot.home.ui.view.RepositionCancelDialog;
import com.ainirobot.home.utils.ResType;


public class RemoteRepositionFragment extends BaseFragment implements View.OnClickListener {

    private static final String TAG = "RemoteRepFragment:Home";
    private Context mContext;

    private View mMenualGuideV;
    private View mMenualSearchV;
    private View mSuccessView;
    private View mMenualFailureV;
    private ImageView mSearchImageV;
    private ImageView mSearchShadowIV;
    private MarkView mMarkView;
    private TextView mSuccessTitleView;
    private boolean mAnimateStopped = false;
    private ObjectAnimator mUpAnimator;
    private ObjectAnimator mDownAnimator;
    private AnimatorSet mResetAnimatorSet;
    private AnimatorSet mZoomAnimatorSet;
    private RepositionCancelDialog mDialog;
    private int mOrientation;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        Log.i(TAG, "on create");
        super.onCreate(savedInstanceState);
        mAnimateStopped = false;
        mOrientation = ApplicationWrapper.getContext().getResources().getConfiguration().orientation;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        Log.i(TAG, "on create view");
        View view = initSubViews(inflater);
        showGuideView(false);
        return view;
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        mContext = context;
        Log.d(TAG, "onAttach");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.d(TAG, "onDestroyView");
        if (mDialog != null) {
            mDialog.dismiss();
        }
        destroyAllAnimationAndView();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.reposition_cancel:
            case R.id.reposition_cancel_text:
            case R.id.reposition_failure_cancel:
                clickCancel();
                break;
            default:
                break;
        }
    }

    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
        Log.d(TAG, "onMessage : " + type + ", " + message);
        switch (type) {
            case MSG_REMOTE_CHARGE_STATUS: //charging status
                showGuideView("true".equals(message));
                break;
            case MSG_REMOTE_REPO_ESTIMATE://正在定位中
                showSearchView();
                upThrowAnimator();
                zoomImageViewScale();
                break;
            case MSG_REMOTE_REPO_SUCCESS://定位成功
                mSuccessTitleView.setText(getResources().getString(R.string.reposition_manual_success));
                stopRepositionAnimate();
                showSuccessView();
                break;
            case MSG_REMOTE_REPO_FAILURE://定位失败
                stopRepositionAnimate();
                showFailureView();
                break;
        }
    }

    private View initSubViews(LayoutInflater inflater) {
        View view = inflater.inflate(R.layout.fragment_remote_reposition, null);
        mMenualGuideV = (View) view.findViewById(R.id.locate_guide_remote);
        mMenualSearchV = (View) view.findViewById(R.id.locate_search);
        mSuccessView = (View) view.findViewById(R.id.locate_success);
        mMenualFailureV = (View) view.findViewById(R.id.locate_failure);
        mSearchImageV = (ImageView) view.findViewById(R.id.locate_search_img);
        mSearchShadowIV = (ImageView) view.findViewById(R.id.locate_search_shadow);
        mMarkView = (MarkView) view.findViewById(R.id.locate_success_mark_view);
        mSuccessTitleView = (TextView)view.findViewById(R.id.locate_success_title);

        mMenualGuideV.findViewById(R.id.reposition_cancel_text).setOnClickListener(this);
        mMenualGuideV.findViewById(R.id.reposition_cancel).setOnClickListener(this);
        return view;
    }

    private void destroyAllAnimationAndView() {
        Log.i(TAG, "destroyAllAnimationAndView");
        stopRepositionAnimate();
        mAnimateStopped = false;
        if (mMarkView != null) {
            mMarkView.post(new Runnable() {
                @Override
                public void run() {
                    mMarkView.stopAllAnimate();
                    mMarkView = null;
                }
            });
        }
    }

    private void showGuideView(final boolean charging) {
        Log.i(TAG, "showGuideView, chargin= " + charging);
        mMenualGuideV.post(new Runnable() {
            @Override
            public void run() {
                mMenualGuideV.setVisibility(View.VISIBLE);
                mMenualSearchV.setVisibility(View.GONE);
                mMenualFailureV.setVisibility(View.GONE);
                mSuccessView.setVisibility(View.GONE);
                TextView textView = (TextView) mMenualGuideV.findViewById(R.id.locate_guide_subtitle);
                GifView gifView = (GifView) mMenualGuideV.findViewById(R.id.locate_guide_bg_remote);
                gifView.setVisibility(View.VISIBLE);
                if(charging){
                    Log.i(TAG, "showGuideView, in charging");
                    gifView.setMovieResource(
                            ResType.GIF_SET_CHARGE_SUC.getResIdByType()
                    );
                    textView.setText(R.string.charge_success);
                }else {
                    Log.i(TAG, "showGuideView, finding charge pile");
                    gifView.setMovieResource(
                            ResType.GIF_PUSH_TO_CHARGE_PILE.getResIdByType()
                    );
                    textView.setText(R.string.reposition_reset_remind_msg);
                }
            }
        });
    }

    private void showSearchView() {
        Log.i(TAG, "showSearchView");
        mMenualGuideV.post(new Runnable() {
            @Override
            public void run() {
                mAnimateStopped = false;
                mMenualGuideV.setVisibility(View.GONE);
                mMenualSearchV.setVisibility(View.VISIBLE);
                mMenualFailureV.setVisibility(View.GONE);
                mSuccessView.setVisibility(View.GONE);
            }
        });
    }

    private void showSuccessView() {
        Log.i(TAG, "showSuccessView");
        mMenualGuideV.post(new Runnable() {
            @Override
            public void run() {
                mMenualGuideV.setVisibility(View.GONE);
                mMenualSearchV.setVisibility(View.GONE);
                mMenualFailureV.setVisibility(View.GONE);
                mSuccessView.setVisibility(View.VISIBLE);
                if (mMarkView.getStatus() != MarkView.Status.LoadSuccess) {
                    mMarkView.setStatus(MarkView.Status.LoadSuccess);
                    mMarkView.startAnima();
                }
            }
        });
    }

    private void showFailureView() {
        Log.i(TAG, "showFailureView");
        mMenualSearchV.post(new Runnable() {
            @Override
            public void run() {
                mMenualGuideV.setVisibility(View.GONE);
                mMenualSearchV.setVisibility(View.GONE);
                mMenualFailureV.setVisibility(View.VISIBLE);
                mSuccessView.setVisibility(View.GONE);
            }
        });
    }

    @TargetApi(Build.VERSION_CODES.M)
    private void clickCancel() {
        Log.i(TAG, "showFailureView");
        if (mDialog == null) {
            mDialog = new RepositionCancelDialog(getContext(), R.style.OTADialog)
                    .setDialogClickListener(new RepositionCancelDialog.ClickListener() {
                        @Override
                        public void onConfirmClick() {
                            mDialog = null;
                        }

                        @Override
                        public void onCancelClick() {
                            ControlManager.getControlManager().sendMessageToModule(
                                    ModuleDef.LOCAL_MESSAGE_REPOSITION_CANCEL);
                            mDialog = null;
                        }
                    });
            mDialog.show();
        }
    }

    private void zoomImageViewScale() {
        if (mAnimateStopped) {
            return;
        }
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(mSearchShadowIV, "scaleX", 1.0f, 0.7f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(mSearchShadowIV, "scaleY", 1.0f, 0.7f);
        mZoomAnimatorSet = new AnimatorSet();
        mZoomAnimatorSet.playTogether(scaleX, scaleY);
        mZoomAnimatorSet.setDuration(500);
        mZoomAnimatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (!mAnimateStopped) {
                    resetImageViewScale();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        mZoomAnimatorSet.start();
    }

    private void resetImageViewScale() {
        if (mAnimateStopped) {
            return;
        }
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(mSearchShadowIV, "scaleX", 0.7f, 1.0f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(mSearchShadowIV, "scaleY", 0.7f, 1.0f);
        mResetAnimatorSet = new AnimatorSet();
        mResetAnimatorSet.playTogether(scaleX, scaleY);
        mResetAnimatorSet.setDuration(500);
        mResetAnimatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (!mAnimateStopped) {
                    zoomImageViewScale();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        mResetAnimatorSet.start();
    }

    private void upThrowAnimator() {
        if (mAnimateStopped) {
            return;
        }
        mUpAnimator = ObjectAnimator.ofFloat(mSearchImageV, "translationY", 0, -160);
        mUpAnimator.setDuration(500);
        mUpAnimator.setInterpolator(new DecelerateInterpolator(1.2f));
        mUpAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (!mAnimateStopped) {
                    freeFall();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        mUpAnimator.start();
    }

    private void freeFall() {
        if (mAnimateStopped) {
            return;
        }
        mDownAnimator = ObjectAnimator.ofFloat(mSearchImageV, "translationY", -160, 0);
        mDownAnimator.setDuration(500);
        mDownAnimator.setInterpolator(new AccelerateInterpolator(1.2f));
        mDownAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (mAnimateStopped == false) {
                    upThrowAnimator();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        mDownAnimator.start();
    }

    private void stopRepositionAnimate() {
        Log.i(TAG, "stopRepositionAnimate mAnimateStoppen:" + mAnimateStopped);
        if (mAnimateStopped) {
            return;
        }
        if(mMenualGuideV == null){
            Log.i(TAG, "stopRepositionAnimate mMenualGuideV is null");
            return;
        }
        mMenualGuideV.post(new Runnable() {
            @Override
            public void run() {
                mAnimateStopped = true;
                if (mUpAnimator != null && mUpAnimator.isRunning()) {
                    mUpAnimator.end();
                    mUpAnimator = null;
                }
                if (mDownAnimator != null && mDownAnimator.isRunning()) {
                    mDownAnimator.end();
                    mDownAnimator = null;
                }
                if (mResetAnimatorSet != null && mResetAnimatorSet.isRunning()) {
                    mResetAnimatorSet.end();
                    mResetAnimatorSet = null;
                }
                if (mZoomAnimatorSet != null && mZoomAnimatorSet.isRunning()) {
                    mZoomAnimatorSet.end();
                    mZoomAnimatorSet = null;
                }
            }
        });

    }
}
