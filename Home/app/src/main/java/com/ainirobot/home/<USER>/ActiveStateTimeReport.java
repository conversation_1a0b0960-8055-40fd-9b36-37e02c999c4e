package com.ainirobot.home.bi;

import android.support.annotation.NonNull;
import android.util.Log;

import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

import static com.ainirobot.home.bi.BiGbActiveStateReport.TIME_REPORT;

/**
 * active state time report
 *
 * @version V1.0.0
 * @date 2019/3/6 15:01
 */
public class ActiveStateTimeReport {

    private static final String TAG = "ActiveStateTimeReport";
    private static ActiveStateTimeReport mInstance;
    private static final int CORE_THREAD_NUM = 1;
    private static final int PERIOD = 15;
    private BiGbActiveStateReport biGbActiveStateReport=new BiGbActiveStateReport();
    private ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(CORE_THREAD_NUM, new ThreadFactory() {
        @Override
        public Thread newThread(@NonNull Runnable r) {
            return new Thread(r, "active_state_time_report_thread");
        }
    });

    private ActiveStateTimeReport() {
    }

    public static ActiveStateTimeReport getInstance() {
        if (mInstance == null) {
            synchronized (ActiveStateTimeReport.class) {
                if (mInstance == null) {
                    mInstance = new ActiveStateTimeReport();
                }
            }
        }
        return mInstance;
    }

    /**
     * time report active state every fifteen minutes
     */
    public void timeReportActiveState() {
        executor.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                biGbActiveStateReport.addState(TIME_REPORT).report();
            }
        }, 0, PERIOD, TimeUnit.MINUTES);
    }


}
