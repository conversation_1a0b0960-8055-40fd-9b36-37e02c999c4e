package com.ainirobot.home.bi;

import android.content.Intent;
import android.util.Log;

import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.control.RequestAnalysis;

import java.util.ArrayList;
import java.util.List;

import static com.ainirobot.home.ModuleDef.REQ_WAKEUP;


/**
 * @version V1.0.0
 * @date 2019/1/7 15:17
 */
public class BiModuleChangeNotifier {

    private static final String TAG = "BiModuleChangeNotifier";
    private volatile static String currentModule;
    private volatile static String waitModule;
    private static final String ACTION_CHANGE_MODULE = "action_change_module";
    private static final String ACTION_WAIT_MODULE = "action_wait_module";
    private static final String ACTION_REMOVE_WAIT_MODULE = "action_remove_wait_module";
    private static final String DATA_CHANGE_MODULE = "data_change_module";
    private static final String DATA_WAIT_MODULE = "data_wait_module";
    private static final String CHANGE_MODULE_RECEIVER_PERMISSION = "com.ainirobot.permission.CHANGE_MODULE_RECEIVER";
    private static final String LAUNCHER = "launcher";
    private static final String SETTING_MODULE = "settingmodule";

    private static List<String> intentBackList = new ArrayList<>();
    private static List<String> moduleBackList = new ArrayList<>();

    static {
        intentBackList.add(REQ_WAKEUP);
        moduleBackList.add(LAUNCHER);
        moduleBackList.add(SETTING_MODULE);
    }


    private static void broadcastChangeModule(String moduleName) {
        Log.i(TAG, "broadcastChangeModule: " + currentModule + "  change to:" + moduleName);
        currentModule = moduleName;
        Intent intent = new Intent(ACTION_CHANGE_MODULE);
        intent.putExtra(DATA_CHANGE_MODULE, moduleName);
        ApplicationWrapper.getContext().sendBroadcast(intent, CHANGE_MODULE_RECEIVER_PERMISSION);


    }

    public static void broadcastWaitModule(String moduleName) {
        if (moduleName.equals(waitModule)) {
            Log.i(TAG, "broadcastWaitModule: module don't change, wait module " + waitModule);
            return;
        }
        Log.i(TAG, "broadcastWaitModule: waitModuleName:" + moduleName);
        waitModule = moduleName;
        Intent intent = new Intent(ACTION_WAIT_MODULE);
        intent.putExtra(DATA_WAIT_MODULE, moduleName);
        ApplicationWrapper.getContext().sendBroadcast(intent, CHANGE_MODULE_RECEIVER_PERMISSION);
    }

    private static void broadcastWaitRecoverModule() {
        Log.i(TAG, "broadcastWaitRecoverModule: recover wait module " + waitModule);
        resetWaitModule();
        Intent intent = new Intent(ACTION_REMOVE_WAIT_MODULE);
        ApplicationWrapper.getContext().sendBroadcast(intent, CHANGE_MODULE_RECEIVER_PERMISSION);
    }


    public static void broadcastChangeModule(int module, String intent) {
        if (intentBackList.contains(intent)) {
            Log.w(TAG, "broadcastChangeModule:  intent:" + intent);
            return;
        }
        RequestAnalysis requestAnalysis = ControlManager.getControlManager().getRequestAnalysis();
        String moduleName = requestAnalysis.getFeatureName(module);
        if (moduleName.equals(currentModule) || moduleBackList.contains(moduleName)) {
            Log.i(TAG, "broadcastChangeModule: module don't change, current module "
                    + currentModule + " changeModule:" + moduleName);
            return;
        }
        if (moduleName.equals(waitModule)) {
            broadcastWaitRecoverModule();
        }
        broadcastChangeModule(moduleName);
    }

    public static void resetCurrentModule() {
        Log.i(TAG, "resetCurrentModule: ");
        currentModule = "";
        resetWaitModule();
    }

    public static void enterDefaultModule() {
        Log.i(TAG, "enterDefaultModule: " + LAUNCHER);
        broadcastChangeModule(LAUNCHER);
    }

    public static void resetWaitModule() {
        waitModule = "";
    }
}
