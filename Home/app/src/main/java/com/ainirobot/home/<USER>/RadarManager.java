package com.ainirobot.home.control;

import android.content.Context;
import android.content.DialogInterface;
import android.content.DialogInterface.OnDismissListener;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingListener;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.bi.BiRadarReport;
import com.ainirobot.home.ui.view.RadarDialog;

import org.json.JSONException;
import org.json.JSONObject;

public class RadarManager {

    private enum Status {
        /**
         * 正在打开雷达
         */
        OPENING,
        CLOSING,
        OPENED,
        CLOSED
    }

    private static String TAG = "RadarManger";
    private static RadarManager mInstance = new RadarManager();
    private static Status status = Status.OPENED;
    private static RadarListener listener;
    private static boolean isAllowCloseRadar = true;

    private static boolean isRadarOpened = true;

    private BiRadarReport mBiRadarReport = new BiRadarReport();
    /**
     * 关闭雷达，延迟时间5分钟
     */
    private static final long CLOSE_RADAR_DELAY = 5 * 60 * 1000;

    /**
     * 总超时，30s内打开雷达失败，显示失败界面
     */
    private static final long OPEN_RADAR_TIMEOUT = 30 * 1000;

    /**
     * 重试间隔，10秒重试一次
     */
    private static final long INTERVAL = 10 * 1000;

    private final String RESULT_TIMEOUT = "timeout";

    private static final int OPEN_RADAR = 1;
    private static final int CLOSE_RADAR = 2;
    private static final int OPEN_TIMEOUT = 3;
    private static final int OPEN_RADAR_SUCCESS = 4;
    private static final int RADAR_STATUS_CHANGE = 5;
    private Handler mHandler;
    private RadarDialog mRadarDialog;

    private RadarManager() {
        Log.d(TAG, "CREATE instance");
        mHandler = new Handler(Looper.getMainLooper()) {
            @Override
            public void handleMessage(Message msg) {
                onNewMessage(msg);
            }
        };

        queryRadarStatus();

//        registerRadarStatusListener();
    }

    private void queryRadarStatus() {
        SystemApi.getInstance().queryRadarStatus(Definition.DEBUG_REQ_ID, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                sendRadarStatusChangeMsg(message);
            }
        });
    }

    public void registerRadarStatusListener() {
        SystemApi.getInstance().registerStatusListener(Definition.STATUS_RADAR, new StatusListener() {
            @Override
            public void onStatusUpdate(String type, String data) {
                onRadarStatusChange(data);
            }
        });

        RobotSettingApi.getInstance().registerRobotSettingListener(new RobotSettingListener() {
            @Override
            public void onRobotSettingChanged(String key) {
                Log.d(TAG, "onRobotSettingChanged key=" + key
                    + ",value=" + RobotSettingApi.getInstance().getRobotString(key)
                    + ",isRadarOpened=" + isRadarOpened);
                if (Definition.RADAR_CLOSE_DELAY_TIME.equals(key)
                    && ControlManager.getControlManager().getSystemStatusManager().getIsCharging()
                    && isRadarOpened) {
                    updateStatus(Status.OPENED);
                    closeRadar(true);
                }
            }
        }, Definition.RADAR_CLOSE_DELAY_TIME);
    }

    /**
     * 雷达状态修正
     *
     * @param data
     */
    private void onRadarStatusChange(String data) {
        try {
            Log.d(TAG, "Radar status change: " + data);
            JSONObject object = new JSONObject(data);
            isRadarOpened = object.getBoolean(Definition.JSON_NAVI_OPEN_RADAR);
            int radarStatus = object.getInt(Definition.JSON_NAVI_RADAR_STATUS);
            if (isRadarOpened) {
                if (radarStatus == Definition.RADAR_STATUS_CLOSING) {
                    return;
                }
                if (status != Status.OPENED) {
                    Log.d(TAG, "Radar status from: " + status + " change to: opened");
                }

                if (status == Status.OPENING) {
                    openRadarSucceed();
                }

                if (status == Status.OPENING || status == Status.CLOSED) {
                    biOpenRadarReport();
                }

                updateStatus(Status.OPENED);
            } else {
                if (radarStatus == Definition.RADAR_STATUS_OPENING) {
                    return;
                }
                if (status != Status.CLOSED) {
                    Log.d(TAG, "Radar status change : closed");
                }
                updateStatus(Status.CLOSED);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void onNewMessage(Message message) {
        switch (message.what) {
            case CLOSE_RADAR:
                startCloseRadar();
                break;

            case OPEN_RADAR:
                startOpenRadar();
                break;

            case OPEN_TIMEOUT:
                openRadarFailed();
                break;

            case OPEN_RADAR_SUCCESS:
                openRadarSucceed();
                break;

            case RADAR_STATUS_CHANGE:
                onRadarStatusChange((String) message.obj);
                break;

            default:
                break;
        }
    }

    private synchronized void startOpenRadar() {
        Log.d(TAG, "Start open radar : " + status);
        if (status != Status.OPENING) {
            return;
        }

        showRadarDialog(RadarDialog.DIALOG_TYPE_OPENING);
        SystemApi.getInstance().updateRadarStatus(Definition.DEBUG_REQ_ID, true,
                new CommandListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.d(TAG, "Open radar result : " + message + "  status : " + status +
                                " isRadarOpened=" + isRadarOpened);
                        if (Definition.SUCCEED.equals(message)
                                || status == Status.OPENED) {
                            isRadarOpened = true;
                            mHandler.sendEmptyMessage(OPEN_RADAR_SUCCESS);
                            return;
                        }

                        if (RESULT_TIMEOUT.equals(message)) {
                            openRadarTimeout();
                        }
                    }
                });
        mHandler.sendEmptyMessageDelayed(OPEN_RADAR, INTERVAL);
    }

    private synchronized void startCloseRadar() {
        Log.d(TAG, "Start close radar : " + status);
        if (status != Status.CLOSING) {
            return;
        }

        SystemApi.getInstance().updateRadarStatus(Definition.DEBUG_REQ_ID, false,
                new CommandListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.d(TAG, "Close radar result : " + message + "  status : " + status);
                        if (Definition.SUCCEED.equals(message)) {
                            updateStatus(Status.CLOSED);
                            isRadarOpened = false;
                            biCloseRadarReport();
                            return;
                        }

                        status = Status.OPENED;
                        if (RESULT_TIMEOUT.equals(message)) {
                            openRadarTimeout();
                        }
                    }
                });
    }

    private void openRadarSucceed() {
        Log.d(TAG, "Open radar succeed");
        clearMessage();
        if (showRadarDialog(RadarDialog.DIALOG_TYPE_OPENED)) {
            mRadarDialog.setOnDismissListener(new OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    if (listener != null) {
                        listener.onSucceed();
                    }
                }
            });
        }
    }

    private void openRadarTimeout() {
        long timestamp = System.currentTimeMillis();
        String cacheId = RobotSettings.getSystemSn() + "-" + timestamp;
        SystemApi.getInstance().naviTimeOutCmdReport(0, timestamp, cacheId,
                Definition.TYPE_ACTION_NAVI_OPEN_RADAR_ERROR, "Open radar error",
                new CommandListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.i(TAG, "openRadarErrorReport: " + result + " message:" + message);
                    }
                });
    }

    private void openRadarFailed() {
        Log.d(TAG, "Open radar failed : " + status);
        updateStatus(Status.CLOSED);

        clearMessage();
        if (listener != null
                && listener.onFailed()) {
            return;
        }

        dismissRadarDialog();

        mBiRadarReport.addTask(ControlManager.getControlManager().getCurrentModule());

        //打开雷达失败，显示自检失败界面
        Bundle bundle = new Bundle();
        bundle.putString(ModuleDef.MSG_BUNDLE_INTENT, ModuleDef.OPEN_RADAR_FAILED);
        bundle.putString(ModuleDef.MSG_BUNDLE_TEXT, ModuleDef.OPEN_RADAR_FAILED);
        Message msg = Message.obtain();
        msg.what = ModuleDef.MSG_NEW_REQUEST;
        msg.setData(bundle);
        ControlManager.getControlManager().getMainHandler().sendMessage(msg);
    }

    private boolean showRadarDialog(int type) {
        if (mRadarDialog != null
                && mRadarDialog.getType() == type) {
            return false;
        }

        dismissRadarDialog();

        Context context = ApplicationWrapper.getContext();
        mRadarDialog = new RadarDialog(context, R.style.OTADialog, type);
        mRadarDialog.show();
        return true;
    }

    private void dismissRadarDialog() {
        if (mRadarDialog != null) {
            mRadarDialog.setOnDismissListener(null);
            mRadarDialog.dismiss();
            mRadarDialog = null;
        }
    }

    private void clearMessage() {
        mHandler.removeMessages(OPEN_RADAR);
        mHandler.removeMessages(CLOSE_RADAR);
        mHandler.removeMessages(OPEN_TIMEOUT);
        mHandler.removeMessages(OPEN_RADAR_SUCCESS);
    }

    private void sendRadarStatusChangeMsg(String data) {
        Message msg = Message.obtain();
        msg.what = RADAR_STATUS_CHANGE;
        msg.obj = data;

        mHandler.sendMessage(msg);
    }

    public synchronized static void openRadar(RadarListener radarListener) {
        Log.d(TAG, "Open radar : " + status + "  " + isRadarOpened);
        if (isRadarOpened) {
            if (radarListener != null) {
                radarListener.onSucceed();
            }
            return;
        }

        updateStatus(Status.OPENING);
        listener = radarListener;
        mInstance.clearMessage();
        mInstance.mHandler.sendEmptyMessage(OPEN_RADAR);
        mInstance.mHandler.sendEmptyMessageDelayed(OPEN_TIMEOUT, OPEN_RADAR_TIMEOUT);
    }

    public synchronized static void closeRadar() {
        closeRadar(false);
    }

    public synchronized static void closeRadar(boolean isDelay) {
        if (RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_ENABLE_NAVIGATION_INCHARGING) == 1) {
            Log.d(TAG, "enable usable when charging ,don`t closeRadar");
            return;
        }
        if (status != Status.OPENED || !isAllowCloseRadar) {
            return;
        }
        Log.d(TAG, "Close radar : " + status + "  isDelay : " + isDelay);
        updateStatus(Status.CLOSING);
        mInstance.mHandler.removeMessages(CLOSE_RADAR);
        int delayTime = getRadarCloseDelayTime();
        if (delayTime < 0) {
            Log.d(TAG, "radar don't close,because never close");
            return;
        }
        if (isDelay) {
            mInstance.mHandler.sendEmptyMessageDelayed(CLOSE_RADAR, delayTime);
        } else {
            mInstance.mHandler.sendEmptyMessage(CLOSE_RADAR);
        }
    }

    public synchronized static void cancelCloseRadar() {
        if (mInstance.mHandler.hasMessages(CLOSE_RADAR)) {
            Log.d(TAG, "Cancel close radar : " + status);
            mInstance.clearMessage();
            if (status == Status.CLOSING) {
                updateStatus(Status.OPENED);
            }
        }
    }

    public synchronized static void cancelOpenRadar() {
        if (mInstance.mHandler.hasMessages(OPEN_RADAR)) {
            Log.d(TAG, "Cancel open radar : " + status);
            mInstance.clearMessage();
            mInstance.dismissRadarDialog();
            if (status == Status.OPENING) {
                updateStatus(Status.CLOSED);
            }
        }
    }

    private synchronized static void updateStatus(Status destStatus) {
        if (status == destStatus) {
            return;
        }
        Log.d(TAG, "Update radar manager status : " + destStatus);
        status = destStatus;
    }

    /**
     * 是否允许关闭雷达
     *
     * @param isAllow
     */
    public synchronized static void setAllowCloseRadar(boolean isAllow) {
        Log.d(TAG, "Set allow close radar : " + isAllow);
        isAllowCloseRadar = isAllow;
        if (!isAllow) {
            cancelCloseRadar();
        }
    }

    public static void setRadarStatusListener() {
        mInstance.registerRadarStatusListener();
    }

    public void sendBiRadarReport(int type) {
        mBiRadarReport.addType(type);
        mBiRadarReport.report();
    }

    public void biCloseRadarReport() {
        mBiRadarReport.addTask(ControlManager.getControlManager().getCurrentModule());
        sendBiRadarReport(BiRadarReport.RADAR_REPORT_TYPE_CLOSE_RADAR);
        mBiRadarReport.addCloseRadarTime(System.currentTimeMillis());
    }

    public void biOpenRadarReport() {
        mBiRadarReport.addTask(ControlManager.getControlManager().getCurrentModule());
        mBiRadarReport.addLengthTime();
        sendBiRadarReport(BiRadarReport.RADAR_REPORT_TYPE_OPEN_RADAR);
    }

    public static void biOpenRadarFailedReport(String errorId) {
        mInstance.mBiRadarReport.addErrorId(errorId);
        mInstance.mBiRadarReport.addLengthTime();
        mInstance.sendBiRadarReport(BiRadarReport.RADAR_REPORT_TYPE_OPEN_RADAR_FAILED);
    }

    private static int getRadarCloseDelayTime() {
        String strCloseDelayTime = RobotSettingApi.getInstance().getRobotString(Definition.RADAR_CLOSE_DELAY_TIME);
        int delayTime;
        try {
            //单位：分钟
            delayTime = Integer.parseInt(strCloseDelayTime);
            if (delayTime > 0) {
                //转化为毫秒
                delayTime *= 60000;
            }
        }catch (Exception e) {
            e.printStackTrace();
            delayTime = Definition.RADAR_DEFAULT_DELAY_TIME;
        }
        Log.d(TAG, "close radar delayTime=" + delayTime);
        return delayTime;
    }

    public static class RadarListener {
        public boolean onSucceed() {
            return false;
        }

        public boolean onFailed() {
            return false;
        }
    }
}
