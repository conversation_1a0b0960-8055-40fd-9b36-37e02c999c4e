package com.ainirobot.home.bi;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

public class BIRobotLockReport extends BaseBiReport {

    private static final String TABLE_NAME = "gb_lock_robot";
    private static final String LOCK_START = "lock_start";
    private static final String LOCK_END = "lock_end";
    private static final String LOCK_TYPE = "lock_type";

    private Long startTime;
    private Long endTime;
    private int lockType;

    public BIRobotLockReport() {
        super(TABLE_NAME);
    }

    public void addStartTime(Long startTime){
        this.startTime = startTime;
    }

    public void addEndTime(Long endTime){
        this.endTime = endTime;
    }

    public void addLockType(int lockType){
        this.lockType = lockType;
    }

    @Override
    public void report() {
        addData(LOCK_START, startTime);
        addData(LOCK_END, endTime);
        addData(LOCK_TYPE, lockType);
        super.report();
    }
}
