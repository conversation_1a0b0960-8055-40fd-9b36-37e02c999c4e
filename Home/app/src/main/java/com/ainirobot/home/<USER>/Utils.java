package com.ainirobot.home.fallback;

import android.app.ActivityManager;
import android.content.Context;
import android.graphics.Bitmap;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class Utils {
    private static final String TAG = Utils.class.getSimpleName();

    public static void killApp(Context context, String packageName) {
        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        try {
            Method forceStopPackage = am.getClass().getDeclaredMethod("forceStopPackage", String.class);
            forceStopPackage.setAccessible(true);
            forceStopPackage.invoke(am, packageName);
        } catch (Exception e) {
            Log.d(TAG, e.getMessage());
            e.printStackTrace();
        }
    }


    public static String getTopActivity(Context context) {
        ActivityManager manager = (ActivityManager) context.getSystemService(context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningTaskInfo> runningTaskInfos = manager.getRunningTasks(1);
        if (runningTaskInfos != null) {
            return (runningTaskInfos.get(0).topActivity.getClassName());
        } else
            return null;
    }

    public static int getPidFromPackagename(Context context, String packageName) {
        ActivityManager manager = (ActivityManager) context.getSystemService(context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningAppProcessInfo> runningTaskInfos = manager.getRunningAppProcesses();
        for (int i = 0; i < runningTaskInfos.size(); i++) {
            ActivityManager.RunningAppProcessInfo info = runningTaskInfos.get(i);
            if (info.processName.equals(packageName)) {
                return info.pid;
            }
        }
        return 0;
    }


    public static boolean isActivityAlive(Context context, String pkg) {
        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        if (am == null || TextUtils.isEmpty(pkg)) {
            return false;
        }

        try {
            List<ActivityManager.RunningTaskInfo> list = am
                    .getRunningTasks(Integer.MAX_VALUE);
            for (ActivityManager.RunningTaskInfo taskInfo : list) {
                if (pkg.equals(taskInfo.topActivity.getPackageName())) {
                    return true;
                }
            }
        } catch (SecurityException e) {
            e.printStackTrace();
        }

        Log.d(TAG, "Activity is not exit : " + pkg);
        return false;
    }


    public static byte[] readStream(InputStream inStream) throws Exception {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len = -1;
        while ((len = inStream.read(buffer)) != -1) {
            outStream.write(buffer, 0, len);
        }
        outStream.close();
        inStream.close();
        return outStream.toByteArray();
    }


    public static void writeFile(String path, InputStream inputStream) {
        FileOutputStream fileOutputStream = null;
        try {
            int index;
            byte[] bytes = new byte[1024];
            fileOutputStream = new FileOutputStream(path);
            while ((index = inputStream.read(bytes)) != -1) {
                fileOutputStream.write(bytes, 0, index);
                fileOutputStream.flush();
            }
        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, e.getMessage());
        } finally {
            try {
                if (inputStream != null)
                    inputStream.close();
                if (fileOutputStream != null)
                    fileOutputStream.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static String getStringTodayTime() {
        Date todat_date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");
        return simpleDateFormat.format(todat_date);
    }

    public static String getStringTime(long time) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");
        return simpleDateFormat.format(new Date(time));
    }

    public static void fileSortByTime(List<File> fileList) {
        Collections.sort(fileList, new Comparator<File>() {
            public int compare(File p1, File p2) {
                if (p1.lastModified() < p2.lastModified()) {
                    return -1;
                }
                return 1;
            }
        });
    }

    public static void deleteDirWihtFile(File dir) {
        if (dir == null || !dir.exists() || !dir.isDirectory())
            return;
        for (File file : dir.listFiles()) {
            if (file.isFile())
                file.delete();
            else if (file.isDirectory())
                deleteDirWihtFile(file);
        }
        dir.delete();
    }

    public static void sleep(long time) {
        try {
            Thread.sleep(time);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public static void destoryProcess(Process process) {
        destoryProcess(process, 20 * 1000);
    }

    public static void destoryProcess(Process process, int timeout) {
        try {
            if (process != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    process.waitFor(timeout, TimeUnit.MILLISECONDS);
                } else {
                    process.waitFor();
                }

            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                process.destroy();
            } finally {

            }
        }
    }


    public static void destoryBitmap(Bitmap bitmap) {
        if (bitmap == null)
            return;
        try {
            bitmap.recycle();
        } finally {

        }
    }
}
