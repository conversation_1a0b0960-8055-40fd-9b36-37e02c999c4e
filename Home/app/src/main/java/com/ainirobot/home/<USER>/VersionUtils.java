/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.home.utils;

import android.util.Log;

/**
 * 版本比较工具类
 */
public class VersionUtils {
    private static final String TAG = "VersionUtils";

    /**
     * 比较两个版本号
     * @param version1 版本号1
     * @param version2 版本号2
     * @return 如果version1 < version2，返回负数；如果version1 = version2，返回0；如果version1 > version2，返回正数
     */
    public static int compareVersion(String version1, String version2) {
        return 0;
//        if (version1 == null || version2 == null) {
//            Log.w(TAG, "compareVersion: one of the versions is null");
//            return 0;
//        }
//
//        Log.d(TAG, "compareVersion: comparing " + version1 + " with " + version2);
//
//        // 移除版本号中的非数字和点的字符，只保留主版本号部分
//        String cleanVersion1 = extractMainVersion(version1);
//        String cleanVersion2 = extractMainVersion(version2);
//
//        Log.d(TAG, "compareVersion: cleaned versions " + cleanVersion1 + " vs " + cleanVersion2);
//
//        String[] parts1 = cleanVersion1.split("\\.");
//        String[] parts2 = cleanVersion2.split("\\.");
//
//        int maxLength = Math.max(parts1.length, parts2.length);
//
//        for (int i = 0; i < maxLength; i++) {
//            int num1 = i < parts1.length ? parseVersionPart(parts1[i]) : 0;
//            int num2 = i < parts2.length ? parseVersionPart(parts2[i]) : 0;
//
//            if (num1 < num2) {
//                Log.d(TAG, "compareVersion: " + version1 + " < " + version2);
//                return -1;
//            } else if (num1 > num2) {
//                Log.d(TAG, "compareVersion: " + version1 + " > " + version2);
//                return 1;
//            }
//        }
//
//        Log.d(TAG, "compareVersion: " + version1 + " = " + version2);
//        return 0;
    }

    /**
     * 提取主版本号部分
     * 例如：1.3.0.250616.C -> 1.3.0
     */
    private static String extractMainVersion(String version) {
        if (version == null || version.isEmpty()) {
            return "0";
        }

        // 分割版本号
        String[] parts = version.split("\\.");
        
        // 只取前3个数字部分作为主版本号
        StringBuilder mainVersion = new StringBuilder();
        int count = 0;
        for (String part : parts) {
            if (count >= 3) {
                break;
            }
            
            // 检查是否为纯数字
            if (part.matches("\\d+")) {
                if (count > 0) {
                    mainVersion.append(".");
                }
                mainVersion.append(part);
                count++;
            } else {
                // 如果遇到非数字部分，停止处理
                break;
            }
        }

        return mainVersion.length() > 0 ? mainVersion.toString() : "0";
    }

    /**
     * 解析版本号的一个部分
     */
    private static int parseVersionPart(String part) {
        try {
            return Integer.parseInt(part);
        } catch (NumberFormatException e) {
            Log.w(TAG, "parseVersionPart: failed to parse " + part + ", returning 0");
            return 0;
        }
    }

    /**
     * 检查版本是否小于等于指定版本
     * @param version 要检查的版本
     * @param targetVersion 目标版本
     * @return true如果version <= targetVersion
     */
    public static boolean isVersionLessOrEqual(String version, String targetVersion) {
        return compareVersion(version, targetVersion) <= 0;
    }

    /**
     * 检查版本是否大于指定版本
     * @param version 要检查的版本
     * @param targetVersion 目标版本
     * @return true如果version > targetVersion
     */
    public static boolean isVersionGreater(String version, String targetVersion) {
        return compareVersion(version, targetVersion) > 0;
    }
}
