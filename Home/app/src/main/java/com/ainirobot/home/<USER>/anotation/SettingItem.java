package com.ainirobot.home.bi.anotation;


import android.support.annotation.StringDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * back charging annotation
 *
 * @version V1.0.0
 * @date 2019/4/10 15:50
 */
@Retention(RetentionPolicy.SOURCE)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@StringDef({SettingItem.AUTO_UPDATE
        , SettingItem.DEFAULT_APP
        , SettingItem.WORK_IN_CHARGING})
public @interface SettingItem {
    final String AUTO_UPDATE = "auto_update";
    final String DEFAULT_APP = "default_app";
    final String WORK_IN_CHARGING = "work_in_charging";
}
