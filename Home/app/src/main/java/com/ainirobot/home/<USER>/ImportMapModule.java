package com.ainirobot.home.module;

import android.provider.Settings;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.R;
import com.ainirobot.home.bean.MapInfo;
import com.ainirobot.home.bean.PushMapBean;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.utils.MapUtils;
import com.ainirobot.home.utils.ResUtil;
import com.ainirobot.home.utils.ToastUtil;
import com.google.gson.JsonSyntaxException;


public class ImportMapModule extends BaseBackGroundModule {
    private final static String TAG = ImportMapModule.class.getSimpleName();
    private static ImportMapModule mInstance = new ImportMapModule(TAG);

    private ImportMapModule(String threadName) {
        super(threadName);
    }

    public static ImportMapModule getInstance() {
        return mInstance;
    }

    @Override
    protected void onThreadNewSemantics(int reqId, String intent, String text, final String params) {
        Log.d(TAG, "import map onThreadNewSemantics, intent: " + intent + ", params = " + params);

        if (intent.equals(Definition.REMOTE_IMPORT_MAP_BEGIN)) {
            checkMapFileAndVersion(params);
        }
        super.onThreadNewSemantics(reqId, intent, text, params);
    }

    private void checkMapFileAndVersion(String mapName){
        //检查mapName_import文件夹是否存在
        if(!MapUtils.isImportMapExist(mapName)){
            Log.e(TAG, "checkMapFileAndVersion: mapName_import folder not exist!");
            importFail();
            return;
        }
        //检查mapName_import/mapinfo.json是否存在
        if(!MapUtils.isImportMapInfoJsonExists(mapName)){
            Log.e(TAG, "checkMapFileAndVersion: mapName_import/mapinfo.json not exist!");
            importFail();
            return;
        }
        //读取mapinfo.json
        MapInfo mapInfo = MapUtils.getImportMapInfoFromJsonFile(mapName);
        if(mapInfo == null){
            Log.e(TAG, "checkMapFileAndVersion: import mapInfo is null!");
            importFail();
            return;
        }
        Log.d(TAG, "checkMapFileAndVersion: mapInfo = " + mapInfo.toString());

        //判断mapinfo.json中的version是否小于最大兼容版本
        int mapCompatibleVersion = Settings.Global.getInt(ApplicationWrapper.getContext().getContentResolver(),
                Definition.ROBOT_MAP_COMPATIBLE_VERSION, 0);
        Log.d(TAG, "mapCompatibleVersion : " + mapCompatibleVersion);
        if (mapInfo.getMapVersion() > mapCompatibleVersion) {
            Log.e(TAG, "Robot support version is smaller than map version");
            ToastUtil.showToast(ApplicationWrapper.getContext(), ApplicationWrapper.getContext().getString(R.string.map_not_compatible));
            SkillManager.getInstance().speechPlayText(ResUtil.getString(R.string.import_map_fail));
            return;
        }

        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_PUSH_MAP, null, null);

        //导入地图包信息赋值
        //只需要地图整包信息，不需要视觉文件和地图类型信息，拷贝时整个文件夹覆盖即可
        PushMapBean pushMapInfo = new PushMapBean();
        pushMapInfo.setMapName(mapName);
        pushMapInfo.setMapUrl(MapUtils.getImportMapFilePath(mapName));
        pushMapInfo.setMapUuid(mapInfo.getMapUuid());
        pushMapInfo.setPgmMd5(mapInfo.getMd5());
        pushMapInfo.setVersion(mapInfo.getUpdateTime());
        Log.d(TAG, "checkMapFileAndVersion: pushMapInfo = " + pushMapInfo.toString());

        //开始导入地图包
        importMapPackage(pushMapInfo);
    }

    private void importMapPackage(PushMapBean pushMapInfo) {
        Log.d(TAG, "importMapPackage: Start download map package!");

        MapUtils.downLoadWholeMapPkg(pushMapInfo, true, true, mGson, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                Log.d(TAG, "downLoadPkg:downLoadMapPkg:onResult: result=" + result + ", message=" + message);

                if (message.equals(Definition.SUCCEED)) {
                    ToastUtil.showToast(ApplicationWrapper.getContext(), ResUtil.getString(R.string.import_map_success));
                    SkillManager.getInstance().speechPlayText(ResUtil.getString(R.string.import_map_success));
                } else {
                    importFail();
                }
                stop();
            }
        });
    }

    private void importFail(){
        ToastUtil.showToast(ApplicationWrapper.getContext(), ResUtil.getString(R.string.import_map_fail));
        SkillManager.getInstance().speechPlayText(ResUtil.getString(R.string.import_map_fail));
    }

    @Override
    protected void onStop() {
        Log.d(TAG, "onStop: ");
        UIController.getInstance().moveToBack();
        SystemApi.getInstance().onImportMapModuleFinish();
        super.onStop();
    }
}
