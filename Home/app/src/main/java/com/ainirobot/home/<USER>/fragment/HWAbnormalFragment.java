package com.ainirobot.home.ui.fragment;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.ui.UIController;

import java.util.ArrayList;
import java.util.List;

public class HWAbnormalFragment extends BaseFragment implements View.OnClickListener {

    private static final String TAG = HWAbnormalFragment.class.getSimpleName();

    private ImageView mImageView;
    private TextView mErrorView;
    private TextView mSubTitleTV;
    private Button mRebootTV;
    private LinearLayout mPsbErrorLy;
    private TextView mErrorCodeTv;
    private TextView mRobotSnTv;
    private TextView mTimeStampTv;
    public int mTimerTime = DELIVERY_DEFAULT_WAIT_TIME;
    public static final int MEISSA_DEFAULT_WAIT_TIME = 300;
    public static final int DELIVERY_DEFAULT_WAIT_TIME = 120;
    /**
     * 当前是否有异常，正在展示异常界面
     */
    private volatile boolean mShowError = false;
    private Handler mTimerHandler;


    public static final String HEAD_CHASSIS_ERROR = "VISION_CHASSIS_ERROR";
    public static final String CHASSIS_ERROR = "CHASSIS_ERROR";
    public static final String HEAD_ERROR = "VISION_ERROR";
    public static final String PSB_ERROR = "PSB_NO_HEARTBEAT";


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "on create");
    }

    private void initCountdownTime(){
        if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMiniProduct() || ProductInfo.isMeissa2()) {
            mTimerTime = DELIVERY_DEFAULT_WAIT_TIME;
        } else {
            mTimerTime = MEISSA_DEFAULT_WAIT_TIME;
        }
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle
            savedInstanceState) {
        Log.d(TAG, "on onCreateView");
        View view = inflater.inflate(R.layout.fragment_hw_state, null);
        mImageView = (ImageView)view.findViewById(R.id.hw_status_iv);
        mErrorView = (TextView) view.findViewById(R.id.hw_error_message);
        mSubTitleTV = (TextView)view.findViewById(R.id.hw_subtitle_tv);
        mRebootTV = (Button) view.findViewById(R.id.timer_button);
        mRebootTV.setOnClickListener(this);
        mPsbErrorLy = (LinearLayout) view.findViewById(R.id.psb_error_hint);
        mErrorCodeTv = (TextView) view.findViewById(R.id.tv_errorCode);
        mRobotSnTv = (TextView) view.findViewById(R.id.tv_sn);
        mTimeStampTv = (TextView) view.findViewById(R.id.tv_timestamp);
        Bundle bundle = getArguments();
        initCountdownTime();
        if (bundle != null) {
            mRebootTV.setVisibility(View.VISIBLE);
            mRebootTV.setClickable(false);
            mRebootTV.setText(getActivity().getString(R.string.hw_retry_recovery, mTimerTime));
            String params = bundle.getString(ModuleDef.HW_ABNORMAL_STATUS);
            onMessage(UIController.MESSAGE_TYPE.HW_STATUS_UPDATE, params);
            initHandler();

        }else {
            mRebootTV.setVisibility(View.INVISIBLE);
        }

        initRobotInfo();
        return view;
    }

    private void initRobotInfo(){

        String robotSn = RobotSettings.getSystemSn();
        mRobotSnTv.setText(robotSn);

        String timeStamp = Long.toString(System.currentTimeMillis());
        mTimeStampTv.setText(timeStamp);

    }

    private void updateTiemrUI(){
        if (mRebootTV != null){
            mRebootTV.post(new Runnable() {
                @Override
                public void run() {
                    if (isDetached() || !isAdded() || getActivity() == null) {
                        return;
                    }
                    if (mTimerTime <= 0){
                        mRebootTV.setText(getActivity().getString(R.string.hw_state_reboot));
                    }else {
                        mRebootTV.setText(getActivity().getString(R.string.hw_retry_recovery, mTimerTime));
                    }
                }
            });
        }
    }

    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
        Log.d(TAG, " type：" + type);
        switch (type) {
            case HW_STATUS_UPDATE:
                showErrorInfo(message);
                break;
            case HW_STATUS_RECOVERY:
                showRecoverySuccessView();
                break;
            default:
                break;
        }
    }

    private void sendDelayExitMsg(){
        if (mTimerHandler != null){
            mTimerHandler.sendEmptyMessageDelayed(ModuleDef.MSG_HWABNORMAL_DELAY_EXIT, 3000);
        }
    }

    private void showRecoveryFailView(){
        if (mErrorView == null){
            return;
        }
        mErrorView.post(new Runnable() {
            @Override
            public void run() {
                try {
                    mImageView.setImageDrawable(getActivity().getDrawable(R.drawable.hw_recovery_fail));
                    mRebootTV.setVisibility(View.VISIBLE);
                    mSubTitleTV.setVisibility(View.VISIBLE);
                    mErrorView.setText(getString(R.string.hw_recovery_fail));
                    mSubTitleTV.setText(getString(R.string.hw_recovery_fail_des));
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        });
    }

    private void showRecoverySuccessView(){
        if (mErrorView == null){
            return;
        }
        mShowError = false;
        sendDelayExitMsg();
        mErrorView.post(new Runnable() {
            @Override
            public void run() {
                try {
                    mImageView.setImageDrawable(getActivity().getDrawable(R.drawable.first_sync_success));
                    mErrorView.setText(getString(R.string.hw_recovery_suc));
                    mRebootTV.setVisibility(View.INVISIBLE);
                    mSubTitleTV.setVisibility(View.INVISIBLE);
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        });
    }

    private void showErrorInfo(String message){
        Log.d(TAG,"showErrorInfo  message:" + message);
        if (mTimerTime <= 0){
            return;
        }
        if (!TextUtils.isEmpty(message)){
            final List<String> errorList = StringToList(message);
            Log.d(TAG, "showErrorInfo:" + errorList);
            if (errorList == null || errorList.isEmpty() || mErrorView == null){
                return;
            }
            mShowError = true;
            mErrorView.post(new Runnable() {
                @Override
                public void run() {
                    try {
                        mImageView.setImageDrawable(getActivity().getDrawable(R.drawable.ic_warning));
                        mRebootTV.setVisibility(View.VISIBLE);
                        mSubTitleTV.setVisibility(View.VISIBLE);
                        mSubTitleTV.setText(getString(R.string.hw_auto_recovery));
                        mPsbErrorLy.setVisibility(View.GONE);
                        String errorStr = "";
                        boolean headError = errorList.contains(Definition.STATUS_HEAD);
                        boolean chassisError = isChassisError(errorList);
                        boolean psbError = isPsbError(errorList);
                        String errorCode = "";
                        if (chassisError && headError){
                            errorStr = getString(R.string.hw_all_error_info);
                            errorCode = HEAD_CHASSIS_ERROR;
                            sendTimeUpdateMsg();
                        }else {
                            if (headError){
                                errorStr = getString(R.string.hw_head_error_info);
                                errorCode = HEAD_ERROR;
                                sendTimeUpdateMsg();
                            }
                            if (chassisError){
                                errorStr = getString(R.string.hw_chassis_disconnected);
                                errorCode = CHASSIS_ERROR;
                                sendTimeUpdateMsg();
                            }
//                            if(psbError){
//                                errorStr = getString(R.string.hw_psb_error);
//                                errorCode = PSB_ERROR;
//                                mSubTitleTV.setVisibility(View.INVISIBLE);
//                                mPsbErrorLy.setVisibility(View.VISIBLE);
//                                mRebootTV.setVisibility(View.INVISIBLE);
//                            }
                        }
                        mErrorView.setText(errorStr);
                        mErrorCodeTv.setText(errorCode);

                    }catch (Exception e){
                        e.printStackTrace();
                    }
                }
            });

        }
    }

    private void showE70Error(String message){
        Log.d(TAG,"showE70Error  message:" + message);
        if (!TextUtils.isEmpty(message) && message.contains(Definition.STATUS_PSB_ERROR)){
            mErrorView.post(new Runnable() {
                @Override
                public void run() {
                    mImageView.setImageDrawable(getActivity().getDrawable(R.drawable.ic_warning));
                    mRebootTV.setVisibility(View.VISIBLE);
                    mRebootTV.setClickable(false);
                    mRebootTV.setText(getActivity().getString(R.string.hw_retry_recovery, mTimerTime));
                    mSubTitleTV.setVisibility(View.VISIBLE);
                    mErrorCodeTv.setVisibility(View.VISIBLE);
                    mSubTitleTV.setVisibility(View.INVISIBLE);
                    mPsbErrorLy.setVisibility(View.VISIBLE);
                    mErrorView.setText(getString(R.string.hw_psb_error));
                    mErrorCodeTv.setText(PSB_ERROR);
                    initHandler();
                    sendTimeUpdateMsg();
                }
            });
        }
    }

    private boolean isChassisError(List errorList){
        if (errorList.contains(Definition.STATUS_CHASSIS_COMMAND)){
            return true;
        }
        if (errorList.contains(Definition.STATUS_CHASSIS_EVENT)){
            return true;
        }
        if (errorList.contains(Definition.STATUS_CHASSIS_REMOTE)){
            return true;
        }
        return false;
    }

    private boolean isPsbError(List errorList){
        if (errorList.contains(Definition.STATUS_PSB_ERROR)){
            return true;
        }
        return false;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "onDestroy");
        removeHandlerMsg();

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.timer_button:
                sendMessageToRebootRobot();
                break;
            default:
                break;
        }
    }

    private void initHandler(){
        if (mTimerHandler != null){
            mTimerHandler.removeMessages(ModuleDef.MSG_HWABNORMAL_REBOOT_TIMER);
        }
        mTimerHandler = new Handler(Looper.getMainLooper()){
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);
                final Bundle bundle = msg.getData();
              //  Log.d(TAG, "msg:" + msg.what);
                switch (msg.what) {
                    case ModuleDef.MSG_HWABNORMAL_REBOOT_TIMER:
                        mTimerTime--;
                        if (mTimerTime <= 0){
                            sendHwRecoveryTimeoutMsg();
                            showRecoveryFailView();
                            mRebootTV.setClickable(true);
                            removeHandlerMsg();
                        }else {
                            mRebootTV.setClickable(false);
                            sendTimeUpdateMsg();
                        }
                        updateTiemrUI();
                        break;
                    case ModuleDef.MSG_HWABNORMAL_DELAY_EXIT:
                        if (!mShowError){
                            ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_EXIT_HW_MODULE);
                        }
                        break;
                    default:
                        break;
                }
            }
        };
    }

    private void removeHandlerMsg() {
        if (mTimerHandler != null) {
            mTimerHandler.removeMessages(ModuleDef.MSG_HWABNORMAL_REBOOT_TIMER);
            mTimerHandler = null;
        }
    }

    private void sendTimeUpdateMsg(){
        if (mTimerHandler != null) {
            Message newMsg = Message.obtain();
            newMsg.what = ModuleDef.MSG_HWABNORMAL_REBOOT_TIMER;
            mTimerHandler.sendMessageDelayed(newMsg, 1000);
        }
    }

    private void sendMessageToRebootRobot(){
        Log.d(TAG, "sendMessageToRebootRobot");
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_HW_STATUS_CANNOT_RECOVERY);
    }

    private void sendHwRecoveryTimeoutMsg(){
        Log.d(TAG, "sendHwRecoveryTimeoutMsg");
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_HW_AUTOMATIC_RECOVERY_TIMEOUT);
    }

    public static List<String> StringToList(String listText) {
        if (TextUtils.isEmpty(listText)){
            return null;
        }
        String newString = listText.replaceAll(" ", "");
        String finalStr = newString.substring(1, newString.length()-1);

        List<String> list = new ArrayList<String>();
        String[] text = finalStr.split(",");
        for (String str : text) {
            list.add(str);
        }
        return list;
    }

}
