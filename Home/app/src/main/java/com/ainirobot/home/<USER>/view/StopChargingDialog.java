package com.ainirobot.home.ui.view;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Outline;
import android.os.Bundle;
import android.support.constraint.ConstraintLayout;
import android.view.View;
import android.view.ViewOutlineProvider;
import android.view.WindowManager;
import android.widget.TextView;

import com.ainirobot.home.R;

public class StopChargingDialog extends Dialog implements View.OnClickListener{

    private TextView mConfirm;
    private TextView mCancel;
    private ConstraintLayout mLayout;


    public StopChargingDialog(Context context, int theme) {
        super(context, theme);
    }

    private ClickListener mClickListener ;
    public interface ClickListener{
        void onConfirmClick();
        void onCancelClick();
    }

    public StopChargingDialog setDialogClickListener(ClickListener listener){
        this.mClickListener = listener;
        return this;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_dialog_stop_charging);
        WindowManager.LayoutParams p = this.getWindow().getAttributes();
        p.width = 950;
        p.height = WindowManager.LayoutParams.WRAP_CONTENT;
        this.getWindow().setAttributes(p);
        setCanceledOnTouchOutside(false);

        initView();
    }

    private void initView() {
        mLayout = (ConstraintLayout)findViewById(R.id.dialog_stop_charging);
        mConfirm = (TextView) findViewById(R.id.confirm);
        mConfirm.setOnClickListener(this);
        mCancel = (TextView) findViewById(R.id.cancel);
        mCancel.setOnClickListener(this);
        ViewOutlineProvider viewOutlineProvider = new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                view.setClipToOutline(true);
                outline.setRoundRect(0,0,view.getWidth(),view.getHeight(),30);
            }
        };
        mLayout.setOutlineProvider(viewOutlineProvider);
    }

    @Override
    public void show() {
        ObjectAnimator.ofFloat(mLayout,"alpha",0,1)
                .setDuration(170)
                .start();
        super.show();
    }

    @Override
    public void onClick(View v) {
        int vId = v.getId();
        switch (vId){
            case R.id.confirm:
                if (mClickListener != null){
                    mClickListener.onConfirmClick();
                    dismiss();
                }
                break;
            case R.id.cancel:
                ObjectAnimator animator = ObjectAnimator
                        .ofFloat(mLayout,"alpha",1,0)
                        .setDuration(170);
                animator.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        dismiss();
                    }
                });
                animator.start();
                if (mClickListener != null) {
                    mClickListener.onCancelClick();
                }
                break;

        }
    }
}
