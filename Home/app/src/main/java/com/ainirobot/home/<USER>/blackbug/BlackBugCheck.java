package com.ainirobot.home.fallback.blackbug;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.text.format.DateUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.bi.BiRelocationFailReport;
import com.ainirobot.home.fallback.Dump;
import com.ainirobot.home.fallback.Screencap;
import com.ainirobot.home.fallback.Utils;

import java.io.File;
import java.util.Date;

public class BlackBugCheck implements Runnable {
    private static final String TAG = BlackBugCheck.class.getSimpleName();
    private static final String MODULE_PACKAGE_NAME = "com.ainirobot.moduleapp";
    private static final String TOP_ACTIVITY_NAME = "com.ainirobot.platform.react.EveActivity";

    ///> 进程启动延迟检测时间
    private static final int START_DEALY_CHECK_TIME = 20 * 1000;
    ///> 检测时间间隔
    private static final int LOOP_CHECK_TIME = 30 * 1000;
    ///> 复检次数
    private static final int RECHECK_COUNT = 2;
    ///> 复检等待时间
    private static final int RECHECK_DELAY_TIME = 20 * 1000;
    ///> 杀死后等待时间
    private static final int KILL_WAIT_CHECK_TIME = 20 * 1000;
    ///> 忽略四周边缘像素
    private static final int IGNORE_ROUND_PIEX = 100;
    ///> Dump 文件最大数量
    private static final int DUMP_FILE_MAX_COUNT = 20;
    ///> 重置最大重启次数的周期间隔
    private static final long RESET_RESTART_COUNT_INTERVAL_TIME = 8 * DateUtils.HOUR_IN_MILLIS;
    ///> 间隔时间内最大重启次数
    private static final long MAX_RESTART_COUNT = 48;
    ///> RN进程包名
    private static final String MOUDLE_APP_PACKAGE_NAME = "com.ainirobot.moduleapp:sandbox";


    private final Context mContext;
    private Screencap mScreenCap = new Screencap();
    private long mStartCheckTime = new Date().getTime();
    private int mRestart = 0;


    private static BlackBugCheck sInstance;

    public static BlackBugCheck getInstance(Context context) {
        if (sInstance == null) {
            sInstance = new BlackBugCheck(context);
        }
        return sInstance;
    }

    public void startCheck() {
        new Thread(this).start();
    }

    private BlackBugCheck(Context context) {
        mContext = context;
        mScreenCap.init(mContext);
    }

    @Override
    public void run() {
        Utils.sleep(START_DEALY_CHECK_TIME);
        while (true) {
            try {
                Utils.sleep(LOOP_CHECK_TIME);
                check();
            } catch (Exception e) {
                Log.d(TAG, e.getMessage());
                e.printStackTrace();
            }
        }
    }

    public void screencapAndDump() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "screencapAndDump start");
                Bitmap bitmap = null;
                try {
                    bitmap = mScreenCap.screencap();
                    dump(bitmap, PathConstants.getPoweredDownDumpSubDir());
                    Dump.deleteDump(PathConstants.getPoweredDownDumpDir(), DUMP_FILE_MAX_COUNT);
                } catch (Exception e) {
                    e.printStackTrace();
                    Log.d(TAG, e.getMessage());
                } finally {
                    Utils.destoryBitmap(bitmap);
                }
                Log.d(TAG, "screencapAndDump end");
            }
        }).start();
    }


    private void check() {
        Bitmap bitmap = null;
        int count = 0;
        int prePid = 0;
        do {
            Log.d(TAG, "screencap start");
            bitmap = mScreenCap.screencap();
            Log.d(TAG, "screencap end");

            if (!checkBlackground(bitmap, IGNORE_ROUND_PIEX, 100))
                break;

            if (!checkBlackground(bitmap, IGNORE_ROUND_PIEX, 10))
                break;

            if (!checkBlackground(bitmap, IGNORE_ROUND_PIEX, 1))
                break;

            if (count < RECHECK_COUNT) {
                count++;
                Utils.destoryBitmap(bitmap);
                prePid = Utils.getPidFromPackagename(mContext, MOUDLE_APP_PACKAGE_NAME);
                Log.d(TAG, "sandbox pid: " + prePid);
                Utils.sleep(RECHECK_DELAY_TIME);
                Log.d(TAG, "recheck start");
                continue;
            }

            if (!isTopEveActivity(mContext)) {
                break;
            }

            int curPid = Utils.getPidFromPackagename(mContext, MOUDLE_APP_PACKAGE_NAME);
            if (curPid != prePid) {
                Log.d(TAG, "sandbox no match prePid: " + prePid + ", curPid: " + curPid);
                break;
            }

            Log.d(TAG, "black ready process!!!");
            process(bitmap);
            Log.d(TAG, "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
            break;
        } while (true);
        Utils.destoryBitmap(bitmap);

    }

    private void process(Bitmap bitmap) {
        recordRestart();
        dump(bitmap, PathConstants.getExecptionDumpSubDir());
        Dump.deleteDump(PathConstants.getExecptionDumpDir(), DUMP_FILE_MAX_COUNT);
        report();
        kill();
    }

    private void dump(Bitmap bitmap, String dumpDir) {
        Log.d(TAG, "dump path: " + dumpDir);
        Dump.dumpScreen(dumpDir, bitmap);
        Dump.dumpUI(dumpDir);
        Dump.dumpTopActivity(dumpDir);
        Dump.dumpWindowVisiable(dumpDir);
        Dump.dumpServiceStatus(dumpDir);
    }


    private void kill() {
        Log.d(TAG, "Kill module app");
        Utils.killApp(mContext, MODULE_PACKAGE_NAME);
        do {
            Utils.sleep(KILL_WAIT_CHECK_TIME);
            if (Utils.isActivityAlive(mContext, MODULE_PACKAGE_NAME) && isTopEveActivity(mContext)) {
                Log.d(TAG, "module app restart!");
                break;
            }

        } while (true);
    }


    private boolean isTopEveActivity(Context context) {
        Log.d(TAG, "top activity:");
        String topAct = Utils.getTopActivity(context);
        Log.d(TAG, "top activity: " + topAct);
        if (topAct == null)
            return false;
        return topAct.equals(TOP_ACTIVITY_NAME);
    }

    private boolean checkBlackground(Bitmap bitmap, int start, int step) {
        Log.d(TAG, "checkBlackground step: " + step);
        for (int y = start; y < bitmap.getHeight() - step; y += step) {
            for (int x = start; x < bitmap.getWidth() - step; x += step) {
                try {
                    int color = bitmap.getPixel(x, y);
                    int r = Color.red(color);
                    int g = Color.green(color);
                    int b = Color.blue(color);
                    int a = Color.alpha(color);
                    color = r + g + b;
                    if (color != 0) {
                        Log.d(TAG, "checkBlackground false, x: " + x + ", y: " + y);
                        Log.d(TAG, "checkBlackground false, red: " + r + ", green: " + g + ", blue: " + b + ", alpha:" + a);
                        //dump(bitmap);
                        return false;
                    }
                } catch (Exception e) {
                    Log.d(TAG, "checkBlackground  x: " + x + ", y: " + y + ", e: " + e.getMessage());
                }
            }
        }
        Log.d(TAG, "checkBlackground true");
        return true;
    }


    private void report() {
        BiRelocationFailReport report = new BiRelocationFailReport();
        report.addActionType("eve_black_restart");
        report.report();
    }

    private void recordRestart() {
        mRestart++;
        Log.d(TAG, ", restarted: " + mRestart +
                ", maxrestart: " + MAX_RESTART_COUNT);
    }

    private boolean isSatisfyCheckCondition() {
        if (isExistsBackDoorFile("ignore_check_condition")) {
            return true;
        }

        long curTime = new Date().getTime();
        long interval = curTime - mStartCheckTime;
        if (interval > RESET_RESTART_COUNT_INTERVAL_TIME) {
            mRestart = 0;
            mStartCheckTime = new Date().getTime();
        }
        boolean ret = mRestart < MAX_RESTART_COUNT;
        if (!ret) {
            Log.d(TAG, "Start check time: " + Utils.getStringTime(mStartCheckTime) +
                    ",Next reset restart count time: " + Utils.getStringTime(mStartCheckTime + RESET_RESTART_COUNT_INTERVAL_TIME));
        }
        return ret;
    }


    private boolean isEnableCheck() {
        try {
            boolean enable = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_ENABLE_BLACKCHECK) == 1;
            Log.d(TAG, "enable blackcheck:  " + enable);
            return enable;
        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, e.getMessage());
        }
        return false;
    }

    private boolean isExistsBackDoorFile(String name) {
        File file = new File(PathConstants.getBlackCheckDir() + name);
        if (file.exists()) {
            Log.d(TAG, "exists file: " + name);
            return true;
        }
        return false;
    }


}
