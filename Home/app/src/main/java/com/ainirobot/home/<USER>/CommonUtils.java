package com.ainirobot.home.utils;

import android.text.TextUtils;

import com.ainirobot.coreservice.client.Definition;

public class CommonUtils {
    private final static int PRIORITY_FIRST = 0;
    private final static int PRIORITY_SECOND = 1;
    private final static int PRIORITY_THIRD = 2;
    private final static int PRIORITY_LAST = Integer.MAX_VALUE;

    public static int getPriority(String message) {
        if (TextUtils.isEmpty(message)) {
            return PRIORITY_LAST;
        } else {
            if (message.equals(Definition.RESULT_ESTIMATE_FAIL_CHECK_ERR)) {
                return PRIORITY_FIRST;
            }
            if (message.equals(Definition.RESULT_ESTIMATE_FAIL_MATCH_ERR)) {
                return PRIORITY_SECOND;
            }
        }
        return PRIORITY_THIRD;
    }
}
