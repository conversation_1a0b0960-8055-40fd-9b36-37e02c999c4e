package com.ainirobot.home.bean;

import android.content.pm.ResolveInfo;

import java.io.Serializable;

public class ShowAppBean implements Serializable {
    private ResolveInfo resolveInfo;
    private boolean isChecked = true;

    public ShowAppBean(ResolveInfo resolveInfo, boolean isChecked) {
        this.resolveInfo = resolveInfo;
        this.isChecked = isChecked;
    }

    public ResolveInfo getResolveInfo() {
        return resolveInfo;
    }

    public void setResolveInfo(ResolveInfo resolveInfo) {
        this.resolveInfo = resolveInfo;
    }

    public boolean isChecked() {
        return isChecked;
    }

    public void setChecked(boolean checked) {
        isChecked = checked;
    }

    @Override
    public String toString() {
        return "AppBean{" +
                "resolveInfo=" + resolveInfo +
                ", isChecked=" + isChecked +
                '}';
    }
}
