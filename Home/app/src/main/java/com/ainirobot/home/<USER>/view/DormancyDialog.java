/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.home.ui.view;

import android.app.AlertDialog;
import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.R;

import static com.ainirobot.home.utils.ResUtil.getString;

public class DormancyDialog extends AlertDialog {

    private static final String TAG = "DormancyDialog";

    private Context mContext;
    private CountDownTimer mTimer;
    private static final int COUNT_DOWN_TIME = 30;
    private TextView mTextView;
    private TextView mCancel;
    private View mLine;
    private DialogEvent mEvent;

    public DormancyDialog(Context context, DialogEvent event) {
        super(context);
        this.mContext = context;
        this.mEvent = event;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        Log.i(TAG, "create");
        super.onCreate(savedInstanceState);
        View view = LayoutInflater.from(mContext).inflate(R.layout.dormancy_dialog, null);
        setContentView(view);
        setViewStyle();

        mTextView = (TextView) view.findViewById(R.id.dormancy_text);
        mCancel = (TextView) view.findViewById(R.id.dormancy_cancel);
        mLine = view.findViewById(R.id.dormancy_line);

        mCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mEvent != null) {
                    mEvent.onCancel();
                }
                dismiss();
            }
        });

        mTimer = new CountDownTimer(COUNT_DOWN_TIME * Definition.SECOND, Definition.SECOND) {
            @Override
            public void onTick(long millisUntilFinished) {
                SpannableString text = new SpannableString(
                        getString(R.string.dormancy_text, millisUntilFinished / 1000));
                String systemLanguage = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_LANGUAGE);
                if (systemLanguage.contains("zh")){
                    text.setSpan(new ForegroundColorSpan(Color.parseColor("#55C3FB")),
                        5, text.length() - 5, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                }else if (systemLanguage.contains("en")){
                    text.setSpan(new ForegroundColorSpan(Color.parseColor("#55C3FB")),
                            text.length()-5, text.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                }
                mTextView.setText(text);
            }

            @Override
            public void onFinish() {
                if (mEvent != null) {
                    mEvent.onConfirm();
                }
                mTextView.setText(getString(R.string.dormancy_loading));
                mCancel.setVisibility(View.GONE);
                mLine.setVisibility(View.GONE);
            }
        };
        mTimer.start();
    }

    private void setViewStyle() {
        Window window = getWindow();
        if (window == null) {
            return;
        }
        window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
        WindowManager.LayoutParams lp = window.getAttributes();
        lp.width = 940;
        lp.height = 462;
        window.setAttributes(lp);
        window.setBackgroundDrawableResource(R.color.transparent);
        setCanceledOnTouchOutside(false);
    }

    public interface DialogEvent{
        /**
         * 倒计时结束
         */
        void onConfirm();

        /**
         * 点击取消按钮
         */
        void onCancel();
    }

    @Override
    public void dismiss() {
        Log.i(TAG, "dismiss");
        if (mTimer != null) {
            mTimer.cancel();
            mTimer = null;
        }
        super.dismiss();
    }
}
