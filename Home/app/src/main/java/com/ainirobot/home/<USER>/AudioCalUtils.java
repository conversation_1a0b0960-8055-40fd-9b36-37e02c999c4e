package com.ainirobot.home.utils;

import android.media.AudioManager;
import android.util.Log;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.R;

import org.json.JSONArray;
import org.json.JSONObject;

import java.text.NumberFormat;

public class AudioCalUtils {
    private static final String TAG = "AudioCalUtils:Home";

    public static final int MAX_AUDIO_NUM = 10;
    public static final int MIN_AUDIO_NUM = 1;
    public static final String MAX_LEVEL_NOTIFICATION = ApplicationWrapper.getContext().getString(R.string.volume_hight);//15
    public static final String MIN_LEVEL_NOTIFICATION = ApplicationWrapper.getContext().getString(R.string.volume_low);//1
    public static final String AUDIO_UP_NOTIFICATION = ApplicationWrapper.getContext().getString(R.string.volume_up_done);
    public static final String AUDIO_DOWN_NOTIFICATION = ApplicationWrapper.getContext().getString(R.string.volume_down_done);
    public static final String AUDIO_SET_SUC = ApplicationWrapper.getContext().getString(R.string.volume_set_done);

    public static final int[] soundGrade =
            ProductInfo.ProductModel.CM_MINI_TOB.model
                    .equals(RobotSettings.getProductModel())
                    || ProductInfo.ProductModel.CM_MINI_TOC.model
                    .equals(RobotSettings.getProductModel())
                    || ProductInfo.ProductModel.CM_MINI_TOC_OLD.model
                    .equals(RobotSettings.getProductModel()) ?
                    new int[]{0, 2, 3, 5, 6, 8, 9, 11, 12, 14, 15}
                    : new int[]{0, 3, 5, 6, 8, 10, 11, 12, 13, 14, 15};
                    //音量分11个等级，对应MUSIC_STREAM数值
//    public static final String [] percentStrArr = {"零 ", "十", "二十", "三十", "四十", "五十", "六十", "七十", "八十", "九十", "一百"};
//    public static String percentStr(int value) {
//        return String.format("已经是百分之%s音量", percentStrArr[value]);
//    }

    public static int getProperAudioIndex(int value) {
        Log.d(TAG, "getProperAudioIndex value = " + value);
        if (value >= MAX_AUDIO_NUM) {
            return MAX_AUDIO_NUM;
        } else if (value <= MIN_AUDIO_NUM) {
            return MIN_AUDIO_NUM;
        } else {
            return value;
        }
    }

    public static int getValueFromParams(String params, AudioManager manager, int audioType) {
        int value = AudioCalUtils.getCurAudioIndex(manager.getStreamVolume(audioType));
        try {
            JSONObject jsonFristParse = new JSONObject(params);
            JSONObject jsonSecond = new JSONObject(jsonFristParse.getString("slots"));
            JSONArray arr = new JSONArray(jsonSecond.getString("command_value"));
            String valueStr = arr.getJSONObject(0).getString("value");
            if (valueStr.contains("%")) {
//                int maxLevel = manager.getStreamMaxVolume(audioType);
                Number numPercent = NumberFormat.getPercentInstance().parse(valueStr);
                value = (int) (numPercent.doubleValue() * 10);
            } else {
                value = Integer.parseInt(valueStr);
            }

            value = getProperAudioIndex(value);
            Log.d(TAG, "valueStr = " + valueStr + "value = " + value);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return value;
    }

    public static int getCurAudioIndex(int audioVal) {
        Log.d(TAG, "getCurAudioIndex, curAudioVal = " + audioVal);
        try {
            for (int i = 0; i <= 10; i++) {
                if (audioVal == soundGrade[i]) {
                    return i;
                }
                if ((audioVal - soundGrade[i]) * (audioVal - soundGrade[i + 1]) < 0) {
                    if (Math.abs(audioVal - soundGrade[i]) <= Math.abs(audioVal - soundGrade[i + 1])) {
                        return i;
                    } else {
                        return i + 1;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 5;
    }

    public static void setAudioLevelNum(AudioManager manager, int value, int audioType) {
        int maxLevel = manager.getStreamMaxVolume(audioType);
        int preciseValue;
        preciseValue = Math.min(value, maxLevel);
        Log.d(TAG, "maxLevel = " + maxLevel + ", value = " + value
                + ", preciseValue = " + preciseValue);
        manager.setStreamVolume(audioType, preciseValue, 0);
    }
}
