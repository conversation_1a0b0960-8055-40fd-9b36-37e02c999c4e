/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.home.ui.fragment;

import android.os.Bundle;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.utils.ResType;

public class BindSuccessFragment extends BaseFragment implements View.OnClickListener{

    private static final String TAG = "BindSuccFragment:Home";
    private TextView mRobotNameTV;
    private String robotName;
    private TextView mBottomBtn;
    private ImageView mImgIcon;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "BindSuccessFragment onCreate ... ");
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_bind_success, null);
        mImgIcon = (ImageView) view.findViewById(R.id.bind_iv);
        mBottomBtn = (TextView)view.findViewById(R.id.bottomBtn);
        mRobotNameTV = (TextView) view.findViewById(R.id.bing_success_robot_name);
        mImgIcon.setImageResource(ResType.PRODUCT_ICON.getResIdByType());

        mBottomBtn.setOnClickListener(this);

        Bundle bundle = getArguments();
        if (bundle != null) {
            robotName = bundle.getString("robot_name");
            if (!TextUtils.isEmpty(robotName)) {
                mRobotNameTV.setText(robotName);
                Log.d(TAG, "onCreateView robotName : " + robotName);
            }
        }
        Log.d(TAG, "BindSuccessFragment onCreateView ...");
        return view;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.bottomBtn:
                Log.d(TAG,"click to enter module app");
                ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_BIND_END);
                break;
        }
    }

}