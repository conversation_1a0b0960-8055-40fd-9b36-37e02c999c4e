package com.ainirobot.home.ota.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

/**
 * 接收wakeUpId广播,用于埋点上报
 *
 * @version V1.0.0
 * @date 2019/1/9 10:54
 */
public class BiWakeUpIdReceiver extends BroadcastReceiver {

    private static final String TAG = "BiWakeUpIdReceiver";
    /**
     * 当前wakeUpId
     */
    public volatile static String currentWakeUpId = "";
    /**
     * wakeupId广播action
     */
    private static final String ACTION_WAKEUP_ID = "action_wakeup_id";
    /**
     * wakeupId的key
     */
    private static final String DATA_WAKEUP_ID = "data_wakeup_id";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent == null || intent.getAction() == null) {
            Log.i(TAG, "onReceive: intent:" + intent);
            return;
        }
        switch (intent.getAction()) {
            case ACTION_WAKEUP_ID:
                if (TextUtils.isEmpty(intent.getStringExtra(DATA_WAKEUP_ID))) {
                    Log.e(TAG, "onReceive: otaService wakeupId is empty");
                    break;
                }
                currentWakeUpId = intent.getStringExtra(DATA_WAKEUP_ID);
                Log.i(TAG, "onReceive: otaService wakeupId：" + currentWakeUpId);
                break;
            default:
        }
    }
}
