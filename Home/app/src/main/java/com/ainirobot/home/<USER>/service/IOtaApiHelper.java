package com.ainirobot.home.ota.service;


import com.ainirobot.coreservice.client.StatusListener;

public interface IOtaApiHelper {

    boolean isCoreConnected();

    boolean registerStatusListener(String type, StatusListener listener);

    boolean unregisterStatusListener(StatusListener listener);

    // normal command
    void isHeadConntected();

    // command
    String getHeadVersion(int millSecondTimeout);

    String startHeadScp(int millSecondTimeout);

    String startHeadUpdate(String params, int millSecondTimeout);

    String getHeadUpdateParams(int millSecondTimeout);

    String startNavigationUpdate(String params, int millSecondTimeout);

    String getNavigationVersion(int millSecondTimeout);

    String startCanUpdate(String params, int millSecondTimeout);

    String getCanOtaState(int millSecondTimeout);

    String getCanBoardVersion(String params, int millSecondTimeout);

    String getCanMotorHVersion(int millSecondTimeout);

    String getCanMotorVVersion(int millSecondTimeout);

    String getCanPsbVersion(int millSecondTimeout);

    String getCanAcClientVersion(int millSecondTimeout);

    String getCanBmsVersion(int millSecondTimeout);

    String getCanPsbSVersion(int millSecondTimeout);

    // request
    int sendOtaEvent(boolean isForce, String params);

    int sendDowngradeOtaEvent(String params);
    int sendOtaDownloadSuccess(String params);

    int finishOtaUpgrade(String params);

    void updateProgress(String params);
}
