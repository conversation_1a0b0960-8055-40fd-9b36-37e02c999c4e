/*
 *
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
package com.ainirobot.home.ui.view;

import android.animation.ObjectAnimator;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Outline;
import android.os.Bundle;
import android.support.constraint.ConstraintLayout;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.R;
import com.ainirobot.home.bean.CloudServerBean;
import com.ainirobot.home.utils.CloudUtils;
import com.ainirobot.home.utils.ResUtil;
import com.ainirobot.home.utils.SharedPrefUtil;

import java.util.ArrayList;
import java.util.List;


public class CloudDialog extends AlertDialog implements View.OnClickListener{
    private static final String TAG = CloudDialog.class.getSimpleName();

    private Context mContext;
    private TextView mConfirm, mCancel;
    private ConstraintLayout mLayout;
    private ListView mServerList;
    private MyAdapter mAdapter;
    private String mServerCode;
    private List<CloudServerBean> mList = new ArrayList<>();
    private boolean serverSwitched = false;


    public CloudDialog(Context context) {
        this(context,0);
        mContext = context;
    }

    public CloudDialog(Context context, int theme) {
        super(context, theme);
        mContext = context;
    }

    private ClickListener mClickListener ;
    public interface ClickListener{
        void onConfirmClick();
        void onCancelClick();
    }

    public CloudDialog setDialogClickListener(ClickListener listener){
        this.mClickListener = listener;
        return this;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_cloud_dialog);
        WindowManager.LayoutParams p = this.getWindow().getAttributes();
        p.width = 950;
        p.height = 960;
        this.getWindow().setAttributes(p);
        setCanceledOnTouchOutside(false);

        initView();
    }

    private void initView() {
        mLayout = (ConstraintLayout)findViewById(R.id.dialog_parent);
        mConfirm = (TextView) findViewById(R.id.confirm);
        mConfirm.setOnClickListener(this);
        ViewOutlineProvider viewOutlineProvider = new ViewOutlineProvider() {

            @Override
            public void getOutline(View view, Outline outline) {
                view.setClipToOutline(true);
                outline.setRoundRect(0,0,view.getWidth(),view.getHeight(),21);
            }
        };
        mLayout.setOutlineProvider(viewOutlineProvider);
        mServerList = (ListView)findViewById(R.id.lv_servers);
        mAdapter = new MyAdapter(mContext);
    }

    private boolean isServerSwitched(String newCode) {
        String localDefaultCode = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_SETTING_CLOUD_SERVER_CODE);
        Log.d(TAG, "isServerSwitched currentServerCode : " + localDefaultCode + " newServerCode：" + newCode);
        return CloudUtils.isServerSwitched(newCode, localDefaultCode);
    }

    @Override
    public void show() {
        ObjectAnimator.ofFloat(mLayout,"alpha",0,1)
                .setDuration(170)
                .start();
        super.show();

        Log.d(TAG, "show Cloud Dialog");
        updateServerList();
    }

    private void updateServerList() {
        mServerList.setAdapter(mAdapter);
        mServerList.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                CloudServerBean cloudServerBean = (CloudServerBean) mAdapter.getItem(position);
                String serverId = cloudServerBean.getServer_id();
                CloudDialog.this.itemClick(position);
                serverSwitched = CloudDialog.this.isServerSwitched(serverId);
                mConfirm.setText(serverSwitched ? ResUtil.getString(R.string.cloud_reboot) : ResUtil.getString(R.string.cloud_select_ok));
                mServerCode = serverId;
            }
        });
        mAdapter.updateData(initCloudServers());
        mAdapter.notifyDataSetChanged();
    }

    private void itemClick(int clickPosition) {
        int count = mAdapter.getCount();
        for (int i = 0; i < count; i++) {
            ((CloudServerBean) mAdapter.getItem(i)).setDefault(i == clickPosition);
        }
        mAdapter.notifyDataSetChanged();
    }

    private List<CloudServerBean> initCloudServers() {
        String configCloud = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_SETTINGS_CLOUD_SERVER_ZONE);
        if (TextUtils.isEmpty(configCloud)){
            configCloud = Definition.CloudServerZone.DEFAULT.getValue();
        }
        mServerCode = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_SETTING_CLOUD_SERVER_CODE);
        Log.d(TAG, "initCloudServers localServerCode: " + mServerCode + ", systemServer :" + configCloud);
        mList.clear();
        mList.addAll(CloudUtils.getAllCloudServerBean(configCloud, mServerCode));
        return mList;
    }

    @Override
    public void onClick(View v) {
        int vId = v.getId();
        switch (vId){
            case R.id.confirm:
                RobotSettingApi.getInstance().setRobotString(Definition.ROBOT_SETTING_CLOUD_SERVER_CODE,mServerCode);
                if (serverSwitched){
                    RobotSettingApi.getInstance().setRobotString(Definition.ROBOT_SETTINGS_CLOUD_SERVER_ZONE, CloudUtils.getServerZone(mServerCode));
                    reboot();
                }
                if (mClickListener != null){
                    mClickListener.onConfirmClick();
                }
                dismiss();
                break;
            default:
                Log.d(TAG, "default case");
                break;

        }
    }

    private void reboot(){
        Intent intent = new Intent(Intent.ACTION_REBOOT);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        ApplicationWrapper.getContext().startActivity(intent);
    }

    class MyAdapter extends BaseAdapter {

        private LayoutInflater mInflater;
        private List<CloudServerBean> mData = new ArrayList<>();

        public MyAdapter(Context mContext) {
            mInflater = LayoutInflater.from(mContext);
        }

        public List<CloudServerBean> getData() {
            return mData;
        }

        public void updateData(List<CloudServerBean> list) {
            mData.clear();
            mData.addAll(list);
        }

        @Override
        public int getCount() {
            return mData.size();
        }

        @Override
        public Object getItem(int position) {
            return mData.get(position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder = null;
            if (convertView == null) {
                holder = new ViewHolder();
                convertView = mInflater.inflate(R.layout.item_lv_language, null);
                holder.tvLanguage = (TextView) convertView.findViewById(R.id.tv_language);
                holder.imSelect = (ImageView) convertView.findViewById(R.id.im_select);
                convertView.setTag(holder);
            } else {
                holder = (ViewHolder) convertView.getTag();
            }
            CloudServerBean bean = (CloudServerBean) mData.get(position);
            holder.tvLanguage.setText(bean.getServer_name());
            holder.tvLanguage.setTextColor(Color.BLACK);
            holder.imSelect.setImageResource(bean.isDefault() ? R.drawable.radio_check : R.drawable.radio_normal);
            return convertView;
        }

        class ViewHolder {
            TextView tvLanguage;
            ImageView imSelect;
        }
    }
}
