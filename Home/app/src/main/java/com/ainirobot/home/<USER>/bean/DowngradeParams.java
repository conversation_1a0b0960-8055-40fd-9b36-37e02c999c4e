package com.ainirobot.home.ota.bean;

import android.content.Context;
import android.util.Log;

import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.home.ota.constants.OtaConstants;
import com.ainirobot.home.ota.receiver.BiWakeUpIdReceiver;
import com.ainirobot.home.ota.utils.FileUtils;
import com.ainirobot.home.ota.utils.MD5;
import com.ainirobot.home.ota.utils.SettingsUtils;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class DowngradeParams {

    private final static String TAG = OtaConstants.TAG_PREFIX + DowngradeParams.class.getSimpleName();

    // public update header params
    public final static String KEY_PF = "pf";
    public final static String KEY_BRAND = "brand";
    public final static String KEY_MODEL = "model";
    public final static String KEY_OSV = "osv";         //os version
    public final static String KEY_APPV = "appv";       //app version
    public final static String KEY_APPID = "app_id";     //app id, get from server
    public final static String KEY_RDID = "rdid";       //robot id, get from server
    public final static String KEY_HWID = "hwid";       //robot id, get from server
    public final static String KEY_RUID = "ruid";       //robot uuid, get from server
    public final static String KEY_CORPID = "corpid";   //cooperation id(not needed)
    public final static String KEY_CH = "ch";           //channel id
    public final static String KEY_TOKEN = "token";
    public final static String KEY_WAKEUP_ID = "wakeup_id";
    public final static String KEY_NAVI_VERSION = "naviv"; //lxc version
    public final static String KEY_OTA_ACTION = "ota_action";

    public final static String KEY_SIGN = "sign";       //generate by signCalculate

    private final static String KEY_CLIENT_SECRET = "client_secret";
    private final static String client_secret = "2d21cdfb58fbcf6ddcb8f0e6055fd0f1";

    private Context mContext;
    private ConcurrentHashMap<String, String> keyMap = new ConcurrentHashMap<>();

    public DowngradeParams(Context context) {
        this.mContext = context;
        initKeyMap();
    }


    private void initKeyMap() {
        keyMap.put(KEY_PF, RobotSettings.getPlatform());
        keyMap.put(KEY_BRAND, RobotSettings.getBrand());
        keyMap.put(KEY_MODEL, RobotSettings.getProductModel());
        // ro.product.releasenum is whole release version;
        // ro.build.description is host/nav version for check update result.
        keyMap.put(KEY_APPV, RobotSettings.getVersion());
        keyMap.put(KEY_OSV, RobotSettings.getAndroidVersion());
        keyMap.put(KEY_APPID, "100001");
        keyMap.put(KEY_RDID, getSystemSn());
        keyMap.put(KEY_HWID, RobotSettings.getQualcommSn());
        keyMap.put(KEY_RUID, RobotSettings.getRobotUUID(mContext));
        keyMap.put(KEY_CORPID, RobotSettings.getCorpUUID(mContext));
        keyMap.put(KEY_CH, "999999");
        keyMap.put(KEY_TOKEN, SettingsUtils.getRobotToken(mContext));
        String sign = calculateUpdateSign();
        keyMap.put(KEY_SIGN, sign);
        keyMap.put(KEY_WAKEUP_ID, BiWakeUpIdReceiver.currentWakeUpId);
        keyMap.put(KEY_NAVI_VERSION, FileUtils.getLXCVersion());
    }

    public Map<String, String> getHashMap() {
        return keyMap;
    }

    // 设置回退版本参数
    public void setRomGoBack() {
        keyMap.put(KEY_OTA_ACTION,"rom_goback");
    }

    /**
     * generate sign.
     *
     * @return
     */
    private String calculateUpdateSign() {
        String sign = null;
        try {
            ConcurrentHashMap<String, String> sMap = new ConcurrentHashMap<>();
            keyMap.putAll(sMap);
            sMap.put(KEY_CLIENT_SECRET, client_secret);

            Collection<String> keyset = sMap.keySet();
            ArrayList<String> keyList = new ArrayList<String>(keyset);
            Collections.sort(keyList);

            StringBuilder builder = new StringBuilder();
            Iterator<String> iterator = keyList.iterator();

            String keyValue;
            boolean isFirst = true;
            while (iterator.hasNext()) {
                keyValue = iterator.next();
                if (!isFirst) {
                    builder.append("&");
                }
                builder.append(keyValue)
                        .append("=")
                        .append(sMap.get(keyValue));
                isFirst = false;
            }

            String key = new String(builder.toString().getBytes("UTF-8"));
            Log.d(TAG, "value:" + key);
            //String result = getBase64Code(key);
            sign = MD5.createSHA1(key);
            Log.d(TAG, "sign:" + sign);

        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } finally {
            return sign;
        }
    }


    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("DowngradeParams{" +
                "keyMap=" + keyMap + "}");

        return builder.toString();
    }

    private static String getSystemSn() {
        return RobotSettings.getSystemSn();
    }
}
