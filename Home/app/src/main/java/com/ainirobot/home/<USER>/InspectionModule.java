/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.home.module;

import android.content.Context;
import android.content.Intent;
import android.database.ContentObserver;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.InspectionResult;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.client.techreport.AbnormalReport;
import com.ainirobot.coreservice.client.techreport.CallChainReport;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.bi.BiGbCharging;
import com.ainirobot.home.bi.BiInspectionReport;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.control.LightManager;
import com.ainirobot.home.control.RadarManager;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.report.Constant;
import com.ainirobot.home.report.TaskReport;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.utils.MapUtils;
import com.ainirobot.home.utils.ResUtil;
import com.ainirobot.home.utils.SharedPrefUtil;
import com.ainirobot.home.utils.SystemUtils;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Locale;
import java.util.Timer;
import java.util.TimerTask;

public class InspectionModule extends BaseModule {
    private static final String TAG = "InspectionModule:Home";

    private static final int OTA_FAIL_REBOOT_TIME = 3;
    private static final long TIME_OUT_INTERVAL = 4 * 60 * 1000;
    private static final String LOGIN_URI = "content://com.ainirobot.account_provider/token";
    private static final int MSG_START_PLAT_SOUND = 1;
    private static final int MSG_STOP_PLAT_SOUND = 2;

    private static InspectionModule mIntance = null;
    private Context mContext;
    private int mReqId = 0;
    private MediaPlayer mMediaPlayer;
    private String mErrorId = "";
    private StringBuilder errorMsg = new StringBuilder();
    private long mTimeStamp = 0;
    private int mLineNum = 1;
    private String mParam;
    private CommandListenerProxy mCommandListenerProxy = new CommandListenerProxy();
    private BiGbCharging mBiGbCharging = new BiGbCharging();
    private InspectionHandler mHandler;
    private ContentObserver mTokenObserver = new ContentObserver(new Handler()) {
        @Override
        public void onChange(boolean selfChange) {
            super.onChange(selfChange);
            TaskReport.getInstance().reportModuleChange(ModuleDef.FEATURE_LAUNCHER);
        }
    };

    private CallChainReport callChain;

    private enum SelfCheckStatus {
        IDLE,
        CHECKING,
        CHECK_ERROR
    }

    private SelfCheckStatus mCheckStatus = SelfCheckStatus.IDLE;

    public static InspectionModule getInstance() {
        if (null == mIntance) {
            mIntance = new InspectionModule();
        }
        return mIntance;
    }

    private InspectionModule() {
        HandlerThread thread = new HandlerThread("inspection");
        thread.start();
        mHandler = new InspectionHandler(thread.getLooper());
    }

    public void init(Context context) {
        mContext = context;
        callChain = new CallChainReport(mContext, this.getClass().getName(), "inspect");
    }

    private class InspectionHandler extends Handler {

        InspectionHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(Message msg) {
            Log.d(TAG, "InspectionHandler handleMessage what: " + msg.what);
            switch (msg.what) {
                case MSG_START_PLAT_SOUND:
                    playPowerOnSound();
                    break;
                case MSG_STOP_PLAT_SOUND:
                    try {
                        if (mMediaPlayer != null) {
                            if (mMediaPlayer.isPlaying()) {
                                mMediaPlayer.stop();
                            }
                            mMediaPlayer.release();
                            mMediaPlayer = null;
                        }
                    } catch (IllegalStateException e) {
                        e.printStackTrace();
                    }
                    break;
                default:
                    break;
            }
        }
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.d(TAG, "ycLog intent:" + intent + ", params:" + params + ", state:" + mState);
        switch (intent) {
            case ModuleDef.START_INSPECTION:
                if (mCheckStatus == SelfCheckStatus.CHECKING) {
                    Log.d(TAG, mContext.getString(R.string.inspect_running));
                    SystemApi.getInstance().finishModuleParser(reqId, true);
                    return true;
                }
                mParam = params;
                if (ModuleDef.FIRST_BOOT.equals(mParam)) {
                    SystemUtils.setThreeFinger(false);
                    SystemUtils.checkImeAvailable(mContext);
                    mHandler.removeMessages(MSG_START_PLAT_SOUND);
                    mHandler.removeMessages(MSG_STOP_PLAT_SOUND);
                    mHandler.sendEmptyMessage(MSG_START_PLAT_SOUND);
                }
                registerObserver();
                startInspection(reqId, params);
                SystemApi.getInstance().setXDPowerEnable(0, true);
                SystemApi.getInstance().setXDRank(0, Definition.JSON_CAN_XD_RANK_CLOSE);
                if (ProductInfo.isSlimProduct()) {
                    SystemApi.getInstance().setTrayLedEffect(0, Definition.LED_EFFECT_ALLOFF, null);
                } else {
                    SystemApi.getInstance().setTrayLedEffect(0, Definition.TRAY_LIGHT_EFFECT_ALLOFF, null);
                }
                return true;

            case ModuleDef.REQ_STOP:
//                if (ModuleDef.FIRST_BOOT.equals(mParam) && !mIsOtaFail) {
                stopInspection();
                TaskReport.getInstance().setTaskResult(Constant.TaskEvent.task_cancel);
//                }
                return true;

            case ModuleDef.OPEN_RADAR_FAILED:
                mCheckStatus = SelfCheckStatus.CHECK_ERROR;
                SystemApi.getInstance().onInspectionStart();
                mParam = "";
                mErrorId = generateErrorId();
                errorMsg.append("1.雷达未启动");
                sendInspectError(true);
                RadarManager.biOpenRadarFailedReport(mErrorId);
                openAsrRecognize();
                return true;

            default:
                return false;
        }

    }

    @Override
    public void onStop() {
        Log.d(TAG, "on stop : " + mCheckStatus + ", " + mParam);
        if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus()) {
            LightManager.getInstance().setLightEffectType(LightManager.LIGHT_EFFECT_TYPE
                    .LIGHT_EFFECT_BLUE_LIGHT);
        }
        SystemApi.getInstance().uploadMapPlaceList(0, null, null);
        if (mCheckStatus == SelfCheckStatus.IDLE) return;
        mLineNum = 1;
        errorMsg.delete(0, errorMsg.length());
        mErrorId = "";
        mTimeStamp = 0;
        mCheckStatus = SelfCheckStatus.IDLE;
        ApplicationWrapper
                .getContext()
                .getContentResolver()
                .unregisterContentObserver(mTokenObserver);
        if (ModuleDef.NORMAL_CHECK.equals(mParam)) {
            Log.i(TAG, "finish normal check");

            mCommandListenerProxy.setCommandType(CommandListenerProxy.TYPE_IS_ESTIMATE);
            SystemApi.getInstance().isRobotEstimate(mReqId, mCommandListenerProxy);
            return;
        } else {
            //检查日常自检失败重启标记位，进行离桩
            int normalCheckFailed = SharedPrefUtil.getInstance().getInt(ModuleDef.NORMAL_CHECK_FAILED);
            Log.d(TAG, "onStop: normalCheckFailed=" + normalCheckFailed);
            if(normalCheckFailed == 1){
                releaseByNormalCheckDelay();
            } else {
                boolean result = UIController.getInstance().moveToBack();
                if (!result) {
                    SystemApi.getInstance().onInspectionFinished(Definition.INSPECTION_FINISHED_FIRST);
                }
            }
        }

        SystemUtils.setThreeFinger(true);
        this.release(mReqId, RESULT_OK, null);
    }

    private void releaseByNormalCheckDelay() {
        float distance = ProductInfo.isSaiphXdOrBigScreen()
                || ProductInfo.isSaiphChargeIr() || ProductInfo.isSaiphPro()
                || ProductInfo.isAlnilamPro() || ProductInfo.isMeissaPlus() ? 0.25f : 0.1f;
        distance += MapUtils.getStopChargeMoveOffset();

        SystemApi.getInstance().goForward(0, 0.2f, distance, new CommandListener() {

            @Override
            public void onResult(int result, String message, String extraData) {
                Log.d(TAG, "releaseByNormalCheckDelay:goForward:onResult: result=" + result + " msg=" + message);
            }
        });
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                boolean result = UIController.getInstance().moveToBack();
                Log.d(TAG, "releaseByNormalCheckDelay:run: result=" + result);
                if (!result) {
                    SystemApi.getInstance().onInspectionFinished(Definition.INSPECTION_FINISHED_FIRST);
                }
            }
        }, 4000);
    }

    @Override
    protected void onFinish() {
        mHandler.removeMessages(MSG_START_PLAT_SOUND);
        mHandler.removeMessages(MSG_STOP_PLAT_SOUND);
        mHandler.sendEmptyMessage(MSG_STOP_PLAT_SOUND);
        SystemUtils.setThreeFinger(true);
        super.onFinish();
    }

    private void startInspection(int reqId, String params) {
        Log.d(TAG, "startInspection reqId:" + reqId + ", params:" + params);
        if (ModuleDef.FIRST_BOOT.equals(mParam)) {
            SystemApi.getInstance().onInspectionStart();
        }
        mReqId = reqId;
        mCheckStatus = SelfCheckStatus.CHECKING;
        RadarManager.setAllowCloseRadar(false);
        if (!ModuleDef.NORMAL_CHECK.equals(mParam)) {
            String result = SystemApi.getInstance().getInspectResult();
            Log.i(TAG, "startInspection: not force inspect result=" + result);
            if (!TextUtils.isEmpty(result)) {
                dealWithInspectResult(result);
                return;
            }
        }
        callChain.invokeNodeMethod("startInspection");
        callChain.invokeNodeInfo(params, "start inspect");
        callChain.report();
        SystemApi.getInstance().startInspection(reqId, TIME_OUT_INTERVAL, new ActionListener() {
            @Override
            public void onResult(int status, String responseString, String extraData) {
                Log.d(TAG, "status:" + status + ", result:" + responseString);
                if (status == Definition.ACTION_RESPONSE_STOP_SUCCESS) {
                    return;
                }
                callChain.invokeNodeInfo("status:" + status + " ++ response:" + responseString, "inspection result return! ");
                callChain.report();
                dealWithInspectResult(responseString);
            }

            @Override
            public void onError(int errorCode, String errorString, String extraData) {
                Log.d(TAG, "onError errorCode:" + errorCode + ", errorString:" + errorString);
                errorMsg.append(errorCode).append(":").append(errorString);
                invokeResponse(null, null);
                callChain.invokeNodeInfo(errorCode + errorString, "inspect action return error",
                        CallChainReport.NodeInfo.TYPE_ERROR);
                callChain.report();
            }
        });
    }

    private void dealWithInspectResult(String result) {
        Gson gson = new Gson();
        InspectionResult bean = gson.fromJson(result, InspectionResult.class);
        mErrorId = generateErrorId();
        invokeResponse(bean, result);
        RadarManager.setAllowCloseRadar(true);
    }

    private void reportInspectBi(InspectionResult bean, String inspectResult, int rebootAction) {
        if (bean == null) {
            Log.i(TAG, "reportInspectBi: bean is null,don't report inspect bi");
            return;
        }
        List<InspectionResult.FailBean> failBeans = bean.getFail();
        boolean isHasFailItem = false;
        if (failBeans != null) {
            isHasFailItem = failBeans.size() > 0;
        }
        boolean isFailItemShow = bean.getResult() != 1;
        int inspectBiType = isFailItemShow ? BiInspectionReport.SHOW_TO_USER
                : BiInspectionReport.NO_SHOW_TO_USER;
        BiInspectionReport biInspectionReport = new BiInspectionReport();
        biInspectionReport.addData(inspectResult)
                .addResult(bean.getResult())
                .addType(isHasFailItem ? inspectBiType : "")
                .addErrorId(isHasFailItem ? mErrorId : "")
                .addAction(rebootAction)
                .report();
    }

    private void stopInspection() {
        Log.d(TAG, "stopInspection current check state:" + mCheckStatus);
        if (mCheckStatus == SelfCheckStatus.IDLE
                || mCheckStatus == SelfCheckStatus.CHECK_ERROR) {
            stop();
        }
        Log.d(TAG, "stopInspection open asrRecognize");
        openAsrRecognize();
    }

    private void openAsrRecognize() {
        //open speech asr recognize
        Log.d(TAG, "openAsrRecognize open asrRecognize");
        SkillManager.getInstance().openSpeechAsrRecognize();
        ApplicationWrapper.setIsAlreadyOpenAsr(true);
    }

    private void invokeResponse(InspectionResult bean, String result) {
        SystemApi.getInstance().setXDPowerEnable(0, false);
        openAsrRecognize();
        Log.d(TAG, "invokeResponse bean:" + bean);
        if (Build.VERSION.SDK_INT < 28) {
            if (ModuleDef.FIRST_BOOT.equals(mParam) && !dealShutDownReason()) {
                Log.i(TAG, "android power on may be abnormal, reboot!!!");
                mContext.startActivity(new Intent(Intent.ACTION_REBOOT));
                return;
            }
        }
        if (bean == null) {
            mCheckStatus = SelfCheckStatus.CHECK_ERROR;
            sendInspectError(true);
        } else {
            if (!doReboot(bean.isOtaResult())) {
                boolean inspectResult = inspectResult(bean);
                Log.d(TAG, "inspection result: " + inspectResult);
                if (inspectResult) {
                    resetAutoRetryRebootValue();
                    reportInspectBi(bean, result, BiInspectionReport.ACTION_SUCCESS);
                    stop();
                } else {
                    mCheckStatus = SelfCheckStatus.CHECK_ERROR;
                    int canReboot = SettingsUtil.getInt(mContext, SettingsUtil.ROBOT_SETTING_INSPECT_FAIL_AUTO_REBOOT,
                            ModuleDef.SETTING_CAN_REBOOT_ROBOT);
                    Log.d(TAG, "invokeResponse canReboot=" + canReboot);
                    boolean needReport = (ProductInfo.isMiniProduct() && canReboot == ModuleDef.SETTING_CAN_REBOOT_ROBOT)
                            || ((ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) && canReboot < ModuleDef.SETTING_CAN_REBOOT_ROBOT_COUNT);

                    checkReportInspectBi(bean, result, needReport);
                    sendInspectError(bean.isOtaResult());
                }
            }
        }
    }

    public void checkReportInspectBi(InspectionResult bean, String result, boolean needReboot) {
        if (needReboot) {
            reportInspectBi(bean, result, BiInspectionReport.ACTION_FAIL_REBOOT);
        } else {
            reportInspectBi(bean, result, BiInspectionReport.ACTION_FAIL_ALREADY_REBOOT);
        }
    }

    private boolean dealShutDownReason() {
        callChain.invokeNodeMethod("dealShutDownReason");
        int shutReasonFlag = SharedPrefUtil.getInstance().getInt("develop_shut_reason_flag");
        if (shutReasonFlag == 0) {
            SharedPrefUtil.getInstance().putInt("develop_shut_reason_flag", 1);
            Log.i(TAG, "first judge shutdown reason, return!!");
            callChain.invokeNodeInfo("normal", "first judge shutdown reason");
            callChain.report();
            return true;
        }
        String reason = getSysShutReason();
        Log.i(TAG, "android last shutdown reason: " + reason);
        cleanSysShutReason();
        callChain.invokeNodeInfo(reason, "get shutdown reason");
        callChain.report();
        if ("normal".equals(reason) || "long_press".equals(reason) || "sleep".equals(reason)) {
            return true;
        }
        AbnormalReport report = new AbnormalReport(this.getClass().getName(), "dealShutDownReason");
        report.setAbnormalInfo("abnormal_reboot", "", TextUtils.isEmpty(reason) ? "other" : reason);
        report.report();
        return false;
    }

    private void cleanSysShutReason() {
        Method systemPropertiesSet = null;
        try {
            systemPropertiesSet = Class.forName("android.os.SystemProperties").getMethod("set",
                    String.class, String.class);
            systemPropertiesSet.invoke(null, "debug.tp.c_shutdown", "1");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String getSysShutReason() {
        try {
            String ret;
            Method systemPropertiesGet = Class.forName("android.os.SystemProperties").getMethod("get", String.class);
            if ((ret = (String) systemPropertiesGet.invoke(null, "persist.sys.shutdown")) != null)
                return TextUtils.isEmpty(ret) ? "" : ret;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    private boolean doReboot(boolean otaResult) {
        if (otaResult) {
            SharedPrefUtil.getInstance().putInt(ModuleDef.KEY_SP_OTA_REBOOT, 0);
            SharedPrefUtil.getInstance().putInt(ModuleDef.NORMAL_CHECK_FAILED, 0);
            return false;
        } else {
            if (ModuleDef.NORMAL_CHECK.equals(mParam)) {
                SharedPrefUtil.getInstance().putInt(ModuleDef.NORMAL_CHECK_FAILED, 1);
            }
            callChain.invokeNodeMethod("doReboot");
            int rebootTime = SharedPrefUtil.getInstance().getInt(ModuleDef.KEY_SP_OTA_REBOOT);
            callChain.invokeNodeInfo("reboot time: " + rebootTime, "ota fail, will try three time by reboot");
            callChain.report();
            Log.d(TAG, "ota fail, rebootTime:" + rebootTime);
            if (rebootTime < OTA_FAIL_REBOOT_TIME) {
                SharedPrefUtil.getInstance().putInt(ModuleDef.KEY_SP_OTA_REBOOT, ++rebootTime);
                Intent intent = new Intent(Intent.ACTION_REBOOT);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                mContext.startActivity(intent);
                return true;
            } else {
                return false;
            }
        }
    }

    private boolean inspectResult(InspectionResult bean) {
        Log.i(TAG, "inspectResult: " + bean);
        if (bean.getResult() != 1) {
            List<InspectionResult.FailBean> fails = bean.getFail();
            if (fails == null || fails.isEmpty()) {
                errorMsg.append(" ");
                return bean.getResult() == 1;
            }
            for (InspectionResult.FailBean fail : fails) {
                if (fail.isIgnore()) {
                    Log.i(TAG, "inspectResult: ignore item fail:" + fail);
                    continue;
                }
                errorMsg.append(formatErrorMsg(fail));
            }
        }
        if (!bean.isOtaResult()) {
            errorMsg.append(String.format(Locale.ENGLISH, "%d.%s\n",
                    mLineNum++, ResUtil.getString(R.string.ota_inspection_failed)));
            return false;
        }
        return bean.getResult() == 1;
    }

    private String formatErrorMsg(InspectionResult.FailBean fail) {
        Context context = ApplicationWrapper.getContext();
        int id = context.getResources().getIdentifier(fail.getErrorMsg(), "string", context.getPackageName());
        String itemErrorMsg = id > 0 ? context.getString(id) : fail.getErrorMsg();
        String msg = String.format(Locale.ENGLISH, "%d.%s\n", mLineNum++, itemErrorMsg);
        Log.i(TAG, "inspectResult: error msg:" + msg);
        return msg;
    }

    private void sendInspectError(boolean otaResult) {
        if (ModuleDef.FIRST_BOOT.equals(mParam) && otaResult
                && !(ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMiniProduct() || ProductInfo.isMeissa2())) {
            Log.i(TAG, "sendInspectError，first boot");
            ControlManager.getControlManager().getSystemStatusManager().setInspectionResult(errorMsg.toString(), mErrorId);
            stop();
        } else {
            Log.i(TAG, "sendInspectError，show inspect error fragment");
            if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus()) {
                LightManager.getInstance().setLightEffectType(LightManager.LIGHT_EFFECT_TYPE
                        .LIGHT_EFFECT_YELLOW_LIGHT);
            }
            excuteInspectFailRebootRobot();
        }
    }

    private void registerObserver() {
        ApplicationWrapper
                .getContext()
                .getContentResolver()
                .registerContentObserver(Uri.parse(LOGIN_URI), true, mTokenObserver);
    }

    private void playPowerOnSound() {
        if (mMediaPlayer != null) {
            if (mMediaPlayer.isPlaying()) {
                mMediaPlayer.stop();
            }
            mMediaPlayer.release();
            mMediaPlayer = null;
        }
        Log.i(TAG, "play power on sound");
        mMediaPlayer = MediaPlayer.create(ApplicationWrapper.getContext(), R.raw.power_on);
        mMediaPlayer.start();
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
        if (type == ModuleDef.LOCAL_MESSAGE_STOP_INSPECTION) {
            stopInspection();
        }
    }

    private void releaseByNormalCheck() {
        UIController.getInstance().moveToBack();
        mBiGbCharging.stopChargingBaseResult().ctime().mode(1)
                .eq(ControlManager.getControlManager().getSystemStatusManager().getBatteryLevel()).report();

        goForward();
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                Log.d(TAG, "send finish to core");
                SystemApi.getInstance().onInspectionFinished(Definition.INSPECTION_FINISHED_NORMAL);
                release(mReqId, RESULT_OK, null);
            }
        }, 4000);
    }

    private void goForward(){
        float distance = ProductInfo.isSaiphXdOrBigScreen()
                || ProductInfo.isSaiphChargeIr() || ProductInfo.isSaiphPro()
                || ProductInfo.isAlnilamPro() || ProductInfo.isMeissaPlus() ? 0.25f : 0.1f;
        distance += MapUtils.getStopChargeMoveOffset();
        SystemApi.getInstance().goForward(0, 0.2f, distance, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                Log.d(TAG, "goForward:onResult result=" + result + " msg=" + message);
            }
        });
    }

    class CommandListenerProxy extends CommandListener {

        static final int TYPE_GET_CHARGE_PILE = 1;
        static final int TYPE_RESET_ESTIMATE_CHARGING_PILE = 2;
        static final int TYPE_IS_ESTIMATE = 3;
        private CommandListener listener;

        void reset() {
            listener = null;
        }

        void setCommandType(int type) {
            Log.i(TAG, "set command type: " + type);
            switch (type) {
                case TYPE_GET_CHARGE_PILE:
                    listener = new GetChargePilePoseListener();
                    break;

                case TYPE_IS_ESTIMATE:
                    listener = new IsEstimateListener();
                    break;

                case TYPE_RESET_ESTIMATE_CHARGING_PILE:
                    listener = new FixEstimateListener();
                    break;

                default:
                    listener = null;
                    break;
            }
        }

        @Override
        public void onResult(int result, String message, String extraData) {

            if (mState == ModuleDef.MODULE_STATE_IDLE) {
                Log.i(TAG, "state is idle, return!");
                return;
            }

            if (listener != null) {
                listener.onResult(result, message, extraData);
            } else {
                Log.i(TAG, "listener is null, how that?");
            }
        }
    }

    //command listener for SystemApi.getInstance().isRobotEstimate
    class IsEstimateListener extends CommandListener {
        @Override
        public void onResult(int result, String message, String extraData) {
            Log.d(TAG, "isRobotEstimate result : " + result + ", msg :" + message);
            switch (result) {
                case Definition.RESULT_OK:
                    if ("true".equals(message)) {
                        releaseByNormalCheck();
                    } else {
                        mCommandListenerProxy.setCommandType(CommandListenerProxy.TYPE_GET_CHARGE_PILE);
                        SystemApi.getInstance().getLocation(mReqId,
                                Definition.CHARGING_POINT_TYPE,
                                mCommandListenerProxy);
                    }
                    break;
                case Definition.RESULT_FAILURE:
                case Definition.RESULT_STOP:
                    mCommandListenerProxy.setCommandType(CommandListenerProxy.TYPE_GET_CHARGE_PILE);
                    SystemApi.getInstance().getLocation(mReqId,
                            Definition.CHARGING_POINT_TYPE,
                            mCommandListenerProxy);
                    break;
            }
        }
    }

    //command listener for SystemApi.getInstance().getLocation
    class GetChargePilePoseListener extends CommandListener {
        @Override
        public void onResult(int result, String message, String extraData) {
            Log.i(TAG, "Get charging pile result:" + result + " message:" + message);
            Pose mChargingPose = new Pose();
            try {
                JSONObject jsonObject = new JSONObject(message);
                boolean state = jsonObject.optBoolean(Definition.JSON_NAVI_SITE_EXIST, false);
                if (state) {
                    float x = (float) jsonObject.getDouble(Definition.JSON_NAVI_POSITION_X);
                    float y = (float) jsonObject.getDouble(Definition.JSON_NAVI_POSITION_Y);
                    float theta = (float) jsonObject.getDouble(Definition.JSON_NAVI_POSITION_THETA);
                    mChargingPose.setX(x);
                    mChargingPose.setY(y);
                    mChargingPose.setTheta(theta);

                    mCommandListenerProxy.setCommandType(CommandListenerProxy.TYPE_RESET_ESTIMATE_CHARGING_PILE);
                    SystemApi.getInstance().setFixedEstimate(mReqId, mGson.toJson(mChargingPose), mCommandListenerProxy);
                } else {
                    releaseByNormalCheck();
                }
            } catch (JSONException e) {
                releaseByNormalCheck();
                e.printStackTrace();
            }
        }
    }

    //command listener for SystemApi.getInstance().setFixedEstimate
    class FixEstimateListener extends CommandListener {
        @Override
        public void onResult(int result, String message, String extraData) {
            Log.i(TAG, "Set pose estimate result:" + result + " message:" + message);

            if (Definition.RESULT_OK == result && "succeed".equals(message)) {
                Log.i(TAG, "reset estimate success");
                SkillManager.getInstance().speechPlayText(ResUtil.getString(R.string.reposition_reset_success));
            }
            mCommandListenerProxy.reset();
            releaseByNormalCheck();
        }
    }

    private String generateErrorId() {
        mTimeStamp = System.currentTimeMillis();
        return RobotSettings.getSystemSn() + "-" + mTimeStamp;
    }

    /**
     * 如果首次自检失败，自动重启一次，随后只有自检成功再重置状态。
     */
    private void resetAutoRetryRebootValue() {
        if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMiniProduct() || ProductInfo.isMeissa2()) {
            SettingsUtil.putInt(mContext, SettingsUtil.ROBOT_SETTING_INSPECT_FAIL_AUTO_REBOOT,
                    ModuleDef.SETTING_CAN_REBOOT_ROBOT);
        }
    }

    /**
     * 如果是招财豹，自检失败后自动重启一次
     */
    private void excuteInspectFailRebootRobot() {
        int canReboot = SettingsUtil.getInt(mContext, SettingsUtil.ROBOT_SETTING_INSPECT_FAIL_AUTO_REBOOT,
                ModuleDef.SETTING_CAN_REBOOT_ROBOT);
        Log.d(TAG, "excuteInspectFailRebootRobot canReboot=" + canReboot);
        if ((ProductInfo.isMiniProduct() && canReboot == ModuleDef.SETTING_CAN_REBOOT_ROBOT)
                || ((ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) && canReboot < ModuleDef.SETTING_CAN_REBOOT_ROBOT_COUNT)) {

            Log.d(TAG, "excuteInspectFailRebootRobot reboot robot!");
            SettingsUtil.putInt(mContext, SettingsUtil.ROBOT_SETTING_INSPECT_FAIL_AUTO_REBOOT,
                    ProductInfo.isMiniProduct() ? ModuleDef.SETTING_CANNOT_REBOOT_ROBOT : canReboot + 1);

            Intent intent = new Intent(Intent.ACTION_REBOOT);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            mContext.startActivity(intent);
            return;
        }
        Log.d(TAG, "excuteInspectFailRebootRobot show error ui!");
        SkillManager.getInstance().speechPlayText(ResUtil.getString(R.string.inspect_err_speech_tips));
        SystemUtils.setThreeFinger(false);
        Bundle bundle = new Bundle();
        bundle.putString(ModuleDef.INSPECT_ERROR_MESSAGE, errorMsg.toString());
        bundle.putString(ModuleDef.INSPECT_NAVI_ERROR_ID, mErrorId);
        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_INSPECT_ERROR, bundle, null);
    }

}
