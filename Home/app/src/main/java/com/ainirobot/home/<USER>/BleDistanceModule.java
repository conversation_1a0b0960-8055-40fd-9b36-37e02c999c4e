package com.ainirobot.home.module;

import android.content.Context;
import android.media.AudioManager;
import android.os.Handler;
import android.os.Looper;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.coreservice.listener.IMusicListener;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.utils.ResUtil;
import com.ainirobot.home.utils.SystemUtils;

public class BleDistanceModule extends BaseModule {

    private static final String TAG = "BleDistanceModule:Home";

    private static final long TTS_INTERVAL = 10 * Definition.SECOND;
    public static final Handler mHandler = new Handler(Looper.getMainLooper());
    private static BleDistanceModule sInstance = null;
    private Context mContext;
    private volatile boolean mIsInClose = false;
    private volatile boolean mIsInBleDanger = false;// 底层的实时的danger状态
    private AudioManager audioManager;


    public static BleDistanceModule getInstance() {
        if (sInstance == null) {
            sInstance = new BleDistanceModule();
        }
        return sInstance;
    }

    private BleDistanceModule() {}

    public void init(Context context) {
        mContext = context;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.d(TAG, "reqId=" + reqId + "; intent=" + intent + "; params=" + params);
        switch (intent) {
            case Definition.REQ_BLE_SIGNAL_NEAR:
                mIsInBleDanger = true;
                if (!mIsInClose){
                    mIsInClose = true;
                    SystemApi.getInstance().resetHead(0, null);
                    SystemUtils.setThreeFinger(false);// 禁止三指下拉状态栏
                    playWarningAudio();
                    UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_BLE_NEAR, null, null);
                }else {
                    Log.d(TAG, "already in  BleDistanceModule , handling req_ble_signal_near");
                }
                break;
            case Definition.REQ_BLE_SIGNAL_FAR:
                mIsInBleDanger = false;
//                stop();
                break;
        }
        SystemApi.getInstance().finishModuleParser(reqId, true);
        return false;
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
        switch (type) {
            case ModuleDef.LOCAL_MESSAGE_BLE_PASSWORD_UNLOCKED:
                exitBleControl();
            default:
                break;
        }
    }

    private void exitBleControl() {
        Log.d(TAG, "exitBleControl is in danger :"+ mIsInBleDanger);
        if (!mIsInBleDanger){
            stop();
        }else {
            UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.CURRENT_BLE_NEAR_DANGER,"");
        }
    }

    private void playWarningAudio() {
        Log.d(TAG, "start to play warning audio ");
        audioManager = (AudioManager) ApplicationWrapper.getContext()
                .getSystemService(Context.AUDIO_SERVICE);
        ControlManager.getControlManager().setLastVolumeIndex(audioManager.getStreamVolume(AudioManager.STREAM_MUSIC));
        int musicVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
        audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, musicVolume, 0);

//        playMusic(R.raw.ble_danger_warning);
        voice(mContext.getString(R.string.ble_danger_tts));
    }

    /**
     * 播报功能
     */
    private void voice(String tts) {
        Log.d(TAG, "voice : " + tts);
        SkillManager.getInstance().speechPlayText(tts);
    }

    @Override
    protected void onStop() {
        super.onStop();
        mIsInClose = false;
//        mHandler.removeCallbacks(playTextRunnable);
        SkillManager.getInstance().stopTTSOnly();
        SkillManager.getInstance().stopMusic();
        audioManager.setStreamVolume(AudioManager.STREAM_MUSIC,
                ControlManager.getControlManager().getLastVolumeIndex(), 0);
        SystemUtils.setThreeFinger(true);// 恢复三指下拉
        //通知CoreService SystemStatus状态清理
        SystemApi.getInstance().onBleSystemControlFinished();
    }
}


