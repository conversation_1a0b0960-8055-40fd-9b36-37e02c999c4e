package com.ainirobot.home.ui.view;

import android.content.Context;
import android.support.annotation.Nullable;
import android.support.constraint.ConstraintLayout;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;

import com.ainirobot.home.R;

/**
 * 定位方式选择页
 */
public class RelocationTypeChoiceView extends FrameLayout {
    private static final String TAG = RelocationTypeChoiceView.class.getSimpleName();
    public View mRepositionBack;
    public ConstraintLayout mChoosePointLocate,
            mChoosePileLocate;

    public RelocationTypeChoiceView(Context context) {
        this(context, null);
    }

    public RelocationTypeChoiceView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RelocationTypeChoiceView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public RelocationTypeChoiceView(Context context, @Nullable AttributeSet attrs, int defStyleAttr,
                                    int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initSubViews(context);
    }

    private void initSubViews(Context context) {
        View mView = LayoutInflater.from(context).inflate(R.layout.layout_reposition_choose, this);
        mRepositionBack = mView.findViewById(R.id.relocate_choose_type_cancel);
        mChoosePointLocate = (ConstraintLayout) mView.findViewById(R.id.choose_point_locate);
        mChoosePileLocate = (ConstraintLayout) mView.findViewById(R.id.choose_pile_locate);
    }

}
