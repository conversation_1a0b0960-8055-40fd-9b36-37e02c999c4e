package com.ainirobot.home.ota.task;

import static com.ainirobot.home.ota.bean.VersionData.STATUS.FAILED;
import static com.ainirobot.home.ota.bean.VersionData.STATUS.SUCCESS;
import static com.ainirobot.home.ota.bean.VersionData.STATUS.WAITING;
import static com.ainirobot.home.ota.constants.OtaConstants.CMD_TIMEOUT;
import static com.ainirobot.home.ota.constants.OtaConstants.HOST_VERSION;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_AC_CLIENT;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_BMS;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_HOST;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_MOTOR_LEFT;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_MOTOR_RIGHT;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_PSB;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_TK1;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_TX1;
import static com.ainirobot.home.ota.constants.OtaConstants.OTA_UPDATE_STATUS_COMPLIETE;
import static com.ainirobot.home.ota.constants.OtaConstants.PARAM_BOARD;
import static com.ainirobot.home.ota.constants.OtaConstants.PARAM_CODE;
import static com.ainirobot.home.ota.constants.OtaConstants.PARAM_FREE_SIZE;
import static com.ainirobot.home.ota.constants.OtaConstants.PARAM_MESSAGE;
import static com.ainirobot.home.ota.constants.OtaConstants.PARAM_PATHS;
import static com.ainirobot.home.ota.constants.OtaConstants.PARAM_STATUS;
import static com.ainirobot.home.ota.constants.OtaConstants.PARAM_VERSION;
import static com.ainirobot.home.ota.constants.OtaConstants.TX1_OTA_DIR;
import static com.ainirobot.home.ota.constants.OtaConstants.getOsByBoardId;
import static com.ainirobot.home.ota.network.NetDefine.OTA_STATUS.OTA_FAILED;
import static com.ainirobot.home.ota.network.NetDefine.OTA_STATUS.OTA_START;
import static com.ainirobot.home.ota.network.NetDefine.OTA_TYPE.OTA_INSTALLING;


import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.actionbean.CanOtaBean;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.ota.bean.InstallData;
import com.ainirobot.home.ota.bean.VersionData;
import com.ainirobot.home.ota.config.OtaConfig;
import com.ainirobot.home.ota.constants.DowngradeConstants;
import com.ainirobot.home.ota.constants.OtaConstants;
import com.ainirobot.home.ota.database.DataBaseManager;
import com.ainirobot.home.ota.executor.ThreadPoolManager;
import com.ainirobot.home.ota.network.NetDefine;
import com.ainirobot.home.ota.network.NetHelper;
import com.ainirobot.home.ota.parser.IParserInterface;
import com.ainirobot.home.ota.service.OtaApiHelper;
import com.ainirobot.home.ota.utils.MessageGenerater;
import com.ainirobot.home.ota.utils.Preferences;
import com.ainirobot.home.ota.utils.SettingsUtils;
import com.ainirobot.home.ota.utils.Utils;
import com.ainirobot.home.ota.utils.Scp;
import com.google.gson.Gson;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;

public class InstallThreadTask extends AbstractOtaTask implements Runnable {
    private static final String TAG = InstallThreadTask.class.getSimpleName() + "Home:";
    private final AtomicInteger mPsbUpgradeFailCount = new AtomicInteger(0);   // 记录单个psb模块失败的次数
    private final AtomicInteger mHeadUpgradeFailCount = new AtomicInteger(0);   // 记录单个Head模块失败的次数
    private final Object mInstallLock = new Object();
    private final Gson mGson = new Gson();
    private volatile boolean isHeadConnected = false;
    private final ConcurrentMap<String, String> mOsVersionMap = new ConcurrentHashMap<>();

    /**
     * host update.zip verify listener.
     */
    private final HostInstallTask.HostProgressCallback mHostProgressCallback = new HostInstallTask.HostProgressCallback() {

        @Override
        public void onProgress(int progress) {
        }

        @Override
        public void onVerifyFailed(int errorCode, Object object) {
            Log.e(TAG, "update.zip verify failed. errorCode=" + errorCode);
            mPreference.setOtaIsUpgrading(false);
            checkAndUpdateHostResult();

            checkNotifyInstallLock();

            Log.d(TAG,"host 升级文件校验失败");
        }

        @Override
        public void onCopyProgress(int progress) {
        }

        @Override
        public void onCopyResult(int errorCode, Object object) {
        }

        @Override
        public void onInstallSuccess() {
            Log.d(TAG, "Host update success.");
            checkNotifyInstallLock();
        }

        @Override
        public void onInstallError(Exception e) {
            Log.e(TAG, "onInstallError host install failed.");
            mPreference.setOtaIsUpgrading(false);
            checkAndUpdateHostResult();
            checkNotifyInstallLock();
            e.printStackTrace();
            Log.e(TAG,"host got exception " + e.getMessage());
        }
    };

    public InstallThreadTask(Context context, Preferences preferences, NetHelper netHelper,
                             OtaApiHelper otaApiHelper, DataBaseManager dataBaseManager, Scp scpHelper,
                             Handler mainHandler, IParserInterface iParserInterface, OtaTaskFactory otaTaskFactory, OtaConfig otaConfig, ThreadPoolManager threadPoolManager) {
        super(context, preferences, netHelper, otaApiHelper, dataBaseManager, scpHelper, mainHandler, iParserInterface, otaTaskFactory, otaConfig, threadPoolManager);
        mInstallTask = new HostABUpdater(mContext);
        mInstallTask.setOnProgressListener(mHostProgressCallback);
        Log.d(TAG, "InstallThreadTask 初始化 success");
    }


    // for can update start and result check, if got internal error just quit task thread.
    private boolean startCanUpdate(String osName, int boardId) {
        Log.d(TAG, "startCanUpdate. osName:" + osName + " boardId:" + boardId);
        String result = null;
        JSONObject jsonObject = MessageGenerater.
                generateCanUpdateMessage(boardId, mPreference.getOsPackagePath(osName));


        // start can update and check start result.
        result = mOtaApiHelper.startCanUpdate(jsonObject.toString(), OTA_INSTALL_PACKAGE_TIMEOUT);
        if (result == null || result.isEmpty()) {
            Log.e(TAG, "startCanUpdate send failed. internal error.");
            setOtaUpdateFailedReason(osName + " upgrade result is null or empty", null);
            return false;
        }

        if (result.contains(CMD_TIMEOUT)) {
            Log.e(TAG, "startCanUpdate send failed. timeout.");
            setOtaUpdateFailedReason(osName + " update failed.", CMD_TIMEOUT);
            return false;
        }

        //internal error will be sent in this function.
        if (!parseCanUpdateStartResult(result)) {
            Log.d(TAG, "parseCanUpdateStartResult got error. exit install task.");
            setOtaUpdateFailedReason(osName + " parse update result error", null);
            return false;
        }

        //parse can update final result
        result = mOtaApiHelper.getCanOtaState(-1);
        if (!parseCanUpdateStateResult(result)) {
            Log.d(TAG, "parseCanUpdateStartResult got error. exit install task.");
            return false;
        }

        return true;
    }

    private boolean installCan(InstallData osData) {
        Log.d(TAG, "install " + osData.getOsName() + "start: ");

        uploadUpdateProgress(OTA_INSTALLING, OTA_START, "install " + osData.getOsName(),
                NetDefine.getInstallProgressId(osData.getOsName()));

        // internal error. need break.
        if (!startCanUpdateWithRetry(osData.getOsName(), OtaConstants.getBoardIdByOs(osData.getOsName()))) {
            return false;
        }

        // wait 3s for ac_client and bms upgradation when psb is upgraded.
        boolean needUpgradeAcclientOrBms = installTaskQueue.contains(new InstallData(OS_AC_CLIENT)) ||
                installTaskQueue.contains(new InstallData(OS_BMS));
        if (osData.equals("psb") && needUpgradeAcclientOrBms && !checkIfInstallHasFailed()) {
            try {
                Log.d(TAG, "psb sleep 3s");
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        return true;
    }

    /**
     * 失败后需重试2次，所以最多进行3次
     *
     * @param osName 系统任务名称
     * @param boardIdByOs
     * @return 返回can升级结果, 包括重试
     */
    private boolean startCanUpdateWithRetry(String osName, Integer boardIdByOs) {
        do {
            Log.d(TAG, "startCanUpdateWithRetry count : " + mPsbUpgradeFailCount.get());
            boolean b = startCanUpdate(osName, boardIdByOs);
            if (b) {
                mPsbUpgradeFailCount.set(0);
                return true;
            }
        } while (ProductInfo.ProductModel.CM_MINI_TOB.model.equals(RobotSettings.getProductModel())
                && mPsbUpgradeFailCount.getAndIncrement() < 3);

        return false;
    }

    /**
     * 失败后需重试2次，所以最多进行3次
     *
     * @param installData 安装数据实体类
     * @param remoteDir
     * @return 返回 Head升级结果,包括重试
     */
    private boolean startInstallHeadWithRetry(InstallData installData, String remoteDir) {
        do {
            Log.d(TAG, "startInstallHeadWithRetry count :" + mHeadUpgradeFailCount.get());
            boolean b = startInstallHead(installData, remoteDir);
            if (b) {
                mHeadUpgradeFailCount.set(0);
                return true;
            }

        } while (mHeadUpgradeFailCount.getAndIncrement() < 3);

        return false;
    }

    private boolean installHead(InstallData osData) {
        Log.d(TAG, "install " + osData.getOsName() + " call getHeadUpdateParams first.");
        String remoteDir = mPreference.getTx1packagePath(TX1_OTA_DIR);
        Log.d(TAG, "remoteDir = " + remoteDir);

        Log.d(TAG, "获取" + osData.getOsName() + "参数.");
        String model = RobotSettings.getProductModel();
        if (ProductInfo.ProductModel.CM_MINI_TOB.model.equals(model)) {
            return startInstallHeadWithRetry(osData, remoteDir);
        } else {
            return startInstallHead(osData, remoteDir);
        }
    }

    private boolean startInstallHead(InstallData osData, String remoteDir) {
        String result;
        String filePath;
        JSONObject jsonObject;
        String retHeadParams = mOtaApiHelper.getHeadUpdateParams(10 * 1000);
        if (retHeadParams == null || retHeadParams.isEmpty()) {
            Log.e(TAG, "get Tx1UpdateParams failed! use default params.");
        } else {
            parseTx1GetUpdateStatusResult(retHeadParams);
            remoteDir = mPreference.getTx1packagePath(TX1_OTA_DIR);
        }

        Log.d(TAG,"准备下发" + osData.getOsName() + "升级包");
        String retHeadScp = mOtaApiHelper.startHeadScp(10 * 1000);
        if (retHeadScp == null || retHeadScp.isEmpty()) {
            Log.e(TAG, osData.getOsName() + " prepare scp package failed! result is null or empty.");
            setOtaUpdateFailedReason(osData.getOsName() + " prepare scp package result is null or empty", null);
            return false;
        }

        if (retHeadScp.equals(CMD_TIMEOUT)) {
            Log.e(TAG, osData.getOsName() + " prepare scp package failed! timeout.");
            setOtaUpdateFailedReason(osData.getOsName() + " prepare scp package failed!", CMD_TIMEOUT);
            return false;
        }

        if (!parseTx1PreScpResult(retHeadScp)) {
            Log.e(TAG, osData.getOsName() + " prepare scp package failed!");
            setOtaUpdateFailedReason(osData.getOsName() + " parse prepare scp package error", null);
            return false;
        }

        Log.d(TAG, "prepare scp package success.");
        Log.d(TAG, "开始下发" + osData.getOsName() + "升级包...");

        jsonObject = MessageGenerater.
                generateUpdateJsonObject(dataBaseManager, mPreference,
                        headOsArray, osData.getOsName());

        uploadUpdateProgress(OTA_INSTALLING, OTA_START, "install head",
                NetDefine.getInstallProgressId(osData.getOsName()));

        filePath = mPreference.getOsPackagePath(OS_TX1);
        if (remoteDir.isEmpty()) {
            Log.e(TAG, osData.getOsName() + " remoteDir is empty");
            setOtaUpdateFailedReason(osData.getOsName() + " remoteDir is empty", null);
            return false;
        } else {
            boolean putResult = false;
            for (int i = 0; i < 3; i++) {
                if (scpHelper.putFile(filePath, remoteDir)) {
                    putResult = true;
                    break;
                }
            }
            if (!putResult) {
                Log.e(TAG, osData.getOsName() + " send pkg failed! update failed.");
                setOtaUpdateFailedReason(osData.getOsName() + " send pkg failed", null);
                return false;
            }
        }
        Log.d(TAG,osData.getOsName() + " 升级包发送成功！");

        //wait for tx1 install done.
        Log.d(TAG, "send command startUpdate. wait " + osData.getOsName() + " install... "
                + "\nparam=" + jsonObject.toString());
        Log.d(TAG,osData.getOsName() + "开始升级...");

        result = mOtaApiHelper.startHeadUpdate(jsonObject.toString(), OTA_INSTALL_PACKAGE_TIMEOUT);
        if (result == null || result.isEmpty()) {
            Log.e(TAG, "startHeadUpdate send error. internal error.");
            setOtaUpdateFailedReason(osData.getOsName() + " update result is null or empty", null);
            return false;
        }

        if (result.contains(CMD_TIMEOUT)) {
            Log.e(TAG, osData.getOsName() + " update timeout.");
            setOtaUpdateFailedReason(osData.getOsName() + " ota update failed!", CMD_TIMEOUT);
            return false;
        }
        Log.d(TAG, "head update result:" + result);
        // wait head connect status change. timeout 5s.
        try {
            Thread.currentThread().sleep(5 * 1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        // wait max 3min
        Log.d(TAG, "wait max 3min for head connect");
        int MaxTime = 180 * 1000;
        long currentTime = System.currentTimeMillis();
        long waitTime;
        try {
            while (!isHeadConnected) {
                mOtaApiHelper.isHeadConntected();
                waitTime = System.currentTimeMillis();
                if (waitTime - currentTime < MaxTime) {
                    Thread.currentThread().sleep(1000);
                } else {
                    Log.e(TAG, "has wait " + MaxTime + "ms for head connected. just go on.");
                    break;
                }
            }
            // reset head connect status.
            isHeadConnected = false;
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        Log.d(TAG, "head connected");

        //internal err will send in this function.
        if (!parseTx1UpdateResult(result)) {
            Log.d(TAG, "parseTx1UpdateResult got error. exit install task.");
            return false;
        }

        return true;
    }

    private void prepareNaviInstall(InstallData osData, JSONArray jsonNavArray) {
        Log.d(TAG, "install " + osData.getOsName());

        JSONObject jsonNavObject = MessageGenerater.
                generateUpdateJsonObject(dataBaseManager, mPreference,
                        navOsArray, osData.getOsName());
        jsonNavArray.put(jsonNavObject);

        uploadUpdateProgress(OTA_INSTALLING, OTA_START, "install " + osData.getOsName(),
                NetDefine.getInstallProgressId(osData.getOsName()));
    }

    private boolean needInstallNavi(InstallData osData) {
        boolean hasMotorR = installTaskQueue.contains(new InstallData(OS_MOTOR_RIGHT));
        boolean hasNavi = installTaskQueue.contains(new InstallData(OS_TK1));

        if (OS_MOTOR_LEFT.equals(osData.getOsName())) {
            return !(hasMotorR || hasNavi);
        } else if (OS_MOTOR_RIGHT.equals(osData.getOsName())) {
            return !(hasNavi);
        } else return OS_TK1.equals(osData.getOsName());
    }

    private boolean installNavi(JSONArray jsonNavArray) {
        Log.d(TAG, "send command startNavUpdate. wait navigation install... "
                + "\nparam = " + jsonNavArray.toString());
        Log.d(TAG,"Navi boards开始升级...");

        String result;

        result = mOtaApiHelper.startNavigationUpdate(jsonNavArray.toString(), OTA_INSTALL_PACKAGE_TIMEOUT);
        if (result == null || result.isEmpty()) {
            Log.e(TAG, "startTk1Update send error. internal error.");
            setOtaUpdateFailedReason("Navigation update result is null or empty", null);
            return false;
        }

        if (result.contains(CMD_TIMEOUT)) {
            Log.e(TAG, "startTk1Update send error. timeout.");
            setOtaUpdateFailedReason("TK1 ota update failed!", CMD_TIMEOUT);
            return false;
        }
        if (!parseTk1UpdateResult(result)) {
            Log.d(TAG, "parseTk1UpdateResult got error. exit install task.");
            return false;
        }

        return true;
    }

    private boolean installHost(InstallData osData) {
        Log.d(TAG, "start install " + osData.getOsName());

        uploadUpdateProgress(OTA_INSTALLING, OTA_START, "install " + osData.getOsName(),
                NetDefine.getInstallProgressId(osData.getOsName()));

        try {
            mInstallTask.execute();
        } catch (IllegalStateException e) {
            Log.e(TAG, "illegal installTask status. recreate install async task.");
            mInstallTask = new HostInstallTask(mContext);
            mInstallTask.setOnProgressListener(mHostProgressCallback);
            mInstallTask.execute();
        }
        return true;
    }

    @Override
    public void run() {
        try {
            Log.i(TAG, "InstallThreadTask start");
            int totalInstallSize = installTaskQueue.size();
            int installInc = 1;

            JSONArray jsonNavArray = new JSONArray();

            synchronized (mInstallLock) {
                boolean isHostUpdate = false;
                uploadUpdateStatus(OTA_INSTALLING, OTA_START, installOsToString());
                Log.d(TAG, "installTaskQueue: " +
                        String.valueOf(installTaskQueue.isEmpty()) + ", update content： " + new Gson().toJson(installTaskQueue));
                while (!installTaskQueue.isEmpty()) {
                    // 处理待安装任务队列数据
                    InstallData osData = installTaskQueue.poll();

                    if (osData == null) {
                        Log.e(TAG, "install task is null, quit!");
                        break;
                    }

                    mOtaApiHelper.updateProgress(MessageGenerater.
                            generateProgressMessage(installInc++, totalInstallSize));

                    SettingsUtils.setSettingsGlobalOtaInstallingOs(mContext, osData.getType().name());

                    boolean isInstallFailed = false;
                    switch (osData.getType()) {
                        case CAN:
                            if (!installCan(osData)) {
                                Log.d(TAG, "install " + osData.getOsName() + " failed.");
                                isInstallFailed = true;
                            }
                            break;

                        case HEAD:
                            if (!installHead(osData)) {
                                Log.d(TAG, "install " + osData.getOsName() + " failed.");
                                isInstallFailed = true;
                            }
                            break;

                        case NAVI:
                            prepareNaviInstall(osData, jsonNavArray);
                            if (!needInstallNavi(osData)) {
                                break;
                            }
                            if (!installNavi(jsonNavArray)) {
                                Log.d(TAG, "install " + osData.getOsName() + " failed.");
                                isInstallFailed = true;
                            }

                            break;

                        case HOST:
                            isHostUpdate = true;
                            installHost(osData);
                            mInstallLock.wait();
                            break;

                        default:
                            break;
                    }

                    if (isInstallFailed) {
                        updateOtaUpdateStatus(osData.getOsName(), FAILED, null, null);
                    }

                    // check if update failed
                    if (checkIfInstallHasFailed()) {
                        Log.d(TAG, osData.getOsName() + " install update failed! clear queue");

                        uploadUpdateStatus(OTA_INSTALLING, OTA_FAILED,
                                osData.getOsName() + " update failed, reason: " + getOtaUpdateFailedReason(),
                                NetDefine.getInstallProgressId(osData.getOsName()));
                        installTaskQueue.clear();
                        isHostUpdate = false;
                        break;
                    }
                }

                // send ota update finish message if no host update.
                if (isHostUpdate) {
                    mPreference.setOtaIsUpgrading(false);
                    if (SettingsUtils.getIsAbUpdate()) {
                        Log.i(TAG, "AB update finished, and reboot the robot");
                        Log.d(TAG,"升级完成，正在重启机器...");
                        mContext.startActivity(new Intent(Intent.ACTION_REBOOT).setFlags(Intent.FLAG_ACTIVITY_NEW_TASK));
                    }
                } else {
                    Log.i(TAG, "update finished, and reboot the robot");
                    Log.i(TAG,"升级完成，正在重启机器...");
                    mContext.startActivity(new Intent(Intent.ACTION_REBOOT).setFlags(Intent.FLAG_ACTIVITY_NEW_TASK));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RobotSettingApi.getInstance().setRobotString(Definition.ROBOT_SETTING_OTA_TYPE, "1");
            mPsbUpgradeFailCount.set(0);
            mHeadUpgradeFailCount.set(0);
            mInstallStatus = DowngradeConstants.InstallStatus.IDLE;
            clearInstallTaskQueue();
            installThreadRunning = false;
            clearInstallData();
            Log.d(TAG, "InstallThreadTask quit. reset installThreadRunning");
        }
    }

    private boolean parseCanUpdateStartResult(String val) {
        Log.d(TAG, "startCanUpdateResult. " + val);
        boolean ret = true;
        try {
            JSONObject params = new JSONObject(val);
            String result = (String) params.get(Definition.JSON_CAN_OTA_START_RESULT);
            String errorMsg;
            switch (result) {
                case OtaConstants.SUCCESS:
                    break;

                case OtaConstants.FAILED:
                    ret = false;
            }
        } catch (JSONException e) {
            Log.e(TAG, "startCanUpdateResult parseError");
            e.printStackTrace();
            ret = false;
        }

        return ret;
    }

    private boolean parseTk1UpdateResult(String val) {
        Log.d(TAG, "parseTk1UpdateResult. val=" + val);

        boolean ret = true;
        try {
            JSONObject params = new JSONObject(val);
            String result = (String) params.get(PARAM_STATUS);
            int errorCode;
            String errorMsg;

            switch (result) {
                case OtaConstants.SUCCESS:
                    Log.d(TAG, "tk1 update success.");
                    ret = checkAndUpdateTk1UpgradeResult();
                    if (!ret) Log.e(TAG, "tk1_update_result check failed.");
                    break;

                case OtaConstants.FAILED:
                    errorCode = params.getInt(PARAM_CODE);
                    errorMsg = params.getString(PARAM_MESSAGE);
                    Log.e(TAG, "tk1 update failed. "
                            + " errorCode:" + errorCode
                            + " errorMsg:" + errorMsg);

                    ret = checkAndUpdateTk1UpgradeResult();
                    if (!ret) Log.e(TAG, "tk1_update_result check failed.");
                    break;

                case OtaConstants.ONGOING:
                    Log.d(TAG, "on going");
                    break;
            }
        } catch (JSONException e) {
            Log.e(TAG, "tk1UpdateResult parseError");
            e.printStackTrace();
            ret = false;
        }
        return ret;
    }

    private boolean parseTx1VersionResult(String result) {
        boolean ret = true;
        String type;
        String version;
        JSONObject jsonObject;
        try {
            JSONArray params = new JSONArray(result);

            for (int i = 0; i < params.length(); i++) {
                jsonObject = (JSONObject) params.get(i);
                if (jsonObject != null) {
                    type = (String) jsonObject.get(PARAM_BOARD);
                    version = (String) jsonObject.get(PARAM_VERSION);

                    Log.d(TAG, "versionResult. os type:" + type + " version:" + version);
                    mOsVersionMap.put(type, version);
                }
            }

        } catch (JSONException | NullPointerException e) {
            try {
                jsonObject = new JSONObject(result);
                type = jsonObject.getString(PARAM_BOARD);
                version = jsonObject.getString(PARAM_VERSION);

                Log.d(TAG, "versionResult. os type:" + type + " version:" + version);
                mOsVersionMap.put(type, version);

            } catch (JSONException | NullPointerException f) {
                f.printStackTrace();
                ret = false;
            }
        }
        return ret;
    }

    private void parseTx1GetUpdateStatusResult(String result) {
        Log.d(TAG, "getUpdateStatusResult. result=" + result);
        try {
            JSONObject params = new JSONObject(result);
            String scpPath = (String) params.get(PARAM_PATHS);
            if (scpPath != null && !scpPath.isEmpty()) {
                Log.d(TAG, "set scpPath=" + scpPath);
                mPreference.setTx1PackagePath(scpPath);
            } else {
                Log.e(TAG, "get bad scp path");
            }

            long freeSize = Long.parseLong((String) params.get(PARAM_FREE_SIZE));
            Log.d(TAG, "freeSize=" + freeSize);
            Log.d(TAG,"scpPath=" + scpPath + " freeSize=" + freeSize);
        } catch (JSONException | NumberFormatException e) {
            e.printStackTrace();
        }
    }

    private boolean parseTx1PreScpResult(String result) {
        Log.d(TAG, "parseTx1PreScpResult:" + result);
        boolean ret = false;
        JSONObject jsonObject;
        try {
            jsonObject = new JSONObject(result);
            String status = jsonObject.optString("status");
            if (!TextUtils.isEmpty(status) && "ok".equals(status)) {
                ret = true;
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return ret;
    }

    private boolean parseTx1UpdateResult(String val) {
        Log.d(TAG, "tx1UpdateResult. " + val);
        boolean ret = true;

        try {
            JSONObject params = new JSONObject(val);
            String result = (String) params.get(PARAM_STATUS);
            int errorCode;
            String errorMsg;
            switch (result) {
                case OtaConstants.SUCCESS:
                    Log.d(TAG, "tx1 update success.");

                    ret = checkAndUpdateTx1UpgradeResult();
                    if (!ret)
                        Log.e(TAG, "tx1_update_result check failed.");

                    break;

                case OtaConstants.FAILED:
                    errorCode = params.getInt(PARAM_CODE);
                    errorMsg = params.getString(PARAM_MESSAGE);
                    Log.d(TAG, "tx1 update failed. "
                            + " errorCode:" + errorCode
                            + " errorMsg:" + errorMsg);

                    ret = checkAndUpdateTx1UpgradeResult();
                    if (!ret)
                        Log.e(TAG, "tx1_update_result check failed.");

                    break;

                case OtaConstants.ONGOING:
                    Log.d(TAG, "on going");
                    break;
            }
        } catch (JSONException e) {
            Log.e(TAG, "tx1UpdateResult parseError");
            e.printStackTrace();
            ret = false;
        }

        return ret;
    }

    /**
     * Check algorithm board update result and update database.
     */
    private boolean checkAndUpdateTx1UpgradeResult() {
        String result;
        boolean hasGetVersion = false;

        //check tx1 update version result.
        List<VersionData> dataList = dataBaseManager.getOsData(downOsList);
        for (VersionData data : dataList) {
            if (data.getStatus() == WAITING) {

                if (!hasGetVersion) {
                    hasGetVersion = true;
                    Log.d(TAG, "UpdateTx1UpgradeResult. call getVersion. timeout set 20s");
                    result = mOtaApiHelper.getHeadVersion(20 * 1000);
                    if (result == null || result.isEmpty()) {
                        Log.e(TAG, "getTx1Version failed. internal error!");
                        Log.d(TAG,"获取版本号超时");
                        setOtaUpdateFailedReason("get TX1 version result is null or empty", null);
                        return false;
                    }

                    if (result.contains(CMD_TIMEOUT)) {
                        Log.e(TAG, "getTx1Version failed. timeout!");
                        setOtaUpdateFailedReason("get Tx1 version failed.", CMD_TIMEOUT);
                        return false;
                    }
                    if (!parseTx1VersionResult(result)) {
                        Log.e(TAG, "parseTx1VersionResult error. result=" + result);
                        Log.d(TAG,"版本号结果解析错误");
                        setOtaUpdateFailedReason("parse TX1 version result error", null);
                        return false;
                    }
                }

                String name = data.getName();
                String cVersion = mOsVersionMap.get(name);
                String tVersion = data.getTargetVersion();

                //Compatibility with target version.
                cVersion = handleCurrentVersionAccordingToTargetVersion(tVersion, cVersion);

                Log.d(TAG, "UpdateTx1UpgradeResult. " + " os:" + name
                        + " currentVersion:" + cVersion
                        + " targetVersion:" + tVersion);

                if (cVersion != null && cVersion.equals(tVersion)) {
                    Log.d(TAG, "os:" + name + " update success");
                    Log.d(TAG,name + "升级成功! currentVersion:" + cVersion
                            + " targetVersion:" + tVersion);

                    updateOtaUpdateStatus(name, SUCCESS, null, null);
                } else {
                    Log.d(TAG, "os:" + name + " update failed");
                    Log.d(TAG,name + "升级失败! currentVersion:" + cVersion
                            + " targetVersion:" + tVersion);
                    setOtaUpdateFailedReason(name + " version check failed", null);
                    updateOtaUpdateStatus(name, FAILED, null, null);
                }
            }
        }
        return true;
    }

    private String handleCurrentVersionAccordingToTargetVersion(String targetVersion, String currentVersion) {
        if (targetVersion == null || currentVersion == null) return currentVersion;

        String[] tx1TargetVer = targetVersion.split("v");
        String cVersion = currentVersion;
        if (tx1TargetVer.length <= 1) {
            String[] tx1CurrentVer = cVersion.split("v");
            cVersion = tx1CurrentVer[tx1CurrentVer.length > 1 ? 1 : 0];
        }

        return cVersion;
    }

    private boolean parseCanUpdateStateResult(String val) {
        Log.d(TAG, "parseCanUpdateStateResult. " + val);
        boolean ret = true;

        if (CMD_TIMEOUT.equals(val)) {
            setOtaUpdateFailedReason("can ota update failed.", CMD_TIMEOUT);
            return false;
        }

        CanOtaBean bean = mGson.fromJson(val, CanOtaBean.class);

        switch (bean.getStatus()) {
            case OTA_UPDATE_STATUS_COMPLIETE: {
                Log.d(TAG, getOsByBoardId(bean.getBoard()) + " update success.");
                ret = checkAndUpdateCanUpgradeResult(bean.getBoard());
                if (!ret)
                    Log.e(TAG, getOsByBoardId(bean.getBoard()) + " update_result check failed.");
                break;
            }

            case OtaConstants.OTA_UPDATE_STATUS_ABNORMAL: {
                int errorCode = bean.getFailReason();
                Log.d(TAG, bean.getBoard() + " update failed. "
                        + " errorCode:" + errorCode);
                ret = checkAndUpdateCanUpgradeResult(bean.getBoard());
                if (!ret)
                    Log.e(TAG, bean.getBoard() + " update_result check failed.");
                break;
            }
        }

        return ret;
    }

    private boolean checkIfInstallHasFailed() {
        List<VersionData> listData = dataBaseManager.getAllVersion();
        for (VersionData data : listData) {
            if (data.getStatus() == FAILED) {
                Log.e(TAG, "checkIfInstallHasFailed. " + data.getName() + " install failed!");
                Log.d(TAG, "os: " + data.getName() + " install failed!");
                return true;
            }
        }

        return false;
    }
    /**
     * check can update result. if get version failed, will try 3 times.
     */
    private boolean checkAndUpdateCanUpgradeResult(int board) {
        String osName = getOsByBoardId(board);
        String result = null;
        VersionData data = dataBaseManager.getOsData(osName);
        Log.d(TAG, "checkAndUpdateCanUpgradeResult. "
                + " osName:" + osName + " board:" + board
                + " call getVersion. timeout set 10s");

        for (int i = 0; i < 3; i++) {
            result = getCanBoardVersionInternal(board);
            if (result == null || result.isEmpty() || result.contains(CMD_TIMEOUT)) {
                Log.e(TAG, "getCanBoardVersionInternal board: " + board
                        + " result:" + result
                        + " failed. retry:" + i);
                continue;
            } else {
                break;
            }
        }

        if (result == null || result.isEmpty()) {
            Log.e(TAG, "getCanBoardVersion failed. internal error!");
            Log.d(TAG,osName + "版本号获取超时");
            return false;
        }

        if (result.contains(CMD_TIMEOUT)) {
            setOtaUpdateFailedReason("get " + osName + " version failed.", CMD_TIMEOUT);
            return false;
        }

        // update os to mOsVersionMap
        if (!parseCanVersionResult(osName, result)) {
            Log.e(TAG, "parseCanVersionResult error. result=" + result);
            Log.d(TAG,osName + "版本号解析错误");
            return false;
        }
        // update database
        if (data.getStatus() == WAITING) {
            String cVersion = mOsVersionMap.get(osName);
            String tVersion = data.getTargetVersion();
            boolean isPsbRollback = mPreference.getPrefsRollback() && osName.equals(OS_PSB);

            Log.d(TAG, "checkAndUpdateCanUpgradeResult. " + " os:" + osName
                    + " currentVersion:" + cVersion
                    + " targetVersion:" + tVersion
                    + " isPsbRollback:" + isPsbRollback);

            if (cVersion != null && tVersion != null && (isPsbRollback || cVersion.equals(tVersion))) {
                Log.d(TAG, "os:" + osName + " update success");
                Log.d(TAG,osName + "升级成功！ currentVersion:" + cVersion
                        + " targetVersion:" + tVersion);

                updateOtaUpdateStatus(osName, SUCCESS, null, null);
            } else {
                Log.d(TAG, "os:" + osName + "update failed");
                Log.d(TAG,osName + "升级失败！ currentVersion:" + cVersion
                        + " targetVersion:" + tVersion);

                setOtaUpdateFailedReason(osName + " version check failed", null);
                updateOtaUpdateStatus(osName, FAILED, null, null);
            }
        }

        return true;
    }

    private boolean parseCanVersionResult(String osName, String result) {
        boolean ret = true;
        try {
            JSONObject jsonObject = new JSONObject(result);

            // board maybe not equal osName?
            String board = jsonObject.getString(Definition.JSON_CAN_BOARD);
            String version = jsonObject.getString(Definition.JSON_CAN_BOARD_APP_VERSION);

            // handle motor_vertical/motor_horizon/psb/tx1 version
            if (canOsArray.contains(new InstallData(osName))) {
                String[] ver = version.split("v");
                version = ver[1];
            }
            Log.d(TAG, "versionResult. os type:" + osName + " version:" + version);
            mOsVersionMap.put(osName, version);

        } catch (JSONException e) {
            e.printStackTrace();
            ret = false;
        }

        return ret;
    }

    /**
     * Check host update result and update database for later check.
     * If host update failed, host has rebooted, mOsVersionMap need regenerate.
     */
    private void checkAndUpdateHostResult() {
        VersionData data = dataBaseManager.getOsData(OS_HOST);
        boolean isOtaUpgrading = mPreference.getOtaIsUpgrading();

        String version = Utils.getSystemProperties(HOST_VERSION, "1.0.0");
        Log.d(TAG, " current version=" + version);
        if (!isOtaUpgrading && data.getStatus() == WAITING) {
            if (data.getTargetVersion() != null && data.getTargetVersion().equals(version)) {
                Log.e(TAG, "host update success!");
                data.setStatus(SUCCESS);
                dataBaseManager.update(data);

                mPreference.setPrefsHostUpdateResult("success");
                Log.d(TAG,"success! os:" + data.getName() + " currentVersion:" + version
                        + " targetVersion:" + data.getTargetVersion());
            } else {
                Log.e(TAG, "host update failed!");
                data.setStatus(FAILED);
                dataBaseManager.update(data);

                mPreference.setPrefsHostUpdateResult("failed");
                Log.d(TAG,"failed! os:" + data.getName() + " currentVersion:" + version
                        + " targetVersion:" + data.getTargetVersion());
            }
        }
    }

    /**
     * Check navigation/motor_left/motor_right update result, and update database.
     */
    private boolean checkAndUpdateTk1UpgradeResult() {
        String result;
        boolean hasGetVersion = false;

        //check tx1 update version result.
        List<VersionData> dataList = dataBaseManager.getOsData(navOsList);
        for (VersionData data : dataList) {
            if (data.getStatus() == WAITING) {

                if (!hasGetVersion) {
                    hasGetVersion = true;
                    Log.d(TAG, "UpdateTk1UpgradeResult. call getVersion. timeout set 20s");
                    result = mOtaApiHelper.getNavigationVersion(20 * 1000);
                    if (result == null || result.isEmpty()) {
                        Log.e(TAG, "getTk1Version failed. internal error!");
                        Log.d(TAG,data.getName() + "版本号获取超时");
                        setOtaUpdateFailedReason("get TK1 version result is null or empty", null);
                        return false;
                    }

                    if (result.contains(CMD_TIMEOUT)) {
                        Log.e(TAG, "getTk1Version failed. timeout!");
                        setOtaUpdateFailedReason("get TX1 version failed.", CMD_TIMEOUT);
                        return false;
                    }

                    // update os to mOsVersionMap
                    if (!parseTk1VersionResult(result)) {
                        Log.e(TAG, "parseTk1VersionResult error. result=" + result);
                        Log.d(TAG,data.getName() + "版本号解析错误");
                        setOtaUpdateFailedReason("parse TK1 version result error", null);
                        return false;
                    }
                }

                String name = data.getName();
                String cVersion = mOsVersionMap.get(name);
                String tVersion = data.getTargetVersion();

                Log.d(TAG, "UpdateTk1UpgradeResult. " + " os:" + name
                        + " currentVersion:" + cVersion
                        + " targetVersion:" + tVersion);

                if (cVersion != null && cVersion.equals(tVersion)) {
                    Log.d(TAG, "os:" + name + " update success");
                    Log.d(TAG,name + "升级成功！ currentVersion:" + cVersion
                            + " targetVersion:" + tVersion);

                    updateOtaUpdateStatus(name, SUCCESS, null, null);
                } else {
                    Log.d(TAG, "os:" + name + "update failed");
                    Log.d(TAG,name + "升级失败！ currentVersion:" + cVersion
                            + " targetVersion:" + tVersion);
                    setOtaUpdateFailedReason(name + " version check failed", null);
                    updateOtaUpdateStatus(name, FAILED, null, null);
                }
            }
        }
        return true;
    }

    private void checkNotifyInstallLock() {
        synchronized (mInstallLock) {
            mInstallLock.notify();
        }
    }

    private boolean parseTk1VersionResult(String result) {
        boolean ret = true;
        try {
            JSONArray params = new JSONArray(result);
            params.length();

            JSONObject jsonObject;
            String type;
            String version;

            for (int i = 0; i < params.length(); i++) {
                jsonObject = (JSONObject) params.get(i);
                if (jsonObject != null) {
                    type = (String) jsonObject.get(PARAM_BOARD);
                    version = (String) jsonObject.get(PARAM_VERSION);

                    // handle motor_left/motor_right version
                    if (navOsArray.contains(new InstallData(type)) && !OS_TK1.equals(type)) {
                        String[] navVer = version.split("v");
                        // handle motor_left/right version is "",
                        // in this case motor update is failed
                        version = navVer[navVer.length > 1 ? 1 : 0];
                    }

                    Log.d(TAG, "versionResult. os type:" + type + " version:" + version);
                    mOsVersionMap.put(type, version);
                }
            }

        } catch (JSONException | NullPointerException e) {
            e.printStackTrace();
            ret = false;
        }
        return ret;
    }

}
