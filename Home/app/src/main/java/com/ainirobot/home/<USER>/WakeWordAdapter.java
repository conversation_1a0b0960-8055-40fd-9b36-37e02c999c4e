package com.ainirobot.home.adapter;

import android.content.Context;
import android.support.annotation.NonNull;
import android.support.v7.widget.RecyclerView;
import android.util.SparseIntArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;

import com.ainirobot.home.R;
import com.ainirobot.home.bean.WakeWord;
import com.ainirobot.home.control.SkillManager;

import java.util.List;

public class WakeWordAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private SparseIntArray polyPhoneIndexMapping;
    private Context mContext;
    private List<WakeWord> mWakeWords;
    private RadioButtonCheckedListener mRadioButtonCheckedListener;

    public WakeWordAdapter(List<WakeWord> wakeWords, Context context) {
        if (mWakeWords != null) {
            mWakeWords.clear();
        }
        mWakeWords = wakeWords;
        mappingPolyPhoneIndex();
        this.mContext = context;
    }

    private void mappingPolyPhoneIndex() {
        polyPhoneIndexMapping = new SparseIntArray(mWakeWords.size());
        int polyPhoneIndexStart = 0;
        for (int i = 0; i < mWakeWords.size(); i++) {
            if (mWakeWords.get(i).isPolyPhone) {
                polyPhoneIndexMapping.put(polyPhoneIndexStart++, i);
            }
        }
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View v = LayoutInflater.from(parent.getContext()).inflate(R.layout
                .item_poly_phone_choose, parent, false);
        return new WakeWordViewHolder(v);
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, final int position) {
        WakeWord wakeWord = mWakeWords.get(polyPhoneIndexMapping.get(position));
        ((WakeWordViewHolder) holder).spell_choose.setText(String.format(mContext.getString
                (R.string.polyphone_select_placeholder), wakeWord.word));

        for (int i = 0; i < wakeWord.spells.size(); i++) {
            RadioButton radioButton = buildButton(wakeWord.spells.get(i).str, i);
            ((WakeWordViewHolder) holder).radio_group.addView(radioButton);
        }

        ((WakeWordViewHolder) holder).radio_group.check(0);
        ((WakeWordViewHolder) holder).radio_group.setOnCheckedChangeListener(new RadioGroup
                .OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                if (mRadioButtonCheckedListener != null) {
                    mRadioButtonCheckedListener.onRadioButtonChecked(polyPhoneIndexMapping.get(position), checkedId);
                }
            }
        });
    }

    @NonNull
    private RadioButton buildButton(String pinyin, int buttonId) {
        RadioButton radioButton = new RadioButton(mContext);
        radioButton.setButtonDrawable(mContext.getResources().getDrawable(android.R.color
                .transparent));
        radioButton.setBackgroundDrawable(null);
        radioButton.setCompoundDrawablesWithIntrinsicBounds(mContext.getResources()
                .getDrawable(R.drawable.selector_radio_button), null, null, null);
        radioButton.setCompoundDrawablePadding(20);
        radioButton.setPadding(1, 0, 100, 0);
        radioButton.setText(pinyin);
        radioButton.setId(buttonId);
        return radioButton;
    }

    @Override
    public int getItemCount() {
        return polyPhoneIndexMapping.size();
    }

    public static class WakeWordViewHolder extends RecyclerView.ViewHolder {
        public TextView spell_choose;
        public RadioGroup radio_group;

        public WakeWordViewHolder(View v) {
            super(v);
            spell_choose = (TextView) v.findViewById(R.id.spell_choose);
            radio_group = (RadioGroup) v.findViewById(R.id.radio_group);
        }
    }

    public interface RadioButtonCheckedListener {
        void onRadioButtonChecked(int checkedGroupIndex, int checkedId);
    }

    public void setRadioButtonCheckedListener(RadioButtonCheckedListener listener) {
        this.mRadioButtonCheckedListener = listener;
    }
}
