package com.ainirobot.home.module;

import android.os.Bundle;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.R;
import com.ainirobot.home.bi.BIRobotLockReport;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.floatdialog.FloatDialogManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Locale;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class LiteLockModule extends BaseBackGroundModule {

    private static final String TAG = "LiteLockModule";
    private static final long PERIOD_TIME = 60 * 60 * 1000;
    private static final int SHOW_LITE_LOCK_DIALOG = 100;
    private static final int HIDE_LITE_LOCK_DIALOG = 101;
    private static final int MODULE_STOP = 102;

    private static final int TYPE_SPEECH = 2;
    private static final int TYPE_DIALOG = 1;

    private int mType = -1;

    private static LiteLockModule mInstance;

    private ScheduledThreadPoolExecutor mExecutor;

    public LiteLockModule() {
        super("liteLockModuleThread");
    }

    public static LiteLockModule getInstance(){
        if(mInstance == null){
            mInstance = new LiteLockModule();
        }
        return mInstance;
    }

    @Override
    protected void onThreadNewSemantics(int reqId, String intent, String text, String params) {
        Log.i(TAG, "on new request, intent: " + intent + "lock type: " + mType);

        String lockMsg = "";
        String lockMsgI18n;
        String defaultLocale = Locale.US.toString();
        String currentLocale = Locale.getDefault().toString();
        if (!TextUtils.isEmpty(params)) {
            try {
                JSONObject json = new JSONObject(params);
                lockMsg = json.optString(Definition.JSON_REMOTE_LOCK_MSG);
                lockMsgI18n = json.optString(Definition.JSON_REMOTE_LOCK_MSG_I18N);
                if (!TextUtils.isEmpty(lockMsgI18n)) {
                    JSONObject jsonObject = new JSONObject(lockMsgI18n);
                    Log.d(TAG, "currentLocale: " + currentLocale + ", defaultLocale: " + defaultLocale);
                    String lockMsgI18nDefault = jsonObject.optString(defaultLocale);
                    String lockMsgI18nTarget = jsonObject.optString(currentLocale);
                    Log.d(TAG, "lockMsgI18nDefault: " + lockMsgI18nDefault + ", lockMsgI18nTarget: " + lockMsgI18nTarget);
                    if (TextUtils.isEmpty(lockMsgI18nTarget)) {
                        lockMsg = TextUtils.isEmpty(lockMsgI18nDefault) ? lockMsg : lockMsgI18nDefault;
                    } else {
                        lockMsg = lockMsgI18nTarget;
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        switch (intent){
            case "system_remote_lock_dialog":
                if(mType == TYPE_DIALOG){
                    return;
                } else if (mType == TYPE_SPEECH){
                    reportLockExit();
                }
                mType = TYPE_DIALOG;
                mHandler.removeMessages(HIDE_LITE_LOCK_DIALOG);
                mHandler.removeMessages(MODULE_STOP);
                SystemApi.getInstance().sendStatusReport("status_robot_lock","lock_lite");
                startTimer(lockMsg);
                if (TextUtils.isEmpty(lockMsg)) {
                    SkillManager.getInstance().speechPlayText(ApplicationWrapper.getContext().getString(R.string.lock_warning));
                }
                else {
                    SkillManager.getInstance().speechPlayText(lockMsg);
                }
                break;

            case "system_remote_lock_speech":
                if(mType == TYPE_DIALOG){
                    reportLockExit();
                }
                mType = TYPE_SPEECH;
                mHandler.removeMessages(MODULE_STOP);
                mHandler.removeMessages(SHOW_LITE_LOCK_DIALOG);
                mHandler.sendEmptyMessage(HIDE_LITE_LOCK_DIALOG);
                SystemApi.getInstance().sendStatusReport("status_robot_lock","lock_lite");
                startTimer(lockMsg);
                reportLockStart();
                break;

            case "system_remote_lock_lite_exit":
                mHandler.sendEmptyMessage(HIDE_LITE_LOCK_DIALOG);
                SystemApi.getInstance().sendStatusReport("status_robot_lock","lock_lite_exit");
                mHandler.sendEmptyMessageDelayed(MODULE_STOP,1000);
                reportLockExit();
                mType = -1;
                break;

            default:
                break;
        }

        super.onThreadNewSemantics(reqId, intent, text, params);
    }

    private void startTimer(final String lockMsg){
        resetThreadTimer();
        mExecutor = new ScheduledThreadPoolExecutor(1);
        mExecutor.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                Log.i(TAG, "timer do, current type: " + mType);
                switch (mType){
                    case TYPE_DIALOG:
                        Message message = mHandler.obtainMessage();
                        message.what = SHOW_LITE_LOCK_DIALOG;
                        if (!TextUtils.isEmpty(lockMsg)) {
                            message.obj = lockMsg;
                        }
                        mHandler.sendMessage(message);
                        reportLockStart();
                        break;

                    case TYPE_SPEECH:
                        Log.i(TAG, "robot lock by speech");

                        break;
                    default:
                        break;
                }

            }
        }, 0, PERIOD_TIME, TimeUnit.MILLISECONDS);
    }

    private void resetThreadTimer(){
        Log.i(TAG, "reset thread timer");
        if(mExecutor != null){
            if (!mExecutor.isShutdown()){
                mExecutor.shutdownNow();
            }
        }
        mExecutor = null;
    }

    @Override
    protected void onStop() {
        Log.i(TAG, "onstop");
        mType = -1;
        resetThreadTimer();
        mHandler.removeMessages(SHOW_LITE_LOCK_DIALOG);
        mHandler.removeMessages(HIDE_LITE_LOCK_DIALOG);
        super.onStop();
    }

    @Override
    protected void dispatchHandleMessage(Message msg) {
        Log.i(TAG, "dispatch message, what: "+msg.what);
        final Bundle bundle = msg.getData();
        switch (msg.what){
            case SHOW_LITE_LOCK_DIALOG:
                String lockMsg = msg.obj != null ? (String) msg.obj : "";
                FloatDialogManager.getInstance().showLiteLockDialog(lockMsg);
                break;

            case HIDE_LITE_LOCK_DIALOG:
                FloatDialogManager.getInstance().removeLiteLockView();
                break;

            case MODULE_STOP:
                stop();
                break;
        }
    }

    private void reportLockExit(){
        BIRobotLockReport report = new BIRobotLockReport();
        report.addEndTime(System.currentTimeMillis());
        report.addLockType(mType == TYPE_SPEECH ? 2 : 0);
        report.report();
    }

    private void reportLockStart(){
        BIRobotLockReport report = new BIRobotLockReport();
        report.addStartTime(System.currentTimeMillis());
        report.addLockType(mType == TYPE_SPEECH ? 2 : 0);
        report.report();
    }
}
