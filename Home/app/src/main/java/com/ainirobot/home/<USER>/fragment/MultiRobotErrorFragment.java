package com.ainirobot.home.ui.fragment;

import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.ui.UIController;

public class MultiRobotErrorFragment extends BaseFragment {
    private final String TAG = MultiRobotErrorFragment.class.getSimpleName() + ":Home";

    private TextView mErrorMessage;
    private TextView mErrorDetail;
    private TextView mTvRightBtn;
    private int errorType = 0;

    public MultiRobotErrorFragment() {

    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        Log.d(TAG, "on create view");
        View view = inflater.inflate(R.layout.fragment_multi_robot_error, null);
        view.findViewById(R.id.tv_to_bind).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_REPOSITION_CANCEL);
            }
        });
        mTvRightBtn = view.findViewById(R.id.tv_to_restore);
        mTvRightBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onRightBtnClick();
            }
        });

        String errorMsg = "";
        Bundle bundle = getArguments();
        if (bundle != null) {
            errorType = bundle.getInt("errorType");
            errorMsg = bundle.getString("errorMsg");
        }
        mErrorMessage = view.findViewById(R.id.tv_alert_message);
        mErrorMessage.setText(String.format(getString(R.string.multi_robot_error_message) + "%d ", errorType));

        if (!TextUtils.isEmpty(errorMsg)) {
            mErrorDetail = view.findViewById(R.id.tv_alert_detail);
            mErrorDetail.setText(errorMsg);
            mErrorDetail.setVisibility(View.VISIBLE);
        }

        initRightBtnByType();
        return view;
    }

    private void onRightBtnClick() {
        switch (errorType) {
            case Definition.STATUS_NAVI_MULTI_MAP_NOT_MATCH:
            case Definition.STATUS_NAVI_MULTI_LORA_CONFIG_FAIL:
                ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_REPOSITION_CANCEL);
                startMapTool();
                break;
            case Definition.STATUS_NAVI_MULTI_LORA_DISCONNECT:
                rebootRobot();
                break;
        }
    }

    private void initRightBtnByType() {
        if (null != mTvRightBtn) {
            switch (errorType) {
                case Definition.STATUS_NAVI_MULTI_MAP_NOT_MATCH:
                    mTvRightBtn.setVisibility(View.VISIBLE);
                    mTvRightBtn.setText(R.string.switch_map);
                    break;
                case Definition.STATUS_NAVI_MULTI_LORA_CONFIG_FAIL:
                    mTvRightBtn.setVisibility(View.VISIBLE);
                    mTvRightBtn.setText(R.string.set_multi_robot_config);
                    break;
                case Definition.STATUS_NAVI_MULTI_LORA_DISCONNECT:
                    mTvRightBtn.setVisibility(View.VISIBLE);
                    mTvRightBtn.setText(R.string.restart);
                    break;
                default:
                    mTvRightBtn.setVisibility(View.GONE);
                    break;
            }
        }
    }

    private void startMapTool(){
        String packageName = "com.ainirobot.maptool";
        Log.d(TAG, "Start map app : " + packageName);

        PackageManager pm = getActivity().getPackageManager();
        Intent intent = pm.getLaunchIntentForPackage(packageName);
        if (intent != null) {
            startActivity(intent);
        }
    }

    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "onResume");
    }

    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
    }

    @Override
    public void onPause() {
        super.onPause();
        Log.d(TAG, "onPause: ");
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    private void rebootRobot() {
        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                startActivity(new Intent(Intent.ACTION_REBOOT));
            }
        });
    }
}
