package com.ainirobot.home.ui.fragment;

import android.os.Bundle;
import android.os.Handler;
import android.support.constraint.ConstraintLayout;
import android.support.v4.app.Fragment;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.ainirobot.coreservice.ISystemApi;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.view.ChargingWarningDialog;
import com.ainirobot.home.ui.view.StopChargingDialog;
import com.ainirobot.home.utils.LocationUtil;
import com.ainirobot.home.utils.ResType;
import com.ainirobot.home.utils.ResUtil;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * A simple {@link Fragment} subclass.
 */
public class ChargingFragment extends BaseFragment {

    private static final String TAG = "ChargingFragment_Home";
    private static final int MAX_TOUCH_COUNT = 7;
    private TextView mTVLevel;
    private ProgressBar mPBLevel;
    private StopChargingDialog mDialog = null;
    private LinearLayout mUpdateLayout;
    private ConstraintLayout mConstraintLeavingLayout;
    private TextView mBtnChargingWarning;
    private TextView mTvChargingText;
    private TextView mTvChargingSubText;
    private ImageView mChargingLightningImg;
    private TextView mbtn_end_charging;
    private ImageView mImageOffcharger;
    private LinearLayout mLinCharging;
    private LinearLayout mLayoutBmsLowTemp;
    private TextView mBatteryLevelLow;
    private Handler mUIHandler = new Handler();
    private long chargingWarningTime = 0L;
    private ChargingWarningDialog mChargingWarningDialog = null;
    private boolean isChargingWarningScreenShowed = false;

    public ChargingFragment() {
    }


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        Log.d(TAG, "on create view");
        View view = inflater.inflate(R.layout.fragment_charging, container, false);
        mTVLevel = (TextView) view.findViewById(R.id.charging_level);
        mPBLevel = (ProgressBar) view.findViewById(R.id.charging_image);
        mUpdateLayout = (LinearLayout) view.findViewById(R.id.layout_update);
        mBtnChargingWarning = (TextView) view.findViewById(R.id.btn_charging_warning);
        mTvChargingText = (TextView) view.findViewById(R.id.charging_text);
        mTvChargingSubText = (TextView) view.findViewById(R.id.charging_sub_text);
        mChargingLightningImg = (ImageView) view.findViewById(R.id.charge_lightning_img);
        mbtn_end_charging = (TextView) view.findViewById(R.id.btn_end_charging);
        mLinCharging = (LinearLayout) view.findViewById(R.id.lin_charging);
        mLayoutBmsLowTemp = (LinearLayout)view.findViewById(R.id.layout_charging_low_temp);

        mConstraintLeavingLayout = (ConstraintLayout) view.findViewById(R.id.leaving_layout);
        mImageOffcharger = (ImageView) view.findViewById(R.id.image_off_charger);
        mImageOffcharger.setImageResource(ResType.PNG_LEAVING_CHARGE_PILE.getResIdByType());

        Bundle bundle = getArguments();
        if (bundle != null) {
            int level = bundle.getInt(ModuleDef.PARAM_LEVEL);
            onMessage(UIController.MESSAGE_TYPE.BATTERY_LEVEL, level + "");
            boolean isUpgrade = bundle.getBoolean(ModuleDef.PARAM_WAIT_UPGRADE, false);
            if (isUpgrade) {
                mUpdateLayout.setVisibility(View.VISIBLE);
            } else {
                mUpdateLayout.setVisibility(View.GONE);
            }
        }

        mBtnChargingWarning.setVisibility(View.GONE);
        isChargingWarningScreenShowed = false;
        return view;
    }

    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
        Log.d(TAG, " onMessage:: type = " + type + " message = " + message);
        switch (type) {
            case BATTERY_LEVEL:
                Log.d(TAG, "level " + message);
                isChargingWarningScreenShowed = false;
                showChargingScreen(Integer.valueOf(message));
                break;

            case CHARGING_LEVEL_UPDATE:
                Log.d(TAG, " update level :" + message);
                setTVLevel(Integer.valueOf(message));
                break;

            case TOUCH_EVENT_DOWN:
                if ((!ProductInfo.isDeliveryProduct() && !ProductInfo.isMeissaPlus())
                        || ProductInfo.isSaiphXdOrBigScreen()
                        || ProductInfo.isSaiphChargeIr()
                        || ProductInfo.isSaiphPro()
                        || ProductInfo.isAlnilamPro()
                        || ProductInfo.isMeissaPlus()
                        || ProductInfo.isMeissa2()
                        || ProductInfo.isCarryProduct()
                        || ProductInfo.isSlimProduct()
                ) {
                    handleTouchDown();
                }
                break;

            case WAIT_UPGRADE:
                Log.d(TAG, "show upload" + message);
                if (isChargingWarningScreenShowed) {
                    break;
                }
                mLayoutBmsLowTemp.setVisibility(View.GONE);
                if (message.equals("true")) {
                    mUpdateLayout.setVisibility(View.VISIBLE);
                } else {
                    mUpdateLayout.setVisibility(View.GONE);
                }
                break;

            case CHARGING_WARNING:
                showChargingWarningScreen(Integer.valueOf(message));
                if (!isChargingWarningScreenShowed) {
                    isChargingWarningScreenShowed = true;
                    SkillManager.getInstance().speechPlayText(ResUtil.getString(R.string.speech_charging_warning));
                }
                break;
            case CHARGING_BMS_LOW_TEMP:
                isChargingWarningScreenShowed = false;
                SkillManager.getInstance().speechPlayText(getContext().getString(R.string.low_temp_tts));
                try {
                    JSONObject jsonObject = new JSONObject(message);
                    int level = jsonObject.getInt(ModuleDef.PARAM_LEVEL);
                    int bmsTemp = jsonObject.getInt(ModuleDef.PARAM_BMS_TEM);
                    showChargingLowTempScreen(level, bmsTemp);
                } catch (JSONException e) {
                    e.printStackTrace();
                }

                break;
            case LEAVING_CHARGE_POINT:
                showLeavingScreen();
                break;
            case LEAVING_CHARGE_POINT_PRE:
                if (mDialog != null) {
                    mDialog.dismiss();
                    mDialog = null;
                }
                break;
            default:
                break;
        }
    }

    private void showLeavingScreen() {
        mLayoutBmsLowTemp.setVisibility(View.GONE);
        mUpdateLayout.setVisibility(View.GONE);
        mPBLevel.setVisibility(View.GONE);
        mChargingLightningImg.setVisibility(View.GONE);
        mbtn_end_charging.setVisibility(View.GONE);
        mLinCharging.setVisibility(View.GONE);
        mBtnChargingWarning.setVisibility(View.GONE);
        mConstraintLeavingLayout.setVisibility(View.VISIBLE);

        if (mChargingWarningDialog != null) {
            mChargingWarningDialog.dismiss();
            mChargingWarningDialog = null;
        }
    }

    private void showChargingLowTempScreen(int level, int bmsTemp){
        mLayoutBmsLowTemp.setVisibility(View.VISIBLE);
        mUpdateLayout.setVisibility(View.GONE);
        mPBLevel.setVisibility(View.GONE);
        mChargingLightningImg.setVisibility(View.GONE);
        mbtn_end_charging.setVisibility(View.GONE);
        mLinCharging.setVisibility(View.GONE);
        mBtnChargingWarning.setVisibility(View.GONE);
        mConstraintLeavingLayout.setVisibility(View.GONE);

        mBatteryLevelLow = mLayoutBmsLowTemp.findViewById(R.id.tv_low_temp_battery_level);
        setTVLevel(level);
        TextView temp_tip = mLayoutBmsLowTemp.findViewById(R.id.tv_current_temp_tip);
        temp_tip.setText(String.format(getString(R.string.charging_bms_low_temp_tip1),
                bmsTemp+""));
    }

    private void showChargingScreen(int batteryLevel) {
        mLinCharging.setVisibility(View.VISIBLE);
        mPBLevel.setVisibility(View.VISIBLE);
        mChargingLightningImg.setVisibility(View.VISIBLE);
        if(!LocationUtil.getInstance().isChargingTypeWire()){
            mbtn_end_charging.setVisibility(View.VISIBLE);
        }
        mLayoutBmsLowTemp.setVisibility(View.GONE);
        mBtnChargingWarning.setVisibility(View.GONE);
        mConstraintLeavingLayout.setVisibility(View.GONE);

        mTvChargingText.setText(getActivity().getResources().getString(R.string.charging_text));
        mTvChargingText.setTextColor(getActivity().getResources().getColor(R.color.white));
        if (mTvChargingSubText != null) {
            mTvChargingSubText.setVisibility(View.GONE);
        }
        mChargingLightningImg.setImageDrawable(getActivity().getResources().getDrawable(R.drawable.charge_lightning_img));
        setTVLevel(batteryLevel);

        if (mChargingWarningDialog != null) {
            mChargingWarningDialog.dismiss();
            mChargingWarningDialog = null;
        }
        mbtn_end_charging.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d(TAG, "stop charging dialog show");
                SystemApi.getInstance().setPowerMotor(-1,0,null);
                multiClickCountRunnable.onReachClickCount();
            }
        });
    }

    private void showChargingWarningScreen(int batteryLevel) {
        mLayoutBmsLowTemp.setVisibility(View.GONE);
        mBtnChargingWarning.setVisibility(View.VISIBLE);
        mChargingLightningImg.setVisibility(View.VISIBLE);
        mbtn_end_charging.setVisibility(View.GONE);
        mPBLevel.setVisibility(View.VISIBLE);
        mUpdateLayout.setVisibility(View.GONE);
        mConstraintLeavingLayout.setVisibility(View.GONE);

        mTvChargingText.setText(getActivity().getResources().getString(R.string.charging_warning_title));
        mTvChargingText.setTextColor(getActivity().getResources().getColor(R.color.white));
        if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) {
            if (mTvChargingSubText != null) {
                mTvChargingSubText.setVisibility(View.VISIBLE);
            }
        }
        setTVLevel(batteryLevel);

        mChargingLightningImg.setImageDrawable(getActivity().getResources().getDrawable(R.drawable.charge_warning_img));
        mBtnChargingWarning.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d(TAG, "charging warning dialog show");
                showChargingWarningDialog();
                SystemApi.getInstance().setPowerMotor(-1,0,null);
                ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_CW_BI_CLICK_VS_BUTTON);
            }
        });

        if (mDialog != null) {
            mDialog.dismiss();
            mDialog = null;
        }
    }

    private void setTVLevel(int level) {
        Log.d(TAG, "setTVLevel: level = " + level);
        if (mTVLevel != null) {
            mTVLevel.setTextColor(getActivity().getResources().getColor(R.color.white));
            mTVLevel.setText(level + "%");
            if (level >= 0 && level <= 10) {
                mPBLevel.setProgressDrawable(getActivity().getDrawable(R.drawable.charging_progress_law));
            } else if (level > 10 && level <= 50) {
                mPBLevel.setProgressDrawable(getActivity().getDrawable(R.drawable.charging_progress_mid));
            } else {
                mPBLevel.setProgressDrawable(getActivity().getDrawable(R.drawable.charging_progress_high));
            }
            mPBLevel.setProgress(level);
        }
        if (mBatteryLevelLow != null) {
            mBatteryLevelLow.setText(level + "%");
        }
    }

    private void handleTouchDown() {
        mUIHandler.removeCallbacks(multiClickCountRunnable);
        mUIHandler.postDelayed(multiClickCountRunnable, 500);
        int count = ++multiClickCountRunnable.mClickCount;
        if (count <= MAX_TOUCH_COUNT) {
            final long time = System.currentTimeMillis();
            if (time - chargingWarningTime > 6000L) {
                SkillManager.getInstance().speechPlayText(ResUtil.getString(R.string.speech_is_charging));
                chargingWarningTime = time;
            }
        }
        Log.d(TAG, "click count : " + count);
        if (count >= MAX_TOUCH_COUNT) {   //  点击第7下弹框
            multiClickCountRunnable.onReachClickCount();
            multiClickCountRunnable.mClickCount = 0;
        }
    }

    abstract class MultiClickCountRunnable implements Runnable {
        int mClickCount = 0;

        @Override
        public final void run() {
            mClickCount = 0;
        }

        public abstract void onReachClickCount();
    }

    private MultiClickCountRunnable multiClickCountRunnable = new MultiClickCountRunnable() {

        @Override
        public void onReachClickCount() {
            Log.d(TAG, "onReachClickCount:");
            if(LocationUtil.getInstance().isChargingTypeWire()){
                Log.d(TAG, "onReachClickCount: isChargingTypeWire");
                return;
            }
            delayDisDialog();
            showStopChargingDialog();
        }
    };

    private void delayDisDialog() {
        mUIHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (mDialog != null && mDialog.isShowing()) {
                    mDialog.dismiss();
                    mDialog = null;
                }
            }
        }, 30 * Definition.SECOND);
    }

    private void showStopChargingDialog() {
        if (mDialog != null) {
            Log.d(TAG, "dialog already show");
            return;
        }

        mDialog = new StopChargingDialog(getContext(), R.style.OTADialog)
                .setDialogClickListener(new StopChargingDialog.ClickListener() {
                    @Override
                    public void onConfirmClick() {
                        ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_STOP_CHARGING);
                        Log.d(TAG, "StopChargingDialog click ok");
                        mDialog = null;
                    }

                    @Override
                    public void onCancelClick() {
                        Log.d(TAG, "StopChargingDialog click cancel");
                        mDialog = null;
                    }
                });
        mDialog.show();
        SkillManager.getInstance().speechPlayText(getContext().getString(R.string.stop_charging_tips));
    }

    private void showChargingWarningDialog() {
        if (mChargingWarningDialog != null) {
            Log.d(TAG, "charging warning dialog already show");
            return;
        }

        mChargingWarningDialog = new ChargingWarningDialog(getContext(), R.style.OTADialog)
                .setDialogClickListener(new ChargingWarningDialog.ClickListener() {
                    @Override
                    public void onConfirmClick() {
                        Log.d(TAG, "ChargingWarningDialog click ok");
                        mChargingWarningDialog = null;
                    }
                });
        mChargingWarningDialog.show();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mUIHandler != null) {
            mUIHandler.removeCallbacksAndMessages(null);
            mUIHandler = null;
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mDialog != null) {
            mDialog.dismiss();
            mDialog = null;
        }

        if (mChargingWarningDialog != null) {
            mChargingWarningDialog.dismiss();
            mChargingWarningDialog = null;
        }

        isChargingWarningScreenShowed = false;
    }
}
