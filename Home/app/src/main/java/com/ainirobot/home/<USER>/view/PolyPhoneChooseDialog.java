package com.ainirobot.home.ui.view;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.ainirobot.home.R;
import com.ainirobot.home.adapter.WakeWordAdapter;
import com.ainirobot.home.bean.WakeWord;
import com.ainirobot.home.bean.WakeWordSpell;

import java.util.List;

public class PolyPhoneChooseDialog extends Dialog implements
        WakeWordAdapter.RadioButtonCheckedListener, View.OnClickListener {
    private Context mContext;
    private TextView mTvWakeWord;
    private TextView mTvSpell;
    private List<WakeWord> mWakeWords;
    private RecyclerView mRecyclerView;
    private TextView mConfirm;
    private TextView mCancel;
    private SpaceItemDecoration mSpaceItemDecoration;

    public PolyPhoneChooseDialog(@NonNull Context context) {
        this(context, R.style.Wake_Word_Dialog);
        this.mContext = context;
        if (!(mContext instanceof Activity)) {
            Window window = getWindow();
            if (window != null) {
                window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_DIALOG);
            }
        }
    }

    private PolyPhoneChooseDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    private ClickListener mClickListener;

    @Override
    public void onRadioButtonChecked(int checkedGroupIndex, int checkedId) {
        Log.i("PolyPhoneChooseDialog", "onRadioButtonChecked: " + checkedGroupIndex
                + " checkedId: " + checkedId);
        updateDataStatus(checkedGroupIndex, checkedId);
        updateTextForView(checkedGroupIndex, checkedId);
    }

    private void updateDataStatus(int checkedGroupIndex, int checkedId) {
        for (int i = 0; i < mWakeWords.size(); i++) {
            if (i == checkedGroupIndex) {
                for (int j = 0; j < mWakeWords.get(checkedGroupIndex).spells.size(); j++) {
                    mWakeWords.get(checkedGroupIndex).spells.get(j).checked = checkedId == j;
                }
            }
        }
    }

    private void updateTextForView(int checkedGroupIndex, int checkedId) {
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < mWakeWords.size(); i++) {
            List<WakeWordSpell> spells = mWakeWords.get(i).spells;
            if (checkedGroupIndex == i) {
                stringBuilder.append(spells.get(checkedId).str.replace(":", ""));
            } else {
                for (WakeWordSpell spell : spells) {
                    if (spell.checked) {
                        stringBuilder.append(spell.str.replace(":", ""));
                    }
                }
            }
        }
        mTvSpell.setText(stringBuilder.toString());
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.confirm:
                if (mClickListener != null) {
                    if (mClickListener.onConfirmClick(mWakeWords)) {
                        dismiss();
                    }
                }
                break;

            case R.id.cancel:
                dismiss();
                if (mClickListener != null) {
                    mClickListener.onCancelClick();
                }
                break;
        }
    }

    public interface ClickListener {
        boolean onConfirmClick(List<WakeWord> wakeWordsAfterChoose);

        void onCancelClick();
    }

    public PolyPhoneChooseDialog setDialogClickListener(ClickListener listener) {
        this.mClickListener = listener;
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.poly_phone_choose_dialog);
        initView();
    }

    private void initView() {
        mTvWakeWord = (TextView) findViewById(R.id.wake_word_set);
        mTvSpell = (TextView) findViewById(R.id.spell_set);
        mRecyclerView = (RecyclerView) findViewById(R.id.recycler_view);
        mConfirm = (TextView) findViewById(R.id.confirm);
        mCancel = (TextView) findViewById(R.id.cancel);
        mConfirm.setOnClickListener(this);
        mCancel.setOnClickListener(this);
    }

    @Override
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        fillTextForView();
    }

    private void fillTextForView() {
        if (mWakeWords == null || mWakeWords.size() <= 0) {
            return;
        }
        StringBuilder wakeWordBuilder = new StringBuilder();
        StringBuilder spellBuilder = new StringBuilder();
        for (WakeWord wakeWord : mWakeWords) {
            wakeWordBuilder.append(wakeWord.word);
            spellBuilder.append(wakeWord.spells.get(0).str);
        }

        mTvWakeWord.setText(wakeWordBuilder.toString());
        mTvSpell.setText(spellBuilder.toString().replace(":", ""));

        LinearLayoutManager layoutManager = new LinearLayoutManager(mContext);
        mRecyclerView.setLayoutManager(layoutManager);
        mRecyclerView.setItemViewCacheSize(mWakeWords.size());
        WakeWordAdapter wakeWordAdapter = new WakeWordAdapter(mWakeWords, mContext);
        wakeWordAdapter.setRadioButtonCheckedListener(this);
        if (mSpaceItemDecoration == null) {
            mSpaceItemDecoration = new SpaceItemDecoration(63);
            mRecyclerView.addItemDecoration(mSpaceItemDecoration);
        }
        mRecyclerView.setAdapter(wakeWordAdapter);
    }

    public PolyPhoneChooseDialog setWakeWords(List<WakeWord> wakeWords) {
        this.mWakeWords = wakeWords;
        return this;
    }
}
