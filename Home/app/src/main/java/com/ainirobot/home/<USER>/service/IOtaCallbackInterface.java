package com.ainirobot.home.ota.service;

import android.os.Bundle;

public interface IOtaCallbackInterface {
    boolean onOtaUpgradeStart(boolean needDownload);
    boolean onOtaRollbackStart();

    String onOtaGetDescription();

    boolean onOtaCancelDownload();

    void installPatch(Bundle bundle);

    void headScpResult(String result);

    void headUpdateResult(String result);

    void headVersionResult(String result);

    void headUpdateParamsResult(String result);

    void navigationUpdateResult(String result);

    void navigationVersionResult(String result);

    void canOtaStartResult(String result);

    void canOtaGetStateResult(String result);

    void canGetBoardVersionResult(String result);

    void canGetPsbVersionResult(String result);

    void canGetMotorHVersionResult(String result);

    void canGetMotorVVersionResult(String result);

    void canGetAcClientVersionResult(String result);

    void canGetBmsVersionResult(String result);

    void canGetPsbSVersionResult(String result);

    void updateHeadConnectStatus(String result);

    void onOtaInterrupted(String reason);
}
