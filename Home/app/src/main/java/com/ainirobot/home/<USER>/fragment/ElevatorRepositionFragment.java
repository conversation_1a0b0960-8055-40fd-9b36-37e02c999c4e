package com.ainirobot.home.ui.fragment;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.Html;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.coreservice.bean.MultiFloorInfo;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.view.GifView;
import com.ainirobot.home.ui.view.LoadingDialog;
import com.ainirobot.home.ui.view.NumberPicker;
import com.ainirobot.home.ui.view.RepositionCancelDialog;
import com.ainirobot.home.utils.LocationUtil;
import com.ainirobot.home.utils.ResType;
import com.ainirobot.home.utils.ResUtil;
import com.ainirobot.home.utils.ToastUtil;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.ArrayList;


/**
 * 梯控时，定位交互
 */
public class ElevatorRepositionFragment extends BaseFragment implements View.OnClickListener {

    private static final String TAG = ElevatorRepositionFragment.class.getSimpleName();
    private Context mContext;
    private TextView mPointLocateView, mChargePileLocateView, mCurrentFloorView;
    private View mSuccessLayout;
    private TextView mSuccessDes, mSuccessDes_1, mBackToChooseView, mChargePileSubtitle,title;
    private View mChargeLocateLayout, mPointLocateLayout, mChargeLocatingLayout;
    private ObjectAnimator mUpAnimator;
    private ObjectAnimator mDownAnimator;
    private AnimatorSet mZoomAnimatorSet;
    private AnimatorSet mResetAnimatorSet;
    private boolean mAnimateStopped = false;
    private ImageView mSearchImageV, mSearchShadowIV;
    private ImageView mListBG, mListSelectBG,mListSelectIcon;
    private ImageButton mCancelBtn;
    private RepositionCancelDialog mDialog;
    private MultiFloorInfo currentFloorInfo;
    private Button mGuideConfirmBtn;

    private static final int DEFAULT_FLOOR = 0;
    private static final int MAIN_FLOOR = 1; // 主楼层
    private int mPoseIndex = 0;
    private volatile int mMainIndex = 0; // 主楼层，默认是有充电桩的.
    private NumberPicker mFloorNumberPicker, mPoseNumberPicker;
    private ArrayList<MultiFloorInfo> mFloorList = new ArrayList<>();
    private ArrayList<Pose> mPoseList = null;
    private LoadingDialog mLoadingDialog;
    private boolean mHasMainFloorInfo = false;

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        mContext = context;
        Log.d(TAG, "onAttach");
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        Log.i(TAG, "on create view");

        View view = inflater.inflate(R.layout.fragment_reposition_elevator, null);
        title = view.findViewById(R.id.title);
        mPointLocateLayout = view.findViewById(R.id.point_locate_guide);
        mChargeLocateLayout = view.findViewById(R.id.charge_locate_guide);
        mChargeLocatingLayout = view.findViewById(R.id.charge_locating);

        mFloorNumberPicker = (NumberPicker) view.findViewById(R.id.floorNumberPicker);
        mPoseNumberPicker = (NumberPicker) view.findViewById(R.id.poseNumberPicker);
        mFloorNumberPicker.setOnValueChangedListener(new NumberPicker.OnValueChangeListener() {
            @Override
            public void onValueChange(NumberPicker picker, int oldVal, int newVal) {
                Log.d(TAG, "onValueChange oldVal : " + oldVal + ", newVal : " + newVal +
                        ", value :" + picker.getDisplayedValueForCurrentSelection());
                if (mFloorList.size() > 0) {
                    updateCurrentFloor(newVal);
                }
            }
        });
        mPoseNumberPicker.setOnValueChangedListener(new NumberPicker.OnValueChangeListener() {
            @Override
            public void onValueChange(NumberPicker picker, int oldVal, int newVal) {
                updateCurrentPose(newVal);
            }
        });
        mPoseNumberPicker.setVisibility(View.GONE);
        mCurrentFloorView = view.findViewById(R.id.current_floor);
        mPointLocateView = view.findViewById(R.id.confirmFloor);
        mPointLocateView.setOnClickListener(this);
        mBackToChooseView = view.findViewById(R.id.backToChoose);
        mBackToChooseView.setOnClickListener(this);
        mBackToChooseView.setVisibility(View.GONE);
        mChargePileLocateView = view.findViewById(R.id.chargePileReposition);
        mChargePileLocateView.setOnClickListener(this);
        mSuccessLayout = view.findViewById(R.id.ll_locate_success);
        mSuccessDes = view.findViewById(R.id.success_des);
        mSuccessDes_1 = view.findViewById(R.id.success_des_1);
        mSuccessLayout.setVisibility(View.GONE);
        mChargePileSubtitle = view.findViewById(R.id.locate_guide_subtitle);
        mSearchImageV = (ImageView) view.findViewById(R.id.locate_search_img);
        mSearchShadowIV = (ImageView) view.findViewById(R.id.locate_search_shadow);
        mCancelBtn = (ImageButton) view.findViewById(R.id.elevator_reposition_cancel);
        mCancelBtn.setOnClickListener(this);
        mGuideConfirmBtn = view.findViewById(R.id.locate_guide_confirmBtn);
        mGuideConfirmBtn.setVisibility(View.INVISIBLE);
        mListBG = view.findViewById(R.id.bg_select_list);
        mListSelectBG = view.findViewById(R.id.bg_select_item);
        mListSelectIcon = view.findViewById(R.id.bg_select_icon);
        ((ImageButton) view.findViewById(R.id.reposition_cancel)).setOnClickListener(this);


        Bundle bundle = getArguments();
        if (bundle != null) {
            initData(bundle);
        }
        return view;
    }


    private void initData(@NonNull Bundle bundle) {
        mFloorList.clear();
        String usedMapName = bundle.getString(ModuleDef.CURRENT_MAP_NAME, "");
        Log.d(TAG, "initData usedMapName : " + usedMapName);
        if (!TextUtils.isEmpty(bundle.getString(ModuleDef.FLOOR_LIST))) {
            TypeToken<ArrayList<MultiFloorInfo>> token = new TypeToken<ArrayList<MultiFloorInfo>>() {
            };
            mFloorList.addAll((ArrayList) mGson.fromJson(bundle.getString(ModuleDef.FLOOR_LIST), token.getType()));
            Log.d(TAG, "onCreateView floorList : " + mFloorList);
            if (mFloorList != null && mFloorList.size() > 0) {
                int size = mFloorList.size();
                Log.d(TAG, "floorList size : " + size);
                mFloorNumberPicker.setMaxValue(size - 1);
                String[] display = new String[size];
                int defaultValue = 0;
                for (int i = 0; i < size; i++) {
                    MultiFloorInfo info = mFloorList.get(i);
                    display[i] = info.getFloorAlias();
                    if (info.getFloorState() == 1) {
                        mMainIndex = info.getFloorIndex();
                    }
                    if (info.getMapName().equals(usedMapName)){
                        defaultValue = i;
                    }
                }
                mFloorNumberPicker.setDisplayedValues(display);
                Log.d(TAG, "initData defaultValue : " + defaultValue);
                mFloorNumberPicker.setValue(defaultValue);
                currentFloorInfo = queryFloorInfoByName(mFloorNumberPicker.getDisplayedValueForCurrentSelection().toString());
            }

            //设置主楼层, 如果当前楼层不是主楼层，如何显示
            Log.d(TAG, "initData mMainIndex : "+ mMainIndex);
            MultiFloorInfo info = queryFloorInfoByFloorIndex(mMainIndex);
            if (info != null) {
                mHasMainFloorInfo = true;
                String alias = info.getFloorAlias();
                mChargePileLocateView.setText(ResUtil.getString(R.string.elevator_reposition_charge_pile, alias));
            } else {
                mHasMainFloorInfo = false;
                mChargePileLocateView.setText(ResUtil.getString(R.string.elevator_reposition_charge_pile, mFloorNumberPicker.getDisplayedValueForCurrentSelection().toString()));
            }

        }
    }


    private void updateCurrentFloor(int newVal) {
        CharSequence currentSelection = mFloorNumberPicker.getDisplayedValueForCurrentSelection();
        Log.d(TAG, "updateCurrentFloor: " + currentSelection);
        if (TextUtils.isEmpty(currentSelection)) {
            return;
        }
        if (!mHasMainFloorInfo) {
            mChargePileLocateView.setText(ResUtil.getString(R.string.elevator_reposition_charge_pile, currentSelection.toString()));
        }
        currentFloorInfo = queryFloorInfoByName(currentSelection.toString());
    }

    private void updateCurrentPose(int newVal) {
        mPoseIndex = newVal;
    }


    @Override
    public void onStart() {
        super.onStart();
        Log.i(TAG, "onstart");
    }

    private MultiFloorInfo queryFloorInfoByName(String floorAlias) {
        if (TextUtils.isEmpty(floorAlias)) {
            return null;
        }
        for (MultiFloorInfo bean : mFloorList) {
            if (floorAlias.equals(bean.getFloorAlias())) {
                return bean;
            }
        }
        return null;
    }

    private MultiFloorInfo queryFloorInfoByFloorIndex(int floorIndex) {
        Log.d(TAG, "queryFloorInfoByFloorIndex : " + floorIndex);
        for (MultiFloorInfo bean : mFloorList) {
            if (floorIndex == bean.getFloorIndex()) {
                return bean;
            }
        }
        return null;
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.elevator_reposition_cancel:
                clickCancel();
                break;

            case R.id.confirmFloor:
                if (mFloorNumberPicker.getVisibility() == View.VISIBLE) {
                    clickConfirmFloor();
                } else {
                    clickPointReposition();
                }
                break;
            case R.id.backToChoose:
                clickToChooseFloor();
                break;
            case R.id.chargePileReposition:
                clickToMainFloor();
                break;
            case R.id.reposition_cancel:
                clickChargePileCancel();
                break;
            default:
                break;
        }
    }

    private void clickChargePileCancel() {
        clickToChooseFloor();
    }

    private void clickCancel() {
        if (mDialog == null) {
            mDialog = new RepositionCancelDialog(getContext(), R.style.OTADialog)
                    .setDialogClickListener(new RepositionCancelDialog.ClickListener() {
                        @Override
                        public void onConfirmClick() {
                            mDialog = null;
                        }

                        @Override
                        public void onCancelClick() {
                            ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_REPOSITION_CANCEL);
                            mDialog = null;
                        }
                    });
            mDialog.show();
        }
    }

    // 点击充电桩定位,先切换到主楼层
    private void clickToMainFloor() {
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_ELEVATOR_CHARGE_LOCATE, queryFloorInfoByFloorIndex(mMainIndex));
    }

    // 点击重新选择楼层
    private void clickToChooseFloor() {
        showStartChooseFloor();
        mCurrentFloorView.setText(ResUtil.getString(R.string.elevator_reposition_choose_floor));
        mPointLocateView.setText(ResUtil.getString(R.string.elevator_reposition_confirm_floor));
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_ELEVATOR_BACK_TO_CHOOSE_FLOOR);
    }

    //选择确认楼层
    private void clickConfirmFloor() {
        if (null == mLoadingDialog) {
            mLoadingDialog = new LoadingDialog(getContext(), getString(R.string.switch_map_loading));
        }
        mLoadingDialog.startLoading();
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_ELEVATOR_CONFIRM_FLOOR, currentFloorInfo);
    }


    // 梯控定位点定位
    private void clickPointReposition() {
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_ELEVATOR_POINT_LOCATE, mPoseIndex);
    }

    // 充电桩定位中
    private void chargePileLocating() {
        showChargePileLocatingUI();
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_ELEVATOR_CHARGE_LOCATING, "");
    }


    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
        Log.d(TAG, "onMessage type : " + type + " , message : " + message);
        switch (type) {
            case ELEVATOR_SWITCH_MAP_SUCCEED:
                onSwitchMapSuccess(message);
                break;
            case ELEVATOR_SWITCH_MAP_FAIL:
                break;

            case ELEVATOR_REPOSITION_SUCCEED:
                showSuccessView(message);
                break;
            case ELEVATOR_CHARGE_PILE_REPOSITION_FAIL: // 充电桩定位失败
                showChargeLocateFailView();
                break;
            case ELEVATOR_CHARGE_PILE_LOCATE_START:
                showChargePileLocate(message);
                break;
            case ELEVATOR_EXIT:
                stopRepositionAnimate();
            default:
                break;
        }
    }

    private void showChargeLocateFailView() {
        mChargeLocateLayout.post(new Runnable() {
            @Override
            public void run() {
                ToastUtil.showToast(getContext(), ResUtil.getString(R.string.elevator_reposition_fail));
                mPointLocateLayout.setVisibility(View.VISIBLE);
                mChargeLocateLayout.setVisibility(View.GONE);
                mChargeLocatingLayout.setVisibility(View.GONE);
            }
        });
    }

    //正在定位中
    private void showChargePileLocatingUI(){
        showChargePileSearching();
        upThrowAnimator();
        zoomImageViewScale();
    }

    private void showChargePileSearching() {
        mChargeLocateLayout.post(new Runnable() {
            @Override
            public void run() {
                mPointLocateLayout.setVisibility(View.GONE);
                mChargeLocateLayout.setVisibility(View.GONE);
                mChargeLocatingLayout.setVisibility(View.VISIBLE);
            }
        });
    }

    private void showChargePileLocate(final String isCharging) {
        mChargeLocateLayout.post(new Runnable() {
            @Override
            public void run() {
                mPointLocateLayout.setVisibility(View.GONE);
                mChargeLocateLayout.setVisibility(View.VISIBLE);
                mChargeLocatingLayout.setVisibility(View.GONE);

                TextView subtitle = (TextView) mChargeLocateLayout.findViewById(R.id.locate_guide_subtitle);
                GifView gifView = (GifView) mChargeLocateLayout.findViewById(R.id.locate_guide_bg);
                MultiFloorInfo info = queryFloorInfoByFloorIndex(mMainIndex);
                if (info != null) {
                    subtitle.setText(ResUtil.getString(R.string.elevator_reposition_charge_remind_msg, info.getFloorAlias()));
                }
                if ("true".equals(isCharging)) {
                    chargePileLocating();
                } else {
                    Log.d(TAG, "showGuideView: mIsWireCharging=" +
                            LocationUtil.getInstance().isChargingTypeWire());
                    gifView.setMovieResource(
                            ResType.GIF_PUSH_TO_CHARGE_PILE.getResIdByType()
                    );
                    mGuideConfirmBtn.setText(ResUtil.getString(R.string.dialog_confirm));
                }

            }
        });

    }

    private void showStartChooseFloor() {
        mPointLocateLayout.post(new Runnable() {
            @Override
            public void run() {
                mPointLocateLayout.setVisibility(View.VISIBLE);
                mChargeLocateLayout.setVisibility(View.GONE);
                mFloorNumberPicker.setVisibility(View.VISIBLE);
                mPoseNumberPicker.setVisibility(View.GONE);
                mCurrentFloorView.setVisibility(View.VISIBLE);
                mPointLocateView.setVisibility(View.VISIBLE);
                mChargePileLocateView.setVisibility(View.VISIBLE);
                mBackToChooseView.setVisibility(View.GONE);
                mSuccessLayout.setVisibility(View.GONE);

            }
        });

    }

    private void onSwitchMapSuccess(final String message) {
        mPointLocateLayout.post(new Runnable() {
            @Override
            public void run() {
                if (null != mLoadingDialog) {
                    mLoadingDialog.stopLoading();
                }
                mPoseList = new Gson().fromJson(message, new TypeToken<ArrayList<Pose>>() {
                }.getType());
                showSwitchMapSuccessView();
                mCurrentFloorView.setText(Html.fromHtml(ResUtil.getString(R.string.elevator_reposition_current_floor,
                        currentFloorInfo == null ? "" : currentFloorInfo.getFloorAlias())));
                mPointLocateView.setText(ResUtil.getString(R.string.elevator_reposition_try_locate));
                if (mPoseList != null && mPoseList.size() > 0) {
                    int size = mPoseList.size();
                    Log.d(TAG, "mPoseList size : " + size);
                    String[] display = new String[size];
                    for (int i = 0; i < size; i++) {
                        display[i] = mPoseList.get(i).getName();
                    }
                    mPoseNumberPicker.updateDisplayedValuesAndMaxValue(display, size - 1);
                }
            }
        });

    }

    private void showSwitchMapSuccessView() {
        mChargePileLocateView.setVisibility(View.GONE);
        mFloorNumberPicker.setVisibility(View.GONE);
        mPoseNumberPicker.setVisibility(View.VISIBLE);
        mBackToChooseView.setVisibility(View.VISIBLE);
    }

    private void showSuccessView(final String poseName) {
        mPointLocateLayout.post(new Runnable() {
            @Override
            public void run() {
                mPointLocateLayout.setVisibility(View.VISIBLE);
                mChargeLocateLayout.setVisibility(View.GONE);
                mChargeLocatingLayout.setVisibility(View.GONE);

                Log.d(TAG, "show Elevator Reposition Success");
                mFloorNumberPicker.setVisibility(View.GONE);
                mPoseNumberPicker.setVisibility(View.GONE);
                mCurrentFloorView.setVisibility(View.GONE);
                mPointLocateView.setVisibility(View.GONE);
                mBackToChooseView.setVisibility(View.GONE);
                mListBG.setVisibility(View.GONE);
                mListSelectBG.setVisibility(View.GONE);
                title.setVisibility(View.GONE);
                mListSelectIcon.setVisibility(View.GONE);
                mChargePileLocateView.setVisibility(View.GONE);
                mSuccessLayout.setVisibility(View.VISIBLE);
                MultiFloorInfo info = queryFloorInfoByFloorIndex(mMainIndex);

                String divisionStyle = ",";
                String sucDes = ResUtil.getString(R.string.elevator_reposition_success_des,
                        TextUtils.isEmpty(poseName) ? currentFloorInfo.getFloorAlias() + divisionStyle :
                                (info == null ? "" : info.getFloorAlias() + divisionStyle));
                //"定位中："字体颜色重设为 R.color.alpha_50
                SpannableString spannableString = new SpannableString(sucDes);
                if (sucDes.contains(":")) {
                    spannableString.setSpan(new ForegroundColorSpan(ResUtil.getColor(R.color.alpha_50)),
                            0,
                            sucDes.indexOf(":") + 1,
                            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                }

                //分割的逗号字体颜色重设为 R.color.alpha_50
                if (sucDes.contains(",")) {
                    spannableString.setSpan(new ForegroundColorSpan(ResUtil.getColor(R.color.alpha_50)),
                            sucDes.indexOf(","),
                            sucDes.indexOf(",") + 1,
                            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                }

                mSuccessDes.setText(spannableString);
                // 充电桩定位使用poseName , 定位点定位由mPoseList获取
                mSuccessDes_1.setText(TextUtils.isEmpty(poseName) ? mPoseList.get(mPoseIndex).getName() : poseName);
            }
        });

    }

    private void upThrowAnimator() {
        if (mAnimateStopped) {
            return;
        }
        mUpAnimator = ObjectAnimator.ofFloat(mSearchImageV, "translationY", 0, -160);
        mUpAnimator.setDuration(500);
        mUpAnimator.setInterpolator(new DecelerateInterpolator(1.2f));
        mUpAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (!mAnimateStopped) {
                    freeFall();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        mUpAnimator.start();
    }

    private void freeFall() {
        if (mAnimateStopped) {
            return;
        }
        mDownAnimator = ObjectAnimator.ofFloat(mSearchImageV, "translationY", -160, 0);
        mDownAnimator.setDuration(500);
        mDownAnimator.setInterpolator(new AccelerateInterpolator(1.2f));
        mDownAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (!mAnimateStopped) {
                    upThrowAnimator();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        mDownAnimator.start();
    }

    private void zoomImageViewScale() {
        if (mAnimateStopped) {
            return;
        }
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(mSearchShadowIV, "scaleX", 1.0f, 0.7f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(mSearchShadowIV, "scaleY", 1.0f, 0.7f);
        mZoomAnimatorSet = new AnimatorSet();
        mZoomAnimatorSet.playTogether(scaleX, scaleY);
        mZoomAnimatorSet.setDuration(500);
        mZoomAnimatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (!mAnimateStopped) {
                    resetImageViewScale();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        mZoomAnimatorSet.start();
    }

    private void resetImageViewScale() {
        if (mAnimateStopped) {
            return;
        }
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(mSearchShadowIV, "scaleX", 0.7f, 1.0f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(mSearchShadowIV, "scaleY", 0.7f, 1.0f);
        mResetAnimatorSet = new AnimatorSet();
        mResetAnimatorSet.playTogether(scaleX, scaleY);
        mResetAnimatorSet.setDuration(500);
        mResetAnimatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (!mAnimateStopped) {
                    zoomImageViewScale();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        mResetAnimatorSet.start();
    }

    private void stopRepositionAnimate() {
        Log.i(TAG, "stopRepositionAnimate mAnimateStoppen:" + mAnimateStopped);
        if (mAnimateStopped) {
            return;
        }
        if (mPointLocateView == null) {
            Log.i(TAG, "stopRepositionAnimate mMenualGuideV is null");
            return;
        }
        mPointLocateView.post(new Runnable() {
            @Override
            public void run() {
                mAnimateStopped = true;
                if (mUpAnimator != null && mUpAnimator.isRunning()) {
                    mUpAnimator.end();
                    mUpAnimator = null;
                }
                if (mDownAnimator != null && mDownAnimator.isRunning()) {
                    mDownAnimator.end();
                    mDownAnimator = null;
                }
                if (mResetAnimatorSet != null && mResetAnimatorSet.isRunning()) {
                    mResetAnimatorSet.end();
                    mResetAnimatorSet = null;
                }
                if (mZoomAnimatorSet != null && mZoomAnimatorSet.isRunning()) {
                    mZoomAnimatorSet.end();
                    mZoomAnimatorSet = null;
                }
            }
        });

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.d(TAG, "onDestroyView");
        if (mDialog != null) {
            mDialog.dismiss();
        }
    }


}
