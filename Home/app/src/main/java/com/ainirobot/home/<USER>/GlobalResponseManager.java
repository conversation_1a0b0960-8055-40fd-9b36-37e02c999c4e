package com.ainirobot.home.control;


import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.R;
import com.ainirobot.home.floatdialog.FloatDialogManager;


public class GlobalResponseManager {

    private static GlobalResponseManager globalResponseManager;

    private GlobalResponseManager() {
    }

    public static GlobalResponseManager getInstance() {
        if (globalResponseManager == null) {
            globalResponseManager = new GlobalResponseManager();
        }

        return globalResponseManager;
    }

    public void showGlobalToastResponse() {

        if (FloatDialogManager.getInstance().floatDialogIsShow()) {
            return;
        }

        if (!ProductInfo.isDeliveryProduct() && !ProductInfo.isMeissaPlus()) {
            FloatDialogManager.getInstance().showLightResponseDialog(ApplicationWrapper.getContext()
                    .getString(R.string.response_lite_lite_page));
        }
    }

}
