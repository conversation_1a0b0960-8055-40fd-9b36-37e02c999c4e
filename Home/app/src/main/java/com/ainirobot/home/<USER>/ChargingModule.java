package com.ainirobot.home.module;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.bi.BiChargingWarningReport;
import com.ainirobot.home.bi.BiGbCharging;
import com.ainirobot.home.bi.BiRunningErrorReport;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.control.GlobalResponseManager;
import com.ainirobot.home.control.LxcMemoryManager;
import com.ainirobot.home.control.RadarManager;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.report.TaskReport;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.utils.LocationUtil;
import com.ainirobot.home.utils.MapUtils;
import com.ainirobot.home.utils.ResUtil;

import org.json.JSONException;
import org.json.JSONObject;

public class ChargingModule extends BaseModule {

    private static final String TAG = "ChargingModule_Home";
    private static ChargingModule sInstance = null;
    private boolean mCharging = false;
    private int mBatteryLevel = -1;
    private long mLastUpdateBatteryTime = 0;
    private int mBatteryTemp = -1;
    private boolean mIsUpgrade = false;
    private int mReqId;
    private BiGbCharging mBiGbCharging = new BiGbCharging();
    private BiChargingWarningReport biChargingWarningReport = new BiChargingWarningReport();

    private Context mContext;

    private final static int EVENT_CHARGING_WARNING = 1;
    private final static int EVENT_DISABLE_CHARGING_WARNING = 2;

    private static final int CHARGING_WARNING_STATUS_NORMAL = 0;
    private static final int CHARGING_WARNING_STATUS_CHARGING_SLOW = 1;
    private static final int CHARGING_BMS_LOW_TEMP = 5;

    private static final int LOW_TEMP_MAX_TIME = 60;
    private static final long BATTERY_NOT_CHANGE_INTERVAL = 1000 * 60 * 30;

    private boolean isChargingWarningScreenShowed = false;

    private volatile int lowTemperatureTimes = 0;

    private volatile boolean mRequestStop = false;
    private volatile long mGoForwardEndTime = 0;

    private ChargingModule() {
    }

    public static ChargingModule getInstance() {
        if (sInstance == null) {
            sInstance = new ChargingModule();
        }
        return sInstance;
    }

    private final Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            Log.d(TAG, "msg: " + msg);
            switch (msg.what) {
                case EVENT_CHARGING_WARNING:
                    if (!isChargingWarningScreenShowed) {
                        isChargingWarningScreenShowed = true;
                        biReportChargingWarningInfo(Integer.toString(BiChargingWarningReport.SCENE_TYP_POP_UP));
                        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.CHARGING_WARNING, mBatteryLevel + "");
                    }
                    break;

                case EVENT_DISABLE_CHARGING_WARNING:
                    isChargingWarningScreenShowed = false;
                    biReportChargingWarningInfo(Integer.toString(BiChargingWarningReport.SCENE_TYP_DISAPPEAR));
                    UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.BATTERY_LEVEL, mBatteryLevel + "");
                    break;

                default:
                    break;
            }

        }
    };

    private final StatusListener mChargingListener = new StatusListener() {
        @Override
        public void onStatusUpdate(String type, String data) {
            if (TextUtils.isEmpty(data)) {
                return;
            }
            try {
                JSONObject object = new JSONObject(data);
                int status = object.optInt("status");
                Log.d(TAG, "status: " + status);
                Message message = mHandler.obtainMessage();
                //1 - indicates the charging error.
                if (status == Definition.BMS_WARNING_STATUS_BMS_CHARGING_SLOW) {
                    message.what = EVENT_CHARGING_WARNING;
                } else if (status == Definition.BMS_WARNING_STATUS_CAN_CHARGING_NORMAL) {
                    message.what = EVENT_DISABLE_CHARGING_WARNING;
                }
                message.arg1 = -1;
                message.obj = status;
                mHandler.sendMessage(message);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    };

    public void init(Context context) {
        mContext = context;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.d(TAG, " onNewSemantics:: text = " + text + ", params = " + params +
                ", mCharging = " + mCharging);
        mReqId = reqId;
        switch (intent) {
            case Definition.REQ_BATTERY_CHARGING:
                if (mGoForwardEndTime > 0) {
                    Log.e(TAG, "Leaving ChargePile");
                    break;
                }
                int bmsTemp = CHARGING_BMS_LOW_TEMP;
                try {
                    JSONObject jsonObject = new JSONObject(params);
                    int level = jsonObject.getInt(ModuleDef.PARAM_LEVEL);
                    bmsTemp = jsonObject.getInt(ModuleDef.PARAM_BMS_TEM);
                    Log.d(TAG, " onNewSemantics:: req_battery_charging level = " + level
                            + ", mBatteryLevel = " + mBatteryLevel + ", bmsTemp = " + bmsTemp
                            + ", isChargingWarningScreenShowed = " + isChargingWarningScreenShowed);

                    if (bmsTemp < CHARGING_BMS_LOW_TEMP) {
                        lowTemperatureTimes++;
                        if (lowTemperatureTimes == LOW_TEMP_MAX_TIME) { // 温度过低
                            Log.d(TAG, " onNewSemantics:: send low temperature message");
                            UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.CHARGING_BMS_LOW_TEMP, params);
                        }
                    } else {
                        Log.d(TAG, " onNewSemantics:: lowTemperatureTimes=" + lowTemperatureTimes);
                        if (isChargingWarningScreenShowed) { // 充电过慢
                            UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.CHARGING_WARNING, level + "");
                        } else { // 正常充电
                            UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.BATTERY_LEVEL, level + "");
                        }
                        lowTemperatureTimes = 0;
                    }

                    if (level != mBatteryLevel) {
                        if (level < mBatteryLevel) {
                            Log.d(TAG, " onNewSemantics:: send low battery message, currentLevel:" + mBatteryLevel + " updateLevel:" + level);
                            new BiRunningErrorReport().addError(BiRunningErrorReport.CHARGE_TYPE,"充电过程中电量从" + mBatteryLevel + "降到" + level).report();
                        }
                        mBatteryLevel = level;
                        mLastUpdateBatteryTime = System.currentTimeMillis();
                        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.CHARGING_LEVEL_UPDATE, level + "");
                    } else {
                        if (level < 100 && System.currentTimeMillis() - mLastUpdateBatteryTime > BATTERY_NOT_CHANGE_INTERVAL) {
                            Log.d(TAG, " onNewSemantics:: battery charge not change, currentLevel:" + mBatteryLevel);
                            new BiRunningErrorReport().addError(BiRunningErrorReport.CHARGE_TYPE, "充电过程中电量持续为" + mBatteryLevel).report();
                            mLastUpdateBatteryTime = System.currentTimeMillis();
                        }
                    }

                    boolean isUpgrade = jsonObject.getBoolean(ModuleDef.PARAM_WAIT_UPGRADE);
                    if (mIsUpgrade != isUpgrade) {
                        mIsUpgrade = isUpgrade;
                        if (!isChargingWarningScreenShowed) {
                            UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.WAIT_UPGRADE, isUpgrade + "");
                        }
                    }
                } catch (NumberFormatException | JSONException e) {
                    e.printStackTrace();
                }
                if (!mCharging) {
                    RadarManager.setAllowCloseRadar(true);
                    startCharge();
                }
                break;
            case Definition.REQ_BATTERY_FULL:
                if (mBatteryLevel != 100) {
                    mBatteryLevel = 100;
                    if (isChargingWarningScreenShowed) {
                        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.CHARGING_WARNING, 100 + "");
                    } else {
                        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.BATTERY_LEVEL, 100 + "");
                    }
                }
                if (!mCharging) {
                    startCharge();
                }
                break;
            case Definition.REQ_NORMAL:
                mRequestStop = true;
                checkStopTime();
                break;

            case Definition.REQ_STOP_CHARGING:
                SystemApi.getInstance().onInspectionStart();
                Bundle bundle = new Bundle();
                bundle.putString(ModuleDef.MSG_BUNDLE_INTENT, ModuleDef.START_INSPECTION);
                bundle.putString(ModuleDef.MSG_BUNDLE_TEXT, ModuleDef.START_INSPECTION);
                bundle.putString(ModuleDef.MSG_BUNDLE_PARAM, ModuleDef.NORMAL_CHECK);
                Message msg = Message.obtain();
                msg.what = ModuleDef.MSG_NEW_REQUEST;
                msg.setData(bundle);
                ControlManager.getControlManager().getMainHandler().sendMessage(msg);
                break;

            case Definition.REQ_STOP_CHARGING_BY_CHARGING_SLOW:
                if (!ControlManager.getControlManager().getSystemStatusManager().getIsCharging()) {
                    Log.d(TAG, "charge_slow_recharge no charging and no need recharge");
                    break;
                }

                Log.d(TAG, "charge_slow_recharge stop charging req");
                TaskReport.getInstance().warningReport("charging_slowly");
                try {
                    String data = new JSONObject(params).getString("data");
                    JSONObject jsonData = new JSONObject(data);
                    int uiLevel = jsonData.getInt(Definition.JSON_CAN_UI_LEVEL);
                    int bmsLevel = jsonData.getInt(Definition.JSON_CAN_BMS_LEVEL);
                    biChargingWarningReport.addUIPer(Integer.toString(uiLevel));
                    biChargingWarningReport.addBmsPer(Integer.toString(bmsLevel));
                    biReportChargingWarningInfo(Integer.toString(BiChargingWarningReport.SCENE_TYP_AUTO_CHARGE));
                } catch (NumberFormatException | JSONException e) {
                    Log.d(TAG, "charge_slow_recharge get data error");
                    e.printStackTrace();
                }

                SkillManager.getInstance().speechWithFinishCallBack(mContext.getString(R.string.charging_warning_stop_charging), new SkillManager.SpeechFinishCallBack() {
                    @Override
                    public void finish() {
                        Log.d(TAG, "charge_slow_recharge open radar");
                        RadarManager.openRadar(new RadarManager.RadarListener() {
                            @Override
                            public boolean onSucceed() {
                                Log.d(TAG, "charge_slow_recharge stop charging");
                                stopCharging();
                                return true;
                            }
                        });
                    }
                });
                break;

            case Definition.REQ_STOP_CHARGING_BY_APP:
                UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.LEAVING_CHARGE_POINT, null);
                stopCharging();
                break;

            case Definition.REQ_STOP_CHARGING_BY_VOICE:
            case Definition.REQ_STOP_CHARGING_BY_VOICE_NEW:
                Log.d(TAG, "charging status:" + mCharging + ", mode:" + LocationUtil.getInstance().isChargingTypePile());
                String ttsText = mCharging ? (LocationUtil.getInstance().isChargingTypePile() ?
                        ResUtil.getString(R.string.stop_charging_tips) :
                        ResUtil.getString(R.string.charge_receive_voice_mode_error))
                        : ResUtil.getString(R.string.charge_voice_stop_charging_err);
                SkillManager.getInstance().speechPlayText(ttsText);
                if (mCharging) {
                    if (LocationUtil.getInstance().isChargingTypePile()) {
                        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.LEAVING_CHARGE_POINT_PRE, null);
                        SystemApi.getInstance().stopChargingByApp();
                    }
                } else {
                    stop();
                }
                break;

            default:
                GlobalResponseManager.getInstance().showGlobalToastResponse();
                break;
        }
        SystemApi.getInstance().finishModuleParser(reqId, true);
        return true;
    }

    private void startCharge() {
        mCharging = true;
        SkillManager.getInstance().openSpeechAsrRecognizeMode();
        Log.d(TAG, "startCharge");
        playWav();
        SystemApi.getInstance().resetHead(0, null);
        //SkillManager.getInstance().cancleAudioOperation();
//        SkillManager.getInstance().closeSpeechAsrRecognize();
        SystemApi.getInstance().getRobotStatus(Definition.STATUS_BMS_WARNING, mChargingListener);
        SystemApi.getInstance().registerStatusListener(Definition.STATUS_BMS_WARNING, mChargingListener);

        Bundle bundle = new Bundle();
        bundle.putInt(ModuleDef.PARAM_LEVEL, mBatteryLevel);
        bundle.putBoolean(ModuleDef.PARAM_WAIT_UPGRADE, mIsUpgrade);
        UIController.getInstance().showFragment(
                UIController.FRAGMENT_TYPE.FRAGMENT_CHARGING, bundle, null);

        LxcMemoryManager.getInstance().startCheckTimer();
    }

    private void playWav() {
        try {
            SkillManager.getInstance().speechPlayTone(R.raw.chargeok, null);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onStop() {
        Log.d(TAG, "on stop");
        super.onStop();
        SystemApi.getInstance().stopMove(0, null);
        DelayTask.cancel(this);
        lowTemperatureTimes = 0;
        mRequestStop = false;
        mGoForwardEndTime = 0;
        mBatteryLevel = -1;
        mLastUpdateBatteryTime = 0;
        mIsUpgrade = false;
        mCharging = false;
        if (isChargingWarningScreenShowed) {
            biReportChargingWarningInfo(Integer.toString(BiChargingWarningReport.SCENE_TYP_DISAPPEAR));
            isChargingWarningScreenShowed = false;
        }
        SkillManager.getInstance().openSpeechAsrRecognize();
        SystemApi.getInstance().unregisterStatusListener(mChargingListener);

        if (mHandler.hasMessages(EVENT_CHARGING_WARNING)) {
            mHandler.removeMessages(EVENT_CHARGING_WARNING);
        }
        if (mHandler.hasMessages(EVENT_DISABLE_CHARGING_WARNING)) {
            mHandler.removeMessages(EVENT_DISABLE_CHARGING_WARNING);
        }

        LxcMemoryManager.getInstance().cancelMemTimer();
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
        switch (type) {
            case ModuleDef.LOCAL_MESSAGE_STOP_CHARGING:
                Log.d(TAG, "Stop charging by ui");
                SystemApi.getInstance().stopChargingByApp();
                break;

            case ModuleDef.LOCAL_MESSAGE_CW_BI_CLICK_VS_BUTTON:
                biReportChargingWarningInfo(Integer.toString(BiChargingWarningReport.SCENE_TYP_CLICK_VIEW_SOLUTION_BUTTON));
                break;
            default:
                break;
        }
    }

    private void stopCharging() {
        if (mGoForwardEndTime != 0) {
            Log.d(TAG, "stopCharging:moving");
            return;
        }
        Log.d(TAG, "stopCharging:goForward:");
        mBiGbCharging.stopChargingBaseResult()
                .ctime()
                .mode(2)
                .eq(ControlManager.getControlManager().getSystemStatusManager().getBatteryLevel())
                .report();
        float speed = 0.1f;

        float distance = ProductInfo.isSaiphXdOrBigScreen()
                || ProductInfo.isSaiphChargeIr() || ProductInfo.isSaiphPro()
                || ProductInfo.isAlnilamPro() || ProductInfo.isMeissaPlus() ? 0.2f : 0.1f;
        distance += MapUtils.getStopChargeMoveOffset();

        long goForwardTimeOut = (long) ((2 + distance / speed) * 1000.0);
        mGoForwardEndTime = System.currentTimeMillis() + goForwardTimeOut;
        Log.d(TAG, "stopCharging:goForward: speed=" + speed + " distance=" + distance
                + " goForwardTimeOut=" + goForwardTimeOut + " mGoForwardEndTime=" + mGoForwardEndTime);
        SystemApi.getInstance().goForward(0, speed, distance, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                super.onResult(result, message);
                Log.d(TAG, "stopCharging:goForward:onResult:status=" + result + " msg=" + message);
                Log.d(TAG, "stopCharging:goForward:onResult:mGoForwardEndTime=" + mGoForwardEndTime);
                mGoForwardEndTime = 0;
                checkStopTime();
            }
        });
    }

    private void checkStopTime() {
        //Fix by yc-20220413：保证goForward走出成功后再停止，避免业务在离桩动作完成之前启动
        Log.d(TAG, "checkStopTime: mRequestStop=" + mRequestStop);
        if (!mRequestStop) {
            Log.d(TAG, "checkStopTime:Has not req stop!");
            return;
        }
        long delay = mGoForwardEndTime - System.currentTimeMillis();
        Log.d(TAG, "checkStopTime: delay=" + delay + " mGoForwardEndTime=" + mGoForwardEndTime);
        DelayTask.cancel(this);
        DelayTask.submit(this, new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "checkStopTime:run: Do stop!");
                stop();
            }
        }, delay > 0 ? delay : 0);
    }

    @Override
    public boolean onHWReport(int hwFunction, String cmdType, String params) {
        Log.i(TAG, "on hardware report, cmdtype:" + cmdType + " , params:" + params);

        if (TextUtils.equals(cmdType, Definition.HW_NAVI_ESTIMATE_LOST)) {
            SystemApi.getInstance().getLocation(mReqId,
                    LocationUtil.getInstance().getLocationTypeIdByChargingType(), new CommandListener() {
                        @Override
                        public void onResult(int result, String message) {
                            Log.i(TAG, "Get charging pile result:" + result + " message:" + message);
                            Pose mChargingPose = new Pose();
                            try {
                                JSONObject jsonObject = new JSONObject(message);
                                boolean state = jsonObject.optBoolean(Definition.JSON_NAVI_SITE_EXIST, false);
                                if (state) {
                                    float x = (float) jsonObject.getDouble(Definition.JSON_NAVI_POSITION_X);
                                    float y = (float) jsonObject.getDouble(Definition.JSON_NAVI_POSITION_Y);
                                    float theta = (float) jsonObject.getDouble(Definition.JSON_NAVI_POSITION_THETA);
                                    mChargingPose.setX(x);
                                    mChargingPose.setY(y);
                                    mChargingPose.setTheta(theta);
                                } else {
                                    return;
                                }
                            } catch (JSONException e) {
                                e.printStackTrace();
                            }
                            setFixedEstimate(mChargingPose, 0);
                        }
                    });
            return true;
        }
        return false;
    }

    private void setFixedEstimate(final Pose pose, final int retryCount) {

        Log.i(TAG, "retry resetEstimate count:" + retryCount);
        if (retryCount > 9) {
            naviCmdTimeOutReport();
            return;
        }

        SystemApi.getInstance().setFixedEstimate(mReqId, mGson.toJson(pose), new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.i(TAG, "Set pose estimate result:" + result + " message:" + message);
                if (mState == ModuleDef.MODULE_STATE_IDLE) {
                    Log.i(TAG, "state is idle, return!");
                    return;
                }
                switch (result) {
                    case Definition.RESULT_OK:
                        if ("succeed".equals(message)) {
                            Log.i(TAG, "reset estimate success");
                            SkillManager.getInstance().speechPlayText(ResUtil.getString(R.string.reposition_reset_success));
                        } else {
                            Log.i(TAG, "reset estimate failure");
                            setFixedEstimate(pose, (retryCount + 1));
                        }
                        break;
                    case Definition.RESULT_FAILURE:
                    case Definition.RESULT_STOP:
                        Log.i(TAG, "reset estimate failure");
                        setFixedEstimate(pose, (retryCount + 1));
                        break;
                    default:
                        break;
                }
            }
        });
    }

    private void naviCmdTimeOutReport() {
        Log.d(TAG, "nviCmdTimeOutReport");
        long timestamp = System.currentTimeMillis();
        String cacheId = RobotSettings.getSystemSn() + "-" + timestamp;
        SystemApi.getInstance().naviTimeOutCmdReport(0, timestamp, cacheId,
                Definition.TYPE_ACTION_CHARGE_PILE_ESTIMATE_FAILUE, "ChargePileEstimateFail", new CommandListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.i(TAG, "nviCmdTimeOutReport: " + result + " message:" + message);
                    }
                });
    }

    private void biReportChargingWarningInfo(String type) {
        Log.d(TAG, "biReportChargingWarningInfo type: " + type);
        biChargingWarningReport.addScene(type);
        biChargingWarningReport.report();
    }
}
