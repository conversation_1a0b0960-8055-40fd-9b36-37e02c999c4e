package com.ainirobot.home.bi;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.bi.anotation.SpeechResponse;


import org.json.JSONException;
import org.json.JSONObject;

/**
 * speech nlp response
 *
 * @version V1.0.0
 * @date 2019/4/19 10:21
 */
public class BiSpeechResponseReport extends BaseBiReport {

    private static final String TAG = "BiSpeechResponseReport";
    private static final String TABLE_NAME = "gb_y_sid";
    private static final String SID = "sid";
    private static final String RESPONSE = "response";
    private static final String APP_NAME = "appname";
    private static final String CTIME = "ctime";
    private static BiSpeechResponseReport biSpeechResponseReport = new BiSpeechResponseReport();

    private BiSpeechResponseReport() {
        super(TABLE_NAME);
    }

    private BiSpeechResponseReport addSid(Object sid) {
        addData(SID, sid);
        return this;
    }

    private BiSpeechResponseReport addResponse(Object response) {
        addData(RESPONSE, response);
        return this;
    }

    @Override
    public void report() {
        addData(CTIME, System.currentTimeMillis());
        addData(APP_NAME, ApplicationWrapper.getContext().getPackageName());
        super.report();
    }


    public static void reportSpeechResponse(String params, @SpeechResponse int response) {
        String sid = parseSid(params);
        if (TextUtils.isEmpty(sid)) {
            Log.i(TAG, "reportSpeechResponse: don't speech request,no sid");
            return;
        }
        biSpeechResponseReport
                .addSid(sid)
                .addResponse(response)
                .report();
    }

    private static String parseSid(String params) {
        Log.i(TAG, "isSpeechRequest: params:" + params);
        if (TextUtils.isEmpty(params)) {
            return null;
        }
        try {
            JSONObject jsonObject = new JSONObject(params);
            String sid = jsonObject.getString("sid");
            return TextUtils.isEmpty(sid) ? null : sid;
        } catch (JSONException e) {
            return null;
        }
    }


}
