package com.ainirobot.home.control;

/**
 * 散乱的命令管理类，用于管理碎片式的命令，消息
 */
public class ScatteredManager {
    private final static String TAG = ScatteredManager.class.getSimpleName();
    private static ScatteredManager mInstance;
    /**
     * 重定位业务中只上报异常TopIR事件，不考虑Home断连重启，或者重定位业务二次执行
     */
    private volatile boolean hasReportTopIrStatus = false;

    private ScatteredManager() {

    }

    public static synchronized ScatteredManager getInstance() {
        if (mInstance == null) {
            mInstance = new ScatteredManager();
        }
        return mInstance;
    }

    public void updateReportTopIrStatus(){
        hasReportTopIrStatus = true;
    }

    public boolean queryTopIrReportStatus(){
        return hasReportTopIrStatus;
    }


}
