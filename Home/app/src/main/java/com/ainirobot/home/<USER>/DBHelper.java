/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.home.data;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import com.ainirobot.home.data.table.TableFeature;
import com.ainirobot.home.data.table.TableIntent;

public class DB<PERSON>elper extends SQLiteOpenHelper {

    private static final String TAG = "DBHelper:Home";

    public static final int DATABASE_VERSION = 150;

    public static String DB_NAME = "home.db";
    private Context context;

    public DBHelper(Context context, String name, SQLiteDatabase.CursorFactory factory, int
            version) {
        super(context, name, factory, version);
        this.context = context;
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        Log.i(TAG, "onCreate");
        createDB(db);
        initDataBase(db);
    }

    private void initDataBase(SQLiteDatabase db) {
        db.execSQL(TableFeature.FEATURE_INSERT);
        db.execSQL(TableIntent.INTENT_INSERT);
        Log.d(TAG, "init database success");
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.i(TAG, "Upgrading database from version " + oldVersion
                + " to " + newVersion + ".");

        upgradeIntentAndFeatureTables(db);

    }

    @Override
    public void onDowngrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.i(TAG, "Downgrading database from version " + oldVersion
                + " to " + newVersion + ".");

        upgradeIntentAndFeatureTables(db);

    }

    private void upgradeIntentAndFeatureTables(SQLiteDatabase db) {
        db.execSQL(TableFeature.FEATURE_DELETE);
        db.execSQL(TableFeature.FEATURE_DELETE_SEQUENCE);
        db.execSQL(TableIntent.INTENT_DELETE);
        db.execSQL(TableIntent.INTENT_DELETE_SEQUENCE);

        initDataBase(db);
    }

    private void createDB(SQLiteDatabase db) {
        String featureSql = "CREATE TABLE feature(_id integer primary key autoincrement," +
                "module_feature integer UNIQUE," +
                "module_name text," +
                "module_priority integer," +
                "module_background integer" +
                ")";

        String intentSql = "CREATE TABLE intent(_id integer primary key autoincrement, " +
                "module_intent varchar(50)," +
                "module_feature integer," +
                "module_pattern text," +
                "module_pattern_type integer"
                + ")";

        db.execSQL(featureSql);
        db.execSQL(intentSql);
    }
}
