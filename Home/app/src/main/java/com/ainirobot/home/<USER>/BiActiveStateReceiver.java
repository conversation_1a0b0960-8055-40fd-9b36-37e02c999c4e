package com.ainirobot.home.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.home.bi.ActiveDurationReport;
import com.ainirobot.home.bi.ActiveStateTimeReport;
import com.ainirobot.home.bi.BiGbActiveStateReport;
import com.ainirobot.home.bi.PerformanceAnalysis;
import com.ainirobot.home.utils.LocationUtil;

import static com.ainirobot.home.bi.BiGbActiveStateReport.*;

/**
 * 埋点活跃状态，当前埋点状态：
 * 1-普通运行,2-充电
 * 3-急停,4-开机
 * 5-关机,6-日期变更
 *
 * @version V1.0.0
 * @date 2019/1/3 15:43
 */
public class BiActiveStateReceiver extends BroadcastReceiver {

    private static final String TAG = "BiActiveStateReceiver";
    /**
     * 开始充电
     */
    public static final String ACTION_START_CHARGING = "action_start_charging";
    /**
     * 停止充电
     */
    public static final String ACTION_STOP_CHARGING = "action_stop_charge";
    /**
     * 按下急停
     */
    public static final String ACTION_PRESS_EMERGENCY = "action_press_emergency";
    /**
     * 释放急停
     */
    public static final String ACTION_RELEASE_EMERGENCY = "action_release_emergency";

    private BiGbActiveStateReport biGbActiveStateReport = new BiGbActiveStateReport();
    private static volatile boolean isCheckFinish = false;

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent == null || intent.getAction() == null) {
            Log.w(TAG, "onReceive: intent:" + intent);
            return;
        }
        switch (intent.getAction()) {
            case Intent.ACTION_BOOT_COMPLETED:
                Log.i(TAG, " receive power on broadcast");
                break;
            case Intent.ACTION_SHUTDOWN:
                boolean reboot = intent.getBooleanExtra("reboot", false);
                Log.i(TAG, " receive power off broadcast " + reboot);
                if (reboot) {
                    LocationUtil.getInstance().startRecordPose();
                }

                String model = RobotSettings.getProductModel();
                if (TextUtils.equals(model, ProductInfo.ProductModel.CM_KTV_BASE.model)) {
                    LocationUtil.getInstance().startRecordPose();
                }
                //biGbActiveStateReport.addState(POWER_OFF_STATE).report();
                break;
            case Intent.ACTION_TIME_CHANGED:
                Log.i(TAG, " receive time changed broadcast ");
                checkSystemTime();
                break;
            case Intent.ACTION_DATE_CHANGED:
                Log.i(TAG, "receive date changed broadcast");
                biGbActiveStateReport.addState(DATE_CHANGE_STATE).report();
                break;
            case ACTION_START_CHARGING:
                Log.i(TAG, "receive start charge broadcast");
                biGbActiveStateReport.addState(CHARGING_STATE).report();
                LocationUtil.getInstance().setIsCharging(true);
                break;
            case ACTION_STOP_CHARGING:
                Log.i(TAG, "receive stop charge broadcast");
                biGbActiveStateReport.addState(NORMAL_STATE).report();
                LocationUtil.getInstance().setIsCharging(false);
                break;
            case ACTION_PRESS_EMERGENCY:
                Log.i(TAG, "receive press emergency broadcast");
                biGbActiveStateReport.addState(EMERGENCY_STATE).report();
                break;
            case ACTION_RELEASE_EMERGENCY:
                Log.i(TAG, " receive release emergency broadcast");
                biGbActiveStateReport.addState(NORMAL_STATE).report();
                break;
            default:
                break;
        }
    }

    private void checkSystemTime() {
        if (!isCheckFinish) {
            isCheckFinish = true;
            long powerOnTimestamp = System.currentTimeMillis() - SystemClock.elapsedRealtime();
            Log.i(TAG, "checkSystemTime: powerOnTimestamp:" + powerOnTimestamp);
            ActiveDurationReport.setChargeOff();
            beginMonitorPerformance();
            //begin time report active state
            ActiveStateTimeReport.getInstance().timeReportActiveState();
            biGbActiveStateReport.addState(POWER_ON_STATE, powerOnTimestamp).report();
        }
    }

    /**
     * begin monitor 821 performance include cpu mem and sdcard
     */
    private void beginMonitorPerformance() {
        PerformanceAnalysis.getInstance().getCpuTopFive();
        PerformanceAnalysis.getInstance().getMemTopFive();
        PerformanceAnalysis.getInstance().getSdcardUsage();
    }
}
