package com.ainirobot.home.ui.fragment;

import android.os.Bundle;
import android.support.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AlphaAnimation;
import android.view.animation.TranslateAnimation;
import android.widget.TextView;

import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.ui.view.GifView;
import com.ainirobot.home.utils.ResType;

/**
 * 导航传感器状态异常UI
 */
public class NaviSensorStateFragment extends BaseFragment {


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {

        View view = inflater.inflate(R.layout.fragment_navi_sensor, null);
        TextView reboot = view.findViewById(R.id.tv_sensor_reboot);
        reboot.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_NAVI_SENSOR_REBOOT);
            }
        });
        return view;
    }
}