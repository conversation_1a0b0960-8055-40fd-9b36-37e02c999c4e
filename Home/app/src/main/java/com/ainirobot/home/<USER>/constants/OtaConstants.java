package com.ainirobot.home.ota.constants;

import android.os.Environment;


import com.ainirobot.coreservice.client.Definition;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;

public class OtaConstants {
    public final static String TAG_PREFIX = "HomeOrionOta";

    // algorithm board install path
    public final static String HEAD_INSTALL_PATH = "/home/<USER>/OTA";

    // host version prop
    public final static String HOST_VERSION = "ro.build.description";

    // host ab update
    public final static String HOST_AB_UPDATE = "ro.build.ab_update";

    // all about database.
    // OS_name must keep the same with Config.xml in Update.zip
    // and BoardKey in CanUtils.java in CanService project.
    public final static String OS_HOST = "host";
    public final static String OS_TK1 = "tk1";
    public final static String OS_TX1 = "tx1";
    public final static String OS_MOTOR_LEFT = "motor_left";
    public final static String OS_MOTOR_RIGHT = "motor_right";
    public final static String OS_MOTOR_HORIZON = "motor_horizon";
    public final static String OS_MOTOR_VERTICAL = "motor_vertical";
    public final static String OS_PSB = "psb";
    public final static String OS_PSB_S = "psb_s";
    public final static String OS_AC_CLIENT = "ac_client";
    public final static String OS_BMS = "bms";
    public static final String AUTHORITY = "com.ainirobot.home.ota.downgradeprovider";

    public final static String PARAM_BOARD = "board";
    public final static String PARAM_NAME = "name";
    public final static String PARAM_MD5 = "md5";
    public final static String PARAM_VERSION = "version";
    public final static String PARAM_TARGET_VERSION = "targetVersion";
    public final static String PARAM_FILE_LENGTH= "filelength";

    // tx to ota
    public final static String PARAM_STATUS = "status";

    public final static String SUCCESS = "success";
    public final static String ONGOING = "ongoing";
    public final static String FAILED = "failed";

    public final static String PARAM_CODE = "code";
    public final static String PARAM_MESSAGE = "message";

    public final static String PARAM_PATHS = "path";
    public final static String PARAM_FREE_SIZE = "freeSize";

    // CoreService timeout info
    public final static String CMD_TIMEOUT = "timeout";

    // all about ota package
    public final static String OTA_DIR = "/sdcard/ota";

    public final static String TX1_OTA_DIR = "/home/<USER>/OTA";

    // can ota board_id. sync with CanCtrlManager.
    public static final int CO_NET_NODE_ID_HOST = 1;
    public static final int CO_NET_NODE_ID_PSB = 2;
    public static final int CO_NET_NODE_ID_TX1 = 3;
    public static final int CO_NET_NODE_ID_MOTORHEAD_H = 4;
    public static final int CO_NET_NODE_ID_MOTORHEAD_V = 5;
    public static final int CO_NET_NODE_ID_BMS = 6;
    public static final int CO_NET_NODE_ID_AC_CLIENT = 7;
    public static final int CO_NET_NODE_ID_TK1 = 8;
    public static final int CO_NET_NODE_ID_SENSOR_HUB = 9;
    public static final int CO_NET_NODE_ID_WHEELS_L = 10;
    public static final int CO_NET_NODE_ID_WHEELS_R = 11;
    public static final int CO_NET_NODE_ID_PSB_S = 12;

    // can ota update result code.
    /*
        public static final int OTA_UPDATEFAIL_NONE = 0;
        public static final int OTA_UPDATEFAIL_FILE_OPEN = 1;
        public static final int OTA_UPDATEFAIL_FILE_OVERWRITE = 2;
        public static final int OTA_UPDATEFAIL_CODE_OFFET = 3;
        public static final int OTA_UPDATEFAIL_WRITE_ERROR = 4;
        public static final int OTA_UPDATEFAIL_REV_BLOCK_ERROR = 5;
        public static final int OTA_UPDATEFAIL_FILE_VERFY = 6;
        public static final int OTA_UPDATEFAIL_OVERTIME = 7;
        public static final int OTA_UPDATEFAIL_ABORT = 8;
        public static final int OTA_UPDATEFAIL_UNKNOWBOARD = 9;
        public static final int OTA_UPDATE_STATUS_IDLE = 0;
        public static final int OTA_UPDATE_STATUS_REQUEST_UPDATE = 1;
        public static final int OTA_UPDATE_STATUS_PREPARE = 2;
        public static final int OTA_UPDATE_STATUS_TRANSFERING = 3;
        public static final int OTA_UPDATE_STATUS_CHECKING = 4;
    */
    public static final int OTA_UPDATE_STATUS_COMPLIETE = 5;
    public static final int OTA_UPDATE_STATUS_ABNORMAL = 6;

    private static HashMap<Integer, String> mBoardIdOsMap = new LinkedHashMap<>();
    private static HashMap<String, Integer> mOsBoardIdMap = new LinkedHashMap<>();

    static {
        mBoardIdOsMap.put(CO_NET_NODE_ID_PSB, OS_PSB);
        mBoardIdOsMap.put(CO_NET_NODE_ID_PSB_S, OS_PSB_S);
        mBoardIdOsMap.put(CO_NET_NODE_ID_MOTORHEAD_H, OS_MOTOR_HORIZON);
        mBoardIdOsMap.put(CO_NET_NODE_ID_MOTORHEAD_V, OS_MOTOR_VERTICAL);
        mBoardIdOsMap.put(CO_NET_NODE_ID_AC_CLIENT, OS_AC_CLIENT);
        mBoardIdOsMap.put(CO_NET_NODE_ID_BMS, OS_BMS);

        mOsBoardIdMap.put(OS_PSB, CO_NET_NODE_ID_PSB);
        mOsBoardIdMap.put(OS_PSB_S, CO_NET_NODE_ID_PSB_S);
        mOsBoardIdMap.put(OS_MOTOR_HORIZON, CO_NET_NODE_ID_MOTORHEAD_H);
        mOsBoardIdMap.put(OS_MOTOR_VERTICAL, CO_NET_NODE_ID_MOTORHEAD_V);
        mOsBoardIdMap.put(OS_AC_CLIENT, CO_NET_NODE_ID_AC_CLIENT);
        mOsBoardIdMap.put(OS_BMS, CO_NET_NODE_ID_BMS);
    }
    public static String getOsByBoardId(int boardId) {
        return mBoardIdOsMap.get(boardId);
    }
    public static Integer getBoardIdByOs(String osName) {
        return mOsBoardIdMap.get(osName);
    }

    public final static String[] OS_LIST = {
            OS_MOTOR_HORIZON,
            OS_MOTOR_VERTICAL,
            OS_PSB,
            OS_PSB_S,
            OS_AC_CLIENT,
            OS_BMS,
            OS_TX1,
            OS_TK1,
            OS_MOTOR_LEFT,
            OS_MOTOR_RIGHT,
            OS_HOST,
    };

    // all ota error code
    public class Error {
        public final static String NO_ERR = "1000, no err";
        public final static String ERR_UPDATE_FAILED = "-4000, update failed. need check extra";

        public final static String ERR_PACKAGE_NOT_EXIST =
                "-1000, /sdcard/ota/download/update.zip not exist";
        public final static String ERR_UNZIP_FAILED =
                "-1001, /sdcard/ota/download/update.zip unzip failed";
        public final static String ERR_MD5_CHECK_FAILED = "-1002, update.zip md5 check failed";
        public final static String ERR_FILE_DOWNLOAD_FAILED = "-1003, update.zip download failed";
        public final static String ERR_SUB_FILE_MD5_CHECK_FAILED =
                "-1004, sub file md5 check failed";

        public final static String ERR_INTERNAL_ERROR = "-5000, got internal error";
    }

    /**
     * update package download patch
     */
    public final static String OTA_DOWNLOAD_PATH = Environment.getExternalStorageDirectory()
            + File.separator + "ota/download";

    /**
     * update package unzip patch
     */
    public final static String OTA_INSTALL_PATH = Environment.getExternalStorageDirectory()
                            + File.separator + "ota/install";

    /**
     * lxc update package unzip patch
     */
    public final static String OTA_LXC_INSTALL_PATH = "/data/lxc_ota";

    /**
     * lxc install flag file path
     */
    public final static String OTA_LXC_INSTALL_FLAGFILE_PATH = "/data/lxc_ota/.flag";

    /**
     * lxc install flag file path
     */
    public final static String OTA_LXC_INSTALL_CTIMEFILE_PATH = "/data/lxc_ota/.ctime";

    /**
     * host update file patch
     */
    public final static String OTA_DEFAULT_INSTALL_FILE = "/cache/update.zip";


    /**
     * download update package
     */
    public final static String DOWNLOAD_PATH = Environment.getExternalStorageDirectory()
            + File.separator + "ota/download/update.zip";

    /**
     * lxc download update package
     */
    public final static String LXC_DOWNLOAD_PATH = Environment.getExternalStorageDirectory()
            + File.separator + "ota/lxcdownload/update.zip";

    /**
     * lxc custom download path
     */
    public final static String LXC_CUSTOM_DOWNLOAD_PATH = Environment.getExternalStorageDirectory()
            + File.separator + "ota/lxcCustomDownload/";

    /**
     * lxc install success check file
     */
    public final static String LXC_INSTALL_SUCCESS_CHECK_FILE = Environment.getExternalStorageDirectory()
            + File.separator + "ota/lxcinstallcheck";

    /**
     * backup update pacakge, download when update success
     */
    public final static String DOWNLOAD_BACKUP_PATH = Environment.getExternalStorageDirectory()
            + File.separator + "ota/download/backup.zip";
    public final static ArrayList<String> BACKUP_FILE_LIST =
            new ArrayList<String>(Arrays.asList("backup2.zip", "backup1.zip", "backup.zip" ));

    /**
     * config file in update.zip
     */
    public final static String INSTALL_CONFIG_FILE = Environment.getExternalStorageDirectory()
            + File.separator + "ota/install/config.xml";

    /**
     * for local update test
     */
    public final static boolean DEBUG = false;

    /**
     * broadcast patch install result
     */
    public final static String ACTION_INSTALL_RESULT = "ainirobot.intent.action.INSTALL_RESULT";
    public final static String INSTALL_RESULT = "result";
    public final static String ERROR_MSG = "error_msg";
    public final static String TASK_ID = "task_id";
    public final static String PACKAGE_NAME = "package_name";

    /**
     * init algorithm board
     */
    public final static String SN_PATH = Environment.getExternalStorageDirectory()
            + "/ota/sn_config";
    public final static String SN_REMOTE_PATH = "/home/<USER>/";

    public final static String STARTING_MODE_BOOT = Definition.OTA_START_BOOT;
    public final static String STARTING_MODE_POWER_ON = "ota_start_power_on";
    public final static String STARTING_MODE_FORCE = "ota_start_force";
    public final static String STARTING_MODE_SETTING_MANUAL = "ota_start_setting_manual";
    public final static String STARTING_MODE_SETTING_FORCE = "ota_start_setting_force";
    public final static String STARTING_MODE_SILENT = "ota_start_silent";
    public final static String STARTING_MODE_REMOTE = Definition.OTA_START_REMOTE;
    public final static String STARTING_MODE_SETTING = Definition.OTA_START_SETTING;
    public final static String STARTING_MODE_NIGHTTIME = Definition.OTA_START_NIGHTTIME;

    /**
     * Ota file check
     */
    public static final String ACTION_OTA_CHECK_FILE = "com.ainirobot.otaservice.ota.checkfile";
    public static final String EXTRA_CHECK_FILE_RESULT = "ota_check_file";

    /**
     * OTA timer
     */
    public static final String ACTION_OTA_CHECK_NEW_VERSION_TIMER = "com.ainirobot.otaservice.intent.action.CHECK_NEW_VERSION_TIMER";
    public static final String ACTION_OTA_SILENT_UPDATE_TIMER = "com.ainirobot.otaservice.intent.action.SILENT_UPDATE_TIMER";
    public static final String OTA_EXTRA_DOWNLOAD_TYPE = "ainirobot.ota.extra.DOWNLOAD_TYPE";

    /**
     * Download operation action.
     */
    public static final String ACTION_OTA_DOWNLOAD_FILE = "com.ainirobot.otaservice.intent.action.DOWNLOAD_FILE";
    public static final String EXTRA_DOWNLOAD_FILE_COMMAND = "download_file_command";
    public static final String DOWNLOAD_FILE_START = "start";
    public static final String DOWNLOAD_FILE_PAUSE = "pause";
    public static final String DOWNLOAD_FILE_CONTINUE = "continue";

    public static final String EXTRA_DOWNLOAD_FILE_TYPE = "download_file_type";
    public static final String DOWNLOAD_FILE_TYPE_NORMAL = "normal";
    public static final String DOWNLOAD_FILE_TYPE_SILENT = "silent";
    public static final String DOWNLOAD_FILE_TYPE_FORCE = "force";

    //Update file download status
    public static final String ACTION_OTA_DOWNLOAD_FILE_STATUS = "com.ainirobot.otaservice.intent.action.DOWNLOAD_STATUS";
    public static final String EXTRA_DOWNLOAD_FILE_STATUS = "download_status";
    //public static final String DOWNLOAD_FILE_STATUS_DOWLOADING = "downloading";
    //public static final String DOWNLOAD_FILE_TYPE_PAUSE = "pause";
    public static final String DOWNLOAD_FILE_STATUS_DONE = "done";
    /**
     * Install operation action.
     * Launched by Ota server
     */
    public static final String ACTION_OTA_INSTALL = "com.ainirobot.otaservice.intent.action.INSTALL";
    public static final String ACTION_PACKAGE_INSTALL_RESULT = "com.ainirobot.ota.action.PKG_INSTALL_RESULT";

    //ota component
    public static final String COMPONENT_ROM = "rom";
    public static final String COMPONENT_LXC = "lxc";

    public static final String OTA_PACKAGE_NAME = "com.ainirobot.ota";
    public static final String ACTION_LXC_PACKAGE_DOWNLOAD_FINISH = "com.ainirobot.moduleapp.ota.lxc.custom.download.finish";

}
