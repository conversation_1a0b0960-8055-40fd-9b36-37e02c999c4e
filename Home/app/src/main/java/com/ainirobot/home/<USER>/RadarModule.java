package com.ainirobot.home.module;

import android.os.Bundle;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.control.RadarManager;
import com.ainirobot.home.control.RadarManager.RadarListener;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.UIController.FRAGMENT_TYPE;

import org.json.JSONException;
import org.json.JSONObject;


public class RadarModule extends BaseModule {

    private static RadarModule sInstance = null;
    private final String TAG = "RadarModule";

    private enum Status {
        /**
         * 空闲状态
         */
        IDLE,
        RUNNING,
        PAUSE,
    }

    private Status mStatus = Status.IDLE;
    private String reason = "";

    public static BaseModule getInstance() {
        if (sInstance == null) {
            sInstance = new RadarModule();
        }
        return sInstance;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        switch (intent) {
            case Definition.REQ_OPEN_RADAR:
                parseReason(params);
                startOpenRadar();
                return true;

            case Definition.REQ_OPEN_RADAR_SUCCEED:
                parseReason(params);
                return true;

            default:
                return false;
        }
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
        switch (type) {
            case ModuleDef.LOCAL_MESSAGE_CHARGE_END:
                Log.d(TAG, "Radar module destroy fragment : charging end");
                UIController.getInstance().moveToBack();
                break;

            default:
                break;
        }
    }

    private void destroyFragment() {
        if (UIController.getInstance().getCurrentFragment() != FRAGMENT_TYPE.FRAGMENT_CHARGING) {
            Log.d(TAG, "Radar module destroy fragment");
            UIController.getInstance().moveToBack();
        }
    }

    private void startOpenRadar() {
        if (mStatus == Status.RUNNING) {
            return;
        }
        mStatus = Status.RUNNING;
        destroyFragment();
        RadarManager.openRadar(new RadarListener() {
            @Override
            public boolean onSucceed() {
                Log.d(TAG, "openRadar Succeed , RadarModule stop");
                stop();
                return true;
            }
        });
    }

    private Bundle getBundle() {
        switch (reason) {
            case Definition.OPEN_BY_ALARM_CHARGING:
                return createBundle(Definition.REQ_STOP_CHARGING);

            case Definition.OPEN_BY_APP_STOP_CHARGING:
                return createBundle(Definition.REQ_STOP_CHARGING_BY_APP);

            default:
                return null;
        }
    }

    private void parseReason(String params) {
        try {
            JSONObject json = new JSONObject(params);
            reason = json.getString("reason");
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private Bundle createBundle(String intent) {
        Bundle bundle = new Bundle();
        bundle.putInt(ModuleDef.MSG_BUNDLE_MODULE, ModuleDef.FEATURE_CHARGING);
        bundle.putString(ModuleDef.MSG_BUNDLE_INTENT, intent);
        bundle.putString(ModuleDef.MSG_BUNDLE_TEXT, intent);
        return bundle;
    }

    @Override
    protected void onStop() {
        Log.d(TAG, "onStop release ");
        mStatus = Status.PAUSE;
        RadarManager.cancelOpenRadar();

        Bundle bundle = getBundle();
        release(-1, RESULT_OK, bundle);
    }
}
