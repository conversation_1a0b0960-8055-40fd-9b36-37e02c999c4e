package com.ainirobot.home.ui.view;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.ainirobot.home.R;

public class RelocationSearchView extends FrameLayout {

    private static final String TAG = RelocationSearchView.class.getSimpleName();
    private Context mContext;
    private View mView;
    public ImageView mSearchImageV;
    public ImageView mSearchShadowIV;
    private ObjectAnimator mUpAnimator;
    private ObjectAnimator mDownAnimator;
    private AnimatorSet mResetAnimatorSet;
    private AnimatorSet mZoomAnimatorSet;
    private boolean mAnimateStopped = false;

    public RelocationSearchView(Context context) {
        this(context, null);
    }

    public RelocationSearchView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RelocationSearchView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public RelocationSearchView(Context context, @Nullable AttributeSet attrs, int defStyleAttr,
                                int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initSubViews(context);
    }

    private void initSubViews(Context context){
        this.mContext = context;
        mView = LayoutInflater.from(mContext).inflate(R.layout.layout_reposition_search, this);
        mSearchImageV = (ImageView) mView.findViewById(R.id.locate_search_img);
        mSearchShadowIV = (ImageView) mView.findViewById(R.id.locate_search_shadow);
    }

    public void startRepositionAnimation(){
        mAnimateStopped = false;
        upThrowAnimator();
        zoomImageViewScale();
    }

    public void stopRepositionAnimate() {
        Log.i(TAG, "stopRepositionAnimate mAnimateStoppen:" + mAnimateStopped);
        if (mAnimateStopped) {
            return;
        }
        post(new Runnable() {
            @Override
            public void run() {
                mAnimateStopped = true;
                if (mUpAnimator != null && mUpAnimator.isRunning()) {
                    mUpAnimator.end();
                    mUpAnimator = null;
                }
                if (mDownAnimator != null && mDownAnimator.isRunning()) {
                    mDownAnimator.end();
                    mDownAnimator = null;
                }
                if (mResetAnimatorSet != null && mResetAnimatorSet.isRunning()) {
                    mResetAnimatorSet.end();
                    mResetAnimatorSet = null;
                }
                if (mZoomAnimatorSet != null && mZoomAnimatorSet.isRunning()) {
                    mZoomAnimatorSet.end();
                    mZoomAnimatorSet = null;
                }
            }
        });
    }

    private void zoomImageViewScale() {
        if (mAnimateStopped) {
            return;
        }
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(mSearchShadowIV, "scaleX", 1.0f, 0.7f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(mSearchShadowIV, "scaleY", 1.0f, 0.7f);
        mZoomAnimatorSet = new AnimatorSet();
        mZoomAnimatorSet.playTogether(scaleX, scaleY);
        mZoomAnimatorSet.setDuration(500);
        mZoomAnimatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (!mAnimateStopped) {
                    resetImageViewScale();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        mZoomAnimatorSet.start();
    }

    private void resetImageViewScale() {
        if (mAnimateStopped) {
            return;
        }
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(mSearchShadowIV, "scaleX", 0.7f, 1.0f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(mSearchShadowIV, "scaleY", 0.7f, 1.0f);
        mResetAnimatorSet = new AnimatorSet();
        mResetAnimatorSet.playTogether(scaleX, scaleY);
        mResetAnimatorSet.setDuration(500);
        mResetAnimatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (!mAnimateStopped) {
                    zoomImageViewScale();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        mResetAnimatorSet.start();
    }

    private void upThrowAnimator() {
        if (mAnimateStopped) {
            return;
        }
        mUpAnimator = ObjectAnimator.ofFloat(mSearchImageV, "translationY", 0, -160);
        mUpAnimator.setDuration(500);
        mUpAnimator.setInterpolator(new DecelerateInterpolator(1.2f));
        mUpAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (!mAnimateStopped) {
                    freeFall();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        mUpAnimator.start();
    }

    private void freeFall() {
        if (mAnimateStopped) {
            return;
        }
        mDownAnimator = ObjectAnimator.ofFloat(mSearchImageV, "translationY", -160, 0);
        mDownAnimator.setDuration(500);
        mDownAnimator.setInterpolator(new AccelerateInterpolator(1.2f));
        mDownAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (mAnimateStopped == false) {
                    upThrowAnimator();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        mDownAnimator.start();
    }

}
