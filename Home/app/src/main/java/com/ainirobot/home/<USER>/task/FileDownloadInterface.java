package com.ainirobot.home.ota.task;

import com.liulishuo.filedownloader.FileDownloadListener;
import com.liulishuo.filedownloader.FileDownloader;
import com.liulishuo.filedownloader.util.FileDownloadUtils;

import java.io.File;

public class FileDownloadInterface {

    public FileDownloadInterface() {
    }

    public int startDownloadTask(String downloadURL, String saveFilePath, FileDownloadListener listener) {
        return FileDownloader.getImpl().create(downloadURL).setPath(saveFilePath).setCallbackProgressTimes(300).setMinIntervalUpdateSpeed(400).setListener(listener).start();
    }

    public void pauseFileDownload(int pauseID) {
        FileDownloader.getImpl().pause(pauseID);
    }

    public void deleteDownloadFile(String filePath) {
        new File(filePath).delete();
        new File(FileDownloadUtils.getTempPath(filePath)).delete();
    }
}
