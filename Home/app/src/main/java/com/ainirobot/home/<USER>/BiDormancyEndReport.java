package com.ainirobot.home.bi;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;


public class BiDormancyEndReport extends BaseBiReport {
    private static final String TABLE_NAME = "base_robot_dormancy_end";

    private static final String CTIME = "ctime";

    private static final String END_TYPE = "end_type";

    public static final int TYPE_INPUT = 1;
    public static final int TYPE_LOW_BATTERY = 2;
    public static final int TYPE_MULTIPLE_FUNCTION_RELEASE = 3;
    public static final int TYPE_MULTIPLE_REMOTE = 4;

    public BiDormancyEndReport() {
        super(TABLE_NAME);
    }

    public BiDormancyEndReport addEndType(int end_type) {
        addData(END_TYPE, end_type);
        return this;
    }

    @Override
    public void report() {
        addData(CTIME, System.currentTimeMillis());
        super.report();
    }

}
