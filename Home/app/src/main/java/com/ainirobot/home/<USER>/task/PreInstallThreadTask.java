package com.ainirobot.home.ota.task;

import static com.ainirobot.home.ota.bean.VersionData.STATUS.NA;
import static com.ainirobot.home.ota.bean.VersionData.STATUS.WAITING;
import static com.ainirobot.home.ota.constants.DowngradeConstants.KEY_START_COMMAND;
import static com.ainirobot.home.ota.constants.DowngradeConstants.MSG_NOT_EXIST;
import static com.ainirobot.home.ota.constants.DowngradeConstants.MSG_SUB_FILE_MD5_CHECK_FAILED;
import static com.ainirobot.home.ota.constants.DowngradeConstants.MSG_UNZIP_FAILED;
import static com.ainirobot.home.ota.constants.DowngradeConstants.MSG_UPDATE_SUCCESS;
import static com.ainirobot.home.ota.constants.OtaConstants.CMD_TIMEOUT;
import static com.ainirobot.home.ota.constants.OtaConstants.DOWNLOAD_PATH;
import static com.ainirobot.home.ota.constants.OtaConstants.HOST_VERSION;
import static com.ainirobot.home.ota.constants.OtaConstants.INSTALL_CONFIG_FILE;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_HOST;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_TK1;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_TX1;
import static com.ainirobot.home.ota.constants.OtaConstants.OTA_INSTALL_PATH;
import static com.ainirobot.home.ota.constants.OtaConstants.PARAM_BOARD;
import static com.ainirobot.home.ota.constants.OtaConstants.PARAM_VERSION;
import static com.ainirobot.home.ota.constants.OtaConstants.getBoardIdByOs;

import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.home.ota.bean.InstallData;
import com.ainirobot.home.ota.bean.VersionData;
import com.ainirobot.home.ota.config.OtaConfig;
import com.ainirobot.home.ota.constants.DowngradeConstants;
import com.ainirobot.home.ota.constants.OtaConstants;
import com.ainirobot.home.ota.database.DataBaseManager;
import com.ainirobot.home.ota.executor.ThreadPoolManager;
import com.ainirobot.home.ota.network.NetHelper;
import com.ainirobot.home.ota.parser.IParserInterface;
import com.ainirobot.home.ota.parser.PackData;
import com.ainirobot.home.ota.service.DowngradeService;
import com.ainirobot.home.ota.service.OtaApiHelper;
import com.ainirobot.home.ota.utils.IOUtils;
import com.ainirobot.home.ota.utils.MD5;
import com.ainirobot.home.ota.utils.Preferences;
import com.ainirobot.home.ota.utils.SettingsUtils;
import com.ainirobot.home.ota.utils.Utils;
import com.ainirobot.home.ota.utils.Scp;
import com.google.gson.Gson;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.List;
import java.util.Vector;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

public class PreInstallThreadTask extends AbstractOtaTask implements Runnable{
    private static final String TAG = PreInstallThreadTask.class.getSimpleName();

    private final ConcurrentMap<String, String> mOsVersionMap = new ConcurrentHashMap<>();

    public PreInstallThreadTask(Context context, Preferences preferences, NetHelper netHelper,
        OtaApiHelper otaApiHelper, DataBaseManager dataBaseManager, Scp scpHelper,
        Handler mainHandler, IParserInterface iParserInterface, OtaTaskFactory otaTaskFactory,
        OtaConfig otaConfig, ThreadPoolManager threadPoolManager) {
        super(context, preferences, netHelper, otaApiHelper, dataBaseManager, scpHelper, mainHandler, iParserInterface, otaTaskFactory, otaConfig, threadPoolManager);
    }

    private void initOsInstallData() {
        mPreference.setOtaIsUpgrading(true);
        mPreference.setBaseVersion(Utils.getSystemProperties("ro.product.releasenum", ""));
        setOtaUpdateFailedReason("", null);
        clearInstallFile();
        clearInstallTaskQueue();
        mInstallErrReason = null;
        mOsVersionMap.clear();
        SettingsUtils.setSettingsGlobalOtaInstallingOs(mContext, "");

        List<VersionData> list = dataBaseManager.getAllVersion();
        Log.d(TAG, "VersionData list1: " + new Gson().toJson(list));
        for (VersionData data : list) {
            if (mPreference.getOsCheckConfig(data.getName())) {
                data.setStatus(WAITING);
            } else {
                data.setStatus(NA);
            }
            data.setTargetVersion(null);
            data.setCurrentVersion(null);
            dataBaseManager.update(data);
        }
        Log.d(TAG, "VersionData list2: " + new Gson().toJson(list));
    }

    @Override
    public void run() {
        // 1. 初始化os 安装数据
        initOsInstallData();

        // 2. 检查即将要安装的文件位置
        String mFilePath = DOWNLOAD_PATH;

        if (mFilePath.isEmpty() || !new File(mFilePath).exists()) {
            Log.e(TAG, "install file find error. ");
            mainHandler.sendEmptyMessage(MSG_NOT_EXIST);
            installThreadRunning = false;
            return;
        }

        // 3. 解压 update.zip文件
        Log.d(TAG, "start unzip update.zip file from " + mFilePath + " to " + OTA_INSTALL_PATH);
        File file = new File(mFilePath);
        if (!file.exists() || !Utils.unZip(file, null, OTA_INSTALL_PATH)) {
            mainHandler.sendEmptyMessage(MSG_UNZIP_FAILED);
            installThreadRunning = false;
            return;
        }

        // 4. 解析config.xml文件
        Vector<PackData> installOsDataVector = parseInstallConfigFileInternal(INSTALL_CONFIG_FILE);

        if (installOsDataVector == null || installOsDataVector.isEmpty()) {
            Log.e(TAG, "install file failed, because config.xml parse error. ");
            mainHandler.sendEmptyMessage(MSG_NOT_EXIST);
            installThreadRunning = false;
            return;
        }

        // 5. md5 check and update the status of OS needed upgrade to WAITING.
        if (!checkMD5AndSetOSUpgradeStatus(installOsDataVector)) {
            Log.e(TAG, "MD5 check error. ");
            mainHandler.sendEmptyMessage(MSG_SUB_FILE_MD5_CHECK_FAILED);
            clearDownloadFile();
            installThreadRunning = false;
            return;
        }

        // 6. 安装 ANDROID 和 其他, 將需要安裝的加入安裝隊列
        VersionData data;
        for (InstallData osN : headOsArray) {
            data = dataBaseManager.getOsData(osN);
            if (checkIfWaitForInstalling(data)) {
                Log.d(TAG, "put installTask " + data.getName());
                installTaskQueue.add(osN);
            }
        }

        for (InstallData osN : navOsArray) {
            data = dataBaseManager.getOsData(osN);
            if (checkIfWaitForInstalling(data)) {
                Log.d(TAG, "put installTask " + data.getName());
                installTaskQueue.add(osN);
            }
        }

        for (InstallData osN : canOsArray) {
            data = dataBaseManager.getOsData(osN);
            if (checkIfWaitForInstalling(data)) {
                Log.d(TAG, "put installTask " + data.getName());
                installTaskQueue.add(osN);
            }
        }

        data = dataBaseManager.getOsData(OS_HOST);
        if (checkIfWaitForInstalling(data)) {
            Log.d(TAG, "put installTask " + data.getName());
            installTaskQueue.add(new InstallData(OS_HOST));
        }

        if (installTaskQueue.isEmpty()) {
            installThreadRunning = false;
            Log.d(TAG, "No need upgrade");
            mainHandler.sendEmptyMessage(MSG_UPDATE_SUCCESS);
        } else {
            try {
                Log.d(TAG, "start install thread # " + Thread.currentThread().getName());
                if (mThreadPoolManager.getCachedThreadPool().isShutdown() || mThreadPoolManager.getCachedThreadPool().isTerminated()) {
                    Log.d(TAG, "重新启动线程池");
                    mThreadPoolManager = ThreadPoolManager.getInstance(); // 必要时重新初始化
                }
                if (mOtaTaskFactory != null && mContext != null) {
                    InstallThreadTask installThreadTask = mOtaTaskFactory.createInstallThreadTask();
                    // 开启安装任务
                    mThreadPoolManager.submitCachedTask(installThreadTask);
                } else {
                    Log.d(TAG, "mContext: " + String.valueOf(mContext != null));
                    if (mContext != null) {
                        Log.d(TAG, "进入安装流程");
                        Intent mIntent = new Intent(mContext, DowngradeService.class);
                        mIntent.putExtra(KEY_START_COMMAND, DowngradeConstants.START_COMMAND_REAL_INSTALL_ROM);
                        mContext.startService(mIntent);
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "发生异常中断：" + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    private Vector<PackData> parseInstallConfigFileInternal(String name) {
        if (name == null) {
            return null;
        }

        File configFile = new File(name);
        if (!configFile.exists()) {
            return null;
        }

        FileInputStream fin = null;
        try {
            fin = new FileInputStream(configFile);
            return mUpdateConfigParser.parse(fin);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } finally {
            IOUtils.close(fin);
        }

        return null;
    }

    /**
     * Check current os version, if not matched with the target-version
     * in install package, add into install list.
     */
    private boolean checkMD5AndSetOSUpgradeStatus(Vector<PackData> dataVector) {
        try {
            Vector<PackData> installDataVector = getNeedInstallData(dataVector);
            String currentVersion;
            String targetVersion;

            //Update the status of database if OS is in config.xml.
            for (PackData data : installDataVector) {
                currentVersion = mOsVersionMap.get(data.getOsName());
                targetVersion = data.getTargetVersion();

                //Compatibility with target version..
                if (OS_TX1.equals(data.getOsName())) {
                    currentVersion = handleCurrentVersionAccordingToTargetVersion(targetVersion, currentVersion);
                }

                Log.d(TAG, "checking os:" + data.getOsName()
                        + " currentVersion:" + currentVersion
                        + " targetVersion:" + data.getTargetVersion());

                if (targetVersion == null || targetVersion.isEmpty() ||
                        targetVersion.equals(currentVersion)) {
                    updateOtaUpdateStatus(data.getOsName(), NA, null, null);
                    continue;
                }

                String updateFilePath = OTA_INSTALL_PATH + File.separator + data.getUpdateFileName();
                if (!MD5.checkMd5(data.getUpdateFileMd5(), updateFilePath)) {
                    Log.e(TAG, "file:" + updateFilePath + " md5 fail!");
                    Log.d(TAG,data.getOsName() + " md5 校验失败。");
                    return false;
                }

                Log.d(TAG,data.getOsName() + "需要升级");
                mPreference.setOsPackagePath(data.getOsName(), updateFilePath);
                mPreference.setOsPackageMD5(data.getOsMd5Name(), data.getUpdateFileMd5());

                updateOtaUpdateStatus(data.getOsName(), WAITING, currentVersion, targetVersion);
            }
        } catch (Exception e) {
            Log.d(TAG, "checkMD5AndSetOSUpgradeStatus exception: " + e.getMessage());
            e.printStackTrace();
            return false;
        }

        return true;
    }

    //Get the need install OS according to local settings/default set/config.xml
    private Vector<PackData> getNeedInstallData(Vector<PackData> dataVector) {
        Vector<PackData> installData = new Vector<>();
        List<VersionData> list = dataBaseManager.getAllVersion();
        boolean needInstall;

        for (VersionData vData : list) {
            needInstall = false;

            for (PackData pData : dataVector) {
                if (pData.getOsName().equals(vData.getName()) &&
                        needOsInstall(vData.getName())) {
                    needInstall = true;
                    installData.add(pData);
                    updateCurrentOsVersion(pData.getOsName());
                    break;
                }
            }

            if (!needInstall) {
                mOsVersionMap.remove(vData.getName());
                updateOtaUpdateStatus(vData.getName(), NA, null, null);
            }
        }

        return installData;
    }

    private String handleCurrentVersionAccordingToTargetVersion(String targetVersion, String currentVersion) {
        if (targetVersion == null || currentVersion == null) return currentVersion;

        String[] tx1TargetVer = targetVersion.split("v");
        String cVersion = currentVersion;
        if (tx1TargetVer.length <= 1) {
            String[] tx1CurrentVer = cVersion.split("v");
            cVersion = tx1CurrentVer[tx1CurrentVer.length > 1 ? 1 : 0];
        }

        return cVersion;
    }

    /**
     * update current os version
     */
    private boolean updateCurrentOsVersion(String osName) {
        int boardId;
        String result;
        boolean ret = false;

        String currentVersion = mOsVersionMap.get(osName);
        if (currentVersion != null && !currentVersion.isEmpty()) {
            return true;
        }

        Log.d(TAG, "updateCurrentOsVersion osName: " + osName);
        if (canOsArray.contains(new InstallData(osName))) {
            boardId = getBoardIdByOs(osName);
            result = getCanBoardVersionInternal(boardId);
            Log.d(TAG, "parse " + osName + " version result=" + result);
            if (result != null && !result.isEmpty() && !result.contains(CMD_TIMEOUT)) {
                ret = parseCanVersionResult(osName, result);
            }
        } else if (headOsArray.contains(new InstallData(osName))) {
            result = mOtaApiHelper.getHeadVersion(20 * 1000);
            Log.d(TAG, "parse head version retult=" + result);
            if (result != null && !result.isEmpty() && !result.contains(CMD_TIMEOUT)) {
                ret = parseTx1VersionResult(result);
            }
        } else if (navOsArray.contains(new InstallData(osName))) {
            // update navigation/motor_left/motor_right version
            result = mOtaApiHelper.getNavigationVersion(20 * 1000);
            Log.d(TAG, "parse navi version result=" + result);
            if (result != null && !result.isEmpty() && !result.contains(CMD_TIMEOUT)) {
                ret = parseTk1VersionResult(result);
            }
        } else if (OS_HOST.equals(osName)) {
            // update host version
            result = Utils.getSystemProperties(HOST_VERSION, "1.0.0");
            Log.d(TAG, "versionResult. os type:" + OS_HOST + " version:" + result);
            mOsVersionMap.put(OS_HOST, result);
            ret = true;
        }

        return ret;
    }

    private boolean parseTx1VersionResult(String result) {
        boolean ret = true;
        String type;
        String version;
        JSONObject jsonObject;
        try {
            JSONArray params = new JSONArray(result);

            for (int i = 0; i < params.length(); i++) {
                jsonObject = (JSONObject) params.get(i);
                if (jsonObject != null) {
                    type = (String) jsonObject.get(PARAM_BOARD);
                    version = (String) jsonObject.get(PARAM_VERSION);

                    Log.d(TAG, "versionResult. os type:" + type + " version:" + version);
                    mOsVersionMap.put(type, version);
                }
            }

        } catch (JSONException | NullPointerException e) {
            try {
                jsonObject = new JSONObject(result);
                type = jsonObject.getString(PARAM_BOARD);
                version = jsonObject.getString(PARAM_VERSION);

                Log.d(TAG, "versionResult. os type:" + type + " version:" + version);
                mOsVersionMap.put(type, version);

            } catch (JSONException | NullPointerException f) {
                f.printStackTrace();
                ret = false;
            }
        }
        return ret;
    }

    private boolean parseTk1VersionResult(String result) {
        boolean ret = true;
        try {
            JSONArray params = new JSONArray(result);
            params.length();

            JSONObject jsonObject;
            String type;
            String version;

            for (int i = 0; i < params.length(); i++) {
                jsonObject = (JSONObject) params.get(i);
                if (jsonObject != null) {
                    type = (String) jsonObject.get(PARAM_BOARD);
                    version = (String) jsonObject.get(PARAM_VERSION);

                    // handle motor_left/motor_right version
                    if (navOsArray.contains(new InstallData(type)) && !OS_TK1.equals(type)) {
                        String[] navVer = version.split("v");
                        // handle motor_left/right version is "",
                        // in this case motor update is failed
                        version = navVer[navVer.length > 1 ? 1 : 0];
                    }

                    Log.d(TAG, "versionResult. os type:" + type + " version:" + version);
                    mOsVersionMap.put(type, version);
                }
            }

        } catch (JSONException | NullPointerException e) {
            e.printStackTrace();
            ret = false;
        }
        return ret;
    }

    private boolean parseCanVersionResult(String osName, String result) {
        boolean ret = true;
        try {
            JSONObject jsonObject = new JSONObject(result);

            String version = jsonObject.getString(Definition.JSON_CAN_BOARD_APP_VERSION);

            // handle motor_vertical/motor_horizon/psb/tx1 version
            if (canOsArray.contains(new InstallData(osName))) {
                String[] ver = version.split("v");
                version = ver[1];
            }
            Log.d(TAG, "versionResult. os type:" + osName + " version:" + version);
            mOsVersionMap.put(osName, version);

        } catch (JSONException e) {
            e.printStackTrace();
            ret = false;
        }

        return ret;
    }
}
