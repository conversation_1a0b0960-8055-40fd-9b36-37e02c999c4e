/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.home.bean;

public class SpeechResult {
    private String intent;
    private String slots;
    private String userText;
    private String answerText;

    public SpeechResult(String intent, String slots, String userText, String answerText) {
        this.intent = intent;
        this.slots = slots;
        this.userText = userText;
        this.answerText = answerText;
    }

    public String getIntent() {
        return intent;
    }

    @Override
    public String toString() {
        return "SpeechResult{" +
                "intent='" + intent + '\'' +
                ", slots='" + slots + '\'' +
                ", userText='" + userText + '\'' +
                ", answerText='" + answerText + '\'' +
                '}';
    }
}
