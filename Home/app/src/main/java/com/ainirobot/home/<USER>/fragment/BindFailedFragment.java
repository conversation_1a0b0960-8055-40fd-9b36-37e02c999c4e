package com.ainirobot.home.ui.fragment;

import android.annotation.TargetApi;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Build;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.ui.view.InputPasswordDialog;
import com.ainirobot.home.utils.Blur;

public class BindFailedFragment extends BaseFragment {
    private static final String TAG = "BindFailedFragment:Home";
    private static final String ACTION_FACTORY_RESET = "android.intent.action.FACTORY_RESET";
    private static final String EXTRA_REASON = "android.intent.extra.REASON";
    private static final String EXTRA_WIPE_EXTERNAL_STORAGE = "android.intent.extra.WIPE_EXTERNAL_STORAGE";
    private static final String EXTRA_WIPE_ESIMS = "com.android.internal.intent.extra.WIPE_ESIMS";
    private static final int CONSTANT_BLUR_RADIUS = 15;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        Log.i(TAG, "on create view");
        View view = inflater.inflate(R.layout.fragment_bind_failed, null);

        view.findViewById(R.id.tv_to_bind).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toBind();
            }
        });

        view.findViewById(R.id.tv_to_restore).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showDialogInputPassWord();
            }
        });
        return view;
    }

    @Override
    public void onStart() {
        super.onStart();
        Log.i(TAG, "onstart");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.d(TAG, "onDestroyView");
    }

    private void toBind() {
        Log.i(TAG, "to bind");
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_TO_BIND);
    }

    private void toRestore() {
        Log.i(TAG, "to restore");

        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.N_MR1) {
            getActivity().sendBroadcast(new Intent(
                    "android.intent.action.MASTER_CLEAR")
                    .addFlags(Intent.FLAG_RECEIVER_FOREGROUND));
        } else {
            final Intent intent = new Intent(ACTION_FACTORY_RESET);
            intent.setPackage("android");
            intent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND);
            intent.putExtra(EXTRA_REASON, getActivity().getPackageName() + " Master Clear");
            intent.putExtra(EXTRA_WIPE_EXTERNAL_STORAGE, true);
            intent.putExtra(EXTRA_WIPE_ESIMS, true);
            getActivity().sendBroadcast(intent);
        }
    }

    private void showDialogInputPassWord() {
        View decorView = getActivity().getWindow().getDecorView();
        decorView.setDrawingCacheQuality(View.DRAWING_CACHE_QUALITY_LOW);
        decorView.setDrawingCacheEnabled(true);
        decorView.buildDrawingCache();
        Bitmap image = decorView.getDrawingCache();
        Bitmap bitmap = Blur.apply(getActivity(), image, CONSTANT_BLUR_RADIUS);
        InputPasswordDialog inputPassword = (new InputPasswordDialog(getActivity(),
                android.R.style.Theme_DeviceDefault_NoActionBar_Fullscreen, bitmap));
        inputPassword.setCallback(new InputPasswordDialog.DialogCallback() {
            @Override
            public void confirm() {
                toRestore();
            }
        });
        inputPassword.show();
    }
}
