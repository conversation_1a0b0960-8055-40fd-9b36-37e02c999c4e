package com.ainirobot.home.bi;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

/**
 * @version V1.0.0
 * @date 2019/4/9 17:52
 */
public class BiGoChargingPointReport extends BaseBiReport {
    private static final String TABLE_NAME = "gb_recharge_point";

    private static final String CTIME = "ctime";

    private static final String RESULT = "result";

    public BiGoChargingPointReport() {
        super(TABLE_NAME);
    }

    public BiGoChargingPointReport addResult(Object result) {
        addData(RESULT, result);
        return this;
    }

    @Override
    public void report() {
        addData(CTIME, System.currentTimeMillis());
        super.report();
    }

}
