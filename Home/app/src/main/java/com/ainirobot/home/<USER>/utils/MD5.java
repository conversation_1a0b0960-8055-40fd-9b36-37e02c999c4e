package com.ainirobot.home.ota.utils;

import android.util.Log;

import com.ainirobot.home.ota.constants.OtaConstants;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class MD5 {
    private static final String TAG = OtaConstants.TAG_PREFIX + MD5.class.getSimpleName();

    public static String createSHA1(String str) {
        String result = null;
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("SHA-1");
            messageDigest.update(str.getBytes());
            byte[] data = messageDigest.digest();
            result = toHex(data);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } finally {
            return result;
        }
    }

    public static String createMd5(String str) {
        String result = null;
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.update(str.getBytes());
            byte[] by = messageDigest.digest();
            result = toHex(by);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } finally {
            return result;
        }
    }

    private static String createMd5FromFile(String str) {
        MessageDigest mMDigest;
        FileInputStream Input;
        File file = new File(str);
        byte buffer[] = new byte[1024];
        int len;
        if (!file.exists())
            return null;
        try {
            mMDigest = MessageDigest.getInstance("MD5");
            Input = new FileInputStream(file);
            while ((len = Input.read(buffer, 0, 1024)) != -1) {
                mMDigest.update(buffer, 0, len);
            }
            Input.close();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            return null;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
        byte[] digest = mMDigest.digest();
        String result = toHex(digest);

        return result;
    }

    /**
     * 将16位byte[] 转换为32位String; byte && 0xFF 转为hex
     *
     * @param buffer
     * @return
     */
    private static String toHex(byte buffer[]) {
        StringBuffer sb = new StringBuffer(buffer.length * 2);
        for (int i = 0; i < buffer.length; i++) {
            sb.append(Character.forDigit((buffer[i] & 0xF0) >> 4, 16));
            sb.append(Character.forDigit(buffer[i] & 0x0F, 16));
        }
        return sb.toString();
    }


    public static boolean checkMd5(String Md5, String file) {
        String str = createMd5FromFile(file);
        Log.d(TAG, "Md5 to check = " + Md5 + " file md5sum = " + str);
        return str != null && Md5 != null && (Md5.compareTo(str) == 0
                || Md5.toLowerCase().compareTo(str) == 0);
    }

    public static String getFileMd5(String file) {
        String str = createMd5FromFile(file);
        Log.d(TAG, file + " md5 = " + str);
        return str;
    }
}
