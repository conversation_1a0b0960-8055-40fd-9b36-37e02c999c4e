package com.ainirobot.home.ota.utils;

import android.annotation.TargetApi;
import android.os.Build;
import android.os.Environment;
import android.os.StatFs;
import android.text.TextUtils;

import com.ainirobot.home.ota.config.OtaConfig;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.Properties;

public class SystemUtils {
    private static final String CONFIG_FILE = "/robot/config/remote.properties";

    public static String getDomain() {
        String domain = getProperty("domain");
        return TextUtils.isEmpty(domain) ? OtaConfig.getInstance().getServerDomain() : domain;
    }


    private static String getProperty(String key, String path) {
        if (TextUtils.isEmpty(key)) {
            return null;
        }

        File root = Environment.getExternalStorageDirectory();
        File file = new File(root, path);
        Properties props = loadProperty(file.getPath());
        if (props != null) {
            return props.getProperty(key);
        }
        return null;
    }

    private static String getProperty(String key) {
        return getProperty(key, CONFIG_FILE);
    }

    private static boolean setProperty(String key, String value, String path) {
        if (TextUtils.isEmpty(key)) {
            return false;
        }

        File root = Environment.getExternalStorageDirectory();
        File file = new File(root, path);
        Properties props = loadProperty(file.getPath());
        if (props != null) {
            props.put(key, value);
            return saveProperty(file.getPath(), props);
        }
        return false;
    }

    private boolean setProperty(String key, String value) {
        return setProperty(key, value, CONFIG_FILE);
    }

    private static Properties loadProperty(String path) {
        FileInputStream in = null;

        File file = new File(path);
        if (!file.exists()) {
            return null;
        }

        Properties properties = new Properties();
        try {
            in = new FileInputStream(path);
            properties.load(in);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            IOUtils.close(in);
        }
        return properties;
    }

    private static boolean saveProperty(String path, Properties properties) {
        FileOutputStream out = null;
        try {
            File file = new File(path);
            if (!file.exists()) {
                file.createNewFile();
            }
            out = new FileOutputStream(file);
            properties.store(out, null);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            IOUtils.close(out);
        }
        return true;
    }

    /**
     * 判断SD卡是否可用
     *
     * @return true : 可用<br false : 不可用
     */
    public static boolean isSDCardEnable() {
        return Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState());
    }

    /**
     * 获取手机外部总空间大小
     *
     * @return 总大小，字节为单位
     */
    public static String getTotalExternalSpaceSize() {
        if (isSDCardEnable()) {
            //获取SDCard根目录
            File path = Environment.getExternalStorageDirectory();
            StatFs stat = new StatFs(path.getPath());
            long blockSize = stat.getBlockSizeLong();
            long totalBlocks = stat.getBlockCountLong();
            long size = totalBlocks * blockSize / 1024L / 1024L;
            String sizeMB = size + "MB";
            return sizeMB;
        } else {
            return "sdcard unable!";
        }
    }

    /**
     * 获取SD卡剩余空间
     *
     * @return SD卡剩余空间
     */
    @TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR2)
    public static String getTotalExternalAvailableSize() {
        if (!isSDCardEnable()) return "sdcard unable!";
        StatFs stat = new StatFs(Environment.getExternalStorageDirectory().getPath());
        long blockSize, availableBlocks;
        availableBlocks = stat.getAvailableBlocksLong();
        blockSize = stat.getBlockSizeLong();
        long size = availableBlocks * blockSize / 1024L / 1024L;
        String sizeMB = size + "MB";
        return sizeMB;
    }
}
