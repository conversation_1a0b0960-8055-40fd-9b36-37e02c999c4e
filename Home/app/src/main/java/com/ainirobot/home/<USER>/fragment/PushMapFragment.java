package com.ainirobot.home.ui.fragment;

import android.os.Bundle;
import android.support.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.R;

public class PushMapFragment extends BaseFragment {

    private static final String TAG = "PushMapFragment:Home";
    private TextView mTitleTV;
    private TextView mSubtitleTV;
    private int mOrientation;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        Log.i(TAG, "onCreateView");
        mOrientation = ApplicationWrapper.getContext().getResources().getConfiguration().orientation;

        View view = inflater.inflate(R.layout.fragment_push_map, null);
        mTitleTV = (TextView) view.findViewById(R.id.push_map_title_tv);
        mSubtitleTV = (TextView) view.findViewById(R.id.push_map_subtitle_tv);

        return view;
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
    }
}