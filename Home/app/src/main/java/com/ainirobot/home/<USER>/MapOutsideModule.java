package com.ainirobot.home.module;

import android.content.Context;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.home.bi.BiOutsideWarningReport;
import com.ainirobot.home.ui.UIController;

public class MapOutsideModule extends BaseModule {
    private static final String TAG = "MapOutsideModule:Home";

    private static MapOutsideModule sInstance = null;

    private Context mContext;

    private int mReqId;

    public static MapOutsideModule getInstance() {
        if (sInstance == null) {
            sInstance = new MapOutsideModule();
        }
        return sInstance;
    }

    private MapOutsideModule() {
    }

    public void init(Context context) {
        mContext = context;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.d(TAG, "onNewSemantics text = " + text + ", params = " + params + ", intent = " + intent);
        this.mReqId = reqId;
        switch (intent) {
            case Definition.REQ_MAP_OUTSIDE:
                new BiOutsideWarningReport().addType(BiOutsideWarningReport.ENTER).report();
                UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_MAP_OUTSIDE, null, null);
                break;
            case Definition.REQ_MAP_OUSIDE_RELEASE:
                new BiOutsideWarningReport().addType(BiOutsideWarningReport.EXIT).report();
                stop();
                break;
        }
        return true;
    }

    @Override
    public void onMessageFromLocal(int type) {
    }

    @Override
    protected void onStop() {
        super.onStop();
    }
}
