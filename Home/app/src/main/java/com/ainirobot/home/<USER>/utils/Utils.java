package com.ainirobot.home.ota.utils;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.util.Log;


import com.ainirobot.home.ota.constants.OtaConstants;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.lang.reflect.Method;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Random;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;

public class Utils {
    private final static String TAG = OtaConstants.TAG_PREFIX  + Utils.class.getSimpleName();

    public final static boolean DEBUG = true;

    public static boolean OTA_DEBUG = true;

    /*小雅OTA*/
    /*正式服务器*/
    private final static String HOST_RELEASE = "http://api.update.ainirobot.com";
    /*升级接口*/
    private final static String UPDATE_INTER = "/check/updateRom";
    /*异常上报接口*/
    private final static String UPDATE_ERR_INTER = "/update/romError";

    /**
     * get update url
     */
    public static String getUpdateUrl() {
        return HOST_RELEASE + UPDATE_INTER;
    }


    /**
     * get random [start, end)
     */
    public static int getRandomInt(int start, int end) {
        Random rd = new Random();
        return rd.nextInt(end - start) + start;
    }

    /**
     * bytes read
     *
     * @param in
     * @param skip
     * @param size
     * @return
     * @throws IOException
     */
    public static byte[] readBytes(InputStream in, long skip, int size) throws IOException {
        byte[] result = null;
        if (skip > 0) {
            long skipped = 0;
            while (skip > 0 && (skipped = in.skip(skip)) > 0) {
                skip -= skipped;
            }
        }
        result = new byte[size];
        for (int i = 0; i < size; i++) {
            result[i] = (byte) in.read();
        }
        return result;
    }

    public static String readInputStream(InputStream is) {
        byte[] result;
        ByteArrayOutputStream bOut = null;
        try {
            bOut = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024 * 4];
            int len;
            while ((len = is.read(buffer)) != -1) {
                bOut.write(buffer, 0, len);
            }
            result = bOut.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        } finally {
            try {
                if (is != null)
                    is.close();
                if (bOut != null)
                    bOut.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return new String(result);
    }

    /**
     * execute shell command
     *
     * @param cmd
     * @return
     */
    public static String[] execCmd(String cmd) {
        String[] ret = new String[2];
        StringBuilder retBuilder = new StringBuilder();
        StringBuilder errBuilder = new StringBuilder();

        String[] cmdString = new String[]{"sh", "-c", cmd};
        BufferedReader stdin = null;
        BufferedReader stderr = null;

        try {
            Process process = Runtime.getRuntime().exec(cmdString);
            stdin = new BufferedReader(new InputStreamReader(process.getInputStream()), 7777);
            stderr = new BufferedReader(new InputStreamReader(process.getErrorStream()), 7777);

            String line = null;
            while ((line = stdin.readLine()) != null) {
                retBuilder.append(line).append("\n");
            }

            String errLine = null;
            while ((errLine = stderr.readLine()) != null) {
                errBuilder.append(errLine).append("\n");
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            IOUtils.close(stdin);
            IOUtils.close(stderr);
        }
        ret[0] = retBuilder.toString();
        ret[1] = errBuilder.toString();

        Log.d(TAG, "execCmd result=" + retBuilder + "\nerr=" + errBuilder);
        return ret;
    }

    /**
     * call SystemProperties.get
     *
     * @param key
     * @param defaultVal
     * @return
     */
    public static String getSystemProperties(String key, String defaultVal) {
        try {
            Method systemProperties_get =
                    Class.forName("android.os.SystemProperties").getMethod("get"
                            , String.class, String.class);

            String ret = (String) systemProperties_get.invoke(null, key, defaultVal);

            if (ret != null)
                return ret;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return defaultVal;
    }

    /**
     * connectivity check
     *
     * @param context
     * @return
     */
    public static boolean checkConnectivity(Context context) {
        ConnectivityManager cm = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo info = cm.getActiveNetworkInfo();
        return info != null && info.isConnected();
    }

    /**
     * wifi connectivity check
     *
     * @param context
     * @return
     */
    public static boolean checkWifiConnected(Context context) {
        ConnectivityManager cm = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo info = cm.getActiveNetworkInfo();
        if (info != null) {
            int workType = info.getType();
            if (workType == ConnectivityManager.TYPE_WIFI
                    || workType == ConnectivityManager.TYPE_ETHERNET) {
                return info.isConnected();
            }
        }
        return false;
    }

    /**
     * unzip file with ZipFile
     *
     * @param file
     * @param outputFolder
     * @return
     */
    public static boolean unZip(File file, String outputFolder) {
        if (!file.exists()) {
            Log.e(TAG, "source file not exist");
            return false;
        }

        File folder = new File(outputFolder);
        if (!folder.exists() && !folder.mkdirs()) {
            Log.e(TAG, "fail to create dest folder");
            return false;
        }

        boolean ret = false;
        InputStream in = null;
        OutputStream out = null;
        ZipFile zipFile = null;
        try {
            zipFile = new ZipFile(file);
            Enumeration emu = zipFile.entries();
            while (emu.hasMoreElements()) {
                ZipEntry entry = (ZipEntry) emu.nextElement();

                if (entry.isDirectory()) {
                    new File(outputFolder + File.separator + entry.getName()).mkdirs();
                    continue;
                }
                File fileDest = new File(outputFolder + File.separator + entry.getName());
                if (fileDest.exists()) {
                    Log.d(TAG, "unzip. delete exist file:" + fileDest.getPath());
                    fileDest.delete();
                    fileDest.createNewFile();
                }

                File parent = fileDest.getParentFile();
                if (parent != null && (!parent.exists())) {
                    parent.mkdirs();
                }

                in = zipFile.getInputStream(entry);
                out = new FileOutputStream(fileDest);

                ret = copyFile(in, out);

                IOUtils.close(in);
                IOUtils.close(out);
            }
        } catch (IOException e) {
            e.printStackTrace();
            ret = false;
        } finally {
            IOUtils.close(zipFile);
            IOUtils.close(in);
            IOUtils.close(out);
        }
        return ret;
    }

    /**
     * unzip file with zipInputStream
     *
     * @param zipFile zip file
     * @param fileName UnZip this file from zipFile to outFolder. If null, zip all file from zipFile.
     * @param outFolder
     * @return
     */
    public static boolean unZip(File zipFile, String fileName, String outFolder) {
        boolean ret = true;

        ZipInputStream fin = null;
        ZipEntry entry;

        String destFileName;
        File destFile = null;
        File destDir = null;

        long fileSize = -1;
        int count;
        int bufferSize = 4 * 1024;
        byte[] buffer = null;
        BufferedOutputStream fout = null;
        try {
            fin = new ZipInputStream(new FileInputStream(zipFile));
            while ((entry = fin.getNextEntry()) != null) {
                fileSize = entry.getSize();
                destFileName = entry.getName();

                if (fileName != null && !fileName.isEmpty() && !fileName.equals(destFileName)) {
                    continue;
                }

                Log.d(TAG, "unzip =" + entry +
                        " fileLength=" + fileSize +
                        " fileName=" + destFileName +
                        " dest=" + (outFolder + "/" + destFileName));
                Log.d(TAG, "unzipping :" + SystemUtils.getTotalExternalAvailableSize());

                destFile = new File(outFolder + "/" + destFileName);
                destDir = new File(destFile.getParent());
                if (!destDir.exists()) {
                    destDir.mkdirs();
                }
                destFile.delete();
                destFile.createNewFile();

                buffer = new byte[bufferSize];
                fout = new BufferedOutputStream(new FileOutputStream(destFile));
                while ((count = fin.read(buffer)) != -1) {
                    fout.write(buffer, 0, count);
                }
                fout.flush();
            }
        } catch (Exception e) {
            e.printStackTrace();
            ret = false;
        } finally {
            IOUtils.close(fin);
            IOUtils.close(fout);
        }
        return ret;
    }

    public static boolean renameFile(String src, String dst) {
        boolean result = false;

        File srcFile = new File(src);
        File dstFile = new File(dst);
        InputStream in = null;
        OutputStream out = null;
        try {
            in = new FileInputStream(srcFile);
            out = new FileOutputStream(dstFile);
            if (Utils.copyFile(in, out)) {
                String updateMd5 = MD5.getFileMd5(srcFile.getCanonicalPath());
                if (!MD5.checkMd5(updateMd5, dstFile.getCanonicalPath())) {
                    Log.e(TAG, "md5 check failed! rename fail. delete dst: " + dst);
                    dstFile.deleteOnExit();
                } else {
                    Log.e(TAG, "rename success. delete src: " + src);
                    srcFile.deleteOnExit();
                    result = true;
                }
            } else {
                Log.e(TAG, "file dst rename failed.");
            }
        } catch (IOException e) {
            result = false;
            e.printStackTrace();
        } finally {
            IOUtils.close(in);
            IOUtils.close(out);
        }

        return result;
    }

    public static boolean copyFile(InputStream src, OutputStream dst) {
        BufferedInputStream in = null;
        BufferedOutputStream out = null;
        try {
            in = new BufferedInputStream(src);
            out = new BufferedOutputStream(dst);
            int length = -1;
            byte[] buf = new byte[1024];
            while ((length = in.read(buf)) != -1) {
                out.write(buf, 0, length);
            }
            out.flush();
            in.close();
            out.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            return false;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }

        return true;
    }

    public static boolean writeToFile(byte[] data, File file) {
        boolean ret = false;
        FileOutputStream fOut = null;
        try {
            if (!file.exists()) {
                file.createNewFile();
            }

            fOut = new FileOutputStream(file);
            fOut.write(data);
            fOut.flush();
            ret = true;
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            IOUtils.close(fOut);
        }
        return true;
    }

    public static byte[] intToByteArray(int val, ByteOrder order) {
        byte[] cmd = null;

        if (order == ByteOrder.BIG_ENDIAN) {
            cmd = new byte[2];
            cmd[0] = (byte) ((val >> 8) & 0xFF);
            cmd[1] = (byte) (val & 0xFF);
        } else if (order == ByteOrder.LITTLE_ENDIAN) {
            cmd = new byte[2];
            cmd[0] = (byte) (val & 0xFF);
            cmd[1] = (byte) (val >> 8 & 0xFF);
        }

        return cmd;
    }

    public static byte[] stringToByteArray(String str, ByteOrder order) {
        ByteBuffer byteBuffer = ByteBuffer.allocate(str.length());
        byteBuffer.order(order);
        byteBuffer.put(str.getBytes());
        byteBuffer.array();
        return byteBuffer.array();
    }

    /**
     * 执行cmd指令；如果进程有阻塞，需手动释放
     *
     * @param cmd
     * @return
     */
    public static String execCmd(String cmd, boolean save, boolean showLog) {
        if (showLog)
            Log.d(TAG, "exec command=" + cmd);
        StringBuilder retBuilder = new StringBuilder("/n");
        String[] cmdString = new String[]{"sh", "-c", cmd};
        BufferedReader stdin = null;
        BufferedReader stderr = null;

        Process process = null;
        try {
            process = Runtime.getRuntime().exec(cmdString);
            stdin = new BufferedReader(new InputStreamReader(process.getInputStream()), 7777);
            stderr = new BufferedReader(new InputStreamReader(process.getErrorStream()), 7777);
            String line = null;
            while ((line = stdin.readLine()) != null || (line = stderr.readLine()) != null) {
                if (showLog)
                    Log.d(TAG, line);
                retBuilder.append(line).append("\n");
            }

        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            IOUtils.close(stdin);
            IOUtils.close(stderr);
            if(process != null)
                process.destroy();
        }


        if (save) {
            BufferedOutputStream bOut = null;
            try {
                File file = new File("/sdcard/ota/" + cmd + System.currentTimeMillis() + ".log");
                file.delete();
                file.createNewFile();
                bOut = new BufferedOutputStream(new FileOutputStream(file));
                bOut.write(retBuilder.toString().getBytes());
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                IOUtils.close(bOut);
            }
        }

        if (showLog)
            Log.d(TAG, "execCmd=" + retBuilder.toString());
        return retBuilder.toString();
    }

    /**
     * 查找进程
     *
     * @param processName
     * @return
     */
    public static List<Integer> findPid(String processName) {
        List<Integer> pidList = new ArrayList<Integer>(20);

        File[] files = new File("/proc").listFiles();
        StringBuffer cmdLine;
        String ret;
        String[] splitStr;
        for (File file: files) {
            cmdLine = new StringBuffer(file.getAbsolutePath());
            cmdLine.append("/cmdline");
            ret = execCmd("cat " + cmdLine.toString(), false, false);
            if (ret.contains(processName)) {
                splitStr = cmdLine.toString().split("/");
                Log.d(TAG, "find pid=" + splitStr[2]);
                pidList.add(Integer.valueOf(splitStr[2]));
            }
        }

        return pidList;
    }

    /**
     * 查找进程名称
     *
     * @param pid
     * @return
     */
    public static String findPidName(int pid) {
        File file = new File("/proc/" + pid + "/cmdline");
        String ret = execCmd("cat " + file.getAbsolutePath(), false, false);
        return ret;
    }

    public static boolean isInUpdateInterval(String startTime, String endTime) {
        if (startTime == null || endTime == null)
            return false;

        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        String currentTime = new SimpleDateFormat("HH:mm").format(new Date());
        try {
            long now = sdf.parse(currentTime).getTime();
            long start = sdf.parse(startTime).getTime();
            long end = sdf.parse(endTime).getTime();

            if (end < start) {
                if (now >= end && now < start) {
                    return false;
                } else {
                    return true;
                }
            }
            else {
                if (now >= start && now < end) {
                    return true;
                } else {
                    return false;
                }
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return false;
    }
}
