package com.ainirobot.home.service;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.os.RemoteException;
import android.support.annotation.Nullable;
import android.util.Log;

import com.ainirobot.coreservice.client.ApiListener;
import com.ainirobot.coreservice.client.speech.SkillApi;
import com.ainirobot.coreservice.client.speech.SkillCallback;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.control.SkillManager;

public class SkillService extends Service {

    private static final String TAG = "SkillService:Home";

    private SkillManager mSkillManager;
    private SkillCallback mSkillCallback = new SkillCallback() {

        @Override
        public void onSpeechParResult(String s) throws RemoteException {
            Log.i(TAG, "onSpeechParResult :" + s);
            if(mSkillManager.isInListeningState()){
                mSkillManager.onSpeechParResult(s);
            }
        }

        @Override
        public void onStart() throws RemoteException {
            Log.i(TAG, "onSpeechRecognitionStart ");
            if(mSkillManager.isInListeningState()){
                mSkillManager.onSpeechRecognitionStart();
            }
        }

        @Override
        public void onStop() throws RemoteException {
            Log.i(TAG, "onSpeechRecognitionStop ");
            if(mSkillManager.isInListeningState()){
                mSkillManager.onSpeechRecognitionStop();
            }
        }

        @Override
        public void onVolumeChange(int i) throws RemoteException {
            Log.i(TAG, "onVolumeChange :" + i);
            if(mSkillManager.isInListeningState()){
                //mSkillManager.onVolumeChange(i);
            }
        }

        @Override
        public void onQueryEnded(int i) throws RemoteException {
            Log.d(TAG, "onQueryEnded()");
            if(mSkillManager.isInListeningState()){
                mSkillManager.onQueryEnded(i);
            }
        }
    };

    @Override
    public void onCreate() {
        mSkillManager = SkillManager.getInstance();
        Log.i(TAG, "onCreate");
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        int result = super.onStartCommand(intent, flags, startId);
        Log.i(TAG, "onStartCommand this result=" + result);
        connectSpeechServer();
        return START_NOT_STICKY;
    }

    private void connectSpeechServer() {
        final SkillApi skillApi = new SkillApi();
        ApiListener listener=new ApiListener() {
            @Override
            public void handleApiDisabled() {

            }

            @Override
            public void handleApiConnected() {
                // home does not need this call back
                //skillApi.registerCallBack(mSkillCallback);
                mSkillManager.setSkillApi(skillApi);
                boolean isAlreadyOpenAsr = ApplicationWrapper.getIsAlreadyOpenAsr();
                Log.d(TAG, "handleApiConnected asr open state:"+isAlreadyOpenAsr);
                if (!isAlreadyOpenAsr) {
                    mSkillManager.closeSpeechAsrRecognize();
                }
            }

            @Override
            public void handleApiDisconnected() {
                Log.e(TAG,"handleApiDisconnected");
                skillApi.connectApi(ApplicationWrapper.getContext());
            }
        };
        skillApi.addApiEventListener(listener);
        skillApi.connectApi(ApplicationWrapper.getContext());
    }
}
