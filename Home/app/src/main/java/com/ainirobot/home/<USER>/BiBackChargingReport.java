package com.ainirobot.home.bi;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

/**
 * @version V1.0.0
 * @date 2019/4/9 20:06
 */
public class BiBackChargingReport extends BaseBiReport {

    private static final String TABLE_NAME = "gb_recharge_result";

    private static final String CTIME = "ctime";

    private static final String RESULT = "result";

    private static final String WHICH = "which";

    public BiBackChargingReport() {
        super(TABLE_NAME);
    }

    public BiBackChargingReport addResult(Object result) {
        addData(RESULT, result);
        return this;
    }

    public BiBackChargingReport addWhich(Object which) {
        addData(WHICH, which);
        return this;
    }


    @Override
    public void report() {
        addData(CTIME, System.currentTimeMillis());
        super.report();
    }
}
