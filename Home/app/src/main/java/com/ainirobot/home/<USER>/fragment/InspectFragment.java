package com.ainirobot.home.ui.fragment;

import android.graphics.drawable.AnimationDrawable;
import android.os.Bundle;
import android.provider.Settings;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.home.R;
import com.ainirobot.home.utils.ResType;

public class InspectFragment extends BaseFragment{

    private static final String TAG = "InspectFragment:Home";

    private ImageView         mIvLogo;
    private ImageView         mInspectLoading;
    private ImageView         mIvSample;
    private AnimationDrawable animationDrawable;
    private static final String NORMAL_ROBOT = "0";
    private static final String REPAIR_ROBOT = "1";

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        animationDrawable = (AnimationDrawable)getActivity().getResources().getDrawable(R.drawable.anim_inspect_loading);

    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        Log.i(TAG, "on create view");
        View view = inflater.inflate(R.layout.fragment_inspect, null);
        mIvLogo = (ImageView) view.findViewById(R.id.iv_img);
        mIvSample = (ImageView) view.findViewById(R.id.sample_img);
        String model = RobotSettings.getProductModel();
        Log.d(TAG, "Model:" + model);
        boolean isOverseas = ProductInfo.isDeliveryOverSea();
        Log.d(TAG, "isModelOverseas:" + isOverseas);
//        mIvLogo.setImageResource(ResType.INSPECT.getResIdByType());
        mInspectLoading = (ImageView)view.findViewById(R.id.inspecting_loading);
        mInspectLoading.setImageDrawable(animationDrawable);

        return view;
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if (animationDrawable != null) {
            animationDrawable.start();
        }

    }

    @Override
    public void onResume() {
        super.onResume();
        initRootLayoutBg();
    }

    private void initRootLayoutBg(){
        String robot_sample = Settings.Global.getString(getActivity().getContentResolver(),"robot_sample");
        Log.i(TAG, "initRootLayoutBg: robot_sample = "+robot_sample);
        if (!TextUtils.isEmpty(robot_sample)){
            switch (robot_sample){
                case REPAIR_ROBOT:
                    mIvSample.setVisibility(View.VISIBLE);
                    break;
                case NORMAL_ROBOT:
                default:
                    mIvSample.setVisibility(View.GONE);
                    break;
            }
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        Log.i(TAG, "onstart");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        animationDrawable.stop();
        Log.d(TAG, "onDestroyView");
    }
}
