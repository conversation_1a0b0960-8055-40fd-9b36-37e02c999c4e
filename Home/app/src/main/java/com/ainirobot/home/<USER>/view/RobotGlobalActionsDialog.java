package com.ainirobot.home.ui.view;

import android.animation.ObjectAnimator;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Bitmap;
import android.graphics.LightingColorFilter;
import android.graphics.drawable.BitmapDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.renderscript.Allocation;
import android.renderscript.Element;
import android.renderscript.RenderScript;
import android.renderscript.ScriptIntrinsicBlur;
import android.support.annotation.NonNull;
import android.text.Editable;
import android.text.InputType;
import android.text.TextWatcher;
import android.util.IntProperty;
import android.util.Log;
import android.util.Property;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.DecelerateInterpolator;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.home.R;

import java.text.SimpleDateFormat;
import java.util.Date;

public class RobotGlobalActionsDialog extends Dialog {

    private static final String TAG = RobotGlobalActionsDialog.class.getSimpleName();
    private final BitmapDrawable mBackgroundBlurBitmapDrawable;
    private final ScriptIntrinsicBlur mScriptIntrinsicBlur;
    private final Allocation mAllocationIn, mAllocationOut;
    private final DialogInterface.OnClickListener mClickListener;
    private EditText mEditTextPassword;
    private ViewGroup mLayoutPasswordDisplay;
    private TextView mTextViewPasswordError;
    private final Handler mUIHandler = new Handler();
    private Toast mToast;

    private final String SerialNo = RobotSettings.getSystemSn();
    private final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat("yyyy/MM/dd HH:mm");

    public RobotGlobalActionsDialog(Context context, int themeResId, Bitmap bitmap, DialogInterface.OnClickListener clickListener) {
        super(context, themeResId);
        Log.d(TAG, "RobotGlobalActionsDialog init bitmap :"+bitmap);
        mBackgroundBlurBitmapDrawable = new BitmapDrawable(getContext().getResources(), bitmap.copy(Bitmap.Config.ARGB_8888, true));
        mClickListener = clickListener;
        final RenderScript renderScript = RenderScript.create(getContext());
        mAllocationIn = Allocation.createFromBitmap(renderScript, mBackgroundBlurBitmapDrawable.getBitmap());
        mAllocationOut = Allocation.createTyped(renderScript, mAllocationIn.getType());
        mScriptIntrinsicBlur = ScriptIntrinsicBlur.create(renderScript, Element.U8_4(renderScript));
    }

    @Override
    public boolean dispatchTouchEvent(@NonNull MotionEvent ev) {
        onUserInteraction();
        return super.dispatchTouchEvent(ev);
    }

    @Override
    public boolean dispatchKeyEvent(@NonNull KeyEvent event) {
        onUserInteraction();
        return super.dispatchKeyEvent(event);
    }

    @Override
    public boolean dispatchTrackballEvent(@NonNull MotionEvent ev) {
        onUserInteraction();
        return super.dispatchTrackballEvent(ev);
    }

    @Override
    public boolean dispatchKeyShortcutEvent(@NonNull KeyEvent event) {
        onUserInteraction();
        return super.dispatchKeyShortcutEvent(event);
    }

    @Override
    public boolean dispatchGenericMotionEvent(MotionEvent ev) {
        // TODO Auto-generated method stub
        onUserInteraction();
        return super.dispatchGenericMotionEvent(ev);
    }

    public void onUserInteraction() {
        resetAutoDismissTimer();
    }

    final Runnable mAutoDismissRunnable = new Runnable() {
        @Override
        public void run() {
            dismiss();
        }
    };

    TextView mTextViewDeviceSerial;

    TextView mTextViewDeviceVersion;

    TextView mTextViewTimeClock;

    TextView mRepairMachine;

    final Runnable mUpdateTimeClockRunnable = new Runnable() {

        @Override
        public void run() {
            // TODO Auto-generated method stub
            mTextViewTimeClock.setText(SIMPLE_DATE_FORMAT.format(new Date()));
            mUIHandler.postDelayed(this, 1000);
        }
    };

    void resetAutoDismissTimer() {
        mUIHandler.removeCallbacks(mAutoDismissRunnable);
        mUIHandler.postDelayed(mAutoDismissRunnable, 15 * 1000);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        Log.v(TAG, "onCreate");
        super.onCreate(savedInstanceState);
        setContentView(R.layout.global_password_dialog);
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
        getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
        getWindow().setBackgroundDrawable(mBackgroundBlurBitmapDrawable);
        getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION // hide nav bar
                        | View.SYSTEM_UI_FLAG_FULLSCREEN // hide status bar
                        | View.SYSTEM_UI_FLAG_LOW_PROFILE | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
        mEditTextPassword = (EditText) findViewById(R.id.robot_global_actions_et_password);
        mTextViewDeviceSerial = (TextView) findViewById(R.id.device_serial);
        mTextViewDeviceVersion = (TextView) findViewById(R.id.device_version);
        mTextViewTimeClock = (TextView) findViewById(R.id.time_clock);

        mLayoutPasswordDisplay = (ViewGroup) findViewById(R.id.layout_password_display);
        mTextViewPasswordError = (TextView) findViewById(R.id.robot_global_actions_tv_password_error);
        ((CheckBox) findViewById(R.id.password_show_hide_checkbox)).setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {

                for (int i = 0; i < mLayoutPasswordDisplay.getChildCount(); i++) {
                    ((TextView) mLayoutPasswordDisplay.getChildAt(i)).setInputType(isChecked ? (InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD)
                            : (InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_PASSWORD));
                }
            }
        });
        findViewById(R.id.cancel_input_password).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        final String robot_uuid = RobotSettings.getCorpUUID(getContext());
        mTextViewDeviceSerial
                .setText((robot_uuid != null) ? robot_uuid.subSequence(Math.max(0, robot_uuid.length() - 8), robot_uuid.length()) : robot_uuid);
        mTextViewDeviceVersion.setText("V" + RobotSettings.getOrionStarPropertyVersion());
//        findViewById(R.id.log_ticker).setOnClickListener(new MultiClickCountImpl(mUIHandler, 30) {
//
//            @Override
//            public void onReachClickCount(View v) {
//                // TODO Auto-generated method stub
//                Toast t = Toast.makeText(getContext(), R.string.thanks_for_your_feedback, Toast.LENGTH_SHORT);
//                t.setGravity(Gravity.CENTER, 0, 0);
//                t.getWindowParams().privateFlags |= WindowManager.LayoutParams.PRIVATE_FLAG_SHOW_FOR_ALL_USERS;
//                t.getWindowParams().type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
//                t.show();
//            }
//        });

        mUpdateTimeClockRunnable.run();

        mEditTextPassword.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                final int childCount = mLayoutPasswordDisplay.getChildCount();
                for (int i = 0; i < childCount; i++) {
                    if (i < s.length()) {
                        ((TextView) mLayoutPasswordDisplay.getChildAt(i)).setText(s.subSequence(i, i + 1));
                    } else {
                        ((TextView) mLayoutPasswordDisplay.getChildAt(i)).setText(null);
                    }
                }

                if (s.length() == childCount) {
                    if (SerialNo.substring(Math.max(0, SerialNo.length() - childCount)).equalsIgnoreCase(s.toString())) {
                        dismiss();
                        mClickListener.onClick(RobotGlobalActionsDialog.this, DialogInterface.BUTTON_POSITIVE);
                    } else {
                        if (mTextViewPasswordError != null) {
                            mTextViewPasswordError.animate().alpha(1).setDuration(500).setStartDelay(0).withEndAction(new Runnable() {

                                @Override
                                public void run() {
                                    mTextViewPasswordError.animate().alpha(0).setStartDelay(2000).setDuration(500).start();
                                }
                            }).start();
                        }
                        mUIHandler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                mEditTextPassword.setText("");
                            }
                        }, 500);
                    }
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        ObjectAnimator blurRadiusAnimator = ObjectAnimator.ofInt(this, BLUR_RADIUS, 1, 100);
        blurRadiusAnimator.setDuration(1000);
        blurRadiusAnimator.setInterpolator(new DecelerateInterpolator(2));
        blurRadiusAnimator.start();

        resetAutoDismissTimer();
        android.view.inputmethod.InputMethodManager imm = (android.view.inputmethod.InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.showSoftInput(mEditTextPassword, android.view.inputmethod.InputMethodManager.SHOW_FORCED);
    }

    @Override
    protected void onStart() {
        // TODO Auto-generated method stub
        Log.v(TAG, "onStart");
        super.onStart();
    }

    @Override
    protected void onStop() {
        // TODO Auto-generated method stub
        Log.v(TAG, "onStop");
        super.onStop();
        mUIHandler.removeCallbacksAndMessages(null);
    }

    public void setBlurRadius(int radius) {
        mScriptIntrinsicBlur.setInput(mAllocationIn);
        mScriptIntrinsicBlur.setRadius(radius * 10 / 100f);
        mScriptIntrinsicBlur.forEach(mAllocationOut);

        mAllocationOut.copyTo(mBackgroundBlurBitmapDrawable.getBitmap());

        int colorMultiply = 255 - 128 * radius / 100;
        mBackgroundBlurBitmapDrawable.setColorFilter(new LightingColorFilter((colorMultiply << 16) | (colorMultiply << 8) | colorMultiply, 0));
    }

    public final static Property<RobotGlobalActionsDialog, Integer> BLUR_RADIUS = new IntProperty<RobotGlobalActionsDialog>("blurRadius") {

        @Override
        public void setValue(RobotGlobalActionsDialog object, int value) {
            // TODO Auto-generated method stub
            object.setBlurRadius(value);
        }

        @Override
        public Integer get(RobotGlobalActionsDialog object) {
            // TODO Auto-generated method stub
            return null;
        }

    };

    static abstract class MultiClickCountImpl implements View.OnClickListener, Runnable {

        final Handler mHandler;
        final int mMaxClickCount;
        int mClickCount = 0;

        public MultiClickCountImpl(Handler handler, int maxClickCount) {
            mHandler = handler;
            mMaxClickCount = maxClickCount;
        }

        @Override
        public final void run() {
            // TODO Auto-generated method stub
            mClickCount = 0;
        }

        @Override
        public final void onClick(View v) {
            // TODO Auto-generated method stub
            mHandler.removeCallbacks(this);
            mHandler.postDelayed(this, 500);
            if (mClickCount++ > mMaxClickCount) {
                onReachClickCount(v);
                mClickCount = 0;
            }
        }

        public abstract void onReachClickCount(View v);
    }
}