package com.ainirobot.home.ui.view;

import android.app.AlertDialog;
import android.content.Context;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.ainirobot.home.R;

import static com.ainirobot.home.utils.ResUtil.getString;

public class OTAInspectFailDialog extends AlertDialog {

    private static final int COUNT_DOWN_TIME = 20;
    private CountDownTimer mTimer;
    private TextView mConfirm;
    private DialogEvent mEvent;
    private Context mContext;
    private String mMessage;

    public OTAInspectFailDialog(Context context, String message, DialogEvent event) {
        super(context);
        mEvent = event;
        mContext = context;
        mMessage = message;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        View view = LayoutInflater.from(mContext).inflate(R.layout.ota_inspect_fail_dialog, null);
        setContentView(view);
        setViewStyle();

        ((TextView)view.findViewById(R.id.ota_inspect_message)).setText(mMessage);
        mConfirm = (TextView) view.findViewById(R.id.ota_inspect_confirm);
        mConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mTimer != null) {
                    mTimer.cancel();
                    mTimer = null;
                }
                if (mEvent != null) {
                    mEvent.onConfirm();
                }
                dismiss();
            }
        });

        mTimer = new CountDownTimer(COUNT_DOWN_TIME * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                mConfirm.setText(getString(R.string.ota_confirm, millisUntilFinished / 1000));
            }

            @Override
            public void onFinish() {
                if (mEvent != null) {
                    mEvent.onConfirm();
                }
                dismiss();
            }
        }.start();
    }

    private void setViewStyle() {
        Window window = getWindow();
        if (window == null) {
            return;
        }
        window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
        WindowManager.LayoutParams lp = window.getAttributes();
        lp.width = 940;
        lp.height = 465;
        window.setAttributes(lp);
        window.setBackgroundDrawableResource(R.color.transparent);
        setCanceledOnTouchOutside(false);
    }

    public interface DialogEvent{
        void onConfirm();
    }
}
