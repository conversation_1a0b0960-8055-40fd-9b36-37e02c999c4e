package com.ainirobot.home.bi;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

public class RemoteControlReport extends BaseBiReport {

    private static final String ACTION = "action";
    private static final String MSG = "msg";
    private static final String CTIME = "ctime";

    public RemoteControlReport() {
        super("sc_restaurant_go_ahead");
    }

    public RemoteControlReport addAction(int action) {
        addData(ACTION, action);
        return this;
    }

    public RemoteControlReport addMsg(int msg) {
        addData(MSG, msg);
        return this;
    }

    @Override
    public void report() {
        addData(CTIME, System.currentTimeMillis());
        super.report();
    }
}
