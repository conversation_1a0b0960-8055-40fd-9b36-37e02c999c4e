package com.ainirobot.home.ui.view;

import android.content.Context;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.home.R;
import com.ainirobot.home.module.RelocationModule;

public class RelocationGuideView extends FrameLayout {

    private View mView;
    private Context mContext;
    public View mCancelTV;
    public ImageView mTipImg;
    public TextView mTvTitle;
    public TextView mTvSubTitle;
    public TextView mConfirmBtn;

    public RelocationGuideView(Context context) {
        this(context, null);
    }

    public RelocationGuideView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RelocationGuideView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public RelocationGuideView(Context context, @Nullable AttributeSet attrs, int defStyleAttr,
                               int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initSubViews(context);
    }

    private void initSubViews(Context context) {
        this.mContext = context;
        mView = LayoutInflater.from(mContext).inflate(R.layout.layout_qrcode_relocation_guide, this);
        mCancelTV = mView.findViewById(R.id.reposition_cancel_text);
        mTipImg = mView.findViewById(R.id.iv_tip);
        mTvTitle = mView.findViewById(R.id.locate_guide_title);
        mTvSubTitle = mView.findViewById(R.id.locate_guide_subtitle);
        mConfirmBtn = (TextView) mView.findViewById(R.id.locate_guide_confirmBtn);
    }

    public void updateView(String relocationType) {
        if (!TextUtils.equals(relocationType, String.valueOf(RelocationModule.RELOCATION_TYPE_TARGET))) {
            mTipImg.setImageResource(R.drawable.location_img_point);
            mTvTitle.setText(R.string.reposition_vision_reminder_title);
            mTvSubTitle.setText(R.string.reposition_vision_reminder_subtitle);
        }
        setVisibility(VISIBLE);
    }
}
