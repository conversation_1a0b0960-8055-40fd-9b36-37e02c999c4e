/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.home.ota.httpclient;

import android.text.TextUtils;
import android.util.Log;


import com.ainirobot.home.ota.httpclient.body.RequestBody;
import com.ainirobot.home.ota.utils.IOUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.net.ssl.HttpsURLConnection;

public class NetworkRunnable implements Runnable {
    private static final String TAG = NetworkRunnable.class.getSimpleName();
    private final HttpClient mClient;
    private final Request mRequest;
    private final RequestListener mCallback;
    private boolean isCanceled = false;
    private static final int HTTPS_LEN = 5;
    private static final String HTTPS = "https";

    public NetworkRunnable(HttpClient client,
                           Request request, RequestListener callback) {
        this.mClient = client;
        this.mRequest = request;
        this.mCallback = callback;
    }

    @Override
    public void run() {
        if (!isCanceled) {
            performRequest();
            return;
        }

        if (mCallback != null) {
            mCallback.onFailure(-1, "thread cancel");
        }
    }

    public void cancel() {
        isCanceled = true;
    }

    public Object getTag() {
        return mRequest.getTag();
    }

    public void performRequest() {
        String url = mRequest.getUrl();
        String method = mRequest.getMethod();
        String sessionId = mRequest.getSessionId();
        RequestBody body = mRequest.getBody();
        Map<String, String> headers = mRequest.getHeaders();
        Map<String, String> params = mRequest.getUrlParams();

        OutputStream out = null;
        InputStream in = null;
        HttpURLConnection urlConnection;
        try {
            StringBuffer buf = new StringBuffer();

            Set<Map.Entry<String, String>> entites = params.entrySet();
            int i = 0;
            for (Map.Entry<String, String> entry : entites) {
                if (i > 0) {
                    buf.append("&");
                }
                buf.append(entry.getKey()).append("=").append(URLEncoder.encode(entry.getValue(), "UTF-8"));
                i++;
            }
            if (!TextUtils.isEmpty(buf.toString())) {
                url += "?" + buf.toString();
            }

            if (connectionIsHttps(url)) {
                urlConnection = (HttpsURLConnection) new URL(url).openConnection();
            } else {
                urlConnection = (HttpURLConnection) new URL(url).openConnection();
            }

            if (method.equals(Request.Builder.POST)) {
                urlConnection.setDoOutput(true);
                urlConnection.setDoInput(true);
            }

            urlConnection.setUseCaches(false);
            urlConnection.setRequestMethod(method);
            if (!TextUtils.isEmpty(sessionId)) {
                urlConnection.setRequestProperty("Cookie", sessionId);
            }
            for (Map.Entry<String, String> header : headers.entrySet()) {
                urlConnection.setRequestProperty(header.getKey(), header.getValue());
            }

            if (body != null) {
                urlConnection.setRequestProperty("Content-Type", body.getContentType());

                out = urlConnection.getOutputStream();
                body.writeTo(out);
                out.flush();
            }

            if (isCanceled) {
                if (mCallback != null) {
                    mCallback.onFailure(-2, "user cancel");
                }
                return;
            }

            int responseCode = urlConnection.getResponseCode();
            if (mCallback != null) {
                Map<String, List<String>> responseHeaders = urlConnection.getHeaderFields();
                long contentLength = urlConnection.getContentLength();
                String contentType = urlConnection.getContentType();
                String contentEncoding = urlConnection.getContentEncoding();
                String responseMessage = urlConnection.getResponseMessage();
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    in = urlConnection.getInputStream();
                }

                Response response = new Response(responseCode, in, contentLength);
                response.setContentType(contentType);
                response.setHeaders(responseHeaders);
                response.setContentEncoding(contentEncoding);
                response.setResponseMessage(responseMessage);
                int status = response.getStatusCode();
                Log.d(TAG, "response status:" + status);
                if (status == HttpURLConnection.HTTP_OK) {
                    mCallback.onSuccess(response);
                } else {
                    mCallback.onFailure(status, response.getResponseMessage());
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
            if (mCallback != null) {
                mCallback.onFailure(-3, e.getMessage());
            }
        } finally {
            IOUtils.close(out);
            IOUtils.close(in);
            mClient.getDispatcher().finished(this);
        }
    }

    public static boolean connectionIsHttps(String url) {
        if (url.regionMatches(0, HTTPS, 0, HTTPS_LEN)) {
            return true;
        } else {
            return false;
        }
    }
}
