package com.ainirobot.home.fallback;

import android.graphics.Bitmap;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;

public class Dump {
    private static final String TAG = Dump.class.getSimpleName();

    public static void dumpScreen(String dir, Bitmap bitmap) {
        File file = new File(dir + "screencap.png");
        FileOutputStream out = null;
        try {
            out = new FileOutputStream(file);
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, out);
            out.flush();
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, e.getMessage());
        }
    }

    public static void dumpTopActivity(String dir) {
        try {
            Process process = Runtime.getRuntime().exec("dumpsys activity top");
            Utils.writeFile(dir + "activity_top.txt", process.getInputStream());
            Utils.destoryProcess(process);
        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, e.getMessage());
        }
    }

    public static void dumpServiceStatus(String dir) {
        try {
            Process process = Runtime.getRuntime().exec("dumpsys activity service com.ainirobot.coreservice/.service.CoreService system status");
            Utils.writeFile(dir + "services_status.txt", process.getInputStream());
            Utils.destoryProcess(process);
        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, e.getMessage());
        }
    }

    public static void dumpWindowVisiable(String dir) {
        try {
            Process process = Runtime.getRuntime().exec("dumpsys window visible");
            Utils.writeFile(dir + "window_visable.txt", process.getInputStream());
            Utils.destoryProcess(process);
        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, e.getMessage());
        }
    }


    public static void dumpUI(String dir) {
        try {
            Process process = Runtime.getRuntime().exec("uiautomator dump " + dir + "ui.xml");
            Utils.destoryProcess(process);
        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, e.getMessage());
        }
    }

    public static void deleteDump(final String dir, int dump_file_max_count) {
        try {
            File file = new File(dir);
            File[] files = file.listFiles();
            List<File> dirs = new ArrayList<File>();
            for (int i = 0; i < files.length; i++) {
                File f = files[i];
                if (f.isDirectory())
                    dirs.add(f);
            }

            Utils.fileSortByTime(dirs);
            int delCount = dirs.size() - dump_file_max_count;
            if (delCount <= 0) {
                return;
            }

            for (int i = 0; i < delCount; i++) {
                Utils.deleteDirWihtFile(dirs.get(i));
            }

        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, e.getMessage());
        }
    }


}
