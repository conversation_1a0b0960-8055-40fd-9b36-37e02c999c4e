package com.ainirobot.home.bi;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

public class BiHwAbnormalReport extends BaseBiReport {

    private static final String TABLE_NAME = "base_robot_hw_status";

    /**
     * 异常类型  底盘算法crash-----1
     */
    private static final String HW_TYPE = "hw_type";

    /**
     * 触发异常---0   恢复---1
     */
    private static final String STATUS = "status";

    /**
     * 是否是弹出异常界面  1是 2否 0默认，断连恢复传0
     */
    private static final String IS_FIRST = "is_first";
    /**
     * 恢复结果 1成功  2失败  0默认
     */
    private static final String RESULT = "result";

    private static final String CTIME = "ctime";


    public static final int HW_TYPE_CHASSIS_REMOTE_CRASH = 1;

    public static final int HW_STATUS_ERROR = 0;
    public static final int HW_STATUS_RECOVERY = 1;

    public static final int HW_VALUE_DEFAULT = 0;
    public static final int HW_VALUE_YES = 1;
    public static final int HW_VALUE_NO = 2;


    public BiHwAbnormalReport() {
        super(TABLE_NAME);
    }

    public BiHwAbnormalReport addResult(int result) {
        addData(RESULT, result);
        return this;
    }

    public BiHwAbnormalReport addHwType(int type) {
        addData(HW_TYPE, type);
        return this;
    }

    public BiHwAbnormalReport addStatus(int status) {
        addData(STATUS, status);
        return this;
    }

    public BiHwAbnormalReport addIsFirst(int firstStatus) {
        addData(IS_FIRST, firstStatus);
        return this;
    }

    @Override
    public void report() {
        addData(CTIME, System.currentTimeMillis());
        super.report();
    }
}
