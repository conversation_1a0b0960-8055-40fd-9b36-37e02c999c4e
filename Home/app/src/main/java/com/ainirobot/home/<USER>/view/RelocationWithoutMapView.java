package com.ainirobot.home.ui.view;

import android.content.Context;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.ainirobot.home.R;

public class RelocationWithoutMapView extends FrameLayout {

    private Context mContext;
    private View mView;
    public Button mRebootBtn;
    public View mCancelBtn;

    public RelocationWithoutMapView(Context context) {
        this(context, null);
    }

    public RelocationWithoutMapView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RelocationWithoutMapView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public RelocationWithoutMapView(Context context, @Nullable AttributeSet attrs, int defStyleAttr,
                                    int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initSubViews(context);
    }

    private void initSubViews(Context context) {
        this.mContext = context;
        mView = LayoutInflater.from(mContext).inflate(R.layout.layout_qrcode_relocation_without_map, this);
        mCancelBtn = mView.findViewById(R.id.reposition_without_map_cancel);
        mRebootBtn = (Button) mView.findViewById(R.id.without_map_reboot_btn);
    }
}
