/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.home.module;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.control.ControlManager;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;

public class BaseModule {

    private static final String TAG = "BaseModule:Home";
    public static final int RESULT_OK = 100;
    public static final int RESULT_FAILURE = 101;
    protected static Gson mGson = new Gson();
    protected int mState;
    protected static BaseModule sInstance = null;

    public static BaseModule getInstance() {
        if (sInstance == null) {
            sInstance = new BaseModule();
        }
        return sInstance;
    }

    protected BaseModule() {
        mState = ModuleDef.MODULE_STATE_IDLE;
    }

    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.d(TAG, "onNewSemantics, sendStatusReport start reqId:" + reqId
                + ", intent:" + intent + ", text:" + text + ", params:" + params);
        if (!ModuleDef.REQ_WAKEUP.equals(intent))
            sendStatusReport(reqId, text, params, true, "start");
        return false;
    }

    public boolean onHWReport(int hwFunction, String cmdType, String params) {
        return true;
    }

    public void updateState(int state) {
        mState = state;
    }

    public int getState() {
        return mState;
    }

    protected int getFeature() {
        return ControlManager.getControlManager().getFeatureIdByModule(this);
    }

    protected void broadcastStop(int module, int resultStatus, Bundle bundle) {
        if (bundle == null) {
            bundle = new Bundle();
        }
        bundle.putInt("featureId", module);
        bundle.putInt("result", resultStatus);
        Message msg = Message.obtain();
        msg.setData(bundle);
        msg.what = ModuleDef.MSG_MODULE_STOP;
        ControlManager.getControlManager().getMainHandler().sendMessage(msg);
    }

    /**
     * 调用此方法使module进入结束动作，如若结束动作较长，请重写onStop
     */
    public final void stop() {
        this.mState = ModuleDef.MODULE_STATE_PAUSE;
        onStop();
    }

    public boolean isRunning() {
        return this.mState == ModuleDef.MODULE_STATE_RUNNING;
    }

    public boolean isPausing() {
        return this.mState == ModuleDef.MODULE_STATE_PAUSE;
    }

    /**
     * callback when method release()
     */
    protected void onStop() {
        release(-1, RESULT_OK, null);
    }

    /**
     * end the request, release module and tell controllmanager the message
     *
     * @param reqId  request id
     * @param result the request result,return to coreservice
     * @param data   msg data for controllmanager
     */
    protected void release(int reqId, int result, Bundle data) {
//        if(reqId > 0){
//            boolean isSuccess = (result==RESULT_OK);
//            ModuleSendCmd.getInstance().finishModuleParser(reqId, isSuccess);
//        }
        int state = mState;
        mState = ModuleDef.MODULE_STATE_IDLE;
        if (state != ModuleDef.MODULE_STATE_IDLE) {
            broadcastStop(getFeature(), result, data);
            onFinish();
        }
    }

    /**
     * callback when the module idle
     */
    protected void onFinish() {
    }

    protected void sendStatusReport(int reqId, String action, String msg, boolean succeed, String status) {
        JsonObject obj = new JsonObject();
        obj.addProperty("reqId", reqId);
        obj.addProperty("type", action);
        obj.addProperty("value", succeed);
        if (!TextUtils.isEmpty(msg)) {
            try {
                JsonElement msgJson = new JsonParser().parse(msg);
                if (msgJson != null && msgJson.isJsonObject()) {
                    obj.add("msg", msgJson);
                } else {
                    obj.addProperty("msg", msg);
                }
            } catch (JsonSyntaxException e) {
                e.printStackTrace();
            }
        }
        obj.addProperty("status", status);
        SystemApi.getInstance().sendStatusReport(ModuleDef.STATUS_PROCESS, mGson.toJson(obj));
    }

    public void onMessageFromLocal(int type) {
        Log.d(TAG, "get message from ui:type=" + type +
                ",current module=" + ControlManager.getControlManager().getCurrentModule());
    }

    public void onMessageFromLocal(int type, Object param) {
        Log.d(TAG, "get message from ui:type=" + type + ", param : " + param +
                ",current module=" + ControlManager.getControlManager().getCurrentModule());
    }

    protected void sendVisionRepositionBroadcast(Context context){
        Intent visionIntent = new Intent();
        visionIntent.setAction(Definition.ACTION_REPOSITION);
        visionIntent.putExtra(Definition.REPOSITION_VISION, true);
        context.sendBroadcast(visionIntent);
    }
}
