package com.ainirobot.home.ui.fragment;

import static com.ainirobot.home.ui.UIController.MESSAGE_TYPE.REMOTE_STOP_CHARGING_AVOID_FAILED;
import static com.ainirobot.home.ui.UIController.MESSAGE_TYPE.REMOTE_STOP_CHARGING_RADAR_FAILED;
import static com.ainirobot.home.ui.UIController.MESSAGE_TYPE.REMOTE_STOP_CHARGING_UNKNOWN_FAILED;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.view.RemoteStopChargingFailedDialog;

/**
 * 远程离桩 结束充电
 */
public class RemoteStopChargingFragment extends BaseFragment {

    private static final String TAG = "RemoteStopChargingFragment_Home";
    private RemoteStopChargingFailedDialog mFailedDialog;

    public RemoteStopChargingFragment() {
    }


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        Log.d(TAG, "on create view");
        View view = inflater.inflate(R.layout.fragment_remotestopcharging, container, false);
        return view;
    }

    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
        switch (type) {
            case REMOTE_STOP_CHARGING_AVOID_FAILED:
                showFailedDialog(REMOTE_STOP_CHARGING_AVOID_FAILED);
                break;
            case REMOTE_STOP_CHARGING_RADAR_FAILED:
                showFailedDialog(REMOTE_STOP_CHARGING_RADAR_FAILED);
                break;
            case REMOTE_STOP_CHARGING_UNKNOWN_FAILED:
                showFailedDialog(REMOTE_STOP_CHARGING_UNKNOWN_FAILED);
                break;
            case REMOTE_STOP_CHARGING_SUCCEED:
                dismissFailedDialog();
                break;
        }
    }

    private void dismissFailedDialog() {
        if (null != mFailedDialog) {
            mFailedDialog.dismiss();
        }
    }

    private void showFailedDialog(UIController.MESSAGE_TYPE type) {
        if (null == mFailedDialog) {
            mFailedDialog = new RemoteStopChargingFailedDialog(getContext(), new RemoteStopChargingFailedDialog.DialogEvent() {
                @Override
                public void onConfirm() {
                    Log.i(TAG, "onConfirm");
                    mFailedDialog.dismiss();
                    ControlManager.getControlManager().sendMessageToModule(ModuleDef.MSG_REMOTE_STOP_CHARGING_EXIT);
                }
            });
            mFailedDialog.show();
        }
        mFailedDialog.updateFailedType(type);
    }
}
