package com.ainirobot.home.bi;


import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

/**
 * inspection bi report
 *
 * @version V1.0.0
 * @date 2019/4/9 17:05
 */
public class BiInspectionReport extends BaseBiReport {

    private static final String TABLE_NAME = "gb_self_test";

    private static final String CTIME = "ctime";

    private static final String RESULT = "result";

    private static final String DATA = "data";

    private static final String TYPE = "type";

    private static final String ERROR_ID = "error_id";

    private static final String ACTION = "action";

    private static final String LXC_VERSION = "lxc_version";

    public static final int SHOW_TO_USER = 1;

    public static final int NO_SHOW_TO_USER = 0;

    /**
     * 是否触发重启 0-旧版本或者自检成功 1-自检失败需要重启 2-自检失败已经重启过
     */
    public static final int ACTION_SUCCESS = 0;
    public static final int ACTION_FAIL_REBOOT = 1;
    public static final int ACTION_FAIL_ALREADY_REBOOT = 2;


    public BiInspectionReport() {
        super(TABLE_NAME);
    }

    public BiInspectionReport addResult(Object result) {
        addData(RESULT, result);
        return this;
    }

    public BiInspectionReport addType(Object type) {
        addData(TYPE, type);
        return this;
    }

    public BiInspectionReport addErrorId(Object errorId) {
        addData(ERROR_ID, errorId);
        return this;
    }

    public BiInspectionReport addData(Object data) {
        addData(DATA, data);
        return this;
    }

    public BiInspectionReport addAction(Object action) {
        addData(ACTION, action);
        return this;
    }

    @Override
    public void report() {
        addData(CTIME, System.currentTimeMillis());
        addData(LXC_VERSION, SystemApi.getInstance().getLxcVersion());
        super.report();
    }
}
