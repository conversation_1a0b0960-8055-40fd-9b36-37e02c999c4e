package com.ainirobot.home.bi;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

/**
 * Shutdown bi report
 *
 * @version V1.0.0
 * @date 2019/3/5 18:23
 */
public class BiShutdownReport extends BaseBiReport {
    private static final String TABLE_NAME = "base_robot_schedule_shutdown_event";
    private static final String TYPE = "event_type";
    private static final String CTIME = "ctime";

    public static final int TYPE_SHUTDOWN_NOW = 1;
    public static final int TYPE_SHUTDOWN_CANCEL = 2;
    public static final int TYPE_SHUTDOWN_TIMEOUT = 3;

    public BiShutdownReport() {
        super(TABLE_NAME);
        initData();
    }

    private void initData() {
        addData(TYPE, "");
        addData(CTIME, "");
    }

    public BiShutdownReport addType(int type) {
        addData(TYPE, type);
        return this;
    }

    public void addCTime() {
        addData(CTIME, System.currentTimeMillis());
    }

    @Override
    public void report() {
        addCTime();
        super.report();
    }
}
