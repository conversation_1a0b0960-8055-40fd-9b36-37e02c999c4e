/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.home.ui.view;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Outline;
import android.os.Bundle;
import android.os.Handler;
import android.support.constraint.ConstraintLayout;
import android.util.Log;
import android.view.View;
import android.view.ViewOutlineProvider;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.home.R;

import static android.view.WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;

public class RadarDialog extends Dialog {
    public static final String TAG = RadarDialog.class.getSimpleName();

    private ConstraintLayout mLayout;
    private TextView mTvMessage;
    private ImageView mIvRadarOpening;
    private ImageView mIvRadarOpened;
    private String mMessage;
    private long mDurant;
    private int mDialogType;

    public static final int DIALOG_TYPE_OPENING = 0;
    public static final int DIALOG_TYPE_OPENED = 1;

    private final Handler mHandler = new Handler();

    public RadarDialog(Context context, int theme, int type) {
        super(context, theme);

        mDialogType = type;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_dialog_radar);

        WindowManager.LayoutParams p = this.getWindow().getAttributes();
        this.getWindow().setType(TYPE_SYSTEM_ALERT);
        this.getWindow().setAttributes(p);
        setCanceledOnTouchOutside(false);

        initView();
    }

    private void initView() {
        Log.d(TAG, "initView");
        mLayout = (ConstraintLayout)findViewById(R.id.dialog_radar);
        mTvMessage = (TextView) findViewById(R.id.tv_message);
        mIvRadarOpening = (ImageView) findViewById(R.id.iv_radar_opening);
        mIvRadarOpened = (ImageView) findViewById(R.id.iv_radar_opened);

        ViewOutlineProvider viewOutlineProvider = new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                view.setClipToOutline(true);
                outline.setRoundRect(0,0,view.getWidth(),view.getHeight(),20);
            }
        };
        mLayout.setOutlineProvider(viewOutlineProvider);

        if (mMessage != null && mTvMessage != null) {
            mTvMessage.setText(mMessage);
        }

        if (mDialogType == DIALOG_TYPE_OPENING) {
            mIvRadarOpening.setVisibility(View.VISIBLE);
            mIvRadarOpened.setVisibility(View.GONE);
            mTvMessage.setText(getContext().getResources().getString(R.string.radar_is_opening));
            mDurant = 0;
        }else {
            mIvRadarOpening.setVisibility(View.GONE);
            mIvRadarOpened.setVisibility(View.VISIBLE);
            mTvMessage.setText(getContext().getResources().getString(R.string.radar_open_success));
            mDurant = 2000;
        }
    }

    public int getType() {
        return mDialogType;
    }

    @Override
    public void show() {
        super.show();

        if (mDurant != 0) {
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    dismiss();
                }
            }, mDurant);
        }
    }
}
