package com.ainirobot.home.ui.fragment;

import android.graphics.Outline;
import android.graphics.drawable.AnimationDrawable;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.ui.UIController;

public class LoadMapFragment extends BaseFragment {

    private static final String TAG = "LoadMapFragment:Home";

    private ImageView mIvProgress;
    private TextView mTitle;
    private TextView mTvPercent;
    private Button mSkipButton;
    private AnimationDrawable mAnimationDrawable;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        Log.i(TAG, "on create view");
        View view = inflater.inflate(R.layout.fragment_load_map, null);
        mTitle = view.findViewById(R.id.load_map_tip);
        mIvProgress = view.findViewById(R.id.load_map_progress);
        mTvPercent = view.findViewById(R.id.load_map_percent);
        mSkipButton = view.findViewById(R.id.load_map_skip);
        mSkipButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_SKIP_LOAD_MAP);
            }
        });

        View bgProcess = view.findViewById(R.id.bg_progress);
        ViewOutlineProvider viewOutlineProvider = new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                view.setClipToOutline(true);
                outline.setRoundRect(0, 0, view.getWidth(), view.getHeight(), 6);
            }
        };
        bgProcess.setOutlineProvider(viewOutlineProvider);
        mIvProgress.setImageResource(R.drawable.frame_upgrade_progress);
        mAnimationDrawable = (AnimationDrawable) mIvProgress.getDrawable();
        mAnimationDrawable.start();
        return view;
    }

    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
        switch (type) {
            case LOAD_MAP_PROGRESS:
                int progress;
                try {
                    progress = Integer.parseInt(message);
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                    break;
                }
                updateProgress(progress);
                break;
            case LOAD_MAP_FAILED:
                mTitle.setText(R.string.qrcode_reposition_without_map_title);
                mSkipButton.setVisibility(View.VISIBLE);
                mAnimationDrawable.stop();
                break;
        }
    }

    private void updateProgress(int progress) {
        if (progress == 0) {
            progress = 1;
        }
        mTvPercent.setText(String.format("%d%%", progress));
        final ViewGroup.LayoutParams layoutParams = mIvProgress.getLayoutParams();
        layoutParams.width = 585 * progress / 100;
        mIvProgress.setLayoutParams(layoutParams);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mAnimationDrawable != null) {
            mAnimationDrawable.stop();
        }
    }
}
