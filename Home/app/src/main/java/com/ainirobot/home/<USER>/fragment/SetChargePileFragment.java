package com.ainirobot.home.ui.fragment;

import android.os.Bundle;
import android.os.CountDownTimer;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.view.GifView;
import com.ainirobot.home.utils.ResType;

public class SetChargePileFragment extends BaseFragment implements View.OnClickListener {

    private static final String Tag = "SetChargeFragment:Home";

    private TextView title;
    private GifView gifView;
    private TextView confirmBtn;
    private ImageView completeImage;
    private LinearLayout cancelLayout;
    private TextView completeFailNumber;
    private ImageView setChargeOk;

    private int mCurState = ModuleDef.SET_PILE_STATUS_START;
    private CountDownTimer mCountDownTimer;
    private int mOrientation;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_set_charge_pile, null, false);
        cancelLayout = (LinearLayout) view.findViewById(R.id.ll_charge_cancel);
        title = (TextView) view.findViewById(R.id.charge_set_title);
        completeImage = (ImageView) view.findViewById(R.id.charge_set_complete);
        gifView = (GifView) view.findViewById(R.id.charge_gif);
        setChargeOk = (ImageView) view.findViewById(R.id.iv_setChargeOk);
        confirmBtn = (TextView) view.findViewById(R.id.charge_confirm_btn);
        completeFailNumber = (TextView) view.findViewById(R.id.charge_set_complete_failnumber);

        mOrientation = ApplicationWrapper.getContext().getResources().getConfiguration().orientation;
        cancelLayout.setOnClickListener(this);
        confirmBtn.setOnClickListener(this);
        return view;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.i(Tag, "onDestroy()");
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
            mCountDownTimer = null;
        }
        mCurState = ModuleDef.SET_PILE_STATUS_START;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        switch (id) {
            case R.id.ll_charge_cancel:
                ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_STOP_SET_PILE);
                break;
            case R.id.charge_confirm_btn:
                ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_ESTIMATE);
                if (mCountDownTimer != null) {
                    mCountDownTimer.cancel();
                    mCountDownTimer = null;
                }
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d(Tag, "onResume");
        Bundle bundle = getArguments();
        if (bundle != null) {
            mCurState = bundle.getInt(ModuleDef.SET_PILE_START_TYPE, ModuleDef.SET_PILE_STATUS_START);
        }
        updateUI();
    }

    private void updateUI() {
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
            mCountDownTimer = null;
        }
        Log.d(Tag, "updateUI" + mCurState);
        switch (mCurState) {
            case ModuleDef.SET_PILE_STATUS_START:
                title.setText(getResources().getString(R.string.charge_start_point_title));title.setTextColor(getResources().getColor(R.color.white));
                completeImage.setVisibility(View.GONE);
                completeFailNumber.setVisibility(View.GONE);
                confirmBtn.setVisibility(View.GONE);
                setChargeOk.setVisibility(View.GONE);
                gifView.setMovieResource(
                        ResType.GIF_PUSH_TO_CHARGE_PILE.getResIdByType()
                );
                gifView.setVisibility(View.VISIBLE);
                break;
            case ModuleDef.SET_PILE_STATUS_CHARGING:
                title.setText(getResources().getString(R.string.charge_success_title));
                completeFailNumber.setVisibility(View.GONE);
                completeImage.setVisibility(View.GONE);
                confirmBtn.setVisibility(View.VISIBLE);
                confirmBtn.setText(getResources().getString(R.string.charge_confirm, 10 + ""));
                gifView.setMovieResource(
                        ResType.GIF_SET_CHARGE_SUC.getResIdByType()
                );
                gifView.setVisibility(View.GONE);
                setChargeOk.setVisibility(View.VISIBLE);
                mCountDownTimer = new CountDownTimer(10000, 1000) {
                    @Override
                    public void onTick(long millisUntilFinished) {
                        confirmBtn.setText(getResources().getString(R.string.charge_confirm, millisUntilFinished / 1000 + ""));
                    }
                    @Override
                    public void onFinish() {
                        ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_ESTIMATE);
                    }
                };
                mCountDownTimer.start();
                break;
            case ModuleDef.SET_PILE_STATUS_SETTING:
                title.setText(getResources().getString(R.string.charge_setting));
                completeFailNumber.setVisibility(View.GONE);
                completeImage.setVisibility(View.GONE);
                confirmBtn.setVisibility(View.GONE);
                confirmBtn.setText(getResources().getString(R.string.charge_confirm, 10 + ""));
                gifView.setMovieResource(
                        ResType.GIF_SET_CHARGE_SUC.getResIdByType()
                );
                gifView.setVisibility(View.GONE);
                setChargeOk.setVisibility(View.VISIBLE);
                break;
            case ModuleDef.SET_PILE_STATUS_SUCCESS:
                title.setText(getResources().getString(R.string.charge_success_complete_title));
                title.setTextColor(getResources().getColor(R.color.white));
                completeImage.setImageResource(R.drawable.charge_set_success);
                completeImage.setVisibility(View.VISIBLE);
                completeFailNumber.setVisibility(View.GONE);
                confirmBtn.setVisibility(View.GONE);
                gifView.setVisibility(View.GONE);
                setChargeOk.setVisibility(View.GONE);
                break;
            case ModuleDef.SET_PILE_STATUS_FAIL:
                cancelLayout.setVisibility(View.GONE);
                title.setText(getResources().getString(R.string.charge_fail_complete_title));
                title.setTextColor(getResources().getColor(R.color.text_fail));
                completeImage.setImageResource(R.drawable.charge_set_fail);
                completeImage.setVisibility(View.VISIBLE);
                completeFailNumber.setVisibility(View.VISIBLE);
                confirmBtn.setVisibility(View.GONE);
                gifView.setVisibility(View.GONE);
                setChargeOk.setVisibility(View.GONE);
                break;
        }
    }

    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
        if (type.equals(UIController.MESSAGE_TYPE.SET_PILE_STATUS)) {
            int status;
            try {
                status = Integer.valueOf(message);
            } catch (NumberFormatException e) {
                e.printStackTrace();
                return;
            }
            Log.d(Tag, "set status" + status);
            mCurState = status;
            updateUI();
        }
    }
}
