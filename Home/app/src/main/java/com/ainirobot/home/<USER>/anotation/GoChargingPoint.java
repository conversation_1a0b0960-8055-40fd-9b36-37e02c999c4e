package com.ainirobot.home.bi.anotation;

import static com.ainirobot.home.bi.anotation.GoChargingPoint.*;
import static com.ainirobot.home.bi.anotation.GoChargingPoint.LARGE_MAP_NAV_TIMEOUT;
import static com.ainirobot.home.bi.anotation.GoChargingPoint.NAVIGATION_FAIL;
import static com.ainirobot.home.bi.anotation.GoChargingPoint.NAV_GLOBAL_PATH_FAILED;
import static com.ainirobot.home.bi.anotation.GoChargingPoint.NAV_INTERCEPT;
import static com.ainirobot.home.bi.anotation.GoChargingPoint.NAV_OUT_MAP;
import static com.ainirobot.home.bi.anotation.GoChargingPoint.NOT_ESTIMATE;
import static com.ainirobot.home.bi.anotation.GoChargingPoint.NOT_MOVE_20S;
import static com.ainirobot.home.bi.anotation.GoChargingPoint.PARSE_LOCATION_FAIL;

import android.support.annotation.IntDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * go charging point annotation
 *
 * @version V1.0.0
 * @date 2019/4/10 15:50
 */

@Retention(RetentionPolicy.SOURCE)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@IntDef({NOT_ESTIMATE
        , NAVIGATION_FAIL
        , LARGE_MAP_NAV_TIMEOUT
        , PARSE_LOCATION_FAIL
        , NOT_MOVE_20S
        , NAV_INTERCEPT
        , GO_CHARGING_POINT_SUCCESS
        , NAV_OUT_MAP
        , NAV_GLOBAL_PATH_FAILED})
public @interface GoChargingPoint {

    final int GO_CHARGING_POINT_SUCCESS = 30000;
    final int NOT_ESTIMATE = -30100;
    final int NAVIGATION_FAIL = -30101;
    final int LARGE_MAP_NAV_TIMEOUT = -30102;
    final int PARSE_LOCATION_FAIL = -30103;
    final int NOT_MOVE_20S = -30104;
    final int NAV_OUT_MAP = -30105;
    final int NAV_GLOBAL_PATH_FAILED = -30106;
    final int NAV_INTERCEPT = -30107;

}
