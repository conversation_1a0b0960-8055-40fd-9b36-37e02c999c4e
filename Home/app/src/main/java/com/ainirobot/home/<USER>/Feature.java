/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.home.bean;

public class Feature {
    public static class DBFiled {
        public static String TABLE_NAME = "feature";
        public static final String FILED_FEATURE_ID = "module_feature";
        public static final String FILED_NAME = "module_name";
        public static final String FILED_PRIORITY = "module_priority";
        public static final String FILED_ISBACK = "module_background";
    }

    private int featureId;
    private String name;
    private int priority;
    private boolean isBackGround;

    public Feature() {
    }
    public int getFeatureId() {
        return featureId;
    }

    public void setFeatureId(int featureId) {
        this.featureId = featureId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public boolean isBackGround() {
        return isBackGround;
    }

    public void setBackGround(boolean isBackGround) {
        this.isBackGround = isBackGround;
    }

    @Override
    public String toString() {
        return "feature featureID:" + featureId + " name:" + name + " priority:" + priority +
                " isBackGround:" + isBackGround;
    }
}
