package com.ainirobot.home.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.support.annotation.CheckResult;
import android.support.annotation.Nullable;
import android.util.Log;
import android.widget.ImageView;

import com.ainirobot.home.ApplicationWrapper;
import com.bumptech.glide.Glide;
import com.bumptech.glide.integration.webp.decoder.WebpDrawable;
import com.bumptech.glide.integration.webp.decoder.WebpDrawableTransformation;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.Transformation;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.target.Target;

import org.jdeferred2.Promise;

public class GlideUtil {

    private static Context mContext = ApplicationWrapper.getContext();

    public static Promise<WebpDrawable, Void, Void> loadWebpAnimation(ImageView imageView, int webp) {
        return loadWebpAnimation(imageView, webp, new CircleCrop());
    }

    @CheckResult
    public static Promise<WebpDrawable, Void, Void> loadWebpAnimation(ImageView imageView, int webp, Transformation<Bitmap> transformation) {
        Log.d("loadWebpAnimation", mContext.getResources().getResourceEntryName(webp));
        final DeferredObjectWrapper<WebpDrawable, Void, Void> deferred = new DeferredObjectWrapper<>();

        WebpDrawableTransformation webpDrawableTransformation = new WebpDrawableTransformation(transformation);
        Glide.with(mContext).load(webp).addListener(new RequestListener<Drawable>() {

            @Override public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Drawable> target, boolean isFirstResource) {
                return false;
            }

            @Override public boolean onResourceReady(Drawable resource, Object model, Target<Drawable> target, DataSource dataSource, boolean isFirstResource) {
                WebpDrawable drawable = (WebpDrawable) resource;
                deferred.resolve(drawable);
                return false;
            }

        }).optionalTransform(transformation).optionalTransform(WebpDrawable.class, webpDrawableTransformation).into(imageView);
        return deferred.promise();
    }
}
