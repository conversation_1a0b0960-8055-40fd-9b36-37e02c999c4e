package com.ainirobot.home.ui.fragment;

import android.os.Bundle;
import android.support.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AlphaAnimation;
import android.view.animation.TranslateAnimation;
import android.widget.Button;
import android.widget.TextView;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ElectricDoorManager;
import com.ainirobot.home.ui.view.GifView;
import com.ainirobot.home.utils.ResType;

public class EmergencyFragment extends BaseFragment implements View.OnClickListener {

    private static final String TAG = "EmergencyFragment:Home";
    private TranslateAnimation mTranslateAnimation;
    private AlphaAnimation mAlphaAnimation;
    private TextView mTextView;
    private TextView mTextView2;
    private Button mElectricDoorButton;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        Log.i(TAG, "onCreateView:");
        mTranslateAnimation = new TranslateAnimation(0, 0, -250, 0);
        mTranslateAnimation.setDuration(500);

        mAlphaAnimation = new AlphaAnimation(0.0f, 1.0f);
        mAlphaAnimation.setDuration(400);
        mAlphaAnimation.setStartOffset(300);

        View view = inflater.inflate(R.layout.fragment_emergency, null);

        mTextView = view.findViewById(R.id.tv_emergency);
        mTextView2 = view.findViewById(R.id.tv_emergency2);
        GifView gif_emergency = view.findViewById(R.id.gif_emergency);
        gif_emergency.setMovieResource(ResType.GIF_EMERGENCY.getResIdByType());

        mElectricDoorButton = view.findViewById(R.id.electric_door_btn);
        if (ProductInfo.isDeliveryProduct() && ProductInfo.hasElectricDoor()) {
            Log.d(TAG, "onCreateView: show electric door button!");
            mElectricDoorButton.setVisibility(View.VISIBLE);
            mElectricDoorButton.setOnClickListener(this);
            initElectricDoorStatus();
        }

        return view;
    }

    @Override
    public void onResume() {
        mTextView.setVisibility(View.VISIBLE);
        mTextView2.setVisibility(View.VISIBLE);
        mTextView.startAnimation(mTranslateAnimation);
        mTextView2.startAnimation(mAlphaAnimation);
        super.onResume();
    }

    @Override
    public void onPause() {
        mTextView.setVisibility(View.INVISIBLE);
        mTextView2.setVisibility(View.INVISIBLE);
        super.onPause();
    }

    @Override
    public void onDestroy() {
        ElectricDoorManager.getInstance().release();
        super.onDestroy();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.electric_door_btn:
                ElectricDoorManager.getInstance().switchDoorStatus();
                break;
        }
    }

    private void initElectricDoorStatus() {
        ElectricDoorManager.getInstance().init(new ElectricDoorManager.ElectricDoorStatusListener() {
            @Override
            public void onElectricDoorStatusChanged(ElectricDoorManager.DoorStatus status) {
                Log.d(TAG, "onElectricDoorStatusChanged: " + status);
                if (status == ElectricDoorManager.DoorStatus.OPEN) {
                    mElectricDoorButton.setText(R.string.close_electric_door);
                } else if (status == ElectricDoorManager.DoorStatus.CLOSE) {
                    mElectricDoorButton.setText(R.string.open_electric_door);
                }
            }
        });
    }
}