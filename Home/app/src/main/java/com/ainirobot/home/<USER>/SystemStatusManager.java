package com.ainirobot.home.control;

import static com.ainirobot.home.utils.CommonUtils.getPriority;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.BatteryBean;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.bi.ActiveDurationReport;
import com.ainirobot.home.bi.BiGbCharging;
import com.ainirobot.home.fallback.blackbug.BlackBugCheck;
import com.ainirobot.home.receiver.BiActiveStateReceiver;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.utils.LocationUtil;
import com.ainirobot.home.utils.ResUtil;
import com.ainirobot.home.utils.ToastUtil;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;


public class SystemStatusManager {
    private static final String TAG = SystemStatusManager.class.getSimpleName() + ":home";

    private volatile BatteryBean mBatteryBean = new BatteryBean();
    private boolean mInspectSuccess = true;
    private String mInspectionResult = "";
    private String mNaviErrorId = "";
    private String failReason;
    private Gson gson = new Gson();

    // for Bi
    private BiGbCharging mBiGbCharging = new BiGbCharging();
    private ActiveDurationReport activeDurationReport = new ActiveDurationReport();

    public void init() {
        registerBatteryListener();
        registerBmsWarningStatusListener();
        registerEmergencyListener();
        registerMultiFunctionListener();
        if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) {
            registerPowerKeyListener();
        }
        registerNaviToGoalExcpListener();

    }

    private void registerBmsWarningStatusListener() {
        SystemApi.getInstance().registerStatusListener(Definition.STATUS_BMS_WARNING, new StatusListener() {
            @Override
            public void onStatusUpdate(String type, String data) {
                if (TextUtils.isEmpty(data)) {
                    return;
                }
                try {
                    JSONObject object = new JSONObject(data);
                    int status = object.optInt("status");
                    Log.d(TAG, "status: " + status);
                    switch (ControlManager.getControlManager().getCurrentModule()) {
                        case ModuleDef.FEATURE_STANDBY:
                            UIController.getInstance().sendMessageToFragment(
                                    UIController.MESSAGE_TYPE
                                            .BMS_WARNING_STATUS, String.valueOf(status));
                            break;
                        default:
                            break;
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private void registerMultiFunctionListener() {
        SystemApi.getInstance().registerStatusListener(Definition.STATUS_MULTI_FUNCTION_SWITCH,
                new StatusListener() {
                    @Override
                    public void onStatusUpdate(String type, String data) throws RemoteException {
                        super.onStatusUpdate(type, data);
//                        Log.d(TAG, "onStatusUpdate type: " + type + ", data: " + data);
                        switch (ControlManager.getControlManager().getCurrentModule()) {
                            case ModuleDef.FEATURE_STANDBY:
                                try {
                                    JSONObject jsonObject = new JSONObject(data);
                                    String switchState = jsonObject
                                            .getString(ModuleDef.MULTI_FUNC_SWITCH_STATE);
                                    if (TextUtils.equals(switchState, ModuleDef.MULTIPLE_PRESS)) {
                                        UIController.getInstance().sendMessageToFragment(
                                                UIController.MESSAGE_TYPE
                                                        .STANDBY_MULTI_FUNC_SWITCH_STATUS_CHANGE,
                                                String.valueOf(true));
                                    } else if (TextUtils.equals(switchState,
                                            ModuleDef.MULTIPLE_RELEASE)) {
                                        UIController.getInstance().sendMessageToFragment(
                                                UIController.MESSAGE_TYPE
                                                        .STANDBY_MULTI_FUNC_SWITCH_STATUS_CHANGE,
                                                String.valueOf(false));
                                    } else {
                                        Log.e(TAG, "wrong message in multiple function switch");
                                    }
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                }
                                break;
                            default:
                                break;
                        }
                    }
                });

        SystemApi.getInstance().registerStatusListener(Definition.STATUS_MAP_DRIFT_WARNING, new StatusListener(){
            @Override
            public void onStatusUpdate(String type, String data) throws RemoteException {
                super.onStatusUpdate(type, data);
                try {
                    JSONObject object = new JSONObject(data);
                    int status = object.getInt("status");

                    Log.d(TAG, "map drift warning status: " + status);

                    switch (status) {
                        case Definition.MAP_DRIFT_WARNING:
                            if (!mBatteryBean.isCharging()) {
                                return;
                            }
                            // 打开雷达
                            RadarManager.openRadar(null);
                            break;
                        default:
                            break;
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private void registerEmergencyListener() {
        SystemApi.getInstance().registerStatusListener(Definition.STATUS_EMERGENCY,
                new StatusListener() {
                    @Override
                    public void onStatusUpdate(String type, String data) {
//                        Log.d(TAG, "onStatusUpdate type: " + type + ", data: " + data);
                        switch (ControlManager.getControlManager().getCurrentModule()) {
                            case ModuleDef.FEATURE_STANDBY:
                                if (TextUtils.equals(data, ModuleDef.EMERGENCY_PRESS)) {
                                    UIController.getInstance().sendMessageToFragment(
                                            UIController.MESSAGE_TYPE.STANDBY_EMERGENCY_STATUS,
                                            String.valueOf(true));
                                } else if (TextUtils.equals(data, ModuleDef.EMERGENCY_RELEASE)) {
                                    UIController.getInstance().sendMessageToFragment(
                                            UIController.MESSAGE_TYPE.STANDBY_EMERGENCY_STATUS,
                                            String.valueOf(false));
                                } else {
                                    Log.e(TAG, "wrong message in emergency");
                                }
                                break;
                            default:
                                break;
                        }
                    }
                });
    }

    private void registerBatteryListener() {
        SystemApi.getInstance().registerStatusListener(Definition.STATUS_BATTERY,
                new StatusListener() {
                    @Override
                    public void onStatusUpdate(String type, String data) {
//                        Log.d(TAG, "onStatusUpdate type: " + type + ", data: " + data);
                        BatteryBean oldBatteryBean = mBatteryBean;
                        mBatteryBean = gson.fromJson(data, BatteryBean.class);
                        controlRadar(mBatteryBean.isCharging());
                        batteryChargeChange(oldBatteryBean);
                        batteryLevelChange(data);
                    }

                });
    }

    private void batteryLevelChange(String data) {
        int curModule = ControlManager.getControlManager().getCurrentModule();
        switch (curModule){
            case ModuleDef.FEATURE_STANDBY:
                UIController.getInstance().sendMessageToFragment(
                        UIController.MESSAGE_TYPE.STANDBY_BATTERY, data);
                break;

            case ModuleDef.FEATURE_AUTO_CHARGE:
                ControlManager.getControlManager()
                        .sendMessageToModule(ModuleDef.LOCAL_MESSAGE_LEVEL_CHANGED, mBatteryBean);
                break;
        }
    }

    private void controlRadar(boolean charging) {
        if (charging) {
            RadarManager.closeRadar(true);
        } else {
            RadarManager.cancelCloseRadar();
        }
    }

    private void batteryChargeChange(BatteryBean oldBatteryBean) {
        if (mBatteryBean.isCharging() != oldBatteryBean.isCharging()) {
            Log.d(TAG, "battery new: " + mBatteryBean +
                    ", old: " + oldBatteryBean +
                    " current module:" +
                    ControlManager.getControlManager().getCurrentModule());
            if (mBatteryBean.isCharging()) {
                ApplicationWrapper.getContext()
                        .sendBroadcast(new Intent(BiActiveStateReceiver
                                .ACTION_START_CHARGING));
                // 充电总Bi
                mBiGbCharging.charging().occur(BiGbCharging.CHARGE_ALL).report();
                activeDurationReport.durationReport();

                //锁轮毂
                Log.d(TAG, "batteryChargeChange: setPowerMotor");

                switch (ControlManager.getControlManager().getCurrentModule()) {
                    case ModuleDef.FEATURE_LAUNCHER:
                    case ModuleDef.FEATURE_CHARGING:
                    case ModuleDef.FEATURE_EMERGENCY:
                        checkCreateMap();
                        break;
                    case ModuleDef.FEATURE_AUTO_CHARGE:
                    case ModuleDef.FEATURE_REPOSITION:
                    case ModuleDef.FEATURE_REMOTE_REPOSITION:
                    case ModuleDef.FEATURE_SET_CHARGE_PILE:
                        ControlManager.getControlManager()
                                .sendMessageToModule(ModuleDef.LOCAL_MESSAGE_CHARGE_START);
                        break;
                }
            } else {
                // 充电时长 Bi
                ApplicationWrapper.getContext()
                        .sendBroadcast(new Intent(BiActiveStateReceiver
                                .ACTION_STOP_CHARGING));
                mBiGbCharging.chargeResult().report();
                activeDurationReport.chargeOff();
                switch (ControlManager.getControlManager().getCurrentModule()) {
                    case ModuleDef.FEATURE_AUTO_CHARGE:
                    case ModuleDef.FEATURE_REPOSITION:
                    case ModuleDef.FEATURE_REMOTE_REPOSITION:
                    case ModuleDef.FEATURE_OPEN_RADAR:
                    case ModuleDef.FEATURE_SET_CHARGE_PILE:
                    case ModuleDef.FEATURE_REMOTE_STOP_CHARGING:
                        ControlManager.getControlManager()
                                .sendMessageToModule(ModuleDef.LOCAL_MESSAGE_CHARGE_END);
                        break;
                }
            }
        }
    }

    private void registerPowerKeyListener() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(Definition.INTENT_POWER_KEY_PRESS);
        filter.addAction(Definition.INTENT_POWER_KEY_LONGPRESS);
        ApplicationWrapper.getContext().registerReceiver(new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String action = intent.getAction();
                if (action == null) {
                    return;
                }
                switch (action) {
                    case Definition.INTENT_POWER_KEY_LONGPRESS:
                    case Definition.INTENT_POWER_KEY_PRESS:
                        SkillManager.getInstance().speechPlayText(ApplicationWrapper.getContext()
                                .getString(R.string.please_charging_robot));
                        BlackBugCheck.getInstance(ApplicationWrapper.getContext()).screencapAndDump();
                        break;
                    default:
                        break;
                }
            }
        }, filter);
    }

    public boolean getIsCharging() {
        return mBatteryBean.isCharging();
    }

    public boolean isBatteryLow() {
        return mBatteryBean != null && mBatteryBean.isLow();
    }

    public int getBatteryLevel() {
        return mBatteryBean.getLevel();
    }

    private void checkCreateMap() {
        //TODO 豹小递（送餐）屏蔽充电桩重定位功能。
        if (ProductInfo.isDeliveryProduct() || LocationUtil.getInstance().isChargingTypeWire()) {
            Log.d(TAG, "checkCreateMap start");
            return;
        }

        SystemApi.getInstance().getMapStatus(0, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                super.onResult(result, message);
                Log.i(TAG, "onResult: " + message);
                if (TextUtils.isEmpty(message)
                        || !TextUtils.equals(Definition.NAVIGATION_WORK_MODE_CREATING_MAP, message.toUpperCase())) {
                    checkIsEstimate();
                }
            }

            @Override
            public void onError(int errorCode, String errorString) throws RemoteException {
                super.onError(errorCode, errorString);
                checkIsEstimate();
            }
        });
    }

    private void checkIsEstimate() {
        Log.d(TAG, "check estimate");
        SystemApi.getInstance().isRobotEstimate(0, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, "is estimate:result=" + result + ",message=" + message);
                super.onResult(result, message);
                //如果消息被CoreService拦截掉，重新查询一次。
                if (result == Definition.ACTION_RESPONSE_STOP_SUCCESS) {
                    checkIsEstimate();
                } else {
                    if (result != Definition.RESULT_OK ||
                            TextUtils.isEmpty(message) || !"true".equals(message)) {
                        getLocation();
                    }
                }
            }
        });

    }

    private void getLocation() {
        Log.d(TAG, "get location");
        SystemApi.getInstance().getLocation(0, Definition.CHARGING_POLE_TYPE, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, "get location:result=" + result + ", message=" + message);
                super.onResult(result, message);
                if (result == Definition.RESULT_OK) {
                    try {
                        JSONObject jsonObject = new JSONObject(message);
                        Pose chargingPose = new Pose();
                        boolean state = jsonObject.optBoolean(Definition.JSON_NAVI_SITE_EXIST, false);
                        if (state) {
                            chargingPose.setX((float) jsonObject.optDouble(Definition.JSON_NAVI_POSITION_X, 0.0));
                            chargingPose.setY((float) jsonObject.optDouble(Definition.JSON_NAVI_POSITION_Y, 0.0));
                            chargingPose.setTheta((float) jsonObject.optDouble(Definition.JSON_NAVI_POSITION_THETA, 0.0));
                            setFixedEstimate(chargingPose, 1);
                        }
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }
        });
    }

    private void setFixedEstimate(final Pose pose, final int retryCount) {

        Log.i(TAG, "retry resetFixedEstimate count:" + retryCount);

        SystemApi.getInstance().setFixedEstimate(0, new Gson().toJson(pose), new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, "pose estimate:result=" + result + ",message=" + message);
                super.onResult(result, message);
                if (result == Definition.RESULT_OK &&
                        !TextUtils.isEmpty(message) && message.equals("succeed")) {
                    SkillManager.getInstance().speechPlayText(
                            ApplicationWrapper.getContext().getString(R.string.reposition_manual_success));
                } else {
                    if (retryCount > 2) {
                        if (!ProductInfo.isDeliveryProduct() && !ProductInfo.isMeissaPlus() && !ProductInfo.isMeissa2()) {
                            SkillManager.getInstance().speechPlayText(getTTSFailMsg(failReason));
                        }
                        naviCmdTimeOutReport();
                        resumeFailReason();//resume reason to null
                        return;
                    }
                    recordFailReason(message);
                    setFixedEstimate(pose, retryCount + 1);
                }
            }
        });

    }

    private void resumeFailReason() {
        failReason = "";
    }

    private void recordFailReason(String message) {
        Log.d(TAG, "recordFailReason = " + message);
        if (getPriority(message) < getPriority(failReason)) {
            // only record high priority reason, smaller is higher
            failReason = message;
        }
    }

    private String getTTSFailMsg(String message) {
        if (!TextUtils.isEmpty(message)
                && message.equals(Definition.RESULT_ESTIMATE_FAIL_MATCH_ERR)) {
            return ApplicationWrapper.getContext().getString(R.string.tts_check_chargepile_loc);
        } else if (!TextUtils.isEmpty(message)
                && message.equals(Definition.RESULT_ESTIMATE_FAIL_CHECK_ERR)) {
            return ApplicationWrapper.getContext().getString(R.string.tts_check_chargepile_is_moved);
        } else {
            return ApplicationWrapper.getContext().getString(R.string.tts_fail_common);
        }
    }

    public void setInspectionResult(String inspectionResult, String naviErrorId) {
        mInspectSuccess = false;
        mInspectionResult = inspectionResult;
        mNaviErrorId = naviErrorId;
    }

    public boolean isInspectSuccess() {
        boolean inspectionSuccess = mInspectSuccess;
        mInspectSuccess = true;
        return inspectionSuccess;
    }

    public String getInspectionResult() {
        String inspectionResult = mInspectionResult;
        mInspectionResult = "";
        return inspectionResult;
    }

    public String getNaviErrorId() {
        String naviErrorId = mNaviErrorId;
        mNaviErrorId = "";
        return naviErrorId;
    }

    private void naviCmdTimeOutReport() {
        Log.d(TAG, "nviCmdTimeOutReport");
        long timestamp = System.currentTimeMillis();
        String cacheId = RobotSettings.getSystemSn() + "-" + timestamp;
        SystemApi.getInstance().naviTimeOutCmdReport(0, timestamp, cacheId,
                Definition.TYPE_ACTION_CHARGE_PILE_ESTIMATE_FAILUE, "ChargePileEstimateFail", new CommandListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.i(TAG, "nviCmdTimeOutReport: " + result + " message:" + message);
                    }
                });
    }

    private void registerNaviToGoalExcpListener() {
        SystemApi.getInstance().registerStatusListener(Definition.STATUS_NAVI_TO_GOAL_EXCEPTION,
                new StatusListener() {
                    @Override
                    public void onStatusUpdate(String type, String data) {
//                        Log.d(TAG, "onStatusUpdate type: " + type + ", data: " + data);
                        if (!Boolean.parseBoolean(data)){
                            ToastUtil.showToast(ApplicationWrapper.getContext(), ResUtil.getString(R.string.navi_to_goal_exception));
                        }
                    }

                });
    }

}
