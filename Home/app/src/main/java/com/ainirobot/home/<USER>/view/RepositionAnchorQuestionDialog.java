package com.ainirobot.home.ui.view;

import android.app.AlertDialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.TextView;

import com.ainirobot.home.R;

public class RepositionAnchorQuestionDialog extends AlertDialog implements View.OnClickListener {

    private TextView mConfirm;

    public RepositionAnchorQuestionDialog(Context context) {
        super(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.reposition_anchor_question_dialog);
        WindowManager.LayoutParams p = this.getWindow().getAttributes();
        p.width = 940;
//        p.height = 875;
        this.getWindow().setAttributes(p);
        setCanceledOnTouchOutside(false);

        initView();
    }

    private void initView() {
        mConfirm = (TextView) findViewById(R.id.anchor_ensure);
        mConfirm.setOnClickListener(this);
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.anchor_ensure:
                dismiss();
                break;

        }
    }
}
