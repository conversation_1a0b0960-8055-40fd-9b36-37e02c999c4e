/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.home.ui.fragment;

import android.app.Activity;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Message;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.bean.QrCodeBean;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.net.INetConnectionChangeListener;
import com.ainirobot.home.net.NetManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.view.CloudDialog;
import com.ainirobot.home.utils.CloudUtils;
import com.ainirobot.home.utils.ResUtil;
import com.ainirobot.home.utils.SystemUtils;
import com.ainirobot.home.utils.WeakHandler;
import com.ainirobot.home.utils.WifiUtils;

import org.json.JSONException;
import org.json.JSONObject;


/**
 * 仅使用绑定码激活的页面处理.
 */
public class BindCodeFragment extends BaseFragment implements INetConnectionChangeListener, View.OnClickListener {

    private static final String TAG = "BindCodeFragment:Home";
    private TextView mQrCodeLoading, mBindCodeTx;
    private MyWeakHandler mHandler;
    private View mCloudServerContainer;
    private TextView mQrCodeRetry;
    private TextView mSnNumberTx;
    private CloudDialog mCloudDialog = null;
    private String mUrl, mCodePC;

    private boolean mIsNeedRequestQR = false; // is has request qrcode;
    private long mExspireTime;
    private TextView mCurrentCloudServer;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mHandler = new MyWeakHandler(getActivity());
    }


    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_bind_code, null);
        mCloudServerContainer = view.findViewById(R.id.cloud_server_container);
        mQrCodeLoading = (TextView) view.findViewById(R.id.qr_code_loading);
        mQrCodeRetry = (TextView) view.findViewById(R.id.qr_code_load_retry);
        mQrCodeRetry.setOnClickListener(this);
        mBindCodeTx = (TextView) view.findViewById(R.id.bind_code_tx);
        mSnNumberTx = (TextView) view.findViewById(R.id.sn_number_tx);
        initCloudServer(view);
        return view;
    }


    @Override
    public void onStart() {
        super.onStart();
        Log.d(TAG, "QRcodeFragment onStart showLoading");
        showLoading();
        requestBindCode();
        NetManager.getInstance().registerNetReceiver();
    }


    public void setQrCodeView(QrCodeBean bean) {
        Log.d(TAG, "QRcodeFragment bean = " + bean);
        if (bean == null || TextUtils.isEmpty(bean.getData())) {
            Log.e(TAG, "qrUrl is Null ====");
            mIsNeedRequestQR = true;
            showQrCodeFail();
            return;
        }
        try {
            String decodeStr = new String(Base64.decode(bean.getData(), Base64.DEFAULT));
            mUrl = new JSONObject(decodeStr).optString("wx_url");
            mExspireTime = bean.getExspireTime();
            mCodePC = bean.getBindCode();
            Message.obtain(mHandler, MSG_QRCODE_COMPLETE).sendToTarget();
            startInspireIn();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }


    private void showQrCodeSuccess() {
        Log.d(TAG, "showQrCodeSuccess ...");
        mCloudServerContainer.setVisibility(View.VISIBLE);
        mQrCodeRetry.setVisibility(View.GONE);
        mQrCodeLoading.setText(SystemUtils.isUsingBindCode() ? "" : ResUtil.getString(R.string.wx_qr_code_loading_success));
        mBindCodeTx.setText(mCodePC);
        mSnNumberTx.setText(SystemUtils.getLast6SystemSerialNo());
    }

    private void showQrCodeFail() {
        Log.d(TAG, "showQrCodeFail ...");
        mCloudServerContainer.setVisibility(View.GONE);
        mQrCodeRetry.setVisibility(View.VISIBLE);
        mQrCodeLoading.setText(ResUtil.getString(R.string.wx_qr_code_loading_fail));
        mQrCodeRetry.setText(ResUtil.getString(R.string.wx_qr_code_load_retry));
    }

    private void showLoading() {
        Log.d(TAG, "showLoading ...");
        mCloudServerContainer.setVisibility(View.VISIBLE);
        mQrCodeRetry.setVisibility(View.GONE);
        mQrCodeLoading.setVisibility(View.VISIBLE);
        mQrCodeLoading.setText(SystemUtils.isUsingBindCode() ? "" : ResUtil.getString(R.string.wx_qr_code_loading));
    }

    private void showExpiresInUI() {
        Log.d(TAG, "showExpiresInUI ...");
        if (SystemUtils.isUsingBindCode()) {
            mBindCodeTx.setText("");
            mSnNumberTx.setText("");
        }
        mQrCodeLoading.setVisibility(View.VISIBLE);
        mQrCodeLoading.setText(SystemUtils.isUsingBindCode() ? ResUtil.getString(R.string.binding_code_expired) :
                ResUtil.getString(R.string.wx_qr_code_load_retry_again_des));
        mCloudServerContainer.setVisibility(View.GONE);
        mQrCodeRetry.setVisibility(View.VISIBLE);
        mQrCodeRetry.setTextColor(Color.WHITE);
        mQrCodeRetry.setText(ResUtil.getString(R.string.wx_qr_code_load_retry_again));
    }

    private void initCloudServer(View view) {
        mCurrentCloudServer = (TextView) view.findViewById(R.id.current_cloud_server);
        TextView notThisServer = (TextView) view.findViewById(R.id.not_this);
        notThisServer.setOnClickListener(this);
        if (SystemUtils.isUsingBindCode()) { //
            mCurrentCloudServer.setText(ResUtil.getString(R.string.cloud_current, getCurrentServer()));
        } else {
            mCurrentCloudServer.setVisibility(View.GONE);
            notThisServer.setVisibility(View.GONE);
        }
    }

    /**
     * 云服务节点选择弹窗
     */
    private void showCloudServerDialog() {
        mCloudDialog = new CloudDialog(getActivity());
        mCloudDialog.setDialogClickListener(new CloudDialog.ClickListener() {
            @Override
            public void onConfirmClick() {
                mCurrentCloudServer.setText(ResUtil.getString(R.string.cloud_current, getCurrentServer()));
            }

            @Override
            public void onCancelClick() {

            }
        });
        mCloudDialog.show();
    }


    /**
     * 获取当前云服务节点名称
     *
     * @return
     */
    private String getCurrentServer() {
        String currentServer = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_SETTING_CLOUD_SERVER_CODE);
        Log.d(TAG, "getCurrentServer: " + currentServer);
        return CloudUtils.getServerResName(currentServer);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.qr_code_load_retry:
                showLoading();
                requestBindCode();
                break;
            case R.id.not_this:
                //显示切换服务器弹框
                showCloudServerDialog();
                break;
        }
    }

    private static final int MSG_QRCODE_COMPLETE = 0x003;
    private static final int MSG_EXPIRES_IN_TIME = 0x004;

    class MyWeakHandler extends WeakHandler {

        public MyWeakHandler(Activity ref) {
            super(ref);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (conflict())
                return;
            switch (msg.what) {
                case MSG_EXPIRES_IN_TIME:
                    showExpiresInUI();
                    break;
                case MSG_QRCODE_COMPLETE:
                    showQrCodeSuccess();
                    break;
            }
        }
    }

    private void requestBindCode() {
        Log.d(TAG, "requestBindCode");
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_REQUEST_QR_CODE);
    }

    private void checkBindState() {
        Log.d(TAG, "checkBindState");
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_GET_REMOTE_BIND_STATUS);
    }

    /**
     * stop loop user's wx  bind state
     */
    private void stopLoopBindState() {
        if (mHandler != null) {
            mHandler.removeMessages(MSG_EXPIRES_IN_TIME);
        }
    }

    private void startInspireIn() {
        if (mHandler != null) {
            mHandler.sendEmptyMessageDelayed(MSG_EXPIRES_IN_TIME, mExspireTime * Definition.SECOND);
        }
        checkBindState();
    }

    @Override
    public void onConnectionChanged(int state) {
        Log.d(TAG, "onConnectionChanged ==== ");
        if (WifiUtils.isConnected(ApplicationWrapper.getContext())) {
            // 再次请求二维码
            if (mIsNeedRequestQR) {
                Log.d(TAG, "onConnectionChanged  msg_qrcode_request_again");
                ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_NET_CONNECTED);
            }
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mHandler != null) {
            mHandler.removeMessages(MSG_QRCODE_COMPLETE);
            mHandler.removeMessages(MSG_EXPIRES_IN_TIME);
        }
        if (mCloudDialog != null && mCloudDialog.isShowing()) {
            mCloudDialog.dismiss();
        }
        NetManager.getInstance().unRegisterNetReceiver();
        Log.d(TAG, "QRcodeFragment onDestroy ");
    }

    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
        Log.d(TAG, "onMessage : " + type + ", " + message);

        switch (type) {
            case MSG_QR_CODE:
                QrCodeBean bean = mGson.fromJson(message, QrCodeBean.class);
                setQrCodeView(bean);
                break;
            case MSG_QR_BIND_SUCCESS:
                stopLoopBindState();
                break;
        }
    }

}
