package com.ainirobot.home.ota.service;

import static com.ainirobot.coreservice.client.Definition.JSON_OTA_INSTALL_PERCENT;
import static com.ainirobot.coreservice.client.Definition.JSON_OTA_IS_DOWNLOAD_PROGRESS;
import static com.ainirobot.coreservice.client.Definition.JSON_OTA_TARGET_DESCRITION;
import static com.ainirobot.coreservice.client.Definition.JSON_OTA_TARGET_VERSION;
import static com.ainirobot.coreservice.client.Definition.JSON_OTA_TO_VERSION_ID;
import static com.ainirobot.home.ota.bean.VersionData.STATUS.FAILED;
import static com.ainirobot.home.ota.bean.VersionData.STATUS.NA;
import static com.ainirobot.home.ota.bean.VersionData.STATUS.SUCCESS;
import static com.ainirobot.home.ota.bean.VersionData.STATUS.WAITING;
import static com.ainirobot.home.ota.constants.DowngradeConstants.KEY_START_COMMAND;
import static com.ainirobot.home.ota.constants.DowngradeConstants.MSG_DOWNLOAD_SUCCESS;
import static com.ainirobot.home.ota.constants.DowngradeConstants.MSG_FILE_DOWNLOAD_DONE;
import static com.ainirobot.home.ota.constants.DowngradeConstants.MSG_FILE_DOWNLOAD_FAILED;
import static com.ainirobot.home.ota.constants.DowngradeConstants.MSG_INTERNAL_ERROR;
import static com.ainirobot.home.ota.constants.DowngradeConstants.MSG_MD5_CHECK_FAILED;
import static com.ainirobot.home.ota.constants.DowngradeConstants.MSG_NOT_EXIST;
import static com.ainirobot.home.ota.constants.DowngradeConstants.MSG_NO_UPDATE;
import static com.ainirobot.home.ota.constants.DowngradeConstants.MSG_SUB_FILE_MD5_CHECK_FAILED;
import static com.ainirobot.home.ota.constants.DowngradeConstants.MSG_UNZIP_FAILED;
import static com.ainirobot.home.ota.constants.DowngradeConstants.MSG_UPDATE_FAILED;
import static com.ainirobot.home.ota.constants.DowngradeConstants.MSG_UPDATE_SUCCESS;
import static com.ainirobot.home.ota.constants.DowngradeConstants.MSG_UPDATE_UPDATING;
import static com.ainirobot.home.ota.constants.DowngradeConstants.START_COMMAND_DOWNLOAD;
import static com.ainirobot.home.ota.constants.DowngradeConstants.START_COMMAND_INSTALL;
import static com.ainirobot.home.ota.constants.DowngradeConstants.START_COMMAND_STOP_SERVICE;
import static com.ainirobot.home.ota.constants.OtaConstants.DOWNLOAD_PATH;
import static com.ainirobot.home.ota.constants.OtaConstants.Error.ERR_FILE_DOWNLOAD_FAILED;
import static com.ainirobot.home.ota.constants.OtaConstants.Error.ERR_INTERNAL_ERROR;
import static com.ainirobot.home.ota.constants.OtaConstants.Error.ERR_MD5_CHECK_FAILED;
import static com.ainirobot.home.ota.constants.OtaConstants.Error.ERR_PACKAGE_NOT_EXIST;
import static com.ainirobot.home.ota.constants.OtaConstants.Error.ERR_SUB_FILE_MD5_CHECK_FAILED;
import static com.ainirobot.home.ota.constants.OtaConstants.Error.ERR_UNZIP_FAILED;
import static com.ainirobot.home.ota.constants.OtaConstants.HOST_VERSION;
import static com.ainirobot.home.ota.constants.OtaConstants.LXC_DOWNLOAD_PATH;
import static com.ainirobot.home.ota.constants.OtaConstants.LXC_INSTALL_SUCCESS_CHECK_FILE;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_HOST;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_LIST;
import static com.ainirobot.home.ota.constants.OtaConstants.OTA_INSTALL_PATH;
import static com.ainirobot.home.ota.constants.OtaConstants.OTA_LXC_INSTALL_PATH;
import static com.ainirobot.home.ota.network.NetDefine.FULL_PACKAGE;
import static com.ainirobot.home.ota.network.NetDefine.OTA_INSTALL_PROGRESS.OTA_INSTALL_DEFAULT;
import static com.ainirobot.home.ota.network.NetDefine.OTA_STATUS.OTA_FAILED;
import static com.ainirobot.home.ota.network.NetDefine.OTA_STATUS.OTA_PRE_START;
import static com.ainirobot.home.ota.network.NetDefine.OTA_STATUS.OTA_START;
import static com.ainirobot.home.ota.network.NetDefine.OTA_STATUS.OTA_SUCCESS;
import static com.ainirobot.home.ota.network.NetDefine.OTA_TYPE.DOWNLOAD_DIFF_PACKAGE;
import static com.ainirobot.home.ota.network.NetDefine.OTA_TYPE.DOWNLOAD_FULL_PACKAGE;
import static com.ainirobot.home.ota.network.NetDefine.OTA_TYPE.OTA_INSTALLING;
import static com.ainirobot.home.ota.network.NetDefine.RESULT_DESC;
import static com.ainirobot.home.ota.network.NetDefine.RESULT_FORCED_UPDATE;
import static com.ainirobot.home.ota.network.NetDefine.RESULT_HASH_DIFF;
import static com.ainirobot.home.ota.network.NetDefine.RESULT_HASH_FULL;
import static com.ainirobot.home.ota.network.NetDefine.RESULT_SUB_STATUS;
import static com.ainirobot.home.ota.network.NetDefine.RESULT_URL_DIFF;
import static com.ainirobot.home.ota.network.NetDefine.RESULT_URL_FULL;
import static com.ainirobot.home.ota.network.NetDefine.RESULT_VERSION;
import static com.ainirobot.home.ota.network.NetDefine.RESULT_VERSION_ID;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.os.Process;
import android.os.SystemClock;
import android.os.SystemProperties;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.config.ServiceConfig;
import com.ainirobot.home.ota.bean.DowngradeParams;
import com.ainirobot.home.ota.bean.OtaMessage;
import com.ainirobot.home.ota.bean.VersionData;
import com.ainirobot.home.ota.bi.BiOtaReport;
import com.ainirobot.home.ota.config.OtaConfig;
import com.ainirobot.home.ota.constants.DowngradeConstants;
import com.ainirobot.home.ota.constants.OtaConstants;
import com.ainirobot.home.ota.database.DataBaseManager;
import com.ainirobot.home.ota.executor.ThreadPoolManager;
import com.ainirobot.home.ota.httpclient.RequestListener;
import com.ainirobot.home.ota.httpclient.Response;
import com.ainirobot.home.ota.network.NetDefine;
import com.ainirobot.home.ota.network.NetHelper;
import com.ainirobot.home.ota.parser.IParserInterface;
import com.ainirobot.home.ota.parser.VersionPullParser;
import com.ainirobot.home.ota.task.DownloadFileTask;
import com.ainirobot.home.ota.task.DownloadTaskListener;
import com.ainirobot.home.ota.task.OtaTaskFactory;
import com.ainirobot.home.ota.utils.FileUtils;
import com.ainirobot.home.ota.utils.MD5;
import com.ainirobot.home.ota.utils.MessageGenerater;
import com.ainirobot.home.ota.utils.Preferences;
import com.ainirobot.home.ota.utils.SettingsUtils;
import com.ainirobot.home.ota.utils.Utils;
import com.ainirobot.home.ota.utils.Scp;
import com.google.gson.GsonBuilder;
import com.google.gson.InstanceCreator;
import com.google.gson.JsonObject;
import com.liulishuo.filedownloader.BaseDownloadTask;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.lang.reflect.Type;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicInteger;

public class DowngradeManager {
    private static final String TAG = DowngradeManager.class.getSimpleName();
    private AtomicInteger downloadReTryCount = new AtomicInteger(0);
    private static final int TOTAL_TRY = 2; // 最多尝试两次降级
    private Context mContext;
    @SuppressLint("StaticFieldLeak")
    private static volatile DowngradeManager instance;
    private NetHelper mNetHelper;
    private OtaConfig mOtaConfig;
    private DataBaseManager mDataBaseManager;
    private Preferences mPreference;
    private IParserInterface mUpdateConfigParser;
    private ThreadPoolManager mThreadPoolManager;
    private Scp mScpHelper;
    private OtaApiHelper mOtaApiHelper;
    /**
     * 降级任务
     */
    private OtaTaskFactory mOtaTaskFactory;
    private DownloadFileTask mDownloadTask; // 下载任务

    private boolean isHeadConnected = true;

    //Battery info.
    private int mBatteryLevel;
    /**
     * (线程)工作状态标记位
     */
    private volatile boolean checkNewVersionThreadRunning = false;
    private volatile boolean installThreadRunning = false;
    private volatile static boolean checkThreadRunning = false;
    private volatile static boolean fileCheckInstallRunning = false;

    private static final HandlerThread mWorkLooper = new HandlerThread("workLooper",
            Process.THREAD_PRIORITY_FOREGROUND);

    static {
        mWorkLooper.start();
    }

    private final OtaApiHelper.OnOtaStartListener otaStartListener =
        new OtaApiHelper.OnOtaStartListener() {

            @Override
            public boolean onOtaStart(boolean needDownload) {
                Log.d(TAG, "on ota start. needDownload: " + needDownload);
                if (needDownload) {
                    startCommand(START_COMMAND_DOWNLOAD);
                } else {
                    startCommand(START_COMMAND_INSTALL);
                    mPreference.setDiffUpdatePackageType(FULL_PACKAGE);
                }
                return true;
            }

            @Override
            public boolean onOtaRollbackStart() {
                Log.d(TAG, "on ota rollback start.");
                return true;
            }

            @Override
            public String onOtaGetDescription() {
                JSONObject jsonObject = new JSONObject();

                try {
                    String serverTargetVersion = mPreference.getServerTargetVersion();
                    jsonObject.put(JSON_OTA_TARGET_VERSION, serverTargetVersion);
                    String description = mPreference.getPackageDescriptor();
                    jsonObject.put(JSON_OTA_TARGET_DESCRITION, description);
                    String versionId = mPreference.getVersionId();
                    jsonObject.put(JSON_OTA_TO_VERSION_ID, versionId);
                    Log.d(TAG, "onOtaGetDescription result: " + jsonObject.toString());
                } catch (JSONException e) {
                    Log.d(TAG, "onOtaGetDescription error: " + e.toString());
                    e.printStackTrace();
                    return null;
                }

                return jsonObject.toString();
            }

            @Override
            public boolean onOtaCancelDownload() {
                // pause download task
                Log.d(TAG, "stop download.");
                mDownloadTask.stop();

                uploadUpdateStatus(DOWNLOAD_DIFF_PACKAGE, OTA_FAILED,
                        "full package download pause");
                return true;
            }

            @Override
            public void installPatch(Bundle bundle) {
            }

            @Override
            public void onOtaInterrupted(String reason) {
            }

            @Override
            public void OnOtaServiceConfig(List<ServiceConfig> serviceConfigs) {
                if (serviceConfigs == null || serviceConfigs.isEmpty()) {
                    Log.d(TAG, "ota service config is empty.");
                    return;
                }

                JsonObject jsonObject = serviceConfigs.get(0).getConfig();
                new GsonBuilder().registerTypeAdapter(OtaConfig.class, new InstanceCreator<OtaConfig>() {
                    @Override
                    public OtaConfig createInstance(Type type) {
                        return mOtaConfig;
                    }
                }).create().fromJson(jsonObject, OtaConfig.class);
                if(mOtaConfig != null){
                    mOtaConfig.processServerZoneData();
                    Log.d(TAG, "ota config is " + mOtaConfig.toString());
                }
            }

            @Override
            public void updateHeadConnectStatus(boolean isConnected) {
                isHeadConnected = isConnected;
            }
        };

    private Handler mMainHandler = new Handler(mWorkLooper.getLooper()) {
        @Override
        public void handleMessage(Message msg) {
            Log.d(TAG, "handle message = " + msg.what);
            String downloadType = mDownloadTask.getDownloadType();
            switch (msg.what) {
                case MSG_NO_UPDATE:
                    Log.d(TAG, "no update downgrade");
                    sendOtaFinishMessage(new OtaMessage(Definition.JSON_OTA_RESULT_NO_UPDATE,
                            false, Definition.JSON_OTA_TYPE_NORMAL,
                            OtaConstants.Error.NO_ERR, null, false));
                    break;
                case MSG_FILE_DOWNLOAD_DONE:
                    Log.i(TAG, "download finished. start checking");
                    uploadUpdateStatus(DOWNLOAD_FULL_PACKAGE, OTA_SUCCESS, DowngradeConstants.UPLOAD_STATUS_DESC_DOWNLOAD_SUCCESS);
                    // 检测MD5，确保下载文件完整性
                    String md5 = mPreference.getFullUpdatePackageMd5();
                    if (!MD5.checkMd5(md5, DOWNLOAD_PATH)) {
                        File file = new File(DOWNLOAD_PATH);
                        boolean ret = file.delete();
                        Log.d(TAG, "delete file:" + file.getName() + " result:" + ret);
                        mMainHandler.sendEmptyMessage(MSG_MD5_CHECK_FAILED);
                        return;
                    }
                    Log.d(TAG, "download finished. downloadType: " + downloadType);
                    if(mOtaApiHelper.sendOtaDownloadSuccess("download success")
                            == Definition.CMD_SEND_ERROR_UNKNOWN) {
                        Log.e(TAG, "sendOtaDownloadSuccess error. got -1");
                    }
                    break;

                case MSG_NOT_EXIST:
                    Log.e(TAG, DOWNLOAD_PATH + " not exist!");
                    uploadUpdateStatus(OTA_INSTALLING, OTA_FAILED, "full package not exist");
                    sendOtaFinishMessage(new OtaMessage(Definition.JSON_OTA_RESULT_FAILED, ERR_PACKAGE_NOT_EXIST));
                    break;

                case MSG_FILE_DOWNLOAD_FAILED:
                    Log.e(TAG, "update.zip download failed! downloadType: " + downloadType);
                    uploadUpdateStatus(DOWNLOAD_FULL_PACKAGE, OTA_FAILED, "full package download failed");
                    if (TextUtils.isEmpty(downloadType) ||
                            downloadType.equals(OtaConstants.DOWNLOAD_FILE_TYPE_NORMAL)) {
                        sendOtaFinishMessage(new OtaMessage(Definition.JSON_OTA_RESULT_FAILED, ERR_FILE_DOWNLOAD_FAILED));
                    }
                    mDownloadTask.clearAll();
                    break;

                case MSG_UNZIP_FAILED:
                    Log.e(TAG, "unzip " + DOWNLOAD_PATH + " failed!");
                    uploadUpdateStatus(OTA_INSTALLING, OTA_FAILED, "full package unzip failed");
                    sendOtaFinishMessage(new OtaMessage(Definition.JSON_OTA_RESULT_FAILED, ERR_UNZIP_FAILED));
                    break;

                case MSG_MD5_CHECK_FAILED:
                    Log.e(TAG, "update.zip md5 check failed! downloadType: " + downloadType);
                    uploadUpdateStatus(DOWNLOAD_FULL_PACKAGE, OTA_FAILED,
                            "full package md5 check failed");
                    if (downloadType == null || downloadType.isEmpty() ||
                            downloadType.equals(OtaConstants.DOWNLOAD_FILE_TYPE_NORMAL)) {
                        sendOtaFinishMessage(new OtaMessage(Definition.JSON_OTA_RESULT_FAILED, ERR_MD5_CHECK_FAILED));
                    }
                    mDownloadTask.clearAll();
                    break;

                case MSG_SUB_FILE_MD5_CHECK_FAILED:
                    Log.e(TAG, "update.zip is ok. but sub file md5 check failed!");
                    uploadUpdateStatus(OTA_INSTALLING, OTA_FAILED,
                            "full package sub file md5 check failed");
                    sendOtaFinishMessage(new OtaMessage(Definition.JSON_OTA_RESULT_FAILED,
                            ERR_SUB_FILE_MD5_CHECK_FAILED));
                    break;

                case MSG_INTERNAL_ERROR:
                    Log.e(TAG, "update got internal error!");
                    uploadUpdateStatus(OTA_INSTALLING, OTA_FAILED,
                            "full package install got internal error");
                    sendOtaFinishMessage(new OtaMessage(Definition.JSON_OTA_RESULT_FAILED, ERR_INTERNAL_ERROR));
                    break;

                case MSG_UPDATE_SUCCESS:
                    Log.e(TAG, "update success. download full update.zip");
                    OtaMessage otaMessage = MessageGenerater.
                            generateOtaFinishMessage(mDataBaseManager, false);
                    sendOtaFinishMessage(otaMessage);
                    break;

                case MSG_UPDATE_UPDATING:
                    Log.e(TAG, "update updating.");
                    sendOtaFinishMessage(new OtaMessage(Definition.JSON_OTA_RESULT_CONTINUE,
                            false, Definition.JSON_OTA_TYPE_NORMAL,
                            OtaConstants.Error.NO_ERR, null, false));
                    checkThreadRunning = false;
                    break;

                case MSG_UPDATE_FAILED:
                    Log.e(TAG, "update failed.");
                    sendOtaFinishMessage(MessageGenerater.
                            generateOtaFinishMessage(mDataBaseManager, false));
                    clearDownloadFile();
                    checkThreadRunning = false;
                    break;
                default:
                    break;
            }
        }
    };

    /**
     * full update package download task listener.
     */
    private final DownloadTaskListener mDownloadListener = new DownloadTaskListener() {
        int prePercent = 0;

        @Override
        public void onProgress(BaseDownloadTask baseDownloadTask, int soFarBytes, int totalBytes) {
            Locale enLocale  = new Locale("en", "US");
            DecimalFormat decimalFormat = (DecimalFormat) NumberFormat.getNumberInstance(enLocale);
            decimalFormat.applyPattern("#.##");

            String perStr = decimalFormat.format((float) soFarBytes / totalBytes);
            int percent = (int) (Float.parseFloat(perStr) * 100);

            resetRetryCount();

            if (percent > prePercent) {
                prePercent = percent;
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put(JSON_OTA_IS_DOWNLOAD_PROGRESS, true);
                    jsonObject.put(JSON_OTA_INSTALL_PERCENT, perStr);
                    Log.d(TAG, " data:" + jsonObject);
                    mOtaApiHelper.updateProgress(jsonObject.toString());

                    Log.d(TAG, "下载进度：" + percent);
                } catch (JSONException e) {
                    e.printStackTrace();
                }

                if (soFarBytes == totalBytes) {
                    prePercent = 0;
                }
            }
        }

        @Override
        public void completed(BaseDownloadTask baseDownloadTask) {
            // start OtaModule install page.
            Log.d(TAG, "full package download completed. start check.");
            mOtaApiHelper.updateProgress(MessageGenerater.generateProgressMessage(0, 1));
            mMainHandler.sendEmptyMessage(MSG_FILE_DOWNLOAD_DONE);
            prePercent = 0;
            incrementRetryCount();
        }

        @Override
        public void error(BaseDownloadTask baseDownloadTask, Throwable throwable) {
            synchronized (this) {
                Log.d(TAG, "download count= " + downloadReTryCount);
                prePercent = 0;

                if (getRetryCount() < TOTAL_TRY) {
                    Log.d(TAG, "download error, retry " + downloadReTryCount + ", downloadType: " + mDownloadTask.getDownloadType());
                    incrementRetryCount();
                    //retry download task right now.
                    Intent mNIntent = new Intent(mContext, DowngradeService.class);
                    mNIntent.putExtra(KEY_START_COMMAND, START_COMMAND_DOWNLOAD);
                    mNIntent.putExtra(OtaConstants.OTA_EXTRA_DOWNLOAD_TYPE, mDownloadTask.getDownloadType());
                    mContext.startService(mNIntent);
                } else {
                    Log.d(TAG, "Has retry " + downloadReTryCount + ". Reset error try count.");
                    resetRetryCount();

                    mMainHandler.sendEmptyMessage(MSG_FILE_DOWNLOAD_FAILED);
                }
            }
        }
    };

    public static DowngradeManager getInstance(Context context) {
        synchronized (DowngradeManager.class) {
            if (instance == null) {
                instance = new DowngradeManager(context);
            }
        }
        return instance;
    }

    private DowngradeManager(Context context) {
        mContext = context;

        mOtaConfig = OtaConfig.getInstance(); // 升级服务实例
        mDataBaseManager = new DataBaseManager(mContext);
        mPreference = Preferences.getInstance();
        mThreadPoolManager = ThreadPoolManager.getInstance(); // 线程池实例初始化
        mUpdateConfigParser = new VersionPullParser();
        mOtaApiHelper = new OtaApiHelper(mContext, otaStartListener);
        mNetHelper = NetHelper.getInstance();
        mScpHelper = Scp.getInstance();

        mOtaTaskFactory = new OtaTaskFactory(mContext, mPreference, mNetHelper, mOtaApiHelper,
            mDataBaseManager, mScpHelper, mMainHandler, mUpdateConfigParser, mOtaTaskFactory, mOtaConfig, mThreadPoolManager);

        mDownloadTask = new DownloadFileTask(mDownloadListener, mMainHandler,"");

        // update host result
        final String shutdownAction = SystemProperties.get(
                "sys.shutdown.requested", "");
        if (shutdownAction == null || shutdownAction.isEmpty()) {
            checkAndUpdateHostResult();
        }

        Log.d(TAG, "DowngradeManager init done. shutdownAction: " + shutdownAction);
    }

    private void startCommand(int command) {
        Intent mNIntent = new Intent(mContext, DowngradeService.class);
        mNIntent.putExtra(KEY_START_COMMAND, command);
        mContext.startService(mNIntent);
    }

    public synchronized void startCheck(final boolean userUpdate) {
        Log.d(TAG, "startCheck. checkThreadRunning=" + checkThreadRunning);
        if (!checkThreadRunning) {
            checkThreadRunning = true;
            mThreadPoolManager.submitCachedTask((new Runnable() {
                @Override
                public void run() {
                    Log.d(TAG, "startCheck thread " + Thread.currentThread().getName());

                    if (installThreadRunning) {
                        Log.d(TAG, "install task is running. skip this check");
                        checkThreadRunning = false;
                        return;
                    }

                    // 检查ota安装结果
                    int ret = checkIfInstallingSuccess();
                    Log.d(TAG, "Install result=" + ret);

                    if (ret == 0) {
                        mMainHandler.sendEmptyMessage(MSG_NO_UPDATE);
                        checkThreadRunning = false;
                        if (mOtaConfig.isBootUpdateEnabled()) {
                            startFileCheck();
                        }
                        return;
                    }

                    if (ret == 1) {
                        Log.d(TAG, "Install success.");
                        mMainHandler.sendEmptyMessage(MSG_UPDATE_SUCCESS);
                        return;
                    }

                    if (ret == -1) {
                        Log.d(TAG, "Install is failed.");
                        mMainHandler.sendEmptyMessage(MSG_UPDATE_FAILED);
                    } else if (ret == 2) {
                        Log.d(TAG, "Continue installing");
                        mMainHandler.sendEmptyMessage(MSG_UPDATE_UPDATING);
                    }
                }
            }));
        } else {
            Log.d(TAG, "checkThread already run. won't start new check.");
        }
    }

    /**
     * 检查是否有可降级的版本
     */
    public synchronized void startCheckNewVersion(String startMode) {
        if (checkNewVersionThreadRunning) {
            Log.d(TAG, "checkNewVersion thread already run. don't start new check");
            Log.d(TAG, "正在检测新版本");
            return;
        }

        if (installThreadRunning) {
            Log.d(TAG, "install task is running. skip ths new version check");
            return;
        }

        if (!isNetworkConnected(mContext)) {
            Log.d(TAG, "No network, don't start new version check.");
            checkNewVersionThreadRunning = false;
            sendStickyBroadcastToAboutRobot();
            return;
        }

        checkNewVersionThreadRunning = true;

        // 开始下载全量包
        mThreadPoolManager.submitCachedTask(new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "startCheckNewVersion thread " + Thread.currentThread().getName());
                // 设置开启降级任务的启动模式，为了后续其他触发方式扩展
                setStartingMode(startMode);
                // 降级实体类
                DowngradeParams downgradeParams = new DowngradeParams(mContext);
                downgradeParams.setRomGoBack(); // 降级版本新增rom_goback类型
                mNetHelper.checkNewVersion(downgradeParams, mOtaCheckNewVersionListener);
            }
        });
    }

    private RequestListener mOtaCheckNewVersionListener = new RequestListener() {
        @Override
        public void onSuccess(Response response) {
            // parse result
            String result = response.getContentString();
            Log.d(TAG, "check new version responseString:" + result);
            try {
                JSONObject jsonObject = new JSONObject(result);
                int code = jsonObject.getInt("code");
                String dataStr = jsonObject.getString("data");
                switch (code) {
                    case 200:
                        if (dataStr != null && !dataStr.isEmpty()) {
                            JSONObject jsonData = new JSONObject(dataStr);
                            JSONArray otaList = jsonData.getJSONArray("ota_list");
                            if(otaList != null && otaList.length() > 0) {
                                //分析ota_list数据，根据component类型，进行对应升级操作
                                if(isRomUpdate(otaList)){
                                    //rom升级处理
                                    JSONObject romUpdateInfo = getRomUpdateInfo(otaList);
                                    handleRomUpdateInfo(romUpdateInfo);
                                } else {
                                    Log.d(TAG, "没有找到rom(main)类型.");
                                    sendStickyBroadcastToAboutRobot();
                                    resetRetryCount();
                                }
                            }else{
                                Log.d(TAG,"服务端返回otaList结果为空.");
                                sendStickyBroadcastToAboutRobot();
                                resetRetryCount();
                            }
                        } else {
                            Log.d(TAG,"服务端返回结果为空.");
                            sendStickyBroadcastToAboutRobot();
                            resetRetryCount();
                        }
                        break;
                    case 201:
                        resetRetryCount();
                        sendStickyBroadcastToAboutRobot();
                        Log.d(TAG,"未检测到新版本.");
                        break;
                    case 400:
                        resetRetryCount();
                        sendStickyBroadcastToAboutRobot();
                        Log.d(TAG,"服务端参数错误");
                        break;
                    case 405:
                        resetRetryCount();
                        sendStickyBroadcastToAboutRobot();
                        Log.d(TAG,"当前版本号错误.");
                        break;
                }
            } catch (Exception e) {
                Log.d(TAG, "check new version get exception. just quit.");
                Log.d(TAG,"服务端返回结果解析失败.");
                resetRetryCount();
                sendStickyBroadcastToAboutRobot();
                e.printStackTrace();
            } finally {
                Log.d(TAG, "check new version is finished. just quit.");
                checkNewVersionThreadRunning = false;
                startCommand(START_COMMAND_STOP_SERVICE);
            }
        }

        @Override
        public void onFailure(int errCode, String errMsg) {
            Log.d(TAG, "check new version failed. errCode:" + errCode + " errMsg:" + errMsg);

            resetRetryCount();

            checkNewVersionThreadRunning = false;
            sendStickyBroadcastToAboutRobot();

            Log.d(TAG,"检查新版本失败。errCode:" + errCode + " errMsg:" + errMsg);
            startCommand(START_COMMAND_STOP_SERVICE);
        }
    };


        /**
     * ota status listener
     */
    private final RequestListener mOtaStatusListener = new RequestListener() {
        @Override
        public void onSuccess(Response response) {
            Log.d(TAG, "uploadUpdateStatus. response=" + response);
        }

        @Override
        public void onFailure(int errCode, String errMsg) {
            Log.d(TAG, "uploadUpdateStatus. " + errCode + " " + errMsg);
        }
    };

    private void uploadUpdateStatus(NetDefine.OTA_TYPE type, NetDefine.OTA_STATUS status,
        String desc) {
        mNetHelper.uploadUpdateStatus(new DowngradeParams(mContext), type,
                mPreference.getServerTargetVersion(),
                mPreference.getBaseVersion(), mPreference.getVersionId(), status, desc,
                OTA_INSTALL_DEFAULT, mOtaStatusListener);
    }

    /**
     * 是否是ROM升级
     * @param otaList ota列表参数
     * @return true/false
     */
    private boolean isRomUpdate(JSONArray otaList){

        boolean result = false;
        String compoentName = "";

        if(otaList != null && otaList.length() > 0){

            for (int j = 0; j < otaList.length(); j++) {
                try {
                    JSONObject jsonObject = otaList.getJSONObject(j);
                    compoentName =  jsonObject.getString("component");
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                if(compoentName.equals("main")){
                    result = true;
                    break;
                }
            }
        }
        return result;
    }

    /**
     * 获取rom升级信息
     * @param otaList ota列表参数
     * @return true/false
     */
    private JSONObject getRomUpdateInfo(JSONArray otaList){

        JSONObject resultObject = null;
        String compoentName = "";

        if(otaList != null && otaList.length() > 0){

            for (int j = 0; j < otaList.length(); j++) {

                JSONObject jsonObject = null;
                try {
                    jsonObject = otaList.getJSONObject(j);
                    compoentName =  jsonObject.getString("component");
                } catch (JSONException e) {
                    Log.e(TAG, "getRomUpdateInfo error: " + e.getMessage());
                }
                if(compoentName.equals("main")){
                    resultObject = jsonObject;
                    break;
                }
            }
        }
        return resultObject;
    }

    private void handleRomUpdateInfo(JSONObject downgradeInfo) {
        Log.d(TAG,"handleRomUpdateInfo downgradeInfo: " + downgradeInfo.toString());
        String desc = null, targetVersion = "";
        String urlFull = "", hashFull = "", urlDiff = "", hashDiff = "";
        String forceUpdate = "";
        String versionId = "";
        int packType = -1;
        try {
            desc = downgradeInfo.getString(RESULT_DESC);
            targetVersion = downgradeInfo.getString(RESULT_VERSION);
            urlFull = downgradeInfo.getString(RESULT_URL_FULL);
            hashFull = downgradeInfo.getString(RESULT_HASH_FULL);
            urlDiff = downgradeInfo.getString(RESULT_URL_DIFF);
            hashDiff = downgradeInfo.getString(RESULT_HASH_DIFF);
            forceUpdate = downgradeInfo.getString(RESULT_FORCED_UPDATE);
            versionId = downgradeInfo.getString(RESULT_VERSION_ID);
            packType = downgradeInfo.getInt(RESULT_SUB_STATUS); //0：差分包 1：全量包 目前后端只有全量包下发
        } catch (JSONException e) {
            Log.e(TAG, "handleRomUpdateInfo parse downgradeInfo error: " + e.getMessage());
        }
        mPreference.setPackageDescriptor(desc);
        mPreference.setServerTargetVersion(targetVersion);
        mPreference.setBaseVersion(Utils.getSystemProperties("ro.product.releasenum", ""));
        mPreference.setFullUpdatePakcageURL(urlFull);
        mPreference.setFullUpdatePackageMd5(hashFull);
        mPreference.setDiffUpdatePackageURL(urlDiff);
        mPreference.setDiffUpdatePackageMd5(hashDiff);
        mPreference.setDiffUpdatePackageType(packType);
        if (mOtaConfig.isForceUpdateEnabled()) {
            mPreference.setForceUpdateSign("true".equals(forceUpdate));
        }
        mPreference.setVersionId(versionId);
        // 设置OTA版本类型, 目前只有rom
        mPreference.setComponentType(OtaConstants.COMPONENT_ROM);

        // 处理发送给core的数据
        boolean isForceUpdate = mPreference.getForceUpdateSign();
        JSONObject downgradeOtaEvent = new JSONObject();
        try {
            downgradeOtaEvent.put(Definition.JSON_OTA_FORCEUPATE, isForceUpdate);
            downgradeOtaEvent.put(Definition.JSON_OTA_IS_USER_TOUCH, true);// 一期目前都是用户主动触发
            downgradeOtaEvent.put(Definition.JSON_OTA_NEED_DOWNLOAD, true);
            downgradeOtaEvent.put(Definition.JSON_OTA_UPTIME, SystemClock.uptimeMillis());
        } catch (JSONException e) {
            Log.e(TAG, "handleRomUpdateInfo send json data to core error.");
        }
        // 调用sendDowngradeOtaEvent方法
        if (mOtaApiHelper.sendDowngradeOtaEvent(downgradeOtaEvent.toString()) == Definition.CMD_SEND_ERROR_UNKNOWN) {
            Log.e(TAG, "sendDowngradeOtaEvent send error. got internal error: " + MSG_INTERNAL_ERROR);
            sendStickyBroadcastToAboutRobot();
            mMainHandler.sendEmptyMessage(MSG_INTERNAL_ERROR);
        }
        Log.d(TAG, "检测到新版本.");
        // 这里需要删除已经存在的update.zip包和lxc包
        clearDownloadFile();
        clearDownloadLxcFile();
        clearLxcInstallFile();
        FileUtils.deleteFile(LXC_INSTALL_SUCCESS_CHECK_FILE);
    }

    /**
     * 根据ota类型下载对应包
     * @param downloadType ota下载类型 目前只有rom类型
     *
     */
    public synchronized void startComponentDownload(String downloadType){

        String componentType = Preferences.getInstance().getComponentType();
        Log.d(TAG,"startComponentDownload componentType: " + componentType);

        if(componentType.equals(OtaConstants.COMPONENT_ROM)){
            //rom包下载
            this.startDownload(downloadType);
        } else {
            Log.d(TAG,"download component type donot support");
        }
    }

    public synchronized void startDownload(String downloadType) {
        Log.d(TAG, "startDownload. downloadType: " + downloadType);

        if (checkNewVersionThreadRunning) {
            Log.d(TAG, "Ota is checking new version. skip the download");
            return;
        }

        if (installThreadRunning || checkIfInstalling() != 0) {
            Log.d(TAG, "Ota is installing. skip the download");
            return;
        }

        String downgradePackageUrl = mPreference.getFullUpdatePackageURL(); // 获取全量包下载地址
        if (downgradePackageUrl == null || downgradePackageUrl.isEmpty()) {
            Log.d(TAG, "url is null or empty. skip the download");
            return;
        }

        String currentDownloadType = mDownloadTask.getDownloadType();
        if (mDownloadTask.isRunning() && currentDownloadType.equals(downloadType)) {
            Log.d(TAG, "download task is running. skip the download");
            return;
        }


        String latestDownloadUrl = mPreference.getPrefsLastestDownloadUrl();
        if (latestDownloadUrl != null && !latestDownloadUrl.equals(downgradePackageUrl)) {
            mDownloadTask.clearAll();
        }

        mDownloadTask.setDownloadType(downloadType);
        mPreference.setPrefsLastestDownloadUrl(downgradePackageUrl);
        mDownloadTask.setUrl(downgradePackageUrl);
        mDownloadTask.setLocalPath(DOWNLOAD_PATH);
        mDownloadTask.start();

        uploadUpdateStatus(DOWNLOAD_FULL_PACKAGE, OTA_START, "full package download start");
    }

    public synchronized void pauseDownload(String downloadType) {
        Log.d(TAG, "pauseDownload.");
        String currentDownloadType = mDownloadTask.getDownloadType();
        if (currentDownloadType == null || currentDownloadType.isEmpty()) {
            Log.d(TAG, "can't pause download because there is no download .");
            return;
        }

        if (!currentDownloadType.equals(downloadType)) {
            Log.d(TAG, "can't pause download due to the different download type.");
            return;
        }

        mDownloadTask.pause();
    }

    public synchronized void continueDownload(String downloadType) {
        Log.d(TAG, "continueDownload.");
        String currentDownloadType = mDownloadTask.getDownloadType();
        if (currentDownloadType == null || currentDownloadType.isEmpty()) {
            Log.d(TAG, "can't continue download because there is no download.");
            return;
        }

        startDownload(downloadType);
    }

    public synchronized void startInstall() {
        Log.d(TAG, "startInstall. installThreadRunning=" + installThreadRunning);
        if (installThreadRunning) {
            Log.i(TAG, "installThread already run. won't start new task");
            return;
        }

        installThreadRunning = true;
        mPreference.setBaseVersion(Utils.getSystemProperties("ro.product.releasenum", ""));
        mThreadPoolManager.submitCachedTask(mOtaTaskFactory.createPreInstallThreadTask());
        uploadUpdateStatus(OTA_INSTALLING, OTA_PRE_START, selectedOsToString());
    }

    public synchronized void startInstallRom() {
        Log.d(TAG, "startInstall. installThreadRunning=" + installThreadRunning);
        if (installThreadRunning) {
            mThreadPoolManager.submitCachedTask(mOtaTaskFactory.createInstallThreadTask());
        }
    }

    public synchronized void startFileCheck() {
        Log.d(TAG, "start file checking");

        if (installThreadRunning || checkIfInstallingSuccess() != 0) {
            Log.d(TAG, "Ota is installing. skip the file check.");
            return;
        }

        if (checkThreadRunning) {
            Log.d(TAG, "Ota is checking. skip the file check.");
            return;
        }

        if (fileCheckInstallRunning) {
            Log.d(TAG, "file check is running. skip the file check.");
            return;
        }
        Log.d(TAG, "start file check thread");

        fileCheckInstallRunning = true;
        mDownloadTask.pause();
        mThreadPoolManager.submitCachedTask(mOtaTaskFactory.createFileCheckTask());
    }

    public synchronized void stopOtaService() {
        if (isThreadRunning() || isDownloadTaskRunning()) {
            Log.d(TAG, "ota task thread is running. skip the ota service stop");
            return;
        }

        Log.d(TAG, "stop ota downgrade service");
        //mThreadPoolManager.getCachedThreadPool().shutdownNow();

        mDownloadTask.stop();

        Intent intentSelf = new Intent(mContext, DowngradeService.class);
        mContext.stopService(intentSelf);
        Log.d(TAG, "stopOtaDowngradeService END");
    }

    private boolean isThreadRunning() {
        return installThreadRunning || checkNewVersionThreadRunning;
    }

    private boolean isDownloadTaskRunning() {
        return mDownloadTask.isRunning();
    }

    /**
     * 发送粘性广播到业务层
     */
    private void sendStickyBroadcastToAboutRobot() {
        Log.i(TAG, "sendStickyBroadcastToAboutRobot");
        Intent intent = new Intent();
        intent.setAction(DowngradeConstants.ACTION_NEW_VERSION_FOUND);
        mContext.sendStickyBroadcast(intent);
        // 这里要换成降级的event方法
        mOtaApiHelper.sendDowngradeOtaEvent("noUpdate");
    }

    /**
     * 检查host的更新结果并更新数据库
     */
    private void checkAndUpdateHostResult() {
        VersionData data = mDataBaseManager.getOsData(OS_HOST);
        boolean isOtaUpgrading = mPreference.getOtaIsUpgrading();

        String version = Utils.getSystemProperties(HOST_VERSION, "1.0.0");
        Log.d(TAG, " current version=" + version);
        if (!isOtaUpgrading && data.getStatus() == WAITING) {
            if (data.getTargetVersion() != null && data.getTargetVersion().equals(version)) {
                Log.e(TAG, "host update success!");
                data.setStatus(SUCCESS);
                mDataBaseManager.update(data);

                mPreference.setPrefsHostUpdateResult("success");
                Log.d(TAG, "success! os:" + data.getName() + " currentVersion:" + version
                        + " targetVersion:" + data.getTargetVersion());
            } else {
                Log.e(TAG, "host update failed!");
                data.setStatus(FAILED);
                mDataBaseManager.update(data);

                mPreference.setPrefsHostUpdateResult("failed");
                Log.d(TAG,"failed! os:" + data.getName() + " currentVersion:" + version
                        + " targetVersion:" + data.getTargetVersion());
            }
        }
    }

    /**
     * 检查当前是否正在安装
     * @return 0:没有正在安装 1:安装完成 -1:安装失败 2:正在安装
     */
    private int checkIfInstalling() {
        int result = 0;
        boolean hasFailed = false;
        boolean hasUpdate = false;
        boolean hasWait = false;

        List<VersionData> listData = mDataBaseManager.getAllVersion();

        for (VersionData data : listData) {
            if (data.getStatus() == FAILED) {
                Log.e(TAG, data.getName() + " install failed");
                hasFailed = true;
                break;
            } else if (data.getStatus() == SUCCESS) {
                Log.d(TAG, data.getName() + " install success");
                hasUpdate = true;
            } else if (data.getStatus() == WAITING) {
                Log.e(TAG, data.getName() + " is waiting, need continue install. ");
                hasWait = true;
            }
        }

        if (hasFailed) {
            result = -1;
        } else if (hasUpdate && !hasWait) {
            Log.d(TAG, "Downgrade install all success!");
            result = 1;
        } else if (hasWait) {
            Log.d(TAG, "Need continue install!");
            result = 2;
        }

        return result;
    }

    /**
     * 当前网络状态是否连接
     */
    private boolean isNetworkConnected(Context context) {
        boolean isMobileConnected = false;
        boolean isWifiConnected = false;

        //获得ConnectivityManager对象
        ConnectivityManager connMgr = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);

        if (connMgr == null) {
            Log.d(TAG, "connectivity manager is null.");
            return false;
        }

        //获取所有网络连接的信息
        NetworkInfo wifiNetworkInfo = connMgr.getNetworkInfo(ConnectivityManager.TYPE_WIFI);
        NetworkInfo mobileNetworkInfo = connMgr.getNetworkInfo(ConnectivityManager.TYPE_MOBILE);

        if (wifiNetworkInfo != null) {
            isWifiConnected = wifiNetworkInfo.isConnected();
        }

        if (mobileNetworkInfo != null) {
            isMobileConnected = mobileNetworkInfo.isConnected();
        }

        Log.d(TAG, "wifi connected: " + isWifiConnected + ", mobile connected: " + isMobileConnected);
        //返回网络连接状态
        return isWifiConnected || isMobileConnected;
    }

    private void setStartingMode(String startingMode) {
        //Don't save default starting mode, because there is no OTA.
        if (OtaConstants.STARTING_MODE_BOOT.equals(startingMode)) {
            Log.d(TAG, "default starting mode and no need save it");
            return;
        }

        Log.d(TAG, "SET STARTING MODE, startingMode: " + startingMode);
        mPreference.setPrefsStartingMode(startingMode);
    }

    private void clearDownloadLxcFile(){
        Log.d(TAG,"clearDownloadLxcFile");
        boolean result = true;
        File file = new File(LXC_DOWNLOAD_PATH);
        if (file.exists() && !file.delete()) {
            result = false;
        }
        Log.e(TAG, "delete download lxc install file " + file.getAbsolutePath()
                + "/" + file.getName() + " ret:" + result);
    }

    private void clearDownloadFile() {
        boolean result = true;
        File file = new File(DOWNLOAD_PATH);
        if (file.exists() && !file.delete()) {
            result = false;
        }
        Log.e(TAG, "delete download install file " + file.getAbsolutePath()
                + "/" + file.getName() + " ret:" + result);
    }

    private boolean clearLxcInstallFile(){
        Log.d(TAG,"clearLxcInstallFile");
        boolean result = true;
        File dir = new File(OTA_LXC_INSTALL_PATH);
        if (dir.isDirectory()) {
            File[] files = dir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (!file.delete()) {
                        result = false;
                        Log.e(TAG, "delete install file " + file.getName() + " failed!");
                    }
                }
            }
            if (!dir.delete()) {
                result = false;
                Log.e(TAG, "delete install file " + dir.getName() + " failed!");
            }
        }
        return result;
    }

    private String selectedOsToString() {
        StringBuilder b = new StringBuilder();
        boolean isFirstOs = true;

        b.append("Ota setting {");

        for (String os : OS_LIST) {
            if (mPreference.getOsCheckConfig(os)) {
                if (isFirstOs) {
                    b.append("'").append(os).append('\'');
                    isFirstOs = false;
                } else {
                    b.append(", '").append(os).append('\'');
                }
            }
        }
        b.append("}");

        Log.d(TAG, b.toString());
        return b.toString();
    }

    private void sendOtaFinishMessage(OtaMessage message) {
        String result = message.result;
        String updateTYpe = message.updateType;
        String codeMsg = message.message;
        boolean needReboot = message.needReboot;
        boolean needClearData = message.needClearData;

        String[] msgList = codeMsg.split(",");
        String code = msgList[0];
        String msg = msgList[1];

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(Definition.JSON_OTA_TYPE, updateTYpe);
            jsonObject.put(Definition.JSON_OTA_RESULT, result);
            jsonObject.put(Definition.JSON_OTA_REBOOT, needReboot);
            jsonObject.put(Definition.JSON_OTA_CODE, code);
            jsonObject.put(Definition.JSON_OTA_MESSAGE, msg);
            jsonObject.put(Definition.JSON_OTA_UPTIME, SystemClock.uptimeMillis());
            if (message.extra != null) {
                jsonObject.put(Definition.JSON_OTA_EXTRA, message.extra);
            }
        } catch (JSONException e) {
            Log.e(TAG, "result=" + result + " code=" + code + " msg=" + msg);
            e.printStackTrace();
        }

        if (Definition.JSON_OTA_RESULT_FAILED.equals(result) && code.equals("-4000")) {
            if (Definition.JSON_OTA_TYPE_NORMAL.equals(updateTYpe) ||
                    (Definition.JSON_OTA_TYPE_ROLLBACK.equals(updateTYpe) && mPreference.getRollbackFailedBiReport())) {
                sendBiOtaReport(result, mPreference.getUpgradeFailedReason());
                if (Definition.JSON_OTA_TYPE_ROLLBACK.equals(updateTYpe)) {
                    mPreference.setRollbackFailedBiReport(false);
                }
            }
        } else {
            sendBiOtaReport(result, msg);
        }


        if (needClearData) {
            clearInstallData();
        }

        Log.d(TAG, "sendOtaFinishMessage:" + jsonObject.toString());
        mOtaApiHelper.finishOtaUpgrade(jsonObject.toString());
    }

    private int checkIfInstallingSuccess() {
        int result = 0;
        boolean hasFailed = false;
        boolean hasUpdate = false;
        boolean hasWait = false;

        List<VersionData> listData = mDataBaseManager.getAllVersion();

        for (VersionData data : listData) {
            if (data.getStatus() == FAILED) {
                Log.e(TAG, data.getName() + " install failed");
                hasFailed = true;
                break;
            } else if (data.getStatus() == SUCCESS) {
                Log.d(TAG, data.getName() + " install success");
                hasUpdate = true;
            } else if (data.getStatus() == WAITING) {
                Log.e(TAG, data.getName() + " is WAITING, need continue install. ");
                hasWait = true;
            }
        }

        if (hasFailed) {
            result = -1;
        } else if (hasUpdate && !hasWait) {
            Log.d(TAG, "Update all success!");
            result = 1;
        } else if (hasWait) {
            Log.d(TAG, "Need continue install!");
            result = 2;
        }

        return result;
    }

    private void sendBiOtaReport(String result, String reason) {
        new BiOtaReport().addUiPer(mBatteryLevel)
                .addFailedReason(reason)
                .addResult(result)
                .report();
    }

    private void clearInstallData() {
        SettingsUtils.setSettingsGlobalOtaInstallingOs(mContext, "");
        mPreference.setOtaIsUpgrading(false);
        clearDataBaseUpdateResult();
        clearInstallFile();
        mDownloadTask.clearAll();
        mPreference.clearPreference();
    }

    private void clearDataBaseUpdateResult() {
        List<VersionData> list = mDataBaseManager.getAllVersion();
        for (VersionData data : list) {
            Log.d(TAG, "clear db status. os=" + data.getName());
            data.setStatus(NA);
            data.setTargetVersion(null);
            data.setCurrentVersion(null);
            mDataBaseManager.update(data);
        }
    }

    private void clearInstallFile() {
        File dir = new File(OTA_INSTALL_PATH);
        if (dir.isDirectory()) {
            File[] files = dir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (!file.delete()) {
                        Log.e(TAG, "delete install file " + file.getName() + " failed!");
                    }
                }
            }
            if (!dir.delete()) {
                Log.e(TAG, "delete install file " + dir.getName() + " failed!");
            }
        }
    }

    public void incrementRetryCount() {
        downloadReTryCount.incrementAndGet();
    }

    public void resetRetryCount() {
        downloadReTryCount.set(0);
    }

    // 获取当前重试计数
    public int getRetryCount() {
        return downloadReTryCount.get();
    }

    public void setBatteryLevel(int batteryLevel) {
        mBatteryLevel = batteryLevel;
    }
}
