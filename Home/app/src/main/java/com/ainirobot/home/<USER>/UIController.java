package com.ainirobot.home.ui;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.support.annotation.Nullable;
import android.support.v4.app.ActivityCompat;
import android.util.Log;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.LaunchActivity;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.SystemActivity;
import com.ainirobot.home.module.ElevatorRepositionModule;
import com.ainirobot.home.module.RelocationModule;
import com.ainirobot.home.ui.fragment.AutoChargeFragment;
import com.ainirobot.home.ui.fragment.BaseFragment;
import com.ainirobot.home.ui.fragment.BindCodeFragment;
import com.ainirobot.home.ui.fragment.BindFailedFragment;
import com.ainirobot.home.ui.fragment.BindSuccessFragment;
import com.ainirobot.home.ui.fragment.BleDistanceFragment;
import com.ainirobot.home.ui.fragment.CalibrationFragment;
import com.ainirobot.home.ui.fragment.ChargingFragment;
import com.ainirobot.home.ui.fragment.ElevatorRepositionFragment;
import com.ainirobot.home.ui.fragment.EmergencyFragment;
import com.ainirobot.home.ui.fragment.FactoryResetFragment;
import com.ainirobot.home.ui.fragment.FullLockFragment;
import com.ainirobot.home.ui.fragment.HWAbnormalFragment;
import com.ainirobot.home.ui.fragment.HWE70StatusFragment;
import com.ainirobot.home.ui.fragment.InspectErrorFragment;
import com.ainirobot.home.ui.fragment.InspectFragment;
import com.ainirobot.home.ui.fragment.LauncherFragment;
import com.ainirobot.home.ui.fragment.LeavePileToPointFragment;
import com.ainirobot.home.ui.fragment.LoadMapFragment;
import com.ainirobot.home.ui.fragment.MapDriftFragment;
import com.ainirobot.home.ui.fragment.MapOutsideFragment;
import com.ainirobot.home.ui.fragment.MultiRobotErrorFragment;
import com.ainirobot.home.ui.fragment.NaviSensorStateFragment;
import com.ainirobot.home.ui.fragment.OTAProgressFragment;
import com.ainirobot.home.ui.fragment.OTAResultFragment;
import com.ainirobot.home.ui.fragment.PushMapFragment;
import com.ainirobot.home.ui.fragment.QRcodeFragment;
import com.ainirobot.home.ui.fragment.RelocationFragment;
import com.ainirobot.home.ui.fragment.RemoteControlFragment;
import com.ainirobot.home.ui.fragment.RemoteRepositionFragment;
import com.ainirobot.home.ui.fragment.RemoteStopChargingFragment;
import com.ainirobot.home.ui.fragment.SetChargePileFragment;
import com.ainirobot.home.ui.fragment.ShutdownFragment;
import com.ainirobot.home.ui.fragment.StandByFragment;
import com.ainirobot.home.ui.fragment.StopChargeConfirmFragment;
import com.ainirobot.home.ui.fragment.TimeWarningFragment;
import com.ainirobot.home.ui.fragment.WheelDangerFragment;
import com.ainirobot.home.utils.LocationUtil;
import com.ainirobot.home.utils.SystemUtils;

import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class UIController {
    private static final String TAG = "UIController:Home";
    private static UIController sUIController;
    private FRAGMENT_TYPE mCurrentFragment = FRAGMENT_TYPE.FRAGMENT_LAUNCHER;



    private static boolean isMapInFloorList = false;

    private Bundle mCurrentBundle = null;

    private ComponentName mLastApp;
    private Handler mUIHandler;
    private UIActionQueue mMessageQueue;
    private ScheduledExecutorService mMessageExecutor;

    public FRAGMENT_TYPE getCurrentFragment() {
        return mCurrentFragment;
    }

    public Bundle getCurrentBundle() {
        return mCurrentBundle;
    }

    public ComponentName getLastApp() {
        return mLastApp;
    }

    public void setLastApp(ComponentName mLastApp) {
        this.mLastApp = mLastApp;
        mCurrentFragment = FRAGMENT_TYPE.FRAGMENT_LAUNCHER;
        mCurrentBundle = null;
    }

    public synchronized void setUIHandler(Handler mUIHandler) {
        this.mUIHandler = mUIHandler;
    }

    public Handler getUIHandler() {
        return mUIHandler;
    }

    public enum FRAGMENT_TYPE {
        /**
         *
         */
        FRAGMENT_AUTO_CHARGE {
            @Override
            public BaseFragment getFragment() {
                return new AutoChargeFragment();
            }

            @Override
            public String getClassName() {
                return AutoChargeFragment.class.getSimpleName();
            }
        },
        FRAGMENT_EMERGENCY {
            @Override
            public BaseFragment getFragment() {
                return new EmergencyFragment();
            }

            @Override
            public String getClassName() {
                return EmergencyFragment.class.getSimpleName();
            }
        },
        FRAGMENT_BLE_NEAR {
            @Override
            public BaseFragment getFragment() {
                return new BleDistanceFragment();
            }

            @Override
            public String getClassName() {
                return BleDistanceFragment.class.getSimpleName();
            }
        },
        FRAGMENT_STANDBY {
            @Override
            public BaseFragment getFragment() {
                return new StandByFragment();
            }

            @Override
            public String getClassName() {
                return StandByFragment.class.getSimpleName();
            }
        },
        FRAGMENT_FULL_LOCK {
            @Override
            public BaseFragment getFragment() {
                return new FullLockFragment();
            }

            @Override
            public String getClassName() {
                return FullLockFragment.class.getSimpleName();
            }
        },
        FRAGMENT_INSPECT_ERROR {
            @Override
            public BaseFragment getFragment() {
                return new InspectErrorFragment();
            }

            @Override
            public String getClassName() {
                return InspectErrorFragment.class.getSimpleName();
            }
        },
        FRAGMENT_INSPECT {
            @Override
            public BaseFragment getFragment() {
                return new InspectFragment();
            }

            @Override
            public String getClassName() {
                return InspectFragment.class.getSimpleName();
            }

            @Override
            public Class getParent() {
                return LaunchActivity.class;
            }
        },
        FRAGMENT_INSPECT_OTA {
            @Override
            public BaseFragment getFragment() {
                return new InspectFragment();
            }

            @Override
            public String getClassName() {
                return InspectFragment.class.getSimpleName();
            }
        },
        FRAGMENT_BIND_FAILED {
            @Override
            public BaseFragment getFragment() {
                return new BindFailedFragment();
            }

            @Override
            public String getClassName() {
                return BindFailedFragment.class.getSimpleName();
            }
        },
        FRAGMENT_BIND {
            @Override
            public BaseFragment getFragment() {
                return SystemUtils.isUsingBindCode() ? new BindCodeFragment(): new QRcodeFragment();
            }

            @Override
            public String getClassName() {
                return QRcodeFragment.class.getSimpleName();
            }
        },
        FRAGMENT_BIND_SUCCESS {
            @Override
            public BaseFragment getFragment() {
                return new BindSuccessFragment();
            }

            @Override
            public String getClassName() {
                return BindSuccessFragment.class.getSimpleName();
            }
        },
        FRAGMENT_LAUNCHER {
            @Override
            public BaseFragment getFragment() {
                return new LauncherFragment();
            }

            @Override
            public String getClassName() {
                return LauncherFragment.class.getSimpleName();
            }

            @Override
            public Class getParent() {
                return LaunchActivity.class;
            }
        },
        FRAGMENT_OTA_PROGRESS {
            @Override
            public BaseFragment getFragment() {
                return new OTAProgressFragment();
            }

            @Override
            public String getClassName() {
                return OTAProgressFragment.class.getSimpleName();
            }
        },
        FRAGMENT_OTA_RESULT {
            @Override
            public BaseFragment getFragment() {
                return new OTAResultFragment();
            }

            @Override
            public String getClassName() {
                return OTAResultFragment.class.getSimpleName();
            }
        },
        FRAGMENT_CHARGE_PILE {
            @Override
            public BaseFragment getFragment() {
                return new SetChargePileFragment();
            }

            @Override
            public String getClassName() {
                return SetChargePileFragment.class.getSimpleName();
            }
        },
        FRAGMENT_CHARGING {
            @Override
            public BaseFragment getFragment() {
                return new ChargingFragment();
            }

            @Override
            public String getClassName() {
                return ChargingFragment.class.getSimpleName();
            }
        },
        FRAGMENT_REMOTE_STOP_CHARGING {
            @Override
            public BaseFragment getFragment() {
                return new RemoteStopChargingFragment();
            }

            @Override
            public String getClassName() {
                return RemoteStopChargingFragment.class.getSimpleName();
            }
        },
        FRAGMENT_REPOSITION {
            @Override
            public BaseFragment getFragment() {
//                if (ProductInfo.isSaiphXdOrBigScreen() || ProductInfo.isSaiphChargeIr() || ProductInfo.isSaiphPro()
//                        || ProductInfo.isMeissaPlus() || ProductInfo.isMeissaPlusOverSea()) {
//                    if (LocationUtil.getInstance().isElevatorControlEnabled()){
//                        return new ElevatorRepositionFragment();
//                    }
//                    return LocationUtil.getInstance().isChargingTypeWire()
//                            ? new QRCodeRelocationFragment() : new RepositionFragment();
//                }
//                if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus()
//                        || (ProductInfo.isMiniProduct() && SystemUtils.hasTopIR() && LocationUtil.getInstance().isChargingTypeWire())) {
//                    return new QRCodeRelocationFragment();
//                }
//                return new RepositionFragment();
                boolean shouldUseElevatorModule = LocationUtil.getInstance().isElevatorControlEnabled()  && (!ProductInfo.isAlnilamPro() || isMapInFloorList);
                if(shouldUseElevatorModule){
                    return new ElevatorRepositionFragment();
                }else {
                    return new RelocationFragment();
                }
            }

            @Override
            public String getClassName() {
//                if (ProductInfo.isSaiphXdOrBigScreen() || ProductInfo.isSaiphChargeIr() || ProductInfo.isSaiphPro()
//                        || ProductInfo.isMeissaPlus() || ProductInfo.isMeissaPlusOverSea()) {
//                    if (LocationUtil.getInstance().isElevatorControlEnabled()){
//                        return ElevatorRepositionFragment.class.getName();
//                    }
//                    return LocationUtil.getInstance().isChargingTypeWire()
//                            ? QRCodeRelocationFragment.class.getName() : RepositionFragment.class.getSimpleName();
//                }
//
//                if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus()
//                        || (ProductInfo.isMiniProduct() && SystemUtils.hasTopIR() && LocationUtil.getInstance().isChargingTypeWire())) {
//                    return QRCodeRelocationFragment.class.getName();
//                }
//                return RepositionFragment.class.getSimpleName();
                boolean shouldUseElevatorModule = LocationUtil.getInstance().isElevatorControlEnabled()  && (!ProductInfo.isAlnilamPro() || isMapInFloorList);
                if(shouldUseElevatorModule){
                    return ElevatorRepositionFragment.class.getName();
                }else {
                    return RelocationFragment.class.getName();
                }
            }
        },
        FRAGMENT_REMOTE_REPOSITION {
            @Override
            public BaseFragment getFragment() {
                return new RemoteRepositionFragment();
            }

            @Override
            public String getClassName() {
                return RemoteRepositionFragment.class.getSimpleName();
            }
        },
        FRAGMENT_SHUTDOWN_NAVI {
            @Override
            public BaseFragment getFragment() {
                return new ShutdownFragment();
            }

            @Override
            public String getClassName() {
                return ShutdownFragment.class.getSimpleName();
            }
        },
        FRAGMENT_HW_ABNORMAL {
            @Override
            public BaseFragment getFragment() {
                return new HWAbnormalFragment();
            }

            @Override
            public String getClassName() {
                return HWAbnormalFragment.class.getSimpleName();
            }
        },
        FRAGMENT_HW_E70 {
            @Override
            public BaseFragment getFragment() {
                return new HWE70StatusFragment();
            }

            @Override
            public String getClassName() {
                return HWE70StatusFragment.class.getSimpleName();
            }
        },

        FRAGMENT_MAP_DRIFT {
            @Override
            public BaseFragment getFragment() {
                return new MapDriftFragment();
            }

            @Override
            public String getClassName() {
                return MapDriftFragment.class.getSimpleName();
            }
        },

        FRAGMENT_MAP_OUTSIDE {
            @Override
            public BaseFragment getFragment() {
                return new MapOutsideFragment();
            }

            @Override
            public String getClassName() {
                return MapOutsideFragment.class.getSimpleName();
            }
        },

        FRAGMENT_MULTI_ROBOT_ERROR {
            @Override
            public BaseFragment getFragment() {
                return new MultiRobotErrorFragment();
            }

            @Override
            public String getClassName() {
                return MultiRobotErrorFragment.class.getSimpleName();
            }
        },

        FRAGMENT_REMOTE_CONTROL {
            @Override
            public BaseFragment getFragment() {
                return new RemoteControlFragment();
            }

            @Override
            public String getClassName() {
                return RemoteControlFragment.class.getSimpleName();
            }
        },
        FRAGMENT_TIME_WARNING {
            @Override
            public BaseFragment getFragment() {
                return new TimeWarningFragment();
            }

            @Override
            public String getClassName() {
                return TimeWarningFragment.class.getSimpleName();
            }
        },
        FRAGMENT_CALIBRATION {
            @Override
            public BaseFragment getFragment() {
                return new CalibrationFragment();
            }

            @Override
            public String getClassName() {
                return CalibrationFragment.class.getSimpleName();
            }
        },

        FRAGMENT_WHEEL_OVER_CONTROL {
            @Override
            public BaseFragment getFragment() {
                return new WheelDangerFragment();
            }

            @Override
            public String getClassName() {
                return WheelDangerFragment.class.getSimpleName();
            }
        },

        FRAGMENT_NAVI_SENSOR_STATE {
            @Override
            public BaseFragment getFragment() {
                return new NaviSensorStateFragment();
            }

            @Override
            public String getClassName() {
                return NaviSensorStateFragment.class.getSimpleName();
            }
        },

        FRAGMENT_PUSH_MAP {
            @Override
            public BaseFragment getFragment() {
                return new PushMapFragment();
            }

            @Override
            public String getClassName() {
                return PushMapFragment.class.getSimpleName();
            }

            @Override
            public Class getParent() {
                return SystemActivity.class;
            }
        },
        FRAGMENT_LOAD_MAP {
            @Override
            public BaseFragment getFragment() {
                return new LoadMapFragment();
            }

            @Override
            public String getClassName() {
                return LoadMapFragment.class.getSimpleName();
            }
        },
        FRAGMENT_LEAVE_PILE_TO_POINT {
            @Override
            public BaseFragment getFragment() {
                return new LeavePileToPointFragment();
            }

            @Override
            public String getClassName() {
                return LeavePileToPointFragment.class.getSimpleName();
            }
        },
        FRAGMENT_STOP_CHARGE_CONFIRM {
            @Override
            public BaseFragment getFragment() {
                return new StopChargeConfirmFragment();
            }

            @Override
            public String getClassName() {
                return StopChargeConfirmFragment.class.getSimpleName();
            }
        },
        FRAGMENT_FACTORY_RESET {
            @Override
            public BaseFragment getFragment() {
                return new FactoryResetFragment();
            }

            @Override
            public String getClassName() {
                return FactoryResetFragment.class.getSimpleName();
            }
        },;

        public abstract BaseFragment getFragment();

        public abstract String getClassName();

        public Class getParent() {
            return SystemActivity.class;
        }
    }

    public enum MESSAGE_TYPE {
        PROGRESS_DOWNLOAD,
        PROGRESS_INSTALL,
        OTA_RESULT,
        OTA_WARNING,
        TOUCH_EVENT_DOWN,
        BATTERY_LEVEL,
        WAIT_UPGRADE,
        SET_PILE_STATUS,
        AUTO_CHARGE_FAIL,
        AUTO_CHARGE_FAIL_NOT_EXIST_CHARGING_PILE_POSE,
        AUTO_CHARGE_FAIL_NOT_EXIST_CHARGING_WIRE_POSE,
        AUTO_CHARGE_WAIT_IN_AREA,
        AUTO_CHARGE_GO_CHARGE_POINT,
        MSG_QR_CODE,
        MSG_QR_REQUEST_INTERVAL,
        MSG_QR_BIND_SUCCESS,
        MSG_REMOTE_REPO_ESTIMATE,
        MSG_REMOTE_REPO_SUCCESS,
        MSG_REMOTE_REPO_FAILURE,
        MSG_REMOTE_CHARGE_STATUS,
        HW_STATUS_UPDATE,
        HW_E70_ERROR,
        HW_E70_RECOVERY,
        CHARGING_WARNING,
        CHARGING_BMS_LOW_TEMP,
        CHARGING_LEVEL_UPDATE,
        DOEMANCY_START,
        AUTO_CHARGE_GOING,
        AUTO_CHARGE_NAVI_ARRIVED,
        AUTO_CHARGE_SUCCESS,
        REPOSITION_LOADING,
        REPOSITION_START,
        REPOSITION_SUCCEED,
        REPOSITION_FAILURE,
        REPOSITION_EXIT,
        REPOSITION_VISION_CIRCLE,
        REPOSITION_VISION_FAILED,
        REPOSITION_MENUAL_GUIDE,
        REPOSITION_VISION_SUCCESS,
        REPOSITION_CHARGING_CHECK,
        REPOSITION_CLEAR_REASON,
        STANDBY_EXIT,
        STANDBY_EMERGENCY_STATUS,
        STANDBY_MULTI_FUNC_SWITCH_STATUS_CHANGE,
        STANDBY_MULTI_FUNC_SWITCH_STATUS,
        STANDBY_BATTERY,
        BMS_WARNING_STATUS,
        QRCODE_REPOSITION_SUCCEED,
        TARGET_VISION_GUIDE,
        ANCHOR_POINT_REPOSITION_FAILURE,
        RESET_HEAD_FAILURE,
        CHARGE_PILE_LOCATE_FAILURE,
        RELOCATE_CHOOSE_TYPE,
        RELOCATE_CHOOSE_ANCHOR_POINT,
        RELOCATE_CHOOSE_CHARGE_PILE,
        SHUTDOWN_NAVI_FINISHED,
        SHUTDOWN_COUNTDOWN,
        MAP_DRIFT_STATUS_CHECK_RADAR,
        MAP_DRIFT_STATUS_CHECK_ESTIMATE,
        MAP_DRIFT_STATUS_RELOAD_CURRENT_MAP,
        MAP_DRIFT_STATUS_RELOAD_MAP_FAIL,
        MAP_DRIFT_STATUS_START_REPOSITION,
        HW_STATUS_RECOVERY,
        TIME_WARNING_EXIT,
        ELEVATOR_UPDATE_SPECIAL_POSE,
        ELEVATOR_SWITCH_MAP_SUCCEED,
        ELEVATOR_SWITCH_MAP_FAIL,
        ELEVATOR_REPOSITION_SUCCEED,
        ELEVATOR_CHARGE_PILE_REPOSITION_FAIL,
        ELEVATOR_CHARGE_PILE_LOCATE_START,
        ELEVATOR_EXIT,
        CURRENT_BLE_NEAR_DANGER,
        LOAD_MAP_PROGRESS,
        LOAD_MAP_FAILED,
        REMOTE_STOP_CHARGING_AVOID_FAILED,
        REMOTE_STOP_CHARGING_RADAR_FAILED,
        REMOTE_STOP_CHARGING_UNKNOWN_FAILED,
        REMOTE_STOP_CHARGING_SUCCEED,
        LEAVING_CHARGE_POINT,
        LEAVING_CHARGE_POINT_PRE,
        LEAVE_PILE_SUCCESS,
        WAIT_LIFT,
        GO_IN_LIFT,
        IN_LIFT,
        GO_OUT_LIFT,
        GO_OUT_LIFT_SUCCESS,

    }

    private UIController() {
        mMessageQueue = new UIActionQueue(new UIActionQueue.ActionListener() {
            @Override
            public void moveToBack(int actionId) {
                doMoveToBack(actionId);
            }

            @Override
            public void showFragment(int actionId, FRAGMENT_TYPE type, Bundle bundle, Bundle animationBundle) {
                doShowFragment(actionId, type, bundle, animationBundle);
            }

            @Override
            public void sendMessage(int actionId, MESSAGE_TYPE type, String messageStr) {
                doSendMessageToFragment(actionId, type, messageStr);
            }
        });
    }

    public static UIController getInstance() {
        if (null == sUIController) {
            sUIController = new UIController();
        }
        return sUIController;
    }

    public synchronized void sendMessageToFragment(MESSAGE_TYPE type, String messageStr) {
        if (messageStr == null){
            //messageStr 为null时，sendMessageToFragment　会发送消息失败,参见BaseActivity中sendMessageToFragment
            messageStr = "";
        }
        mMessageQueue.addNewAction(new UIActionQueue.UIActionMessage(ModuleDef.UI_SEND_MESSAGE, type, messageStr));
    }

    private void doSendMessageToFragment(int actionId, MESSAGE_TYPE type, String messageStr) {
        if (mUIHandler != null) {
            Message message = mUIHandler.obtainMessage();
            message.what = ModuleDef.UI_SEND_MESSAGE;
            message.obj = messageStr;
            message.arg1 = type.ordinal();
            message.arg2 = actionId;
            mUIHandler.sendMessage(message);
        } else {
            UIController.getInstance().actionOver(actionId);
            Log.d("MainActivity", "uihandler null");
        }
    }

    public void actionOver(int actionId) {
        mMessageQueue.actionOver(actionId);
    }

    private void cancelMessageTimer() {
        if (mMessageExecutor != null && !mMessageExecutor.isShutdown()) {
            mMessageExecutor.shutdownNow();
            mMessageExecutor = null;
        }
    }

    public synchronized void sendMessageDelay(final int msg) {
        Log.d(TAG, "sendMessageDelay:" + msg);

        cancelMessageTimer();
        mMessageExecutor = new ScheduledThreadPoolExecutor(1);
        mMessageExecutor.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                try {
                    Log.d(TAG, "send msg timer:" + msg);
                    if (sendMessageToActivity(msg)) {
                        cancelMessageTimer();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }, 1000, 1000, TimeUnit.MILLISECONDS);
    }

    public synchronized boolean sendMessageToActivity(int msg) {
        if (mUIHandler != null) {
            Log.d(TAG, "sendMessageToActivity");
            Message message = mUIHandler.obtainMessage();
            message.what = ModuleDef.UI_SEND_REQUEST;
            message.arg1 = msg;
            mUIHandler.sendMessage(message);
            return true;
        } else {
            Log.d(TAG, "sendMessageToActivity uihandler null");
            return false;
        }
    }

    public synchronized void showFragment(FRAGMENT_TYPE type, @Nullable Bundle bundle, @Nullable Bundle animationBundle) {
        if(ProductInfo.isAlnilamPro() && bundle!=null){
            isMapInFloorList = bundle.getBoolean(ModuleDef.IS_MAP_IN_FLOOR_LIST);
        }
        mMessageQueue.addNewAction(new UIActionQueue.UIActionShow(ModuleDef.UI_SHOW_FRAGMENT, type, bundle, animationBundle));
    }

    private void doShowFragment(int actionId, FRAGMENT_TYPE type, @Nullable Bundle bundle, @Nullable Bundle animationBundle) {
        Log.d(TAG, "show fragment " + type.toString() +
                ",last fragment:" + SystemUtils.getActivityTop(ApplicationWrapper.getContext()));
        if(ProductInfo.isAlnilamPro() && bundle!=null){
            isMapInFloorList = bundle.getBoolean(ModuleDef.IS_MAP_IN_FLOOR_LIST);
        }
        mCurrentFragment = type;
        mCurrentBundle = bundle;
        Intent intent = new Intent(ApplicationWrapper.getContext(), type.getParent());
        intent.putExtra(LaunchActivity.FRAGMENT, type);
        intent.putExtra(LaunchActivity.BUNDLE, bundle);
        intent.putExtra(LaunchActivity.ACTION_ID, actionId);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        ComponentName componentName = SystemUtils.getActivityTop(ApplicationWrapper.getContext());
        String currentPkg = (componentName == null ? null : componentName.getPackageName());
        if (!ApplicationWrapper.getContext().getPackageName().equals(currentPkg)) {
            Log.d(TAG, "set last component:" + currentPkg);
            mLastApp = componentName;
        }

        ActivityCompat.startActivity(ApplicationWrapper.getContext(), intent, animationBundle);
    }

    public synchronized boolean moveToBack() {
        if (mUIHandler == null) {
            Log.d(TAG, "Move to back failed : no activity");
            return false;
        }
        mMessageQueue.addNewAction(new UIActionQueue.UIAction(ModuleDef.UI_MOVE_TO_BACK));
        return true;
    }

    private void doMoveToBack(int actionId) {
        if (mUIHandler == null) {
            UIController.getInstance().actionOver(actionId);
            Log.d(TAG, "Not activity resume");
            return;
        }
        Log.d(TAG, "send move to home");
        Message message = mUIHandler.obtainMessage();
        message.what = ModuleDef.UI_MOVE_TO_BACK;
        message.arg2 = actionId;
        mUIHandler.sendMessage(message);
        mCurrentFragment = FRAGMENT_TYPE.FRAGMENT_LAUNCHER;
    }

    public boolean isCurrentActivity(Activity activity) {
        if (mCurrentFragment != null && activity != null) {
            Log.d(TAG, "Current fragment :" + mCurrentFragment);
            return mCurrentFragment.getParent().getName().equals(activity.getClass().getName());
        }
        return false;
    }

    public void showCurrentFragment() {
        showFragment(mCurrentFragment, mCurrentBundle, null);
    }

    public void setLastApp() {
        ComponentName componentName = SystemUtils.getActivityTop(ApplicationWrapper.getContext());
        if (!componentName.getPackageName().equals(ApplicationWrapper.getContext().getPackageName())) {
            Log.d(TAG, "set last component:" + componentName.getPackageName());
            mLastApp = componentName;
        }
    }

}
