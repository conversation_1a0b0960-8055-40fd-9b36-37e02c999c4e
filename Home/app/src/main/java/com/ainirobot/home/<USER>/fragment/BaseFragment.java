package com.ainirobot.home.ui.fragment;

import android.support.v4.app.Fragment;
import android.util.Log;

import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.UIController.MESSAGE_TYPE;
import com.google.gson.Gson;

public class BaseFragment extends Fragment {

    private static final String TAG = "BaseFragment:Home";

    protected static Gson mGson = new Gson();

    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        if (type != MESSAGE_TYPE.TOUCH_EVENT_DOWN) {
            Log.d(TAG, "on message-type:" + type + ",message:" + message);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
    }
}
