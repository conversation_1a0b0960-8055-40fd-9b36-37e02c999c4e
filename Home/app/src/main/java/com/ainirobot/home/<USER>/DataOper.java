/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.home.data;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.home.bean.Feature;
import com.ainirobot.home.bean.Semantics;

import java.util.ArrayList;
import java.util.List;

public class DataOper {

    private static final String TAG = "DataOper:Home";

    private static String URI_FEATURE = "content://" + DBProvider.authority + "/" + Feature
            .DBFiled.TABLE_NAME;
    private static String URI_INTENT = "content://" + DBProvider.authority + "/" + Semantics
            .DBFiled.TABLE_NAME;

    public static List<Semantics> getAllSemantics(Context context) {
        List<Semantics> list = new ArrayList<>();
        Semantics semantics;
        Cursor cursor = null;
        try {
            cursor = context.getContentResolver().query(Uri.parse(URI_INTENT), null, null, null,
                    null);
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    semantics = new Semantics();
                    semantics.setSemanticsId(cursor.getInt(cursor.getColumnIndex(Semantics
                            .DBFiled.FILED_ID)));
                    semantics.setFeature(cursor.getInt(cursor.getColumnIndex(Semantics.DBFiled
                            .FILED_FEATURE)));
                    semantics.setIntent(cursor.getString(cursor.getColumnIndex(Semantics.DBFiled
                            .FILED_INTENT)));
                    semantics.setPattern(cursor.getString(cursor.getColumnIndex(Semantics.DBFiled
                            .FILED_PATTERN)));
                    semantics.setPatternType(cursor.getInt(cursor.getColumnIndex(Semantics
                            .DBFiled.FILED_TYPE)));
                    list.add(semantics);
                }
                cursor.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return list;
    }

    public static List<Feature> getAllFeature(Context context) {
        List<Feature> list = new ArrayList<>();
        Feature feature;
        Cursor cursor = null;
        try {
            cursor = context.getContentResolver().query(Uri.parse(URI_FEATURE), null, null, null,
                    null);
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    feature = new Feature();
                    feature.setBackGround(cursor.getInt(cursor.getColumnIndex(
                            Feature.DBFiled.FILED_ISBACK)) == 1 ? true : false);
                    feature.setFeatureId(cursor.getInt(cursor.getColumnIndex(Feature.DBFiled
                            .FILED_FEATURE_ID)));
                    feature.setName(cursor.getString(cursor.getColumnIndex(Feature.DBFiled
                            .FILED_NAME)));
                    feature.setPriority(cursor.getInt(cursor.getColumnIndex(Feature.DBFiled
                            .FILED_PRIORITY)));
                    list.add(feature);
                }
                cursor.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return list;
    }
}
