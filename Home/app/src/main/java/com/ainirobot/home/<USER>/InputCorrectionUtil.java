package com.ainirobot.home.utils;

import android.text.Editable;
import android.text.Selection;
import android.widget.EditText;

import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.R;
import com.ainirobot.home.ui.view.ToastForDialog;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class InputCorrectionUtil {
    private static final String DEFAULT_REGEX = "[^\u4E00-\u9FA5]";
    private static final int DEFAULT_MAX_LENGTH = 6;
    private String regex;
    private int maxLength;

    private static InputCorrectionUtil mInstance;

    public static InputCorrectionUtil getInstance() {
        if (mInstance == null) {
            mInstance = new InputCorrectionUtil();
        }
        return mInstance;
    }

    private InputCorrectionUtil() {
        this.regex = DEFAULT_REGEX;
        this.maxLength = DEFAULT_MAX_LENGTH;
    }

    public void setMaxLength(int maxLength) {
        this.maxLength = maxLength;
    }

    public void setRegex(String regex) {
        this.regex = regex;
    }

    public String filterInvalidStr(String str) {
        Matcher m = Pattern.compile(regex).matcher(str);
        if (m.find()) {
            showTips(ApplicationWrapper.getContext()
                    .getString(R.string.wake_up_need_chinese));
        }
        return str.replaceAll(regex, "");
    }

    private static void showTips(String tips) {
        new ToastForDialog(ApplicationWrapper.getContext(), tips);
    }

    public void processLongText(EditText editText) {
        Editable editable = editText.getText();
        int len = editable.length();

        if (len > DEFAULT_MAX_LENGTH) {
            int selEndIndex = Selection.getSelectionEnd(editable);
            String str = editable.toString();
            //Intercept new string
            String newStr = str.substring(0, DEFAULT_MAX_LENGTH);
            editText.setText(newStr);
            editable = editText.getText();

            //The length of the new string
            int newLen = editable.length();
            //Old cursor position exceeds string length
            if (selEndIndex > newLen) {
                selEndIndex = editable.length();
            }
            //Set the location of the new cursor
            Selection.setSelection(editable, selEndIndex);
            showTips(ApplicationWrapper.getContext()
                    .getString(R.string.wake_up_need_chinese));
        }
    }
}
