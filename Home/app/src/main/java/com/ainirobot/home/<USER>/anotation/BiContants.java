package com.ainirobot.home.bi.anotation;

/**
 * bi report constants
 *
 * @version V1.0.0
 * @date 2019/4/10 15:45
 */
public class BiContants {

    /**
     * auto back charging status
     */
    public static final String CHARGE_FAIL_STATUS = "charge_fail_status";
    public static final String CHARGE_FAIL_REASON = "charge_fail_reason";
    public static final int CHARGE_FAIL_WHEN_NOT_ESTIMATE = 1001;
    public static final int CHARGE_FAIL_WHEN_NAVIGATION = 1002;
    public static final int CHARGE_FAIL_WHEN_LARGE_MAP_NAV_TIMEOUT = 1003;
    public static final int CHARGE_FAIL_WHEN_PARSE_IN_LOCATION = 1004;
    public static final int CHARGE_FAIL_WHEN_NOT_MOVE_FOR_20S = 1005;
    public static final int CHARGE_FAIL_WHEN_PSB_CHARGE = 1006;
    public static final int CHARGE_FAIL_WHEN_PSB_NO_SIGNAL = 1007;
    public static final int COUNT_OFFSET = 1;

}
