package com.ainirobot.home.ui;

import android.os.Bundle;
import android.os.CountDownTimer;
import android.util.Log;

import com.ainirobot.coreservice.client.techreport.AbnormalReport;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Iterator;
import java.util.concurrent.LinkedBlockingDeque;

public class UIActionQueue {

    private static final String TAG = "UIQueue:Home";
    private static int actionId = 0;

    private LinkedBlockingDeque<UIAction> mActions;
    private ActionListener mListener;
    private CountDownTimer mActionTimer;

    static class UIAction {
        int mActionId;
        int mActionType;

        public UIAction(int actionType) {
            mActionType = actionType;
            mActionId = actionId++;
        }

        public void retry() {

        }

        public boolean isRetry() {
            return false;
        }
    }

    static class UIActionShow extends UIAction {

        final int MAX_RETRY = 3;

        UIController.FRAGMENT_TYPE mFragmentType;
        Bundle mFragmentBundle;
        Bundle mAnimationBundle;
        int mRetryCount = 0;
        AbnormalReport mAbnormalReport;

        public UIActionShow(int actionType, UIController.FRAGMENT_TYPE fragmentType,
                            Bundle fragmentBundle, Bundle animationBundle) {
            super(actionType);
            mFragmentType = fragmentType;
            mFragmentBundle = fragmentBundle;
            mAnimationBundle = animationBundle;
            mAbnormalReport = new AbnormalReport(fragmentType.getParent().getSimpleName(), fragmentType.getClassName());
            mAbnormalReport.addData("app_name", "OrionHome");
        }

        @Override
        public void retry() {
            Log.e(TAG, "Show fragment retry : " + mFragmentType.name());

            JSONObject content = new JSONObject();
            try {
                content.put("retry_count", mRetryCount);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            mAbnormalReport.setAbnormalInfo("start_activity_failed", "", content.toString());
            mAbnormalReport.report();

            mRetryCount++;
        }

        @Override
        public boolean isRetry() {
            return mRetryCount < MAX_RETRY;
        }
    }

    static class UIActionMessage extends UIAction {
        UIController.MESSAGE_TYPE mType;
        String mMessageStr;

        public UIActionMessage(int actionType, UIController.MESSAGE_TYPE messageType, String messageStr) {
            super(actionType);
            mType = messageType;
            mMessageStr = messageStr;
        }
    }

    public UIActionQueue(ActionListener listener) {

        mActions = new LinkedBlockingDeque<>();
        mListener = listener;
        mActionTimer = new CountDownTimer(5000, 5000) {
            @Override
            public void onTick(long millisUntilFinished) {

            }

            @Override
            public synchronized void onFinish() {
                UIAction action = mActions.peek();
                if (action == null) {
                    Log.d(TAG, "action over by timer.action list null");
                } else {
                    Log.d(TAG, "action over by timer.action id:" + mActions.peek().mActionId);
                    if (!action.isRetry()) {
                        mActions.poll();
                    } else {
                        action.retry();
                    }
                    doNext();
                }
            }
        };
    }

    public synchronized void addNewAction(UIAction action) {
        Log.d(TAG, "add new action.type=" + action.mActionType + ",id=" + action.mActionId);
        switch (action.mActionType) {
            case ModuleDef.UI_SHOW_FRAGMENT:
                removeAction(ModuleDef.UI_SHOW_FRAGMENT);
                removeAction(ModuleDef.UI_MOVE_TO_BACK);
                mActions.offer(action);
                if (mActions.size() == 1) {
                    doNext();
                }
                break;
            case ModuleDef.UI_MOVE_TO_BACK:
                if (mActions.peekLast() == null ||
                        mActions.peekLast().mActionType != ModuleDef.UI_MOVE_TO_BACK) {
                    mActions.offer(action);
                    if (mActions.size() == 1) {
                        doNext();
                    }
                }
                break;
            case ModuleDef.UI_SEND_MESSAGE:
                mActions.offer(action);
                if (mActions.size() == 1) {
                    doNext();
                }
                break;
        }
    }

    public synchronized void actionOver(int actionId) {
        if (mActions.peek() == null) {
            Log.d(TAG, "action over.action id=" + actionId + ",action list null");
        } else {
            Log.d(TAG, "action over.action id=" + actionId + ",first action id=" + mActions.peek().mActionId);
            if (mActions.peek().mActionId == actionId) {
                mActions.poll();
                mActionTimer.cancel();
                doNext();
            }
        }
    }

    private void doNext() {
        UIAction message = mActions.peek();
        if (message == null || mListener == null) return;
        Log.d(TAG, "do next action.id=" + message.mActionId+", message.mActionType = "+message.mActionType);
        mActionTimer.start();
        switch (message.mActionType) {
            case ModuleDef.UI_MOVE_TO_BACK:
                mListener.moveToBack(message.mActionId);
                break;
            case ModuleDef.UI_SHOW_FRAGMENT:
                mListener.showFragment(message.mActionId, ((UIActionShow) message).mFragmentType,
                        ((UIActionShow) message).mFragmentBundle,
                        ((UIActionShow) message).mAnimationBundle);
                break;
            case ModuleDef.UI_SEND_MESSAGE:
                mListener.sendMessage(message.mActionId, ((UIActionMessage) message).mType,
                        ((UIActionMessage) message).mMessageStr);
                break;
        }

    }

    private void removeAction(int actionType) {
        Iterator<UIAction> iterator = mActions.iterator();
        if (iterator.hasNext()) {
            iterator.next();
            while (iterator.hasNext()) {
                UIAction message = iterator.next();
                if (message.mActionType == actionType) {
                    iterator.remove();
                    Log.d(TAG, "remove action:" + actionId);
                }
            }
        }
    }

    public interface ActionListener {
        void moveToBack(int actionId);

        void showFragment(int actionId, UIController.FRAGMENT_TYPE type, Bundle bundle, Bundle animationBundle);

        void sendMessage(int actionId, UIController.MESSAGE_TYPE type, String messageStr);
    }

}
