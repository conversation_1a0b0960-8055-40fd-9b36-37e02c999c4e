package com.ainirobot.home.control;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.SystemApi;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.LinkedHashMap;

/**
 *
 * <AUTHOR>
 * @date 2018/2/2
 */

public class LightManager {

    private static LinkedHashMap<String, LIGHT_EFFECT_TYPE> LIGHTS;
    private int mType;
    private int mCurrentStartColor, mCurrentEndColor;
    private int mDuration, mLoopTimes, mFinalColor;
    private int mStartTime, mEndTime;

    static {
        LIGHTS = new LinkedHashMap<String, LIGHT_EFFECT_TYPE>() {{
            put(Definition.LIGHT_EMERGENCY, LIGHT_EFFECT_TYPE.LIGHT_EFFECT_RED_LIGHT);
            put(Definition.LIGHT_CHARGING, LIGHT_EFFECT_TYPE.LIGHT_EFFECT_GREEN_BREATH);
            put(Definition.LIGHT_BATTERY_LOW, LIGHT_EFFECT_TYPE.LIGHT_EFFECT_RED_LOW_LIGHT);
            put(Definition.LIGHT_CHARGE_FULL, LIGHT_EFFECT_TYPE.LIGHT_EFFECT_GREEN_LIGHT);
            put(Definition.LIGHT_NORMAL, LIGHT_EFFECT_TYPE.LIGHT_EFFECT_BLUE_LIGHT);
            put(Definition.LIGHT_CLOSE, LIGHT_EFFECT_TYPE.LIGHT_EFFECT_CLOSE);
            put(Definition.LIGHT_EMPTY, LIGHT_EFFECT_TYPE.LIGHT_EFFECT_STANDBY);
        }};
    }

    public enum LIGHT_EFFECT_TYPE {
        /**
         * 绿色常亮
         */
        LIGHT_EFFECT_GREEN_LIGHT("light_effect_green_light"),
        /**
         * 绿色呼吸
         */
        LIGHT_EFFECT_GREEN_BREATH("light_effect_green_breath"),
        /**
         * 黄色常亮
         */
        LIGHT_EFFECT_YELLOW_LIGHT("light_effect_yellow_light"),
        /**
         * 蓝色常亮
         */
        LIGHT_EFFECT_BLUE_LIGHT("light_effect_blue_light"),
        /**
         * 蓝色呼吸
         */
        LIGHT_EFFECT_BLUE_BREATH("light_effect_blue_breath"),
        /**
         * 红色常亮
         */
        LIGHT_EFFECT_RED_LIGHT("light_effect_red_light"),
        /**
         * 红色呼吸
         */
        LIGHT_EFFECT_RED_BREATH("light_effect_red_breath"),

        LIGHT_EFFECT_CLOSE("light_effect_close"),

        LIGHT_EFFECT_STANDBY("light_effect_standby"),

        LIGHT_EFFECT_RED_LOW_LIGHT("light_effect_red_low_light"),;

        private String name;

        LIGHT_EFFECT_TYPE(String name) {
            this.name = name;
        }

        public String getName() {
            return this.name;
        }
    }

    private static final String TAG = "LightManager:Home";
    private static volatile LightManager mInstance;

    public static LightManager getInstance() {
        if (mInstance == null) {
            synchronized (LightManager.class) {
                if (mInstance == null) {
                    mInstance = new LightManager();
                }
            }
        }
        return mInstance;
    }

    public void setLight(String param) {
        try {
            JSONObject jsonObject = new JSONObject(param);
            String type = jsonObject.getString("type");
            setLightEffectType(LIGHTS.get(type));
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public void setLightEffectType(LIGHT_EFFECT_TYPE type) {
        if (type == null) {
            return;
        }
        Log.d(TAG, "set light type:" + type.getName());
        switch (type) {
            case LIGHT_EFFECT_BLUE_BREATH:
                setBlueBreathLight();
                break;

            case LIGHT_EFFECT_BLUE_LIGHT:
                setBlueNormalLight();
                break;

            case LIGHT_EFFECT_YELLOW_LIGHT:
                setYellowNormalLight();
                break;

            case LIGHT_EFFECT_GREEN_BREATH:
                setGreenBreathLight();
                break;

            case LIGHT_EFFECT_GREEN_LIGHT:
                setGreenNormalLight();
                break;

            case LIGHT_EFFECT_RED_BREATH:
                setRedBreathLight();
                break;

            case LIGHT_EFFECT_RED_LIGHT:
                setRedNormalLight();
                break;

            case LIGHT_EFFECT_RED_LOW_LIGHT:
                setRedLowLight();
                break;

            case LIGHT_EFFECT_CLOSE:
                setLightEffectClose();
                break;

            case LIGHT_EFFECT_STANDBY:
                setLightEffectStandby();
                break;

            default:
                break;
        }


    }

    private void setLightEffectClose(){
        int bodyLight = 0;
        if (SystemApi.getInstance().isUseAutoEffectLed()) {//底盘灯
            bodyLight ++;
            if (ProductInfo.isSlimProduct()) {
                SystemApi.getInstance().setTrayLedEffect(0, Definition.LED_EFFECT_BLUEBREATH, null);
            } else {
                SystemApi.getInstance().setTrayLedEffect(0, Definition.TRAY_LIGHT_EFFECT_ALLOFF, null);
            }
            SystemApi.getInstance().setBottomLedEffect(0, Definition.LED_EFFECT_BLUEBREATH, null);
        }
        if (SystemApi.getInstance().isHasClavicleLed()){//锁骨灯
            bodyLight ++;
            SystemApi.getInstance().setClavicleLedEffect(0, Definition.LED_EFFECT_ALLOFF, null);
        }
        if (SystemApi.getInstance().isHasChestLed()){//胸口灯
            bodyLight ++;
            SystemApi.getInstance().setClavicleLedEffect(0, Definition.LED_EFFECT_ALLOFF, null);
        }
        if(bodyLight == 0) {
            mCurrentStartColor = 0;
            setLightColor();
        }
    }

    private void setLightEffectStandby() {
        if (SystemApi.getInstance().isUseAutoEffectLed()) {//底盘灯
            if (ProductInfo.isSlimProduct()) {
                SystemApi.getInstance().setTrayLedEffect(0, Definition.LED_EFFECT_ALLOFF, null);
            } else {
                SystemApi.getInstance().setTrayLedEffect(0, Definition.TRAY_LIGHT_EFFECT_ALLOFF, null);
            }
            SystemApi.getInstance().setBottomLedEffect(0, Definition.LED_EFFECT_ALLOFF, null);
        }
        if (SystemApi.getInstance().isHasClavicleLed()){//锁骨灯
            SystemApi.getInstance().setClavicleLedEffect(0, Definition.LED_EFFECT_ALLOFF, null);
        }
        if (SystemApi.getInstance().isHasChestLed()){//胸口灯
            SystemApi.getInstance().setClavicleLedEffect(0, Definition.LED_EFFECT_ALLOFF, null);
        }
    }

    /**
     * set green normal light effect
     */
    private void setGreenNormalLight() {
        int bodyLight = 0;
        if (SystemApi.getInstance().isUseAutoEffectLed()) {
            bodyLight ++;
            SystemApi.getInstance().setBottomLedEffect(0, Definition.LED_EFFECT_GREENNORMAL, null);
            if (ProductInfo.isSlimProduct()) {
                SystemApi.getInstance().setTrayLedEffect(0, Definition.LED_EFFECT_ALLOFF, null);
            } else {
                SystemApi.getInstance().setTrayLedEffect(0, Definition.TRAY_LIGHT_EFFECT_ALLOFF, null);
            }
        }
        if (SystemApi.getInstance().isHasClavicleLed()){//锁骨灯
            bodyLight ++;
            SystemApi.getInstance().setClavicleLedEffect(0, Definition.LED_EFFECT_GREENNORMAL, null);
        }
        if (SystemApi.getInstance().isHasChestLed()){//胸口灯
            bodyLight ++;
            SystemApi.getInstance().setClavicleLedEffect(0, Definition.LED_EFFECT_GREENNORMAL, null);
        }
        if(bodyLight == 0) {
            mCurrentStartColor = Integer.parseInt("37E737", 16);
            setLightColor();
        }
    }

    /**
     * set green breath light effect
     */
    private void setGreenBreathLight() {
        int bodyLight = 0;
        if (SystemApi.getInstance().isUseAutoEffectLed()) {
            bodyLight ++;
            SystemApi.getInstance().setBottomLedEffect(0, Definition.LED_EFFECT_GREENBREATH, null);
            if (ProductInfo.isSlimProduct()) {
                SystemApi.getInstance().setTrayLedEffect(0, Definition.LED_EFFECT_ALLOFF, null);
            } else {
                SystemApi.getInstance().setTrayLedEffect(0, Definition.TRAY_LIGHT_EFFECT_ALLOFF, null);
            }
        }
        if (SystemApi.getInstance().isHasClavicleLed()){//锁骨灯
            bodyLight ++;
            SystemApi.getInstance().setClavicleLedEffect(0, Definition.LED_EFFECT_GREENBREATH, null);
        }
        if (SystemApi.getInstance().isHasChestLed()){//胸口灯
            bodyLight ++;
            SystemApi.getInstance().setClavicleLedEffect(0, Definition.LED_EFFECT_GREENBREATH, null);
        }
        if(bodyLight == 0) {
            mCurrentStartColor = Integer.parseInt("37E737", 16);
            mCurrentEndColor = Integer.parseInt("061A06", 16);
            mType = Definition.LAMP_TYPE_BREATH;
            mDuration = 1300;
            mLoopTimes = -1;
            mStartTime = 0;
            mEndTime = 0;
            mFinalColor = Integer.parseInt("37E737", 16);
            setLightAnimation();
        }
    }

    /**
     * set red normal light effect
     */
    private void setRedNormalLight() {
        int bodyLight = 0;
        if (SystemApi.getInstance().isUseAutoEffectLed()) {
            bodyLight ++;
            if (ProductInfo.isSlimProduct()) {
                SystemApi.getInstance().setTrayLedEffect(0, Definition.LED_EFFECT_REDNORMAL, null);
            } else {
                SystemApi.getInstance().setTrayLedEffect(0, Definition.TRAY_LIGHT_EFFECT_ALLOFF, null);
            }
            SystemApi.getInstance().setBottomLedEffect(0, Definition.LED_EFFECT_REDNORMAL, null);
        }
        if (SystemApi.getInstance().isHasClavicleLed()){//锁骨灯
            bodyLight ++;
            SystemApi.getInstance().setClavicleLedEffect(0, Definition.LED_EFFECT_REDNORMAL, null);
        }
        if (SystemApi.getInstance().isHasChestLed()){//胸口灯
            bodyLight ++;
            SystemApi.getInstance().setClavicleLedEffect(0, Definition.LED_EFFECT_REDNORMAL, null);
        }
        if(bodyLight == 0) {
            mCurrentStartColor = Integer.parseInt("D31812", 16);
            setLightColor();
        }

    }

    private void setRedLowLight() {
        int bodyLight = 0;
        if (SystemApi.getInstance().isUseAutoEffectLed()) {
            bodyLight ++;
            SystemApi.getInstance().setBottomLedEffect(0, Definition.LED_EFFECT_REDNORMAL, null);
            if (ProductInfo.isSlimProduct()) {
                SystemApi.getInstance().setTrayLedEffect(0, Definition.LED_EFFECT_ALLOFF, null);
            } else {
                SystemApi.getInstance().setTrayLedEffect(0, Definition.TRAY_LIGHT_EFFECT_ALLOFF, null);
            }
        }
        if (SystemApi.getInstance().isHasClavicleLed()){//锁骨灯
            bodyLight ++;
            SystemApi.getInstance().setClavicleLedEffect(0, Definition.LED_EFFECT_REDNORMAL, null);
        }
        if (SystemApi.getInstance().isHasChestLed()){//胸口灯
            bodyLight ++;
            SystemApi.getInstance().setClavicleLedEffect(0, Definition.LED_EFFECT_REDNORMAL, null);
        }
        if(bodyLight == 0) {
            mCurrentStartColor = Integer.parseInt("D31812", 16);
            setLightColor();
        }

    }

    /**
     * set red breath light effect
     */
    private void setRedBreathLight() {
        int bodyLight = 0;
        if (SystemApi.getInstance().isUseAutoEffectLed()) {
            bodyLight ++;
            SystemApi.getInstance().setBottomLedEffect(0, Definition.LED_EFFECT_REDFLASH, null);
        }
        if (SystemApi.getInstance().isHasClavicleLed()){//锁骨灯
            bodyLight ++;
            SystemApi.getInstance().setClavicleLedEffect(0, Definition.LED_EFFECT_REDFLASH, null);
        }
        if (SystemApi.getInstance().isHasChestLed()){//胸口灯
            bodyLight ++;
            SystemApi.getInstance().setClavicleLedEffect(0, Definition.LED_EFFECT_REDFLASH, null);
        }
        if(bodyLight == 0){
            mCurrentStartColor = Integer.parseInt("D32812", 16);
            mCurrentEndColor = Integer.parseInt("991E0E", 16);
            mType = Definition.LAMP_TYPE_BREATH;
            mDuration = 1300;
            mLoopTimes = -1;
            mStartTime = 0;
            mEndTime = 0;
            mFinalColor = Integer.parseInt("D32812", 16);
            setLightAnimation();
        }

    }

    /**
     * set blue normal light effect
     */
    private void setBlueNormalLight() {
        int bodyLight = 0;
        if (SystemApi.getInstance().isUseAutoEffectLed()) {
            bodyLight ++;
            SystemApi.getInstance().setBottomLedEffect(0, Definition.LED_EFFECT_BLUENORMAL, null);
            if (ProductInfo.isSlimProduct()) {
                SystemApi.getInstance().setTrayLedEffect(0, Definition.LED_EFFECT_BLUENORMAL, null);
            }
        }
        if (SystemApi.getInstance().isHasClavicleLed()){//锁骨灯
            bodyLight ++;
            SystemApi.getInstance().setClavicleLedEffect(0, Definition.LED_EFFECT_BLUENORMAL, null);
        }
        if (SystemApi.getInstance().isHasChestLed()){//胸口灯
            bodyLight ++;
            SystemApi.getInstance().setClavicleLedEffect(0, Definition.LED_EFFECT_BLUENORMAL, null);
        }
        if(bodyLight == 0) {
            mCurrentStartColor = Integer.parseInt("5493FF", 16);
            setLightColor();
        }
    }

    /**
     * set blue breath light effect
     */
    private void setBlueBreathLight() {
        int bodyLight = 0;
        if (SystemApi.getInstance().isUseAutoEffectLed()) {
            bodyLight ++;
            SystemApi.getInstance().setBottomLedEffect(0, Definition.LED_EFFECT_BLUEBREATH, null);
        }
        if (SystemApi.getInstance().isHasClavicleLed()){//锁骨灯
            bodyLight ++;
            SystemApi.getInstance().setClavicleLedEffect(0, Definition.LED_EFFECT_BLUEBREATH, null);
        }
        if (SystemApi.getInstance().isHasChestLed()){//胸口灯
            bodyLight ++;
            SystemApi.getInstance().setClavicleLedEffect(0, Definition.LED_EFFECT_BLUEBREATH, null);
        }
        if (bodyLight == 0){
            mCurrentStartColor = Integer.parseInt("5493FF", 16);
            mCurrentEndColor = Integer.parseInt("111d33", 16);
            mType = Definition.LAMP_TYPE_BREATH;
            mDuration = 1300;
            mLoopTimes = -1;
            mStartTime = 0;
            mEndTime = 0;
            mFinalColor = Integer.parseInt("5493FF", 16);
            setLightAnimation();
        }
    }

    /**
     * set yellow normal light effect
     */
    private void setYellowNormalLight() {
        int bodyLight = 0;
        if (SystemApi.getInstance().isUseAutoEffectLed()) {
            bodyLight ++;
            SystemApi.getInstance().setBottomLedEffect(0, Definition.LED_EFFECT_YELLOWNORMAL, null);
        }
        if (SystemApi.getInstance().isHasClavicleLed()){//锁骨灯
            bodyLight ++;
            SystemApi.getInstance().setClavicleLedEffect(0, Definition.LED_EFFECT_YELLOWNORMAL, null);
        }
        if (SystemApi.getInstance().isHasChestLed()){//胸口灯
            bodyLight ++;
            SystemApi.getInstance().setClavicleLedEffect(0, Definition.LED_EFFECT_YELLOWNORMAL, null);
        }
        if(bodyLight == 0) {
            mCurrentStartColor = Integer.parseInt("FFD700", 16);
            setLightColor();
        }
    }

    private String getLightColorParam(int target) {
        JSONObject params = new JSONObject();
        try {
            params.put(Definition.JSON_LAMP_TYPE, Definition.LAMP_TYPE_COLOR);
            params.put(Definition.JSON_LAMP_TARGET, target);
            params.put(Definition.JSON_LAMP_COLOR_RGB_VALUE, mCurrentStartColor);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return params.toString();
    }

    private String getLightAnimationParam(int target) {
        JSONObject params = new JSONObject();
        try {
            params.put(Definition.JSON_LAMP_TYPE, mType);
            params.put(Definition.JSON_LAMP_TARGET, target);
            params.put(Definition.JSON_LAMP_RGB_START, mCurrentStartColor);
            params.put(Definition.JSON_LAMP_RGB_END, mCurrentEndColor);
            params.put(Definition.JSON_LAMP_START_TIME, mStartTime);
            params.put(Definition.JSON_LAMP_END_TIME, mEndTime);
            params.put(Definition.JSON_LAMP_REPEAT, mLoopTimes);
            params.put(Definition.JSON_LAMP_ON_TIME, mDuration);
            params.put(Definition.JSON_LAMP_RGB_FREEZE, mFinalColor);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return params.toString();
    }

    private void setLightAnimation() {
        SystemApi.getInstance().setLight(0,
                getLightAnimationParam(Definition.LAMP_TARGET_FOOT),null);
        SystemApi.getInstance().setLight(0,
                getLightColorParam(Definition.LAMP_TARGET_POWER_INSIDE),null);
        SystemApi.getInstance().setLight(0,
                getLightColorParam(Definition.LAMP_TARGET_POWER_OUTSIDE),null);
    }

    private void setLightColor() {
        SystemApi.getInstance().setLight(0,
                getLightColorParam(Definition.LAMP_TARGET_FOOT),null);
        SystemApi.getInstance().setLight(0,
                getLightColorParam(Definition.LAMP_TARGET_POWER_INSIDE),null);
        SystemApi.getInstance().setLight(0,
                getLightColorParam(Definition.LAMP_TARGET_POWER_OUTSIDE),null);
    }
}