package com.ainirobot.home.ota.bean;

/**
 *  OTA 执行事件通知接口 数据类
 *    - 在设备端执行 OTA 的过程中，把相关执行事件通知给服务端；
 *   - 某些关键事件要求必须成功通知到服务端之后才能进行后续步骤。
 */
public class OtaExecutionEvent {
    /**
     * 任务类型
     * main_downgrade：ROM 降级任务
     */
    private String taskType; // 任务类型
    /**
     * 任务事件类型。
     * 不同的任务类型需要上报不同的任务事件，公共事件以 tec_ 开头，非公共事件以 te_ 开头
     * tec_before_flush：下载rom完成后，install rom前
     */
    private String eventType; // 任务事件类型
    /**
     * 事件携带的数据，json 字符串
     */
    private EventData eventData; // 事件携带的数据

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public EventData getEventData() {
        return eventData;
    }

    public void setEventData(EventData eventData) {
        this.eventData = eventData;
    }

    @Override
    public String toString() {
        return "OtaExecutionEvent{" +
                "taskType='" + taskType + '\'' +
                ", eventType='" + eventType + '\'' +
                ", eventData=" + eventData +
                '}';
    }
}
