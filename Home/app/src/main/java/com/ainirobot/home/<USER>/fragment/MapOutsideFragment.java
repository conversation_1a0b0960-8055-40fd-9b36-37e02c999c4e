package com.ainirobot.home.ui.fragment;

import android.content.Context;
import android.media.AudioManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.RemoteException;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.client.speech.entity.LangParamsEnum;
import com.ainirobot.coreservice.listener.IMusicListener;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.ui.UIController;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MapOutsideFragment extends BaseFragment {
    private static final long DELAY_MILLIS = 2000;
    private final String TAG = MapOutsideFragment.class.getSimpleName() + ":Home";

    private Handler handler = new Handler();
    private AudioManager audioManager;
    private boolean isRunning;
    private TextView titleTv;
    private TextView warningTv;

    public MapOutsideFragment() {

    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        Log.d(TAG, "on create view");
        View view = inflater.inflate(R.layout.fragment_map_outside, container, false);
        titleTv = (TextView)view.findViewById(R.id.tv_alert_title);
        warningTv = (TextView)view.findViewById(R.id.tv_alert_message);
        return view;
    }

    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "onResume");
        isRunning = true;
        playAudio();
        reLoadUI();
    }

    private void reLoadUI(){
        titleTv.setText(getString(R.string.out_map_warning_alert_title));
        warningTv.setText(getString(R.string.out_map_warning_alert_message));
    }

    private void playAudio() {
        Log.d(TAG, "playAudio-start");
        audioManager = (AudioManager) ApplicationWrapper.getContext()
                .getSystemService(Context.AUDIO_SERVICE);
        ControlManager.getControlManager().setLastVolumeIndex(audioManager.getStreamVolume(AudioManager.STREAM_MUSIC));
        int maxMusicVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
        Log.d(TAG, "playAudio: maxMusicVolume=" + maxMusicVolume);
        audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, maxMusicVolume, 0);
        try {
            SkillManager.getInstance().playMusicByLocalPath(R.raw.safety_alert, true, true, new IMusicListener.Stub() {
                @Override
                public void onStart() throws RemoteException {
                    handler.post(playTextRunnable);
                }

                @Override
                public void onResume() throws RemoteException {

                }

                @Override
                public void onPause() throws RemoteException {

                }

                @Override
                public void onStop() throws RemoteException {

                }

                @Override
                public void onError() throws RemoteException {

                }

                @Override
                public void onComplete() throws RemoteException {

                }
            });
        } catch (RemoteException e) {
            e.printStackTrace();
        }

    }

    Runnable playTextRunnable = new Runnable() {
        @Override
        public void run() {
            handler.removeCallbacks(playTextRunnable);
            playText();
        }
    };

    private void playText() {
        if (!isRunning){
            return;
        }
        String systemLanguage = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_LANGUAGE);
        Log.d(TAG, "systemLanguage = " + systemLanguage);
        String text;
        String alertMessage = ApplicationWrapper.getContext().getString(R.string.out_map_warning_alert_message);
        if (systemLanguage.contains("zh")) {
            String corpName = getCorpName();
            if (TextUtils.isEmpty(corpName)) {
                text = alertMessage;
            } else {
                text = "我是" + corpName + "的机器人," + alertMessage;
            }
        } else {
            text = alertMessage;
        }

        try {
            SkillManager.getInstance().speechPlayText(text, new TextListener() {
                @Override
                public void onComplete() {
                    super.onComplete();
                    handler.postDelayed(playTextRunnable, DELAY_MILLIS);
                }

                @Override
                public void onError() {
                    super.onError();
                    handler.postDelayed(playTextRunnable, DELAY_MILLIS);
                }

                @Override
                public void onStop() {
                    super.onStop();
                    handler.postDelayed(playTextRunnable, DELAY_MILLIS);
                }
            });
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }


    private String getCorpName() {
        String corpName = SettingsUtil.getString(ApplicationWrapper.getContext(),
                RobotSettings.SETTINGS_GLOBAL_CORP_NAME);
        Log.d(TAG, "getCorpName: " + corpName);
        if (!TextUtils.isEmpty(corpName)) {
            String pattern = "[A-Za-z]+";
            Matcher matcher = Pattern.compile(pattern).matcher
                    (corpName);
            if (matcher.find()) {
                return "";
            } else {
                pattern = "\\(.*\\)|（.*）|\\[.*]|【.*】";
                return corpName.replaceAll(pattern, "");
            }
        }
        return "";
    }

    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
    }

    @Override
    public void onPause() {
        super.onPause();
        Log.d(TAG, "onPause: ");
        handler.removeCallbacks(playTextRunnable);
        isRunning = false;
        SkillManager.getInstance().stopTTSOnly();
        SkillManager.getInstance().stopMusic();
        audioManager.setStreamVolume(AudioManager.STREAM_MUSIC,
                ControlManager.getControlManager().getLastVolumeIndex(), 0);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
