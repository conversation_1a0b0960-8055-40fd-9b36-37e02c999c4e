package com.ainirobot.home.ota.network;

import static com.ainirobot.home.ota.constants.OtaConstants.OS_AC_CLIENT;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_BMS;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_HOST;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_MOTOR_HORIZON;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_MOTOR_LEFT;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_MOTOR_RIGHT;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_MOTOR_VERTICAL;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_PSB;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_PSB_S;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_TK1;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_TX1;

import com.google.gson.JsonObject;

public class NetDefine {

    // OtaService 使用的访问服务端环境
    public static final int RELEASE = 0;
    public static final int DEVELOP = 1;
    public static final int PRE_RELEASE = 2;
    /**
     * Orion. OTA host. also can be changed by /sdcard/robot/config/ota.properties.
     */
    public final static String ORION_UPDATE_HOST = "https://jiedai.ainirobot.com";
//    public final static String ORION_UPDATE_HOST = "http://tjd.ainirobot.com";

    /**
     * upload ota status interface.
     * type: 1, downloading diff package; 2, downloading full package;
     *      3, installing; 4, rollback processing
     * ota_version: package version
     * status: 1, start; 2, success; 3, fail;
     * desc: description
     * params: public ota message header, json string.
     */
    public final static String API_POST_STATUS = "/api/robot/ota_status";

    /**
     * check new version interface.
     * params: public ota message header, key-value.
     * 支持lxc单独升级更新检测地址
     */
    public final static String API_UPDATE_CHECK = "/api/robot/v2/ota_version";

    /**
     * full package url query interface.
     * params: public ota message header, key-value.
     * ota_version: target full package version.
     */
    public final static String API_GET_FULL_PACKAGE = "/api/robot/ota_package";


    /**
     * update status report.
     */
    public enum OTA_TYPE {
        DOWNLOAD_DIFF_PACKAGE(1),
        DOWNLOAD_FULL_PACKAGE(2),
        OTA_INSTALLING(3),
        OTA_ROLLBACK(4),
        OTA_UNZIP(5);

        int val;
        OTA_TYPE(int val) {
            this.val = val;
        }
        public int getValue() {
            return val;
        }
    }
    public final static String OTA_TYPE = "type";
    public final static String OTA_VERSION = "ota_version";
    public final static String OTA_BASE_VERSION = "from_version";
    public final static String OTA_VERSION_ID = "version_id";

    public enum OTA_STATUS {
        OTA_PRE_START(0),
        OTA_START(1),
        OTA_SUCCESS(2),
        OTA_FAILED(3);

        int val;
        OTA_STATUS(int val) {
            this.val = val;
        }
        public int getValue() {
            return val;
        }
    }
    public final static String OTA_STATUS = "status";
    public final static String OTA_DESC = "desc";

    public enum OTA_INSTALL_PROGRESS {
        OTA_INSTALL_DEFAULT(0),
        OTA_INSTALL_HOST(1),
        OTA_INSTALL_HEAD(2),
        OTA_INSTALL_NAVIGATION(3),
        OTA_INSTALL_PSB(4),
        OTA_INSTALL_AC_CLIENT(9),
        OTA_INSTALL_BMS(10),
        OTA_INSTALL_PSB_S(11),
        OTA_INSTALL_MOTOR_H(5),
        OTA_INSTALL_MOTOR_V(6),
        OTA_INSTALL_MOTOR_L(7),
        OTA_INSTALL_MOTOR_R(8);

        String val;
        OTA_INSTALL_PROGRESS(int val) {
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("board", Integer.valueOf(val).toString());
            this.val = jsonObject.toString();
        }

        public String getValue() {
            return val;
        }
    }
    public final static String OTA_PROGRESS = "progress";

    // check_new_version's return value
    public final static String RESULT_DESC = "desc";
    public final static String RESULT_VERSION_ID = "version_id";
    public final static String RESULT_VERSION = "version";
    public final static String RESULT_URL_FULL = "url_full";
    public final static String RESULT_URL_DIFF = "url_diff";
    public final static String RESULT_HASH_FULL = "hash_full";
    public final static String RESULT_HASH_DIFF = "hash_diff";
    public final static String RESULT_FORCED_UPDATE = "forced_update";
    // indicate diff_url is full or diff package. 1: full package 0: diff package
    public final static String RESULT_SUB_STATUS  = "sub_status";
    public final static int FULL_PACKAGE = 1;
    public final static int DIFF_PACKAGE = 0;
    public final static String RESULT_DOWNLOAD_START_TIME  = "download_start_time";
    public final static String RESULT_DOWNLOAD_END_TIME  = "download_end_time";
    public final static String RESULT_INSTALL_START_TIME  = "install_start_time";
    public final static String RESULT_INSTALL_END_TIME  = "install_end_time";
    public final static String RESULT_SILENT_UPDATE  = "silent_update";
    public final static String RESULT_OTA_LIST = "ota_list";
    public final static String RESULT_COMPONENT = "component";


    public static OTA_INSTALL_PROGRESS getInstallProgressId(String os) {
        switch (os) {
            case OS_HOST:
                return OTA_INSTALL_PROGRESS.OTA_INSTALL_HOST;
            case OS_TX1:
                return OTA_INSTALL_PROGRESS.OTA_INSTALL_HEAD;
            case OS_TK1:
                return OTA_INSTALL_PROGRESS.OTA_INSTALL_NAVIGATION;
            case OS_PSB:
                return OTA_INSTALL_PROGRESS.OTA_INSTALL_PSB;
            case OS_PSB_S:
                return OTA_INSTALL_PROGRESS.OTA_INSTALL_PSB_S;
            case OS_AC_CLIENT:
                return OTA_INSTALL_PROGRESS.OTA_INSTALL_AC_CLIENT;
            case OS_BMS:
                return OTA_INSTALL_PROGRESS.OTA_INSTALL_BMS;
            case OS_MOTOR_HORIZON:
                return OTA_INSTALL_PROGRESS.OTA_INSTALL_MOTOR_H;
            case OS_MOTOR_VERTICAL:
                return OTA_INSTALL_PROGRESS.OTA_INSTALL_MOTOR_V;
            case OS_MOTOR_LEFT:
                return OTA_INSTALL_PROGRESS.OTA_INSTALL_MOTOR_L;
            case OS_MOTOR_RIGHT:
                return OTA_INSTALL_PROGRESS.OTA_INSTALL_MOTOR_R;
        }

        return OTA_INSTALL_PROGRESS.OTA_INSTALL_DEFAULT;
    }
}
