/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.home.control;

import android.content.Context;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.RequiresApi;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.ainirobot.coreservice.bean.MultiFloorInfo;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingListener;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.bi.BiModuleChangeNotifier;
import com.ainirobot.home.module.AutoChargeModule;
import com.ainirobot.home.module.BaseModule;
import com.ainirobot.home.module.BleDistanceModule;
import com.ainirobot.home.module.CalibrationModule;
import com.ainirobot.home.module.ChargingModule;
import com.ainirobot.home.module.CustomTargetModule;
import com.ainirobot.home.module.DormancyModule;
import com.ainirobot.home.module.ElevatorRepositionModule;
import com.ainirobot.home.module.EmergencyModule;
import com.ainirobot.home.module.FullLockModule;
import com.ainirobot.home.module.HWAbnormalModule;
import com.ainirobot.home.module.HWE70StatusModule;
import com.ainirobot.home.module.ImportMapModule;
import com.ainirobot.home.module.InspectionModule;
import com.ainirobot.home.module.LauncherModule;
import com.ainirobot.home.module.LeavePileToPointModule;
import com.ainirobot.home.module.LiteLockModule;
import com.ainirobot.home.module.LoadMapModule;
import com.ainirobot.home.module.MapDriftModule;
import com.ainirobot.home.module.MapOutsideModule;
import com.ainirobot.home.module.MultiRobotErrorModule;
import com.ainirobot.home.module.NaviSensorModule;
import com.ainirobot.home.module.OTADowngradeModule;
import com.ainirobot.home.module.OTAModule;
import com.ainirobot.home.module.PushMapNeedSwitchModule;
import com.ainirobot.home.module.PushMapNoSwitchModule;
import com.ainirobot.home.module.RadarModule;
import com.ainirobot.home.module.RelocationModule;
import com.ainirobot.home.module.RemoteBindModule;
import com.ainirobot.home.module.RemoteControlModule;
import com.ainirobot.home.module.RemoteRepositionModule;
import com.ainirobot.home.module.RemoteStopChargingModule;
import com.ainirobot.home.module.RestoreFactorySetModule;
import com.ainirobot.home.module.RobotBeingPushedModule;
import com.ainirobot.home.module.SetChargePileModule;
import com.ainirobot.home.module.SettingModule;
import com.ainirobot.home.module.ShutdownModule;
import com.ainirobot.home.module.StandByModule;
import com.ainirobot.home.module.StopChargeConfirmModule;
import com.ainirobot.home.module.TimeWarningModule;
import com.ainirobot.home.module.WheelOverControlModule;
import com.ainirobot.home.utils.ResUtil;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Map;

public class ModuleManager {
    private Context mContext;
    private static final String TAG = "ModuleManager:Home";
    private static LinkedHashMap<Integer, BaseModule> MODULES;
    private Gson mGson = new Gson();
    private static  String mapName = null ;
    private ArrayList<MultiFloorInfo> mFloorList = new ArrayList<>();

    static {
        MODULES = new LinkedHashMap<Integer, BaseModule>() {{
            put(ModuleDef.FEATURE_ALL, BaseModule.getInstance());
            put(ModuleDef.FEATURE_OTA, OTAModule.getInstance());
            put(ModuleDef.FEATURE_EMERGENCY, EmergencyModule.getInstance());
            put(ModuleDef.FEATURE_STANDBY, StandByModule.getInstance());
            put(ModuleDef.FEATURE_AUTO_CHARGE, AutoChargeModule.getInstance());
            put(ModuleDef.FEATURE_SET_CHARGE_PILE, SetChargePileModule.getInstance());
            put(ModuleDef.FEATURE_INSPECTION, InspectionModule.getInstance());
            put(ModuleDef.FEATURE_LAUNCHER, LauncherModule.getInstance());
            put(ModuleDef.FEATURE_CHARGING, ChargingModule.getInstance());
            put(ModuleDef.FEATURE_SETTING_MODULE, SettingModule.getInstance());
            put(ModuleDef.FEATURE_REPOSITION, RelocationModule.getInstance());
            put(ModuleDef.FEATURE_REMOTE_REPOSITION, RemoteRepositionModule.getInstance());
            put(ModuleDef.FEATURE_REMOTE_BIND, RemoteBindModule.getsInstance());
            put(ModuleDef.FEATURE_HW_ABNORMAL, HWAbnormalModule.getInstance());
            put(ModuleDef.FEATURE_HW_E70, HWE70StatusModule.getInstance());
            put(ModuleDef.FEATURE_OPEN_RADAR, RadarModule.getInstance());
            put(ModuleDef.FEATURE_RESET, RestoreFactorySetModule.getInstance());
            put(ModuleDef.FEATURE_DORMANCY, DormancyModule.getInstance());
            put(ModuleDef.FEATURE_REMOTE_CONTROL, RemoteControlModule.getInstance());
            put(ModuleDef.FEATURE_FULL_LOCK, FullLockModule.getInstance());
            put(ModuleDef.FEATURE_LITE_LOCK, LiteLockModule.getInstance());
            put(ModuleDef.FEATURE_SHUTDOWN, ShutdownModule.getInstance());
            put(ModuleDef.FEATURE_REMOTE_PUSH_MAP_NEED_SWITCH, PushMapNeedSwitchModule.getInstance());
            put(ModuleDef.FEATURE_REMOTE_PUSH_MAP_NO_SWITCH, PushMapNoSwitchModule.getInstance());
            put(ModuleDef.FEATURE_MAP_DRIFT, MapDriftModule.getInstance());
            put(ModuleDef.FEATURE_MAP_OUTSIDE, MapOutsideModule.getInstance());
            put(ModuleDef.FEATURE_ROBOT_BEING_PUSHED, RobotBeingPushedModule.getInstance());
            put(ModuleDef.FEATURE_TIME_WARNING, TimeWarningModule.getInstance());
            put(ModuleDef.FEATURE_BLE_CLOSE, BleDistanceModule.getInstance());
            put(ModuleDef.FEATURE_D430_CALIBRATION, CalibrationModule.getInstance());
            put(ModuleDef.FEATURE_WHEEL_OVER_CONTROL, WheelOverControlModule.getInstance());
            put(ModuleDef.FEATURE_MULTI_ROBOT_ERROR, MultiRobotErrorModule.getInstance());
            put(ModuleDef.FEATURE_CUSTOM_TARGET, CustomTargetModule.getInstance());
            put(ModuleDef.FEATURE_REMOTE_STOP_CHARGING, RemoteStopChargingModule.getInstance());
            put(ModuleDef.FEATURE_NAVI_SENSOR_STATE, NaviSensorModule.getInstance());
            put(ModuleDef.FEATURE_NAVI_LOAD_MAP, LoadMapModule.getInstance());
            put(ModuleDef.FEATURE_NAVI_IMPORT_MAP, ImportMapModule.getInstance());
            put(ModuleDef.FEATURE_STOP_CHARGE_CONFIRM, StopChargeConfirmModule.getInstance());
            put(ModuleDef.FEATURE_OTA_DOWNGRADE, OTADowngradeModule.getInstance());
            put(ModuleDef.FEATURE_LEAVE_PILE_TO_POINT, LeavePileToPointModule.getInstance());
        }};
    }

    public ModuleManager(Context context) {
        this.mContext = context;
        OTAModule.getInstance().init(context);
        EmergencyModule.getInstance().init(context);
        StandByModule.getInstance().init(context);
        SetChargePileModule.getInstance().init(context);
        InspectionModule.getInstance().init(context);
        SettingModule.getInstance().init(context);
        AutoChargeModule.getInstance().init(context);
        RemoteRepositionModule.getInstance().init(context);
        RemoteBindModule.getsInstance().init(context);
        HWAbnormalModule.getInstance().init(context);
        HWE70StatusModule.getInstance().init(context);
        ChargingModule.getInstance().init(context);
        RestoreFactorySetModule.getInstance().init(context);
        DormancyModule.getInstance().init(context);
        MapDriftModule.getInstance().init(context);
        MapOutsideModule.getInstance().init(context);
        RobotBeingPushedModule.getInstance().init(context);
        TimeWarningModule.getInstance().init(context);
        BleDistanceModule.getInstance().init(context);
        CalibrationModule.getInstance().init(context);
        WheelOverControlModule.getInstance().init(context);
        MultiRobotErrorModule.getInstance().init(context);
        NaviSensorModule.getInstance().init(context);
        OTADowngradeModule.getInstance().init(context);
    }

    private void initRelocation(Context context) {


    }

    public boolean handleSemantics(int reqId, int module, String intent, String text, String
            param) {
        if (MODULES.get(module) != null) {
            BiModuleChangeNotifier.broadcastChangeModule(module, intent);
            BaseModule baseModule = MODULES.get(module);
            baseModule.updateState(ModuleDef.MODULE_STATE_RUNNING);
            return baseModule.onNewSemantics(reqId, intent, text, param);
        }
        return false;
    }

    public boolean handleHWReport(int module, int hwFunction, String command, String params) {
        if (MODULES.get(module) != null) {
            return MODULES.get(module).onHWReport(hwFunction, command, params);
        }
        return false;
    }

    public void stopModule(int module) {
        Log.i(TAG, "stop module:" + module);
        if (MODULES.get(module) != null) {
            MODULES.get(module).stop();
        }
    }

    public BaseModule getModule(int moduleId) {
        return MODULES.get(moduleId);
    }

    public int getFeatureId(BaseModule module) {
        for (Map.Entry<Integer, BaseModule> entry : MODULES.entrySet()) {
            if (entry.getValue() == module) {
                return entry.getKey();
            }
        }
        return ModuleDef.FEATURE_NONE;
    }

    public void sendMessageToModule(int module, int type) {
        if (MODULES.get(module) != null) {
            MODULES.get(module).onMessageFromLocal(type);
        }
    }

    public void sendMessageToModule(int module, int type, Object param) {
        if (MODULES.get(module) != null) {
            MODULES.get(module).onMessageFromLocal(type, param);
        }
    }

    /**
     * 机器人梯控配置的监听
     */
    @RequiresApi(api = Build.VERSION_CODES.N)
    public void listenElevatorControl() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            return;
        }
        // 在SystemApi连接成功后，初始化地图名称和楼层列表
        Log.d(TAG, "listenElevatorControl: SystemApi connected, initializing map and floor data");
        initMapNameAndFloorList();
        RobotSettingApi.getInstance().registerRobotSettingListener(new RobotSettingListener() {

            @RequiresApi(api = Build.VERSION_CODES.N)
            @Override
            public void onRobotSettingChanged(String key) {
                super.onRobotSettingChanged(key);
                if (Definition.ROBOT_SETTING_ELEVATOR_CONTROL_ENABLED.equals(key)) {
                    queryFloorList("registerRobotSettingListener");
                }
            }
        }, Definition.ROBOT_SETTING_ELEVATOR_CONTROL_ENABLED);

        SystemApi.getInstance().registerStatusListener(Definition.ACTION_SWITCH_MAP, new StatusListener() {
            @Override
            public void onStatusUpdate(String type, String data) {
                Log.d(TAG, "type = " + type + ", data = " + data);
//                type = action_switch_map, data = {"mapName":"0630pro测试-0630144231","mapTargetNum":3,"hasRoadGraph":1}
                try {
                    if (!TextUtils.isEmpty(data)) {
                        JSONObject json = new JSONObject(data);
                        mapName = json.optString("mapName", null);
                        Log.d(TAG, "Updated mapName to: " + mapName);
                        queryFloorList("registerRobotSettingListener");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error parsing map name from mapInfo: " + e.getMessage());
                }
            }
        });

    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    private void replaceModule() {
        boolean elevatorEnabled = isElevatorControlEnabled();
        boolean isInFloorList = isMapInFloorList(mapName);
        boolean isModule = ProductInfo.isAlnilamPro() ? isInFloorList && elevatorEnabled : elevatorEnabled;
        Log.d(TAG, "replaceModule elevatorEnabled: " + elevatorEnabled + ", isMapInFloorList(" + mapName + ") = " + isInFloorList);
        if (isModule) {
            Log.d(TAG, "replaceModule using ElevatorRepositionModule");
            MODULES.replace(ModuleDef.FEATURE_REPOSITION, ElevatorRepositionModule.getInstance());
            ElevatorRepositionModule.getInstance().init(mContext);
        } else {
            Log.d(TAG, "replaceModule using RelocationModule");
            MODULES.replace(ModuleDef.FEATURE_REPOSITION, RelocationModule.getInstance());
            RelocationModule.getInstance().init(mContext);
        }
    }

    /**
     * 获取所有楼层配置信息
     */
    public void queryFloorList(String from) {
        Log.d(TAG, "queryMultiFloorConfig onResult from : " + from);
        SystemApi.getInstance().queryMultiFloorConfig(0, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                Log.d(TAG, "queryMultiFloorConfig onResult msg : " + message + ", extraData : " + extraData);
                if (Definition.RESULT_OK == result && !TextUtils.isEmpty(message)) {
                    TypeToken<ArrayList<MultiFloorInfo>> token = new TypeToken<ArrayList<MultiFloorInfo>>() {};
                    mFloorList = mGson.fromJson(message, token.getType());
                    if (mapName != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        replaceModule();
                    }
                } else {
                    showToast(ResUtil.getString(R.string.elevator_reposition_no_navi_map));
                }
            }
        });
    }


    /**
     * 初始化时获取地图名称
     * 在SystemApi连接成功后调用，确保服务可用
     */
    private void initMapNameAndFloorList() {
        Log.d(TAG, "initMapNameAndFloorList: start getting map name");
        SystemApi.getInstance().getMapName(0, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                if (!"HWService not registered".equals(message)) {
                    if (!TextUtils.isEmpty(message)){
                        Log.d(TAG, "initMapNameAndFloorList getMapName onResult MapName : " + message);
                        mapName = message;
                        queryFloorList("initData");
                        Log.d(TAG, "initMapNameAndFloorList: map name initialized successfully");
                    } else {
                        Log.w(TAG, "initMapNameAndFloorList: received empty map name");
                    }
                }else {
                    initMapNameAndFloorList();
                }
            }
        });
    }


    /**
     * 当前用作导航的地图是否在多楼层地图列表中
     * @param mapName
     * @return
     */
    private boolean isMapInFloorList(String mapName) {
        if (TextUtils.isEmpty(mapName) || mFloorList == null || mFloorList.isEmpty()) {
            return false;
        }
        for (MultiFloorInfo info : mFloorList){
            if (info != null && TextUtils.equals(mapName, info.getMapName())){
                return true;
            }
        }
        return false;
    }

    /**
     * 电梯控制功能是否启用
     * @return true=已启用，false=已禁用
     */
    private boolean isElevatorControlEnabled() {
        int enabled = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_ELEVATOR_CONTROL_ENABLED);
        boolean isEnabled = enabled == 1;
        Log.d(TAG, "isElevatorControlEnabled: " + isEnabled + " (raw value: " + enabled + ")");
        return isEnabled;
    }

    private void showToast(final String text) {
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                Toast.makeText(ApplicationWrapper.getContext(), text, Toast.LENGTH_SHORT).show();
            }
        });
    }

}
