package com.ainirobot.home.ui.fragment;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.ui.UIController;

import java.util.ArrayList;
import java.util.List;

public class HWE70StatusFragment extends BaseFragment{

    private static final String TAG = HWE70StatusFragment.class.getSimpleName();

    private TextView mErrorView;
    private TextView mErrorCodeTv;
    private TextView mRobotSnTv;
    private TextView mTimeStampTv;
    private Button mRebootTV;

    public int mTimerTime = MINI_DEFAULT_WAIT_TIME;
    public static final int MINI_DEFAULT_WAIT_TIME = 120;
    /**
     * 当前是否有异常，正在展示异常界面
     */
    private Handler mTimerHandler;

    public static final String PSB_ERROR = "PSB_NO_HEARTBEAT";


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "on create");
    }

    private void initCountdownTime() {
        mTimerTime = MINI_DEFAULT_WAIT_TIME;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle
            savedInstanceState) {
        Log.d(TAG, "on onCreateView");
        View view = inflater.inflate(R.layout.fragment_hw_e70_state, null);
        mErrorView = (TextView) view.findViewById(R.id.hw_error_message);
        mErrorCodeTv = (TextView) view.findViewById(R.id.tv_errorCode);
        mRobotSnTv = (TextView) view.findViewById(R.id.tv_sn);
        mTimeStampTv = (TextView) view.findViewById(R.id.tv_timestamp);
        mRebootTV = (Button) view.findViewById(R.id.timer_button);
        mRebootTV.setClickable(false);

        initCountdownTime();
        initRobotInfo();
        return view;
    }

    private void initRobotInfo(){

        String robotSn = RobotSettings.getSystemSn();
        mRobotSnTv.setText(robotSn);

        String timeStamp = Long.toString(System.currentTimeMillis());
        mTimeStampTv.setText(timeStamp);

    }

    private void updateTiemrUI(){
        if (mRebootTV != null){
            mRebootTV.post(new Runnable() {
                @Override
                public void run() {
                    if (isDetached() || !isAdded() || getActivity() == null) {
                        return;
                    }
                    if (mTimerTime <= 0){
                        mRebootTV.setText(getActivity().getString(R.string.restart));
                    }else {
                        mRebootTV.setText(getActivity().getString(R.string.hw_retry_recovery, mTimerTime));
                    }
                }
            });
        }
    }

    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
        Log.d(TAG, " type：" + type);
        switch (type) {
            case HW_E70_ERROR:
                showE70Error(message);
                break;
            case HW_E70_RECOVERY:
                removeHandlerMsg();
                break;
            default:
                break;
        }
    }

    private void showE70Error(String message){
        Log.d(TAG,"showE70Error  message:" + message);
        if (!TextUtils.isEmpty(message) && message.contains(Definition.STATUS_PSB_ERROR)){
            mErrorView.post(new Runnable() {
                @Override
                public void run() {
                    mErrorView.setText(getString(R.string.hw_psb_error));
                    mErrorCodeTv.setText(PSB_ERROR);
                    initHandler();
                    sendTimeUpdateMsg();
                }
            });
        }
    }

    private void initHandler(){
        if (mTimerHandler != null){
            mTimerHandler.removeMessages(ModuleDef.MSG_HWABNORMAL_REBOOT_TIMER);
        }
        mTimerHandler = new Handler(Looper.getMainLooper()){
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);
                switch (msg.what) {
                    case ModuleDef.MSG_HWABNORMAL_REBOOT_TIMER:
                        mTimerTime--;
                        if (mTimerTime <= 0){
                            sendHwRecoveryTimeoutMsg();
                            sendMessageToRebootRobot();
                            removeHandlerMsg();
                        }else {
                            sendTimeUpdateMsg();
                        }
                        updateTiemrUI();
                        break;
                    default:
                        break;
                }
            }
        };
    }

    private void removeHandlerMsg() {
        if (mTimerHandler != null) {
            mTimerHandler.removeMessages(ModuleDef.MSG_HWABNORMAL_REBOOT_TIMER);
            mTimerHandler = null;
        }
    }

    private void sendTimeUpdateMsg(){
        if (mTimerHandler != null) {
            Message newMsg = Message.obtain();
            newMsg.what = ModuleDef.MSG_HWABNORMAL_REBOOT_TIMER;
            mTimerHandler.sendMessageDelayed(newMsg, 1000);
        }
    }

    private void sendMessageToRebootRobot(){
        Log.d(TAG, "sendMessageToRebootRobot");
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_HW_STATUS_CANNOT_RECOVERY);
    }

    private void sendHwRecoveryTimeoutMsg(){
        Log.d(TAG, "sendHwRecoveryTimeoutMsg");
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_HW_AUTOMATIC_RECOVERY_TIMEOUT);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "onDestroy");
        removeHandlerMsg();
    }

}
