package com.ainirobot.home.bi;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;


public class BiDormancyBeginReport extends BaseBiReport {
    public static final String VALUE_FROM_SYSTEM = "system";
    public static final int VALUE_TYPE_FUNCTION = 0;
    public static final int VALUE_TYPE_OTHER = 1;
    public static final int VALUE_TYPE_REMOTE = 2;
    private static final String TABLE_NAME = "base_robot_dormancy_start";

    private static final String CTIME = "ctime";

    private static final String FROM = "from";

    private static final String TYPE = "type";

    public BiDormancyBeginReport() {
        super(TABLE_NAME);
    }

    public BiDormancyBeginReport addFrom(String from) {
        addData(FROM, from);
        return this;
    }

    public BiDormancyBeginReport addType(int type) {
        addData(TYPE, type);
        return this;
    }

    @Override
    public void report() {
        addData(CTIME, System.currentTimeMillis());
        super.report();
    }

}
