package com.ainirobot.home.ota.bean;

import static com.ainirobot.home.ota.constants.OtaConstants.AUTHORITY;

import android.net.Uri;

public class VersionData {
    public final static Uri CONTENT_URI = Uri.parse("content://" + AUTHORITY + "/update");

    public class DB {
        public final static String id = "_id";
        public final static String name = "name";
        public final static String status = "status";
        public final static String currentVersion = "currentVersion";
        public final static String targetVersion = "targetVersion";
    }

    public enum STATUS {
        NA(0),
        SUCCESS(1),
        FAILED(2),
        WAITING(3);

        int val = 0;

        STATUS(int val) {
            this.val = val;
        }

        public int toInt() {
            return this.ordinal();
        }

        public STATUS toEnum(int val) {
            return values()[val];
        }
    }

    private int id;
    private String name;
    private String currentVersion;
    private String targetVersion;
    private STATUS status;

    public VersionData() {
        id = -1;
        name = null;
        currentVersion = null;
        targetVersion = null;
        status = STATUS.NA;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getId() {
        return id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setCurrentVersion(String version) {
        this.currentVersion = version;
    }

    public String getCurrentVersion() {
        return currentVersion;
    }

    public void setTargetVersion(String targetVersion) {
        this.targetVersion = targetVersion;
    }

    public String getTargetVersion() {
        return targetVersion;
    }

    public void setStatus(int status) {
        this.status = this.status.toEnum(status);
    }

    public void setStatus(STATUS status) {
        this.status = status;
    }

    public STATUS getStatus() {
        return status;
    }

    @Override
    public String toString() {
        return DB.id + "= " + id + " "
                + DB.name + "= " + name + " "
                + DB.currentVersion + "= " + currentVersion + " "
                + DB.targetVersion + "= " + targetVersion + " "
                + DB.status + "= " + status;
    }
}