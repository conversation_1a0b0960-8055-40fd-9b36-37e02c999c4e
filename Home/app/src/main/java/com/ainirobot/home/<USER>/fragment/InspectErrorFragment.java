package com.ainirobot.home.ui.fragment;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.IBinder;
import android.os.Message;
import android.os.RemoteException;
import android.os.SystemClock;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.utils.ResType;
import com.ainirobot.home.utils.ResUtil;
import com.ainirobot.home.utils.SettingUtils;
import com.ainirobot.home.utils.WeakHandler;

import java.lang.reflect.Method;

public class InspectErrorFragment extends BaseFragment {
    private final String TAG = this.getClass().getSimpleName() + ":Home";

    private static final int MSG_QR_CODE_RREATED = 101;

    private TextView mTvIdSn;
    private TextView mTvFeedBackTips;
    private ImageView mIvQrCode;
    private TextView mTvQrCodeTips;
    private TextView mTvErrorMessage;
    private TextView mInspectTitle;

    private String mNaviErrorId;
    private String mErrorMessage;
    private MyWeakHandler mHandler;
    private Button mBtnLxcRepair;
    private Button mBtnVersionRollBack;

    class MyWeakHandler extends WeakHandler {

        public MyWeakHandler(Activity ref) {
            super(ref);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (conflict())
                return;
            switch (msg.what) {
                case MSG_QR_CODE_RREATED:
                    Bitmap bitmap = (Bitmap) msg.obj;
                    if (bitmap != null) {
                        mIvQrCode.setImageBitmap(bitmap);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mHandler = new MyWeakHandler(getActivity());
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater,
                             @Nullable ViewGroup container, Bundle savedInstanceState) {
        Log.i(TAG, "on create view");
        View view = inflater.inflate(R.layout.fragment_inspect_error, null);
        initData();
        initView(view);
        return view;
    }

    @Override
    public void onStart() {
        super.onStart();
        Log.i(TAG, "onstart");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.d(TAG, "onDestroyView");
    }

    private void initData() {
        if (getArguments() != null) {
            mErrorMessage = getArguments().getString(ModuleDef.INSPECT_ERROR_MESSAGE);
            mNaviErrorId = getArguments().getString(ModuleDef.INSPECT_NAVI_ERROR_ID);
        }
        Log.i(TAG, "mErrorMessage= " + mErrorMessage + ", mNaviErrorId= " + mNaviErrorId);
    }

    private void initView(View view) {
        mTvIdSn = (TextView) view.findViewById(R.id.tv_id_sn);
        mTvFeedBackTips = (TextView) view.findViewById(R.id.tv_feedback_tips);
        mTvQrCodeTips = (TextView) view.findViewById(R.id.tv_qr_code_tips);
        mIvQrCode = (ImageView) view.findViewById(R.id.iv_qr_code);
        mTvErrorMessage = (TextView) view.findViewById(R.id.inspect_error_message);
        ImageView mIvPhone = (ImageView) view.findViewById(R.id.iv_phone_number);
        TextView mTvPhoneNumber = (TextView)view.findViewById(R.id.tv_phone_number);
        if (ProductInfo.isDeliveryOverSea() || SettingUtils.isOverSea()){
            mIvPhone.setVisibility(View.GONE);
            mTvPhoneNumber.setVisibility(View.GONE);
        }
        mInspectTitle = (TextView) view.findViewById(R.id.inspect_error_title);
        //横屏调整为一行显示
        if (ResType.isLandScape()) {
            mInspectTitle.setText(getContext().getString(R.string.inspect_err_title).replaceAll("\n", ""));
        }
        mInspectTitle.setOnClickListener(new View.OnClickListener() {
            final static int COUNTS = 7;
            final static long DURATION = 3 * 1000;
            long[] mHits = new long[COUNTS];

            @Override
            public void onClick(View v) {
                System.arraycopy(mHits, 1, mHits, 0, mHits.length - 1);
                mHits[mHits.length - 1] = SystemClock.uptimeMillis();
                if (mHits[0] >= (SystemClock.uptimeMillis() - DURATION)) {
                    Log.d(TAG, "title onClick to exit inspection module!");
                    ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_STOP_INSPECTION);
                }
            }
        });

        View inspectShutdown  = view.findViewById(R.id.inspect_shutdown);
        if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) {
            inspectShutdown.setVisibility(View.VISIBLE);
            inspectShutdown.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    try {
                        shutdown("inspect error shutdown!!!");
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
            });
        }else {
            inspectShutdown.setVisibility(View.GONE);
        }


        String systemLanguage = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_LANGUAGE);
        Log.d(TAG,"systemLanguage = "+systemLanguage);
        /*if (systemLanguage.contains("zh")){
            String title = mInspectTitle.getText().toString();
            SpannableStringBuilder builder = new SpannableStringBuilder(title);
            ForegroundColorSpan whiteSpan = new ForegroundColorSpan(Color.WHITE);
            builder.setSpan(whiteSpan, 13, 15, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
            mInspectTitle.setText(builder);
        }*/
        String prefix = "";
        if (getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE) {
            prefix = getResources().getString(R.string.inspect_err_robot_sn_land);
        } else {
            prefix = getResources().getString(R.string.inspect_err_robot_sn);
        }
        Log.d(TAG, "initView prefix :"+prefix+", sn: "+RobotSettings.getSystemSn()+", mNaviErrorId:"+mNaviErrorId);
        mTvIdSn.setText(prefix + RobotSettings.getSystemSn());
        if (!TextUtils.isEmpty(mErrorMessage)) {
            mTvErrorMessage.setText(mErrorMessage);
        }
        if (!TextUtils.isEmpty(mNaviErrorId)) {
            showQrCodeUI();
            if (!SettingUtils.isOverSea()){
                createQrCode((getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE ?
                        getResources().getString(R.string.inspect_err_navigation_log_id_land)
                        : getResources().getString(R.string.inspect_err_navigation_log_id))
                        + mNaviErrorId);
            }
        }

        mBtnLxcRepair = (Button) view.findViewById(R.id.butt_lxc_repair);
        mBtnLxcRepair.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                //跳转LXC修复页面
                naviToLxcRepair();
            }
        });

        mBtnVersionRollBack = view.findViewById(R.id.button_version_roll_back);
        mBtnVersionRollBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                naviToLxcRepair();
            }
        });
    }

    private void showQrCodeUI() {
        mTvIdSn.setText((getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE ?
                getResources().getString(R.string.inspect_err_navigation_log_id_land)
                : getResources().getString(R.string.inspect_err_navigation_log_id))
                + mNaviErrorId);
        mIvQrCode.setVisibility(View.VISIBLE);
        if (!SettingUtils.isOverSea()){
            mTvQrCodeTips.setVisibility(View.VISIBLE);
            mTvFeedBackTips.setVisibility(View.GONE);
        }
    }

    private void createQrCode(final String content) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                Bitmap centerLogo = ResUtil.getCenterLogoBitmap(R.drawable.first_qrcode_logo);
                Bitmap qrImageBitmap = ResUtil.createQRImage(content, 148, 148,
                        "1", centerLogo);
                if (mHandler != null) {
                    Message message = Message.obtain();
                    message.what = MSG_QR_CODE_RREATED;
                    message.obj = qrImageBitmap;
                    mHandler.sendMessage(message);
                }
            }
        }).start();
    }

    private int shutdown(String reason) throws RemoteException {

        try {
            Class<?> serviceManager = Class.forName("android.os.ServiceManager");
            Method getService = serviceManager.getMethod("getService", String.class);
            Object remoteService = getService.invoke(null, Context.POWER_SERVICE);
            Class<?> stub = Class.forName("android.os.IPowerManager$Stub");
            Method asInterface = stub.getMethod("asInterface", IBinder.class);
            Object powerManager = asInterface.invoke(null, remoteService);
            Method shutdown = powerManager.getClass().getDeclaredMethod("shutdown",
                    boolean.class, String.class, boolean.class);
            shutdown.invoke(powerManager, false, reason, true);
        } catch (Exception e) {
            //nothing to do
        }

        return 0;
    }

    /**
     * 跳转到Lxc修复Activity
     */
    private void naviToLxcRepair() {
        Log.d(TAG,"naviToLxcRepair");
        Intent intent = new Intent();
        intent.setClassName("com.ainirobot.settings", "com.ainirobot.settings.activity.LxcRepairActivity");
        startActivity(intent);
    }

}
