package com.ainirobot.home.module;

import static android.media.MediaDrm.PROPERTY_VERSION;

import static com.ainirobot.home.ota.constants.DowngradeConstants.START_COMMAND_DOWNLOAD_CONTINUE;
import static com.ainirobot.home.ota.constants.DowngradeConstants.START_COMMAND_DOWNLOAD_PAUSE;

import android.app.Activity;
import android.app.Dialog;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.provider.Settings;
import android.support.v4.app.ActivityOptionsCompat;
import android.util.Log;
import android.util.Pair;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.ainirobot.base.util.FileUtils;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.ota.constants.DowngradeConstants;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.fragment.OTAResultFragment;
import com.ainirobot.home.ui.view.OTADialog;
import com.ainirobot.home.ui.view.OTAInspectFailDialog;
import com.ainirobot.home.ui.view.OTANetworkDialog;
import com.ainirobot.home.utils.ResUtil;
import com.ainirobot.home.utils.SystemUtils;
import com.ainirobot.home.utils.ToastUtil;
import com.ainirobot.home.utils.WifiUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.subjects.PublishSubject;

/**
 * Ota降级模块实现
 * 1. 接收core消息，下载降级包
 * 2. 降级下载进度 & 降级安装进度
 */
public class OTADowngradeModule extends BaseModule {
    private static final String TAG = OTADowngradeModule.class.getSimpleName() + "Home:";
    private Context mContext;
    private static final int WHAT_REQ_OTA_DOWNGRADE = 0;
    private static final int WHAT_STATUS_OTA_PROGRESS = 1;
    private static final int WHAT_STATUS_OTA_INSTALL = 2;
    private static final int WHAT_REQ_OTA_FINISH = 3;
    private final static int WHAT_REQ_RELEASE = 4;
    private String mStartFrom; // 降级触发方式，一期只有从设置手动触发
    private boolean mNeedDownload = true;
    private String mVersionCode; // 降级版本号
    private String mVersionDescription; // 降级版本描述
    private String mVersionId;
    private boolean mIsOtaDialogShow = false;
    private boolean mIsUpgrading = false;
    private boolean mIsOtaSuccess = false;
    private final static String RN_DATA_PATH = Environment.getExternalStorageDirectory()
            + File.separator + "robot/rndata";
    private final static String RN_DOWNLOAD_PATH = Environment.getExternalStorageDirectory()
            + File.separator + "robot/rndownload";
    private final static String RN_CONFIG_PATH = Environment.getExternalStorageDirectory()
            + File.separator + "robot/rnconfig";

    private boolean mDataCompatibilitySuccess = false; // 数据兼容处理是否成功
    private PublishSubject<Boolean> mCompatibilitySubject = PublishSubject.create();
    private PublishSubject<Boolean> mInstallMessageSubject = PublishSubject.create();
    private CompositeDisposable mDisposables = new CompositeDisposable();
    private boolean mNeedReboot = false;
    private boolean mIsInspect = false;
    private String mInspectionResult;
    private boolean mInspectionSuccess;
    private String mNaviError;

    private boolean mShowAfterInspectIcon;
    private String mShowAfterInspectionMessage;
    private boolean mShowAfterInspectionRelease;
    private final static long NORMAL_DELAY = 1000;
    private final static long BATTERY_LOWER_DELAY = 3000;
    private boolean mIsStartPowerInstall = false;
    private Dialog mOtaDownloadFail;
    Pattern pattern = Pattern.compile("^V(\\d+\\.\\d+)");

    private static class SingletonHolder {
        private static final OTADowngradeModule mInstance = new OTADowngradeModule();
    }

    public static OTADowngradeModule getInstance() {
        return OTADowngradeModule.SingletonHolder.mInstance;
    }
    public void init(Context context) {
        mContext = context;
        // 订阅兼容性变化
        mDisposables.add(Observable.combineLatest(
            mCompatibilitySubject, mInstallMessageSubject, Pair::new)
            .filter(pair -> pair.first && pair.second)
            .subscribe(pair -> sendOtaExecutionEvent())
        );
    }

    private final StatusListener mProgressListener = new StatusListener() {
        @Override
        public void onStatusUpdate(String type, String data) {
            Message message = mHandler.obtainMessage();
            message.arg1 = -1;
            message.obj = data;
            message.what = WHAT_STATUS_OTA_PROGRESS;
            mHandler.sendMessage(message);
        }
    };

    private final Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            String params = (String) msg.obj;
            int reqId = msg.arg1;
            switch (msg.what) {
                case WHAT_REQ_OTA_DOWNGRADE:
                    Log.d(TAG, "WHAT_REQ_OTA_DOWNGRADE: " + params);
                    try {
                        JSONObject object = new JSONObject(params);
                        mStartFrom = object.getString("startup");
                        JSONObject param = new JSONObject(object.getString("otaInfo"));
                        boolean isUserStart = param.getBoolean(Definition.JSON_OTA_IS_USER_TOUCH);
                        mNeedDownload = param.getBoolean(Definition.JSON_OTA_NEED_DOWNLOAD);
                        if (mNeedDownload) {
                            handleDowngradeLogic();
                        } else {
                            processUpgrade(true);
                        }
                    } catch (JSONException e) {
                        Log.e(TAG, "WHAT_REQ_OTA_DOWNGRADE parse params error: " + e.getMessage());
                    }
                    break;

            case WHAT_STATUS_OTA_PROGRESS:
                Log.i(TAG, "ota params = " + params);
                try {
                    JSONObject jsonObject = new JSONObject(params);
                    boolean isDownload = jsonObject.optBoolean(Definition.JSON_OTA_IS_DOWNLOAD_PROGRESS);
                    double progress = jsonObject.optDouble(Definition.JSON_OTA_INSTALL_PERCENT);
                    //download progress
                    if (isDownload) {
                        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.PROGRESS_DOWNLOAD,
                                (int) (100 * progress) + "");
                    } else {
                        // install progress
                        if (!mIsStartPowerInstall) {
                            startPowerListener();
                        }
                        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.PROGRESS_INSTALL,
                                (int) (100 * progress) + "");
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                    Log.e(TAG, e.getMessage());
                }
                break;

            case WHAT_STATUS_OTA_INSTALL:
                if (mDataCompatibilitySuccess) {
                    sendOtaExecutionEvent();
                } else {
                    Log.d(TAG, "mDataCompatibilitySuccess为false，将install消息加入队列。");
                    onInstallMessageReceived();
                }
                break;

            case WHAT_REQ_OTA_FINISH:
                Log.d(TAG, "WHAT_REQ_OTA_FINISH: " + params);
                try {
                    JSONObject object = new JSONObject(params);
                    JSONObject finishJsonObject = new JSONObject(object.getString("otaInfo"));
                    String result = finishJsonObject.optString(Definition.JSON_OTA_RESULT);
                    int code = finishJsonObject.optInt(Definition.JSON_OTA_CODE);
                    String updateType = finishJsonObject.optString(Definition.JSON_OTA_TYPE, "");
                    mNeedReboot = finishJsonObject.optBoolean(Definition.JSON_OTA_REBOOT, false);
                    Log.d(TAG, "analyse result=" + result + ", code=" + code +
                            ",type=" + updateType + ",reboot=" + mNeedReboot);
                    handleOtaFinish(result, updateType, code);
                    SystemApi.getInstance().finishModuleParser(reqId, true);
                } catch (JSONException e) {
                    e.printStackTrace();
                    mNeedReboot = false;
                    SystemApi.getInstance().finishModuleParser(reqId, true);
                    releaseOtaModule();
                }
                break;
            case WHAT_REQ_RELEASE:
                Log.i(TAG, "ota WHAT_REQ_RELEASE mNeedReboot = " + mNeedReboot);
                if (mNeedReboot) {
                    realReboot();
                } else {
                    releaseOtaModule();
                }
                break;
            default:
                break;
            }
        }
    };

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.i(TAG, "reqId=" + reqId + "; intent=" + intent + "; params=" + params + " " + getState());
        switch (intent) {
            case Definition.REQ_OTA_AUTH_DOWNGRADE:
                // 禁用三指下拉
                SystemUtils.setThreeFinger(false);
                mIsOtaSuccess = false;
                Message downgradeMessage = mHandler.obtainMessage();
                downgradeMessage.what = WHAT_REQ_OTA_DOWNGRADE;
                downgradeMessage.arg1 = reqId;
                downgradeMessage.obj = params;
                mHandler.sendMessage(downgradeMessage);
                sendStatusReport(reqId, intent, text, true, "start");
                SystemApi.getInstance().finishModuleParser(reqId, true);
                break;
            case Definition.REQ_OTA_DOWNLOAD_SUCCESS:
                Message otaDownloadSuccessMsg = mHandler.obtainMessage();
                otaDownloadSuccessMsg.what = WHAT_STATUS_OTA_INSTALL; // ota已经下载成功了，可以安装了
                otaDownloadSuccessMsg.arg1 = reqId;
                otaDownloadSuccessMsg.obj = params;
                mHandler.sendMessage(otaDownloadSuccessMsg);
                SystemApi.getInstance().finishModuleParser(reqId, true);
                break;
            case Definition.REQ_OTA_DOWNGRADE_INSPECT:
                SystemUtils.setThreeFinger(false);
                initInspectionInfo();
                Log.d(TAG, "mInspectionSuccess : " + mInspectionSuccess
                        + ", mInspectionResult : " + mInspectionResult + ", mNaviError : " + mNaviError);
                if (!mInspectionSuccess) {
                    UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_INSPECT_OTA, null,
                            ActivityOptionsCompat.makeCustomAnimation(mContext, 0, 0).toBundle());
                }
                break;
            case Definition.REQ_OTA_DOWNGRADE_RESULT:
                SystemUtils.setThreeFinger(false);
                stopPowerListener();
                Message finishMessage = mHandler.obtainMessage();
                finishMessage.what = WHAT_REQ_OTA_FINISH;
                finishMessage.arg1 = reqId;
                finishMessage.obj = params;
                mHandler.sendMessage(finishMessage);
                sendStatusReport(reqId, intent, text, true, "finish");
                break;
            default:
                break;
        }
        return super.onNewSemantics(reqId, intent, text, params);
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
        Log.d(TAG, "onMessageFromLocal: " + type);
        switch (type) {
            case ModuleDef.LOCAL_MESSAGE_PAUSE_OTA:
                pauseOta(true);
                break;
            case ModuleDef.LOCAL_MESSAGE_CONTINUE_OTA:
                pauseOta(false);
                break;
            case ModuleDef.LOCAL_MESSAGE_CANCEL_OTA:
                SystemApi.getInstance().cancelOtaUpgradeDownload();
                releaseOtaModule();
                break;
            case ModuleDef.LOCAL_MESSAGE_STOP_INSPECTION:
                releaseOtaModule();
                break;
            default:
                break;
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    private void initInspectionInfo() {
        mIsInspect = true;
        mInspectionSuccess = ControlManager.getControlManager().getSystemStatusManager().isInspectSuccess();
        mInspectionResult = ControlManager.getControlManager().getSystemStatusManager().getInspectionResult();
        mNaviError = ControlManager.getControlManager().getSystemStatusManager().getNaviErrorId();
    }

    /**
     * 在最接近安装rom的时候，向后端上报消息
     * 只有成功才能继续执行后续操作
     */
    private void sendOtaExecutionEvent() {
        SystemApi.getInstance().notifyOtaExecutionEvents(0, createSendOtaExecutionEventData(),
            new CommandListener() {
                @Override
                public void onResult(int result, String message, String extraData) {
                    Log.d(TAG, "notifyOtaExecutionEvents result : " + result + ", message : " + message + ", extraData :" + extraData);
                    if (result == Definition.RESULT_OK && !message.isEmpty()) {
                        processInstallMessages();
                    }
                }

                @Override
                public void onError(int errorCode, String errorString, String extraData) throws RemoteException {
                    Log.e(TAG, "notifyOtaExecutionEvents error : " + errorString);
                    // 重新再调用一次服务端上报接口
                    sendOtaExecutionEvent();
                }
            });
    }

    /**
     * 删除系统下不兼容的数据
     * 1. Navi中数据库处理
     * 2. 各类服务中数据库管理实现onDowngrade方法
     * 3. platform数据库管理实现onDowngrade方法
     * 4. 删除：所有opk,cfg
     * 5. 修改PresetRomVersion值
     */
    private void handleClearSystemData() {
        String mDowngradeTargetVersion = "";
        if (mVersionCode != null && !mVersionCode.isEmpty()) {
            Matcher matcher = pattern.matcher(mVersionCode);
            if (matcher.find()) {
                mDowngradeTargetVersion = matcher.group(1); // V10.3.2024102519.1019DB -> V10.3
            }
        }
        Log.d(TAG, "mDowngradeTargetVersion : " + mDowngradeTargetVersion);
        // 1. 调用接口 navi中删除导航数据库数据，迁移数据库
        SystemApi.getInstance().startNavigationOtaDowngrade(0, mDowngradeTargetVersion,false, mVersionCode, new CommandListener(){
            @Override
            public void onResult(int result, String message, String extraData) {
                Log.d(TAG, "onResult result: " +  result + " message: " + message + " extraData: " + extraData);
                if (Definition.RESULT_OK == result && !message.isEmpty()) {
                    Log.i(TAG, "Navi 数据删除且数据迁移成功。");
                    // 删除所有opk和cfg & 修改romVersion值
                    boolean presetRomVersionSetSuccess = Settings.Global.putString(mContext.getContentResolver(), "PresetRomVersion", "");
                    boolean deleteResult = deleteOpkList();
                    Log.d(TAG, "deleteResult: " + deleteResult + " presetRomVersionSetSuccess: " + presetRomVersionSetSuccess);
                    if (deleteResult && presetRomVersionSetSuccess) {
                        mDataCompatibilitySuccess = true;
                        onDataCompatibilitySuccess();
                    }
                }
            }

            @Override
            public void onError(int errorCode, String errorString, String extraData) throws RemoteException {
                Log.e(TAG, "兼容处理 onError errorCode: " + errorCode + " errorString: " + errorString + " extraData: " + extraData);
                // 重新删除一次
                handleClearSystemData();
            }
        });
    }

    private void handleDowngradeLogic() {
        String upgradeStr = SystemApi.getInstance().getOtaUpgradeDescription();

        Log.i(TAG, "getOtaUpgradeDescription description = " + upgradeStr);
        try {
            JSONObject upgradeJson = new JSONObject(upgradeStr);
            mVersionCode = upgradeJson.optString(Definition.JSON_OTA_TARGET_VERSION);
            mVersionDescription = upgradeJson.optString(Definition.JSON_OTA_TARGET_DESCRITION);
            mVersionId = upgradeJson.optString(Definition.JSON_OTA_TO_VERSION_ID);
        } catch (JSONException e) {
            Log.e(TAG, "handleDowngradeLogin parse version json Error: " + e.getMessage());
        }

        UIController.getInstance().setLastApp();
        if (mNeedDownload) {
            Log.i(TAG, "isWifi " + WifiUtils.isWifi(mContext));
            if (WifiUtils.isWifi(mContext)) {
                showOtaDialog();
            } else {
                showNetworkDialog();
            }
        } else {
            processUpgrade(true);
        }
    }

    private void showOtaDialog() {
        Log.i(TAG, "showOtaDialog");
        if (mIsOtaDialogShow || mIsUpgrading) {
            return;
        }
        mIsOtaDialogShow = true;
        Dialog dialog = new OTADialog(mContext)
                .isForce(false)
                .setOtaType(Definition.OTA_TARGET_TYPE_ROLL_BACK)
                .setVersionText(mVersionCode)
                .setUpgradeContentText(Settings.Global.getString(mContext.getContentResolver(), "PresetRomVersion"))
                .setOnUpdateListener(new OTADialog.OnUpdateListener() {
                    @Override
                    public void onUpdateNextTime(OTADialog dialog) {
                        mIsOtaDialogShow = false;
                        releaseOtaModule();
                    }

                    @Override
                    public void onUpdateNow(OTADialog dialog) {
                        mIsOtaDialogShow = false;
                        RobotSettingApi.getInstance().setRobotInt(Definition.ROBOT_SETTING_OTA_TYPE, 2);
                        processUpgrade(true);
                    }
                });
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(false);
        dialog.show();
    }

    private void processUpgrade(boolean needInit) {
        mIsUpgrading = true;
        handleClearSystemData(); // 处理系统数据兼容逻辑
        SystemApi.getInstance().startOtaUpgrade(mNeedDownload, false); // 调用下载rom接口
        if (needInit) {
            SystemApi.getInstance().registerStatusListener(Definition.STATUS_OTA_PROGRESS, mProgressListener);
            //开始OTA降级
            Log.i(TAG, "startOtaUpgrade() running");
            try {
                SkillManager.getInstance().speechPlayText(
                        mContext.getString(R.string.ota_start),
                        new TextListener() {
                            @Override
                            public void onComplete() {
                                super.onComplete();
                                SkillManager.getInstance().closeSpeechAsrRecognize();
                            }
                        });
            } catch (RemoteException e) {
                Log.e(TAG, "processUpdate error: " + e.getMessage());
            }
        }

        Bundle bundle = new Bundle();
        bundle.putBoolean(Definition.JSON_OTA_FORCEUPATE, true);
        bundle.putBoolean(Definition.JSON_OTA_NEED_DOWNLOAD, mNeedDownload);
        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_OTA_PROGRESS, bundle,
                ActivityOptionsCompat.makeCustomAnimation(mContext, R.anim.ota_alpha_enter, R.anim.ota_alpha_out).toBundle());
    }

    private void showNetworkDialog() {
        boolean flag = RobotSettingApi.getInstance().getRobotInt(Definition.VERSION_UPGRADE_ON_MOBILE)
                == Definition.ROBOT_SETTING_ENABLE;
        if (flag) {
            Log.i(TAG, "users click no to remind,not OTANetworkDialog but OTADialog");
            showOtaDialog();
            return;
        }
        new OTANetworkDialog(mContext)
            .setDialogEvent(new OTANetworkDialog.DialogEvent() {
                @Override
                public void onConfirm(OTANetworkDialog dialog) {
                    showOtaDialog();
                    dialog.dismiss();
                }

                @Override
                public void onCancel(OTANetworkDialog dialog) {
                    dialog.dismiss();
                    releaseOtaModule();
                }
            }).show();
    }

    private void handleOtaFinish(String result, String updateType, int code) {
        mIsUpgrading = false;
        switch (result) {
            case Definition.JSON_OTA_RESULT_SUCCESS:
                handleOtaSuccess(updateType);
                break;
            case Definition.JSON_OTA_RESULT_FAILED:
                handleOtaFailed(updateType, code);
                break;
            case Definition.JSON_OTA_RESULT_CONTINUE:
                showInspectFailDialog(updateType, mContext.getString(R.string.ota_continue_rollback));
                break;
            default:
                if (mIsInspect && !mInspectionSuccess) {
                    showInspectionErrorFragment();
                } else {
                    releaseDelay();
                }
                break;
        }
        mInspectionSuccess = true;
        mIsInspect = false;
        mInspectionResult = "";
        mNaviError = "";
    }

    private void releaseOtaModule() {
        Log.d(TAG, "releaseOtaModule");
        mNeedDownload = true;
        mIsUpgrading = false;
        mHandler.removeMessages(WHAT_REQ_RELEASE);
        SkillManager.getInstance().openSpeechAsrRecognize();
        if (mIsOtaSuccess) {
            SystemApi.getInstance().onOtaSuccess();
        } else {
            SystemApi.getInstance().onOtaFinished();
        }
        SystemUtils.setThreeFinger(true);
        SystemApi.getInstance().unregisterStatusListener(mProgressListener);
        release(0, 0, null);
        mIsOtaSuccess = false;
        // 释放mDisposables
        if (!mDisposables.isDisposed()) {
            mDisposables.dispose();
        }
        RobotSettingApi.getInstance().setRobotString(Definition.ROBOT_SETTING_OTA_TYPE, "1");
    }

    private void pauseOta(boolean pause) {
        Log.i(TAG, pause ? "ota pause" : "ota continue");
        ComponentName component = new ComponentName("com.ainirobot.home",
                "com.ainirobot.home.ota.service.DowngradeService");
        Intent intent = new Intent();
        intent.putExtra("start_command", pause ? START_COMMAND_DOWNLOAD_PAUSE : START_COMMAND_DOWNLOAD_CONTINUE);
        intent.setComponent(component);
        mContext.startService(intent);
    }

    private boolean deleteOpkList() {
        return FileUtils.deleteDir(new File(RN_DATA_PATH), false) &&
                FileUtils.deleteDir(new File(RN_DOWNLOAD_PATH), false) &&
                FileUtils.deleteDir(new File(RN_CONFIG_PATH), false);
    }

    private void processInstallMessages() {
        // 处理OTA install 消息逻辑
        Log.i(TAG, "processPendingMessages");
        SystemApi.getInstance().startOtaUpgrade(false,false);
    }

    private void onInstallMessageReceived() {
        mInstallMessageSubject.onNext(true);
    }

    private void onDataCompatibilitySuccess() {
        mDataCompatibilitySuccess = true;
        mCompatibilitySubject.onNext(true);
    }

    private String createSendOtaExecutionEventData() {
        JSONObject params = new JSONObject();
        try {
            params.put(Definition.JSON_OTA_TASK_TYPE, "main_downgrade");
            params.put(Definition.JSON_OTA_EVENT_TYPE,"tec_before_flush");
            JSONObject eventData = new JSONObject();
            eventData.put(Definition.JSON_OTA_FROM_VERSION, Settings.Global.getString(mContext.getContentResolver(), "PresetRomVersion"));
            eventData.put(Definition.JSON_OTA_TO_VERSION_ID, mVersionId);
            eventData.put(Definition.JSON_OTA_TO_VERSION, mVersionCode);
            params.put(Definition.JSON_OTA_EVENT_DATA, eventData);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        Log.d(TAG, "ota execution event params: " + params);
        return params.toString();
    }

    private void handleOtaSuccess(String updateType) {
        mIsOtaSuccess = true;
        if (mIsInspect && !mInspectionSuccess) {
            showInspectionErrorFragment();
            mShowAfterInspectIcon = true;
            mShowAfterInspectionMessage = mContext.getString(R.string.ota_rollback_success);
            mShowAfterInspectionRelease = true;
        } else {
            showOtaResultFragment(true, mContext.getString(R.string.ota_rollback_success), true);
        }
    }

    /**
     * ota降级失败情况：
     * 1. 正在自检的过程中失败
     */
    private void handleOtaFailed(String updateType, int code) {
        if (mIsInspect && !mInspectionSuccess) {
            showInspectionErrorFragment();
            mShowAfterInspectIcon = false;
            mShowAfterInspectionMessage = mContext.getString(R.string.ota_rollback_fail);
            mShowAfterInspectionRelease = false;
        } else {
            if (-4000 == code) {
                // 103 check install 失败
                showOtaResultFragment(false, mContext.getString(R.string.downgrade_failed), true);
            } else if(-1003 == code) {
                showOtaDownloadFailDialog();
            } else {
                // 其他情况
                showOtaResultFragment(false, mContext.getString(R.string.downgrade_failed), true);
            }
        }
    }

    private void showOtaResultFragment(boolean isOkIcon, String message, final boolean releaseAfterSpeech) {
        if (releaseAfterSpeech) {
            mHandler.sendEmptyMessageDelayed(WHAT_REQ_RELEASE, 10 * 1000);
        }
        SkillManager.getInstance().speechWithFinishCallBack(message, new SkillManager.SpeechFinishCallBack() {
            @Override
            public void finish() {
                if (releaseAfterSpeech) {
                    mHandler.removeMessages(WHAT_REQ_RELEASE);
                    mHandler.sendEmptyMessage(WHAT_REQ_RELEASE);
                }
            }
        });
        Bundle bundle = new Bundle();
        bundle.putBoolean(OTAResultFragment.SHOW_OK_ICON, isOkIcon);
        bundle.putString(OTAResultFragment.SHOW_MESSAGE, message);
        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_OTA_RESULT, bundle, null);
    }

    private void showInspectionErrorFragment() {
        SkillManager.getInstance().speechPlayText(ResUtil.getString(R.string.inspect_err_speech_tips));
        Bundle bundle = new Bundle();
        bundle.putString(ModuleDef.INSPECT_ERROR_MESSAGE, mInspectionResult);
        bundle.putString(ModuleDef.INSPECT_NAVI_ERROR_ID, mNaviError);
        SystemUtils.setThreeFinger(false);
        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_INSPECT_ERROR, bundle, null);
    }

    private void showInspectFailDialog(final String updateType, String message) {
        mIsUpgrading = true;
        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_INSPECT_OTA, null,
                ActivityOptionsCompat.makeCustomAnimation(mContext, 0, 0).toBundle());
        new OTAInspectFailDialog(mContext, message,
                new OTAInspectFailDialog.DialogEvent() {
                    @Override
                    public void onConfirm() {
                        mNeedDownload = false;
                        processUpgrade(true);
                    }
                }).show();
    }

    private void showOtaDownloadFailDialog() {
        ToastUtil.showToast(mContext, mContext.getString(R.string.ota_fail_download));
        releaseOtaModule();
    }

    private void releaseDelay() {
        mHandler.removeMessages(WHAT_REQ_RELEASE);
        // 考虑到电量不足20% 时,CoreService会下发 ota_wait_update 的string,所以电量不足时适当延迟release module时间。等待tts播报.
        mHandler.sendEmptyMessageDelayed(WHAT_REQ_RELEASE,
                (SystemApi.getInstance().getBatteryLevel() <= 20 ? BATTERY_LOWER_DELAY : NORMAL_DELAY));
    }

    private void realReboot() {
        Log.i(TAG, "complete machine reboot");
        Intent intent = new Intent(Intent.ACTION_REBOOT);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        mContext.startActivity(intent);
    }

    private void startPowerListener() {
        Log.d(TAG, "start power listener");
        mIsStartPowerInstall = true;
        SystemApi.getInstance().registerStatusListener(Definition.STATUS_OTA_WARNING, statusListener);
    }

    private StatusListener statusListener = new StatusListener() {
        @Override
        public void onStatusUpdate(String type, String data) {
            Log.d(TAG, "type:" + type + " data:" + data);
            UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.OTA_WARNING, data);
        }
    };

    private void stopPowerListener() {
        SystemApi.getInstance().unregisterStatusListener(statusListener);
        mIsStartPowerInstall = false;
    }
}
