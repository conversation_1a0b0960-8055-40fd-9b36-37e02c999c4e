package com.ainirobot.home.ota.utils;


import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.os.SystemProperties;
import android.provider.Settings;
import android.util.Log;

import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.home.ota.constants.OtaConstants;

public class SettingsUtils {

    public static final String VERSION_UPGRADE_SETTING = "version_upgrade_on_mobile";

    public static void setSettingsGlobalOtaInstallingOs(Context ctx, String os) {
        Settings.Global.putString(ctx.getContentResolver(), SettingsUtil.ROBOT_SETTINGS_OTA_INSTALLING_OS, os);
    }

    public static String getRobotToken(Context context) {
        String ret = "";
        Cursor cursor = null;
        try {
            cursor = context.getContentResolver().query(
                    Uri.parse("content://com.ainirobot.account_provider/token"), null, null, null, null);

            if (null != cursor) {
                cursor.moveToFirst();
                ret = cursor.getString(0);
                Log.d("SettingsUtils", "token:" + ret);
                cursor.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (cursor != null)
                cursor.close();
        }

        return ret;
    }

    public static boolean getIsAbUpdate() {
        return SystemProperties.getBoolean(OtaConstants.HOST_AB_UPDATE, false);
    }

    public static boolean canUseMobileData(Context context) {
        boolean ret = false;
        try {
            int data = Settings.Global.getInt(context.getContentResolver(), VERSION_UPGRADE_SETTING);
            ret = data == 1;
        } catch (Settings.SettingNotFoundException e) {
            e.printStackTrace();
        }
        return ret;
    }
}
