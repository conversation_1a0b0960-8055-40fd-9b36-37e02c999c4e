package com.ainirobot.home.module;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.report.Constant;
import com.ainirobot.home.report.TaskReport;
import com.ainirobot.home.ui.UIController;

import org.json.JSONException;
import org.json.JSONObject;

public class MultiRobotErrorModule extends BaseModule {
    private static final String TAG = "MultiRobotErrorModule:Home";

    private static MultiRobotErrorModule sInstance = null;

    private Context mContext;

    private int mReqId;

    public static MultiRobotErrorModule getInstance() {
        if (sInstance == null) {
            sInstance = new MultiRobotErrorModule();
        }
        return sInstance;
    }

    private MultiRobotErrorModule() {
    }

    public void init(Context context) {
        mContext = context;
    }

    @SuppressLint("LongLogTag")
    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.d(TAG, "onNewSemantics text = " + text + ", params = " + params + ", intent = " + intent);
        this.mReqId = reqId;
        switch (intent) {
            case Definition.REQ_MULTI_ROBOT_ERROR:
                int errorType = 0;
                try {
                    JSONObject jsonObject = new JSONObject(params);
                    errorType = jsonObject.getInt("errorType");
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                Bundle bundle = new Bundle();
                bundle.putInt("errorType", errorType);
                UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_MULTI_ROBOT_ERROR, bundle, null);
                break;
        }
        return true;
    }

    @SuppressLint("LongLogTag")
    @Override
    public void onMessageFromLocal(int type) {
        Log.d(TAG, "onMessageFromLocal========type:" + type);
        switch (type) {
            case ModuleDef.LOCAL_MESSAGE_REPOSITION_CANCEL:
                TaskReport.getInstance().setTaskResult(Constant.TaskEvent.task_cancel);
                stop();
                break;

            default:
                break;
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
    }
}
