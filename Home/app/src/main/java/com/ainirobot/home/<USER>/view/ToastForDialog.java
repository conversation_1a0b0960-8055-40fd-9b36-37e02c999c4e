package com.ainirobot.home.ui.view;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.support.annotation.NonNull;
import android.util.Log;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.ainirobot.home.R;

public class ToastForDialog extends Dialog {

    public ToastForDialog(@NonNull Context context, String text) {
        super(context, R.style.DialogToast);
        if (!(context instanceof Activity)) {
            Window window = getWindow();
            if (window != null) {
                window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_DIALOG);
                window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
                window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            }
        }
        setContentView(R.layout.layout_toast_for_dialog);
        TextView tv = (TextView) findViewById(R.id.content);
        tv.setText(text);
        show();
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                dismiss();
            }
        }, 1500);
    }
}
