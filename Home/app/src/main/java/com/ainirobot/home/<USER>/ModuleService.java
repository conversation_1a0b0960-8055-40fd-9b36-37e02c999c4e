/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.home.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.provider.Settings;
import android.support.annotation.RequiresApi;
import android.support.v4.app.NotificationCompat;
import android.util.Log;

import com.ainirobot.coreservice.client.ApiListener;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.permission.PermissionApi;
import com.ainirobot.coreservice.client.permission.PermissionListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.control.RadarManager;
import com.ainirobot.home.observer.SettingsObserver;
import com.ainirobot.home.receiver.BiActiveStateReceiver;
import com.ainirobot.home.report.TaskReport;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.utils.LocationUtil;
import com.ainirobot.home.utils.ResUtil;
import com.ainirobot.home.utils.SystemUtils;

/**
 * module app run in service
 */

public class ModuleService extends android.app.Service {
    private static final String TAG = "ModuleService:Home";

    private static final int NOTIFICATION_ID = 1001;

    private ModuleCallback mModuleCallback;
    private boolean isFirstInit = true;
    private BiActiveStateReceiver mBiActiveStateReceiver;
    private static final String ACTION_START_CHARGING = "action_start_charging";
    private static final String ACTION_STOP_CHARGE = "action_stop_charge";
    private static final String ACTION_PRESS_EMERGENCY = "action_press_emergency";
    private static final String ACTION_RELEASE_EMERGENCY = "action_release_emergency";

    @Override
    public void onCreate() {
        Log.e(TAG, "On create");
        Context context = getApplicationContext();
        mModuleCallback = new ModuleCallback(context);
        mBiActiveStateReceiver = new BiActiveStateReceiver();
        IntentFilter filter = new IntentFilter();
        filter.addAction(ACTION_START_CHARGING);
        filter.addAction(ACTION_STOP_CHARGE);
        filter.addAction(ACTION_PRESS_EMERGENCY);
        filter.addAction(ACTION_RELEASE_EMERGENCY);
        filter.addAction(Intent.ACTION_SHUTDOWN);
        filter.addAction(Intent.ACTION_BOOT_COMPLETED);
        filter.addAction(Intent.ACTION_DATE_CHANGED);
        filter.addAction(Intent.ACTION_TIME_CHANGED);
        registerReceiver(mBiActiveStateReceiver, filter);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        int result = super.onStartCommand(intent, flags, startId);
        Log.d(TAG, "onStartCommand this result=" + result);

        final boolean isFirstConfig;
        int syncMapType = Definition.FIRST_CONFIG_END_TO_NEW_MAP;
        if (intent != null && Definition.FIRST_CONFIG_ACTION_COMPLETE.equalsIgnoreCase(intent.getAction())) {
            Log.d(TAG, "Start home service from first config");

            syncMapType = intent.getIntExtra(Definition.FIRST_CONFIG_END,
                    Definition.FIRST_CONFIG_END_TO_NEW_MAP);
            isFirstConfig = true;
        } else {
            isFirstConfig = false;
        }

        startForeground();

        initSystemApi(isFirstConfig, syncMapType);

        return START_STICKY;
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    private void startForeground() {
        String CHANNEL_ID = "com.ainirobot.home";
        String CHANNEL_NAME = "HOME";
        NotificationChannel notificationChannel = null;
        NotificationCompat.Builder builder = null;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            notificationChannel = new NotificationChannel(CHANNEL_ID, CHANNEL_NAME, NotificationManager.IMPORTANCE_HIGH);
            NotificationManager notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            notificationManager.createNotificationChannel(notificationChannel);
        }

        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, new Intent(), 0);
        builder = new NotificationCompat.Builder(this, CHANNEL_ID).
                setContentTitle(ResUtil.getString(R.string.app_name)).
//                setContentText(getResources().getString(R.string.notice_001)).
        setWhen(System.currentTimeMillis()).
                        setSmallIcon(R.drawable.orion_home_icon).
//                setLargeIcon(BitmapFactory.decodeResource(getResources(), R.mipmap.app_icon)).
        setContentIntent(pendingIntent).setDefaults(NotificationCompat.FLAG_ONGOING_EVENT)
                .setPriority(Notification.PRIORITY_MAX);

        builder.setCategory(String.valueOf(Notification.FLAG_ONGOING_EVENT))
                .setVisibility(Notification.VISIBILITY_PUBLIC)
                .setColor(ApplicationWrapper.getContext().getColor(R.color.white));
        Intent intent2 = new Intent();
        PendingIntent pi = PendingIntent.getBroadcast(this, 0, intent2, 0);
        builder.setFullScreenIntent(pi, true);

        Notification notification = builder.build();
        startForeground(NOTIFICATION_ID, notification);

//        Notification notification = new Notification();
//        startForeground(NOTIFICATION_ID, notification);
    }

    public void addApiCallBack() {
        Log.d(TAG, "CoreService connected ");
        SystemApi.getInstance().setCallback(mModuleCallback);
    }

    public static void registerSettingsObserver() {
        SettingsObserver settingsObserver = new SettingsObserver();
        RobotSettingApi.getInstance().registerRobotSettingListener(settingsObserver,
                Definition.ROBOT_SETTING_OTA_UPDATE, Definition.BOOT_APP_PACKAGE_NAME,
                Definition.ROBOT_SETTING_SITU_SERVICE_STATUS, Definition.ROBOT_USABLE_WHEN_CHARGING,
                Definition.ROBOT_SETTING_REMOTE_NAVIGATION_IS_NEED_PLAY_SOUND);
    }

    public void initSystemApi(final boolean isFirstConfig, final int syncMapType) {
        Log.d(TAG, "Init system api : " + isFirstConfig + " " + isFirstInit + " " + isFirstInit);
        SystemApi.getInstance().connect(this, new ApiListener() {
            @Override
            public void handleApiDisabled() {
                Log.d(TAG, "handleApiDisabled");
                delayReconnect();
            }

            @Override
            public void handleApiConnected() {
                Log.d(TAG, "handleApiConnected");
                LocationUtil.getInstance().setIsNeedPlaySound("1".equals(RobotSettingApi.getInstance()
                        .getRobotString("robot_setting_remote_navigation_is_need_play_sound")));

                registerSettingsObserver();
                resetStandbyScreenLight();
                addApiCallBack();
                RadarManager.setRadarStatusListener();

                UIController mUIController = UIController.getInstance();
                if (isFirstConfig) {
                    Log.d(TAG, "First config start map tool : " + syncMapType);
                    if (!mUIController.sendMessageToActivity(syncMapType)) {
                        mUIController.sendMessageDelay(syncMapType);
                    }
                } else if (isFirstInit) {
                    if (!SystemApi.getInstance().updateCurrentStatus()) {
                        Log.d(TAG, "Update current status false");
                        ControlManager.getControlManager().enterInspectionModule(ModuleDef.FIRST_BOOT);
                    } else {
                        SystemUtils.setThreeFinger(true);
                        UIController.getInstance().moveToBack();
                    }
                } else {
                    SystemApi.getInstance().onInspectionFinished("Restart");
                    if (mUIController.getUIHandler() != null) {
                        ControlManager.getControlManager().enterDefaultModule();
                    } else {
                        mUIController.sendMessageDelay(ModuleDef.MESSAGE_ENTER_DEFAULT_APP);
                    }
                }

//                if (UIController.getInstance()) {
//                    Log.d(TAG, "Launch activity is running");
//                    if (isFirstConfig) {
//                        UIController.getInstance().sendMessageToActivity(syncMapType);
//                    } else if (!isFirstInit) {
//                        ControlManager.getControlManager().enterDefaultModule();
//                    } else {
//                        ControlManager.getControlManager().enterInspectionModule(ModuleDef.FIRST_BOOT);
//                    }
//                } else {
//                    Log.e(TAG, "Launch activity not running");
//                    if (isFirstConfig) {
//                        UIController.getInstance().sendMessageDelay(syncMapType);
//                    } else {
//                        UIController.getInstance().sendMessageDelay(ModuleDef.MESSAGE_ENTER_DEFAULT_APP);
//                    }
//                }

                startSkillService();
                ControlManager.getControlManager().getSystemStatusManager().init();
                ControlManager.getControlManager().getWakeUpWordManager().startReceiver();
                ControlManager.getControlManager().getLowBatteryManager().startReceiver();
                ControlManager.getControlManager().getLanguageManager().init();
                ControlManager.getControlManager().getModuleManager().listenElevatorControl();
                isFirstInit = false;
                PermissionApi.getInstance().registerPermissionListener(new PermissionListener() {
                    @Override
                    public void activityStarting(Intent intent, String pkg) {
                        onAppChange(pkg);
                    }

                    @Override
                    public void activityResuming(String pkg) {
                        onAppChange(pkg);
                    }
                });
            }

            @Override
            public void handleApiDisconnected() {
                Log.d(TAG, "handleApiDisconnected");
                delayReconnect();
            }
        });
    }

    private void resetStandbyScreenLight() {
        int brightness = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_SCREEN_LIGHT_BEFOR_STANDBY);
        Log.d(TAG, "brightness : " + brightness);
        if (brightness > 0) {
            Settings.System.putInt(ApplicationWrapper.getContext().getContentResolver(),
                    Settings.System.SCREEN_BRIGHTNESS, brightness);
            RobotSettingApi.getInstance().setRobotInt(Definition.ROBOT_SETTING_SCREEN_LIGHT_BEFOR_STANDBY, -1);
        }
    }

    @Override
    public void onDestroy() {
        Log.e(TAG, "On destroy");
        super.onDestroy();
        unregisterReceiver(mBiActiveStateReceiver);
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    private void startSkillService() {
        startService(new Intent(this, SkillService.class));
    }

    private void delayReconnect() {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "Delay reconnect");
                initSystemApi(false, 2);
            }
        }, 5000);
    }

    private synchronized void onAppChange(String pkg) {
        Log.d(TAG, "onAppChange packageName: " + pkg);
        TaskReport.getInstance().reportPackageChange(pkg);
    }
}
