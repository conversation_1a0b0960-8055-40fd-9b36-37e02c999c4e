/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.home.control;

import android.annotation.SuppressLint;
import android.content.Context;
import android.media.AudioManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.support.annotation.NonNull;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.bean.Semantics;
import com.ainirobot.home.bi.BiModuleChangeNotifier;
import com.ainirobot.home.bi.BiSpeechResponseReport;
import com.ainirobot.home.bi.anotation.SpeechResponse;
import com.ainirobot.home.module.BaseModule;
import com.ainirobot.home.report.TaskReport;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

public class ControlManager extends HandlerThread {
    private static final String TAG = "ControlManager:Home";

    @SuppressLint("StaticFieldLeak")
    private static ControlManager sController;
    @SuppressLint("StaticFieldLeak")
    private static Context sContext;
    private Handler mHandler = null;

    private RequestAnalysis mRequestAnalysis;
    private ModuleManager mModuleManager;
    private SystemStatusManager mSystemStatusManager;
    private WakeUpWordManager mWakeUpWordManager;
    private LowBatteryManager mLowBatteryManager;
    private LanguageManager mLanguageManager;

    //TODO: will add second active module later according requirement design.

    private int mCurModule = ModuleDef.FEATURE_LAUNCHER; //当前module
    private NextModuleData mNextModuleData;//即将运行的module数据
    private boolean mIsControlModule = false;
    private int mLastVolumeIndex = -1;

    private static class NextModuleData {
        int reqId;
        int module;
        String intent;
        String text;
        String param;
        int nextModule;

        NextModuleData() {
            init();
        }

        void init() {
            reqId = -1;
            module = ModuleDef.FEATURE_NONE;
            intent = "";
            text = "";
            param = "";
            nextModule = -1;
        }
    }

    public static void initialize(Context context) {
        if (sController == null || context != sContext) {
            sController = new ControlManager(context);
        }
        sController.start();
        sController.initHandler();
    }

    private ControlManager(Context context) {
        super(ControlManager.class.getSimpleName());
        sContext = context;
        mRequestAnalysis = new RequestAnalysis(context);
        mModuleManager = new ModuleManager(context);
        mNextModuleData = new NextModuleData();
        mSystemStatusManager = new SystemStatusManager();
        mWakeUpWordManager = new WakeUpWordManager();
        mLowBatteryManager = new LowBatteryManager();
        mLanguageManager = new LanguageManager();
        if (mLastVolumeIndex >= 0) {
            AudioManager audioManager = (AudioManager) sContext.getSystemService(Context.AUDIO_SERVICE);
            audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, mLastVolumeIndex, 0);
        }
    }

    public static ControlManager getControlManager() {
        if (sController == null) {
            throw new RuntimeException("ControlManager Not Initialized");
        }
        return sController;
    }

    public SystemStatusManager getSystemStatusManager() {
        return mSystemStatusManager;
    }

    public WakeUpWordManager getWakeUpWordManager() {
        return mWakeUpWordManager;
    }

    public LowBatteryManager getLowBatteryManager() {
        return mLowBatteryManager;
    }

    public LanguageManager getLanguageManager() {
        return mLanguageManager;
    }

    private void initHandler() {
        mHandler = new Handler(this.getLooper()) {
            public void handleMessage(Message msg) {
//            Log.i(TAG,"main handler receive message:"+msg.what);
                final Bundle bundle = msg.getData();
                switch (msg.what) {
                    case ModuleDef.MSG_NEW_REQUEST:
                        handleNewRequest(bundle);
                        break;
                    case ModuleDef.MSG_HW_RESPORT:
                        handleHWReport(bundle);
                        break;
                    case ModuleDef.MSG_MODULE_STOP:
                        handleModuleFinish(bundle);
                        break;
                    case ModuleDef.MSG_MODULE_WAIT:
                        handleModuleWait(bundle);
                        break;
                    default:
                        break;
                }
            }
        };
    }

    private void handleHWReport(Bundle bundle) {
        int cmdId = bundle.getInt(ModuleDef.MSG_BUNDLE_ID);
        String intent = bundle.getString(ModuleDef.MSG_BUNDLE_COMMAND);
        String text = bundle.getString(ModuleDef.MSG_BUNDLE_TEXT);
        mModuleManager.handleHWReport(mCurModule, cmdId, intent, text);
    }

    private void handleModuleFinish(Bundle bundle) {
        int finishModule = (int) bundle.get("featureId");
        int switchModule = bundle.getInt(ModuleDef.MSG_BUNDLE_MODULE);
        String intent = bundle.getString(ModuleDef.MSG_BUNDLE_INTENT);
        int reqId = bundle.getInt(ModuleDef.MSG_BUNDLE_ID);
        String text = bundle.getString(ModuleDef.MSG_BUNDLE_TEXT);
        String param = bundle.getString(ModuleDef.MSG_BUNDLE_PARAM);

        Log.d(TAG, "finishModule:" + finishModule);
        if (finishModule != mCurModule)
            return;

        if (finishModule < 0 || mRequestAnalysis.getIsBackground(finishModule)) {
            return;
        }

        if (mNextModuleData.module == ModuleDef.FEATURE_NONE && switchModule > 0) {
            mCurModule = switchModule;
            TaskReport.getInstance().reportModuleChange(mCurModule);
            handleSemantics(mCurModule, intent, reqId, text, param);
            return;
        }

        if (mNextModuleData.module != ModuleDef.FEATURE_NONE) {
            Log.d(TAG, "switch module:" + mNextModuleData.module);
            mCurModule = mNextModuleData.module;
            TaskReport.getInstance().reportModuleChange(mCurModule);
            handleSemantics(mCurModule, mNextModuleData.intent, mNextModuleData.reqId,
                    mNextModuleData.text, mNextModuleData.param);
            mNextModuleData.init();

            if (switchModule == mCurModule) {
                handleSemantics(mCurModule, intent, reqId, text, param);
            }
        } else {
            enterDefaultModule();
        }


    }

    private void handleModuleWait(Bundle bundle) {
        int waitModule = bundle.getInt("featureId");
        int nextModule = bundle.getInt(ModuleDef.MSG_BUNDLE_MODULE);
        BiModuleChangeNotifier.broadcastWaitModule(mRequestAnalysis.getFeatureName(waitModule));
        Log.d(TAG, "module wait,wait:" + waitModule + " nextModule:" + nextModule);
        if (nextModule > 0) {
            mCurModule = nextModule;
            String intent = bundle.getString(ModuleDef.MSG_BUNDLE_INTENT);
            int reqId = bundle.getInt(ModuleDef.MSG_BUNDLE_ID);
            String text = bundle.getString(ModuleDef.MSG_BUNDLE_TEXT);
            String param = bundle.getString(ModuleDef.MSG_BUNDLE_PARAM);
            handleSemantics(nextModule, intent, reqId, text, param);
        }
    }

    private void handleNewRequest(Bundle bundle) {

        final String requestText = bundle.getString(ModuleDef.MSG_BUNDLE_TEXT);
        Log.d(TAG, "handleNewRequest reqText: " + requestText
                + ", param is " + bundle.getString(ModuleDef.MSG_BUNDLE_PARAM));

        if (Definition.REQ_GET_MODULE_STATUS.equals(requestText)) {
            Log.d(TAG, "report module change, current module is " + mCurModule);
            TaskReport.getInstance().reportModuleChange(mCurModule);
            SystemApi.getInstance().finishModuleParser(bundle.getInt(ModuleDef.MSG_BUNDLE_ID),
                    true);
            return;
        }

        Semantics semantics = mRequestAnalysis.getSupportSemantics(requestText);

        String params = bundle.getString(ModuleDef.MSG_BUNDLE_PARAM);
        if (semantics == null) {
            BiSpeechResponseReport.reportSpeechResponse(params, SpeechResponse.SPEECH_NOT_SUPPORT);
            Log.d(TAG, "not support text:" + requestText);
            SystemApi.getInstance().finishModuleParser(bundle.getInt(ModuleDef.MSG_BUNDLE_ID),
                    false);

            if (mCurModule == ModuleDef.FEATURE_CHARGING || mCurModule == ModuleDef.FEATURE_AUTO_CHARGE) {
                GlobalResponseManager.getInstance().showGlobalToastResponse();
            }
            return;
        }

        int module = semantics.getFeature();
        Log.d(TAG, "new request,new module:" + module + " curren main Module:" + mCurModule);

        if (semantics.getFeature() == ModuleDef.FEATURE_CONTROL) {
            mIsControlModule = true;
            handleControlReq(bundle.getInt(ModuleDef.MSG_BUNDLE_ID), semantics.getIntent(),
                    bundle.getString(ModuleDef.MSG_BUNDLE_PARAM));
            return;
        }
        mIsControlModule = false;

        if (module == ModuleDef.FEATURE_ALL) {
//            module = mCurModule;
            if (semantics.getIntent().equals(Definition.REQ_SPEECH_WAKEUP)) {
                SystemApi.getInstance().finishModuleParser(
                        bundle.getInt(ModuleDef.MSG_BUNDLE_ID), true);
            }

            if ((mCurModule == ModuleDef.FEATURE_CHARGING
                    || mCurModule == ModuleDef.FEATURE_AUTO_CHARGE)
                    && !Definition.REQ_FIRST_TYPE_ROBOT_INFO.equals(semantics.getIntent())) {
                GlobalResponseManager.getInstance().showGlobalToastResponse();
            }
        }

        BaseModule currentModule = mModuleManager.getModule(mCurModule);

        //如果过来的请求是当前请求或者后台请求，直接分给他们
        if ((currentModule.isRunning() && module == mCurModule)
                || mRequestAnalysis.getIsBackground(module)
                || module == ModuleDef.FEATURE_ALL) {
            if (module == ModuleDef.FEATURE_SETTING_MODULE &&
                    (mCurModule == ModuleDef.FEATURE_CHARGING || mCurModule == ModuleDef.FEATURE_AUTO_CHARGE)) {
                GlobalResponseManager.getInstance().showGlobalToastResponse();
                return;
            }
            Log.i(TAG, "bundle = " + bundle.toString());
            if (mRequestAnalysis.getIsBackground(module)) {
                BiSpeechResponseReport.reportSpeechResponse(params, SpeechResponse.SPEECH_BACKGROUND);
            } else {
                BiSpeechResponseReport.reportSpeechResponse(params, SpeechResponse.SPEECH_TRANSMISSION);
            }
            handleSemantics(module, semantics.getIntent(),
                    bundle.getInt(ModuleDef.MSG_BUNDLE_ID), requestText,
                    bundle.getString(ModuleDef.MSG_BUNDLE_PARAM));
            return;
        }

        initNextModule(module, semantics, bundle);
        Log.d(TAG, "stop module:" + mCurModule);
        if (!currentModule.isPausing()) {
            mModuleManager.stopModule(mCurModule);
        }
    }

    private void initNextModule(int module, Semantics semantics, Bundle bundle) {
        mNextModuleData.init();
        mNextModuleData.intent = semantics.getIntent();
        mNextModuleData.module = module;
        mNextModuleData.param = bundle.getString(ModuleDef.MSG_BUNDLE_PARAM);
        mNextModuleData.reqId = bundle.getInt(ModuleDef.MSG_BUNDLE_ID);
        mNextModuleData.text = bundle.getString(ModuleDef.MSG_BUNDLE_TEXT);
    }

    public Handler getMainHandler() {
        return mHandler;
    }

    public void enterInspectionModule(String param) {
        mCurModule = ModuleDef.FEATURE_INSPECTION;
        TaskReport.getInstance().reportModuleChange(mCurModule);
        mModuleManager.handleSemantics(ModuleDef.MODULE_STATE_IDLE, mCurModule, ModuleDef.START_INSPECTION,
                null, param);
    }

    public void enterDefaultModule() {
        Log.d(TAG, "enterDefaultModule" + " mCurModule:" + mCurModule);
        mCurModule = ModuleDef.FEATURE_LAUNCHER;
        mModuleManager.handleSemantics(ModuleDef.MODULE_STATE_IDLE, mCurModule, ModuleDef.REQ_WAKEUP,
                null, null);
    }


    private void handleSemantics(int module, String intent, int reqId, String text,
                                 String param) {
        Log.d(TAG, "handleSemantics intent:" + intent);
        mModuleManager.handleSemantics(reqId, module, intent, text, param);
    }

    private void handleControlReq(final int reqId, String intent, String params) {
        Log.d(TAG, "handleControlReq intent:" + intent + ", params:" + params);
        switch (intent) {
            case Definition.REQ_SET_LIGHT:
                LightManager.getInstance().setLight(params);
                break;

            case Definition.REQ_SPEECH_TEXT:
                SkillManager.getInstance().speechPlayText(params);
                break;
            case ModuleDef.PLAY_TTS:
                SkillManager.getInstance().speechPlayText(params);
                sendStatusToServer(reqId, intent);
                break;
            default:
                break;
        }
        SystemApi.getInstance().finishModuleParser(reqId, true);
    }

    public void sendStatusToServer(int reqId, String type) {
        JsonObject obj = new JsonObject();
        obj.addProperty("reqId", reqId);
        obj.addProperty("type", type);
        obj.addProperty("result", 0);
        SystemApi.getInstance().sendStatusReport(Definition.STATUS_PROCESS_STATE, new Gson().toJson(obj));
    }

    public boolean getIsPlayTTS() {
        return true;
    }

    public ModuleManager getModuleManager(){
        return mModuleManager;
    }

    public int getCurrentModule() {
        return mCurModule;
    }

    public boolean getIsControlModule() {
        return mIsControlModule;
    }

    public RequestAnalysis getRequestAnalysis() {
        return mRequestAnalysis;
    }

    public int getFeatureIdByModule(@NonNull BaseModule module) {
        return mModuleManager.getFeatureId(module);
    }

    public void sendMessageToModule(int type) {
        mModuleManager.sendMessageToModule(mCurModule, type);
    }

    public void sendMessageToModule(int type, Object param) {
        mModuleManager.sendMessageToModule(mCurModule, type , param);
    }

    public void setLastVolumeIndex(int lastVolumeIndex) {
        this.mLastVolumeIndex = lastVolumeIndex;
    }

    public int getLastVolumeIndex() {
        return mLastVolumeIndex;
    }
}
