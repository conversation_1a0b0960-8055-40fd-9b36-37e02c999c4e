package com.ainirobot.home.bi;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;
import com.ainirobot.home.bi.anotation.SettingItem;

/**
 * radar open/close report
 *
 * @version V1.0.0
 * @date 2019/4/30 14:43
 */
public class BiRadarReport extends BaseBiReport {

    private static final String TABLE_NAME = "gb_chassis";
    private static final String MODULE = "module";
    private static final String TYPE = "type";
    private static final String ERRORID = "error_id";
    private static final String TASK = "task";
    private static final String LENGTH_TIME = "length_time";
    private static final String CTIME = "ctime";

    private int mType = RADAR_REPORT_TYPE_NONE;
    private String mErrorId;
    private int mTask = 0;
    private long mLengthTime = 0;

    private long mCloseRadarTime;

    public static final int RADAR_REPORT_TYPE_NONE = 0;
    public static final int RADAR_REPORT_TYPE_OPEN_RADAR_FAILED = 1;
    public static final int RADAR_REPORT_TYPE_CLOSE_RADAR = 2;
    public static final int RADAR_REPORT_TYPE_OPEN_RADAR = 3;

    public BiRadarReport() {
        super(TABLE_NAME);
        initData();
    }

    private void initData() {
        addData(MODULE, "radar");
        addData(TYPE, "");
        addData(ERRORID, "");
        addData(TASK, "");
        addData(LENGTH_TIME, "");
        addData(CTIME, "");
    }

    public BiRadarReport addType(int type) {
        mType = type;
        return this;
    }

    public BiRadarReport addErrorId(String errorId) {
           mErrorId = errorId;
           return this;
     }

     public BiRadarReport addTask(int task) {
        mTask = task;
        return this;
    }

    public BiRadarReport addLengthTime() {
        mLengthTime = mCloseRadarTime != 0?(System.currentTimeMillis() - mCloseRadarTime):0 ;
        return this;
    }

    public BiRadarReport addCloseRadarTime(long startTime) {
        mCloseRadarTime = startTime;
        return this;
    }

    @Override
    public void report() {
        initData();
        addData(TYPE, mType);
        addData(ERRORID, mErrorId);
        addData(TASK, mTask);
        addData(LENGTH_TIME, mLengthTime);
        addData(CTIME, System.currentTimeMillis());
        super.report();
        clearRadarReportData();
    }

    private void clearRadarReportData() {
        mType = RADAR_REPORT_TYPE_NONE;
        mErrorId = "";
        mTask = 0;
        mLengthTime = 0;
        mCloseRadarTime = 0;
    }
}
