/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.home.ui.fragment;

import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.home.R;
import com.ainirobot.home.ui.UIController;

import org.json.JSONException;
import org.json.JSONObject;

public class OTAResultFragment extends BaseFragment {

    private ImageView mIvUpgradeResult;
    private TextView mTvUpgradeResult;
    private TextView mTvRecovery;
    private final String TAG = "OTAResultFragment:Home";
    public static final String SHOW_OK_ICON = "upgrade_result";
    public static final String SHOW_MESSAGE = "show_message";
    private String mShowMessage;

    private View mRootView;
    private Dialog mRecoveryDialog;
    private static final String ACTION_FACTORY_RESET = "android.intent.action.FACTORY_RESET";
    private static final String EXTRA_REASON = "android.intent.extra.REASON";
    private static final String EXTRA_WIPE_EXTERNAL_STORAGE = "android.intent.extra.WIPE_EXTERNAL_STORAGE";
    private static final String EXTRA_WIPE_ESIMS = "com.android.internal.intent.extra.WIPE_ESIMS";

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        mRootView = inflater.inflate(R.layout.fragment_upgrade_result, null);
        initView();
        initData();
        return mRootView;
    }

    private void initView() {
        mIvUpgradeResult = (ImageView) mRootView.findViewById(R.id.iv_upgrade_result);
        mTvUpgradeResult = (TextView) mRootView.findViewById(R.id.tv_upgrade_result);
        mTvRecovery = mRootView.findViewById(R.id.tv_recovery);
        mTvRecovery.setOnClickListener(v -> showRecoveryDialog());
    }

    private void initData() {
        boolean showOk = getArguments().getBoolean(SHOW_OK_ICON, false);
        mShowMessage = getArguments().getString(SHOW_MESSAGE, "");
        Log.i(TAG, showOk ? "show success" : "show failed");
        if (showOk) {
            mIvUpgradeResult.setImageResource(R.drawable.upgrade_success_img);
        } else {
            mIvUpgradeResult.setImageResource(R.drawable.upgrade_fail_img);
        }
        mTvUpgradeResult.setText(mShowMessage);
        if (mShowMessage.equals(this.getString(R.string.downgrade_failed))) {
            mTvRecovery.setVisibility(View.VISIBLE);
            showRecoveryDialog();
        }
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        mRootView.setBackgroundResource(R.color.ota_bg_start_upgrade);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
        switch (type) {
            case OTA_RESULT:
                if (message == null) {
                    return;
                }
                try {
                    JSONObject jsonObject = new JSONObject(message);
                    if (jsonObject.getBoolean(SHOW_OK_ICON)) {
                        mIvUpgradeResult.setImageResource(R.drawable.upgrade_success_img);
                    } else {
                        mIvUpgradeResult.setImageResource(R.drawable.upgrade_fail_img);
                    }
                    mTvUpgradeResult.setText(jsonObject.getString(SHOW_MESSAGE));
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                break;
        }
    }

    private void showRecoveryDialog() {
        mRecoveryDialog = new Dialog(this.getContext());
        mRecoveryDialog.setContentView(R.layout.dialog_ota_failed_recovery);
        mRecoveryDialog.setCanceledOnTouchOutside(false);

        Window window = mRecoveryDialog.getWindow();
        WindowManager.LayoutParams lp = window.getAttributes();
        lp.width = 1100;
        lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
        lp.gravity = Gravity.CENTER;
        window.setBackgroundDrawableResource(R.color.transparent);
        window.setDimAmount(0.7f);
        window.setAttributes(lp);

        mRecoveryDialog.findViewById(R.id.confirm).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                final Intent intent = new Intent(ACTION_FACTORY_RESET);
                intent.setPackage("android");
                intent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND);
                intent.putExtra(EXTRA_REASON, getContext().getPackageName() + " Master Clear");
                intent.putExtra(EXTRA_WIPE_EXTERNAL_STORAGE, true);
                intent.putExtra(EXTRA_WIPE_ESIMS, true);
                getContext().sendBroadcast(intent);
            }
        });

        mRecoveryDialog.findViewById(R.id.cancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                mRecoveryDialog.dismiss();
            }
        });
        mRecoveryDialog.show();
    }
}
