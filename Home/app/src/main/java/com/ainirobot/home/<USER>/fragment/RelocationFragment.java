package com.ainirobot.home.ui.fragment;

import static com.ainirobot.home.utils.CommonUtils.getPriority;

import android.content.Context;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.view.LoadingView;
import com.ainirobot.home.ui.view.RelocationAnchorView;
import com.ainirobot.home.ui.view.RelocationChargePileView;
import com.ainirobot.home.ui.view.RelocationGuideView;
import com.ainirobot.home.ui.view.RelocationResultView;
import com.ainirobot.home.ui.view.RelocationSearchView;
import com.ainirobot.home.ui.view.RelocationTypeChoiceView;
import com.ainirobot.home.ui.view.RelocationWithoutMapView;
import com.ainirobot.home.ui.view.RepositionAnchorQuestionDialog;
import com.ainirobot.home.ui.view.RepositionCancelDialog;
import com.ainirobot.home.ui.view.RepositionSolutionDialog;
import com.ainirobot.home.utils.ResUtil;

public class RelocationFragment extends BaseFragment implements View.OnClickListener {

    private static final String TAG = RelocationFragment.class.getSimpleName();
    private LoadingView mLoadingView;
    private RelocationSearchView mSearchView;
    private RelocationResultView mResultView;
    private RelocationGuideView mGuideView;
    private RelocationAnchorView mAnchorView;
    private RelocationWithoutMapView mWithoutMapView;
    private RepositionCancelDialog mDialog;
    private RelocationTypeChoiceView mRelocateChooseView;
    private RelocationChargePileView mRelocationChargePileView;
    public View mView;
    private Context mContext;
    private String failReason = "";
    private boolean needBackToQrcodeView = false; // 点击定位方式选择中的返回,是否回到二维码定位页
    private String mRelocationType;
    private boolean hasRelocateChooseView;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        Log.i(TAG, "on create");
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        mView = inflater.inflate(R.layout.layout_qrcode_relocation, null);
        mLoadingView = (LoadingView) mView.findViewById(R.id.relocation_loading_view);
        mResultView = (RelocationResultView) mView.findViewById(R.id.relocation_result_view);
        mResultView.mRepositionCancel.setOnClickListener(this);
        mResultView.mRepositionCancelText.setOnClickListener(this);
        mResultView.mFailSolutionBtn.setOnClickListener(this);

        mGuideView = (RelocationGuideView) mView.findViewById(R.id.relocation_guide_view);
        mGuideView.mCancelTV.setOnClickListener(this);
        mGuideView.mConfirmBtn.setOnClickListener(this);

        mAnchorView = (RelocationAnchorView) mView.findViewById(R.id.relocation_anchor_view);
        mAnchorView.mAnchorCancelTV.setOnClickListener(this);
        mAnchorView.mAnchorExitTV.setOnClickListener(this);
        mAnchorView.mResultBtn.setOnClickListener(this);
        mAnchorView.mQuestionBtn.setOnClickListener(this);

        mSearchView = (RelocationSearchView) mView.findViewById(R.id.relocation_search);

        mWithoutMapView = (RelocationWithoutMapView) mView.findViewById(R.id.relocation_without_map);
        mWithoutMapView.mRebootBtn.setOnClickListener(this);
        mWithoutMapView.mCancelBtn.setOnClickListener(this);

        mRelocateChooseView = (RelocationTypeChoiceView)mView.findViewById(R.id.relocation_choose);
        mRelocateChooseView.mRepositionBack.setOnClickListener(this);
        mRelocateChooseView.mChoosePointLocate.setOnClickListener(this);
        mRelocateChooseView.mChoosePileLocate.setOnClickListener(this);

        mRelocationChargePileView = (RelocationChargePileView)mView.findViewById(R.id.relocation_charge_pile);
        mRelocationChargePileView.mChargePileLocateCancel.setOnClickListener(this);
        mRelocationChargePileView.mChargePileLocateConfirm.setOnClickListener(this);

        assert getArguments() != null;
        showDefaultView(getArguments());
        return mView;
    }

    public void showDefaultView(@NonNull Bundle bundle) {
        boolean isLoading = bundle.getBoolean(ModuleDef.QRCODE_LOADING);
        Log.d(TAG,"showDefaultView isLoading : " + isLoading);
        if (isLoading) {
            showLoadingView();
            return;
        }

        String failureType = bundle.getString(ModuleDef.QRCODE_FAILURE_TYPE,
                ModuleDef.QRCODE_FAILURE_LOSE_POINT_WITH_ANCHOR);
        Log.d(TAG,"showDefaultView failureType : " + failureType);
        switch (failureType) {
            case ModuleDef.QRCODE_FAILURE_WITHOUT_MAP:
                showLoseMapView();
                break;
        }
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        mContext = context;
        Log.d(TAG, "onAttach");
    }

    @Override
    public void onStart() {
        super.onStart();
        Log.i(TAG, "onstart");
    }

    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
        switch (type) {
            case REPOSITION_SUCCEED:
                showSuccessView();
                break;
            case TARGET_VISION_GUIDE:
                mRelocationType = message;
                showQrcodeGuideView();
                break;
            case ANCHOR_POINT_REPOSITION_FAILURE:
                showAnchorView(mAnchorView.TYPE_NOT_FIND_ANCHOR);
                break;
            case CHARGE_PILE_LOCATE_FAILURE:
                showChargePileFailView(message);
                break;
            case REPOSITION_EXIT:
                destoryAllViews();
                break;
            case REPOSITION_CHARGING_CHECK:
                boolean isCharging = "true".equals(message);
                if (isCharging){
                    showSearchView();
                    ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_CHARGE_PILE_LOCATING);
                }else {
                    showStartChargePileView();
                }
                break;
            case RELOCATE_CHOOSE_TYPE:
                needBackToQrcodeView = message.equals("true");
                showRelocateChooseView();
                break;
            case RELOCATE_CHOOSE_ANCHOR_POINT:
                showAnchorView(mAnchorView.TYPE_WITH_ANCHOR_INIT);
                break;
            case RELOCATE_CHOOSE_CHARGE_PILE:
                showStartChargePileView();
                break;
            case RESET_HEAD_FAILURE:
                showAnchorView(mAnchorView.TYPE_RESET_HEAD_FAILED);
            default:
                break;
        }
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.reposition_cancel_text:
                clickCancel();
                break;
            case R.id.relocate_choose_type_cancel:
                relocateChooseViewCancelCliked();
                break;
            case R.id.reposition_cancel: //点击充电桩定位页的取消
            case R.id.reposition_failure_cancel:
            case R.id.anchor_cancel_text: // 点击定位点定位页的取消
            case R.id.reposition_anchor_cancel_tv:
            case R.id.cancel_vision_locate://视觉定位页面取消
            case R.id.exit_vision_locate:
                if (hasRelocateChooseView) {
                    ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_TO_CHOOSE_LOCATE_TYPE,
                            needBackToQrcodeView);
                } else {
                    relocateChooseViewCancelCliked();
                }
                break;
            case R.id.anchor_point_btn://开始定位点重定位
                clickAnchorPointRetryBtn();
                break;
            case R.id.locate_guide_confirmBtn:
                clickChooseOther();
                break;
            case R.id.anchor_question_btn:
                showAnchorDialog();
                break;
            case R.id.reposition_without_map_cancel:
                ControlManager.getControlManager().sendMessageToModule(
                        ModuleDef.LOCAL_MESSAGE_REPOSITION_CANCEL);
                break;
            case R.id.without_map_reboot_btn:
                ControlManager.getControlManager().sendMessageToModule(
                        ModuleDef.MSG_REPOSITION_QRCODE_REPOSITION_REBOOT);
                break;
            case R.id.choose_point_locate://选择定位点定位
                ControlManager.getControlManager().sendMessageToModule(
                        ModuleDef.LOCAL_MESSAGE_CHOOSE_ANCHOR_POINT);
                break;
            case R.id.choose_pile_locate://选择充电桩定位
                ControlManager.getControlManager().sendMessageToModule(
                        ModuleDef.LOCAL_MESSAGE_CHOOSE_CHARGE_PILE);
                break;
            case R.id.locate_failure_see_solution:
                showSolutionDialog();
                break;
            default:
                break;
        }
    }

    private void relocateChooseViewCancelCliked() {
        Log.d(TAG, "relocate_choose_type_cancel : " + needBackToQrcodeView);
        if (needBackToQrcodeView){
            ControlManager.getControlManager().sendMessageToModule(ModuleDef.MSG_REPOSITION_QRCODE_REPOSITION);
            showQrcodeGuideView();
        }else {
            clickCancel();
        }
    }

    //根据特殊点进行Fix定位.
    private void clickAnchorPointRetryBtn() {
        showSearchView();
        ControlManager.getControlManager().sendMessageToModule(
                ModuleDef.MSG_REPOSITION_QRCODE_ANCHOR_SET);
    }

    //切换选择其他定位方式，并发送停止二维码定位的消息。
    private void clickChooseOther() {
        Log.d(TAG,"clickChooseOther ");
        ControlManager.getControlManager().sendMessageToModule(
                ModuleDef.MSG_REPOSITION_QRCODE_CHOOSE_OTHER);// 发送停止二维码定位的消息
    }

    /**
     * 显示具体重定位状态页面之前，先显示Loading页面.
     */
    private void showLoadingView() {
        mLoadingView.setVisibility(View.VISIBLE);
        mGuideView.setVisibility(View.GONE);
        mResultView.setVisibility(View.GONE);
        mSearchView.setVisibility(View.GONE);
        mAnchorView.setVisibility(View.GONE);
        mWithoutMapView.setVisibility(View.GONE);
        mRelocateChooseView.setVisibility(View.GONE);
        mRelocationChargePileView.setVisibility(View.GONE);
    }

    private void showLoseMapView() {
        mLoadingView.setVisibility(View.GONE);
        mGuideView.setVisibility(View.GONE);
        mResultView.setVisibility(View.GONE);
        mSearchView.setVisibility(View.GONE);
        mAnchorView.setVisibility(View.GONE);
        mRelocateChooseView.setVisibility(View.GONE);
        mRelocationChargePileView.setVisibility(View.GONE);
        mWithoutMapView.setVisibility(View.VISIBLE);
    }

    /**
     * 显示开始充电桩定位UI
     */
    private void showStartChargePileView() {
        mLoadingView.setVisibility(View.GONE);
        mGuideView.setVisibility(View.GONE);
        mResultView.setVisibility(View.GONE);
        mSearchView.setVisibility(View.GONE);
        mSearchView.stopRepositionAnimate();
        mAnchorView.setVisibility(View.GONE);
        mWithoutMapView.setVisibility(View.GONE);
        mRelocateChooseView.setVisibility(View.GONE);
        mRelocationChargePileView.setVisibility(View.VISIBLE);
        mRelocationChargePileView.updateChargePileView(RelocationChargePileView.INIT);
    }

    private void showQrcodeGuideView() {
        mLoadingView.setVisibility(View.GONE);
        mResultView.setVisibility(View.GONE);
        mSearchView.setVisibility(View.GONE);
        mAnchorView.setVisibility(View.GONE);
        mWithoutMapView.setVisibility(View.GONE);
        mRelocateChooseView.setVisibility(View.GONE);
        mRelocationChargePileView.setVisibility(View.GONE);
        mGuideView.updateView(mRelocationType);
    }

    /**
     * 显示定位点定位UI
     * @param type
     */
    private void showAnchorView(int type) {
        mLoadingView.setVisibility(View.GONE);
        mGuideView.setVisibility(View.GONE);
        mResultView.setVisibility(View.GONE);
        mSearchView.setVisibility(View.GONE);
        mSearchView.stopRepositionAnimate();
        mAnchorView.setVisibility(View.VISIBLE);
        mAnchorView.showAnchorView(type);
        mWithoutMapView.setVisibility(View.GONE);
        mRelocateChooseView.setVisibility(View.GONE);
        mRelocationChargePileView.setVisibility(View.GONE);
    }

    private void showSearchView() {
        mLoadingView.setVisibility(View.GONE);
        mGuideView.setVisibility(View.GONE);
        mResultView.setVisibility(View.GONE);
        mSearchView.setVisibility(View.VISIBLE);
        mSearchView.startRepositionAnimation();
        mAnchorView.setVisibility(View.GONE);
        mWithoutMapView.setVisibility(View.GONE);
        mRelocateChooseView.setVisibility(View.GONE);
        mRelocationChargePileView.setVisibility(View.GONE);
    }

    private void showSuccessView() {
        mLoadingView.setVisibility(View.GONE);
        mGuideView.setVisibility(View.GONE);
        mResultView.setVisibility(View.VISIBLE);
        mResultView.updateResultView(RelocationResultView.SUCCESS);
        mSearchView.setVisibility(View.GONE);
        mSearchView.stopRepositionAnimate();
        mAnchorView.setVisibility(View.GONE);
        mWithoutMapView.setVisibility(View.GONE);
        mRelocateChooseView.setVisibility(View.GONE);
        mRelocationChargePileView.setVisibility(View.GONE);
    }

    private void showChargePileFailView(String message) {
        mLoadingView.setVisibility(View.GONE);
        mGuideView.setVisibility(View.GONE);
        mSearchView.setVisibility(View.GONE);
        mSearchView.stopRepositionAnimate();
        mAnchorView.setVisibility(View.GONE);
        mWithoutMapView.setVisibility(View.GONE);
        mRelocateChooseView.setVisibility(View.GONE);
        mRelocationChargePileView.setVisibility(View.GONE);
        mResultView.setVisibility(View.VISIBLE);
        mResultView.updateResultView(RelocationResultView.CHARGE_PILE_FAIL);
        recordFailReason(message);
    }

    public void showRelocateChooseView(){
        hasRelocateChooseView = true;
        mLoadingView.setVisibility(View.GONE);
        mGuideView.setVisibility(View.GONE);
        mResultView.setVisibility(View.GONE);
        mSearchView.setVisibility(View.GONE);
        mSearchView.stopRepositionAnimate();
        mAnchorView.setVisibility(View.GONE);
        mWithoutMapView.setVisibility(View.GONE);
        mRelocationChargePileView.setVisibility(View.GONE);
        mRelocateChooseView.setVisibility(View.VISIBLE);
    }

    private void showAnchorDialog() {
        RepositionAnchorQuestionDialog dialog = new RepositionAnchorQuestionDialog(mContext);
        dialog.show();
    }

    private void recordFailReason(String message) {
        Log.d(TAG, "recordFailReason = " + message);
        if (getPriority(message) < getPriority(failReason)) {// 仅仅记录高优先级原因, 优先级值越小,表示优先级越高
            failReason = message;
        }
        Log.d(TAG, "finaly fail reason: "+failReason);
    }
    private void showSolutionDialog() {
        RepositionSolutionDialog repositionSolutionDialog = new RepositionSolutionDialog(mContext, R.style.OTADialog);
        repositionSolutionDialog.setReason1(getReason1());
//        repositionSolutionDialog.setReason2(getReason2());
        repositionSolutionDialog.show();
    }

    private void clickCancel() {
        if (mDialog == null) {
            mDialog = new RepositionCancelDialog(mContext, R.style.OTADialog)
                    .setDialogClickListener(new RepositionCancelDialog.ClickListener() {
                        @Override
                        public void onConfirmClick() {
                            mDialog = null;
                        }

                        @Override
                        public void onCancelClick() {
                            ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_REPOSITION_CANCEL);
                            mDialog = null;
                        }
                    });
            mDialog.show();
        }
    }

    public void destoryAllViews() {
        if (mDialog != null) {
            mDialog.dismiss();
        }
        if (mSearchView != null) {
            mSearchView.stopRepositionAnimate();
            mSearchView = null;
        }
        mGuideView = null;
        mResultView = null;
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.d(TAG, "onDestroyView");
        destoryAllViews();
        clearFailReason();
    }

    private void clearFailReason() {
        Log.d(TAG, "reset FailReason empty");
        failReason = "";
    }

    private String getReason1() {
        if (!TextUtils.isEmpty(failReason)
                && failReason.equals(Definition.RESULT_ESTIMATE_FAIL_MATCH_ERR)) {
            return ResUtil.getString(R.string.reason_loss_error_large);
        } else if (!TextUtils.isEmpty(failReason)
                && failReason.equals(Definition.RESULT_ESTIMATE_FAIL_CHECK_ERR)) {
            return ResUtil.getString(R.string.reason_chargepile_moved);
        } else {
            return ResUtil.getString(R.string.reason_reboot);
        }
    }

    private String getReason2() {
        if (!TextUtils.isEmpty(failReason)
                && failReason.equals(Definition.RESULT_ESTIMATE_FAIL_MATCH_ERR)) {
            return ResUtil.getString(R.string.reason_map_wrong);
        } else if (!TextUtils.isEmpty(failReason)
                && failReason.equals(Definition.RESULT_ESTIMATE_FAIL_CHECK_ERR)) {
            return ResUtil.getString(R.string.reason_multi_chargepile);
        } else {
            return "";
        }
    }




}
