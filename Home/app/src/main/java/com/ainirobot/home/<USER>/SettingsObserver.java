package com.ainirobot.home.observer;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingListener;
import com.ainirobot.home.R;
import com.ainirobot.home.bi.BiSettingsReport;
import com.ainirobot.home.bi.anotation.SettingItem;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.utils.LocationUtil;
import com.ainirobot.home.utils.ResUtil;


/**
 * observer setting change report bi
 *
 * @version V1.0.0
 * @date 2019/4/29 19:56
 */
public class SettingsObserver extends RobotSettingListener {

    private static final String TAG = "SettingsObserver";

    @Override
    public void onRobotSettingChanged(String key) {
        Log.i(TAG, "robot setting changed:" + key);
        switch (key) {
            case Definition.ROBOT_SETTING_OTA_UPDATE:
                String otaSwitch = RobotSettingApi.getInstance().getRobotString(key);
                Log.i(TAG, "onChange: switch ota update: " + otaSwitch);
                reportSettingsItem(SettingItem.AUTO_UPDATE, otaSwitch);
                break;
            case Definition.BOOT_APP_PACKAGE_NAME:
                String defaultApp = RobotSettingApi.getInstance().getRobotString(key);
                Log.i(TAG, "onChange: switch default app: " + defaultApp);
                reportSettingsItem(SettingItem.DEFAULT_APP, defaultApp);
                break;
            case Definition.ROBOT_USABLE_WHEN_CHARGING:
                String workCharging = RobotSettingApi.getInstance().getRobotString(key);
                Log.i(TAG, "onChange: switch charging working: " + workCharging);
                reportSettingsItem(SettingItem.WORK_IN_CHARGING, workCharging);
                break;

            case Definition.ROBOT_SETTING_SITU_SERVICE_STATUS:
                String situServiceStatus = RobotSettingApi.getInstance().getRobotString(key);
                Log.i(TAG, "onChange: situ service status: " + situServiceStatus);
                if (situServiceStatus.equals("0")) {
                    SkillManager.getInstance().speechPlayText(ResUtil.getString(R.string.situ_service_end));
                } else if (situServiceStatus.equals("1")) {
                    SkillManager.getInstance().speechPlayText(ResUtil.getString(R.string.situ_service_start));
                } else if (situServiceStatus.equals("2")) {
                    SkillManager.getInstance().speechPlayText(ResUtil.getString(R.string.auto_situ_service_start));
                }
                break;
            case Definition.ROBOT_SETTING_REMOTE_NAVIGATION_IS_NEED_PLAY_SOUND:
                boolean isNeedPlaySound = "1".equals(RobotSettingApi.getInstance()
                        .getRobotString("robot_setting_remote_navigation_is_need_play_sound"));
                LocationUtil.getInstance().setIsNeedPlaySound(isNeedPlaySound);
                break;
            default:
                break;
        }
    }

    /**
     * report settings change
     *
     * @param item  setting item
     * @param value item value
     */
    private void reportSettingsItem(String item, String value) {
        BiSettingsReport settingsReport = new BiSettingsReport();
        settingsReport.addOption(item).addValue(value).report();
    }

}
