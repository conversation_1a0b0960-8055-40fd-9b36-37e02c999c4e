package com.ainirobot.home.control;

import android.content.Context;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.os.RemoteException;
import android.provider.BaseColumns;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.LanguageBean;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.List;

/**
 * 客户端重启后，发现当前使用的语言被服务端下线，
 * 则自动切换到orm主语言，同时弹窗提示“xxx语言包已下线，为您自动切换至xxx”
 */
public class LanguageManager {

    private static final String TAG = ModuleDef.PREFIX + LanguageManager.class.getSimpleName();
    private static final Uri DATA_URI = Uri.parse("content://com.ainirobot.coreservice.LanguageProvider/" + LanguageField.TABLE_NAME);

    public static final String LANGUAGE_CODE_ZH = "zh_CN";// 中文
    public static final String LANGUAGE_CODE_EN = "en_US";// 英文
    public static final String LANGUAGE_CODE_JA = "ja_JP";// 日文
    public static final String LANGUAGE_CODE_KO = "ko_KR";// 韩文

    private final Context mContext;
    private String mLangCode;

    public LanguageManager() {
        mContext = ApplicationWrapper.getContext();
    }

    public void init() {
        if(!ProductInfo.isDeliveryProduct() && !ProductInfo.isMeissaPlus()){
            Log.d(TAG, "Product is not Delivery, Not init. ");
            return;
        }
        mLangCode = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_LANGUAGE);
        registerListener(langObserver);
        checkLanguageChanged();
    }


    private void registerListener(ContentObserver contentObserver) {
        mContext.getContentResolver().registerContentObserver(
                getContentUri(), true, contentObserver);
    }

    private void registerListener(String key, ContentObserver contentObserver) {
        mContext.getContentResolver().registerContentObserver(
                Uri.withAppendedPath(getContentUri(), key), false, contentObserver);
    }

    private void unRegisterListener(ContentObserver contentObserver) {
        mContext.getContentResolver().unregisterContentObserver(contentObserver);
    }

    private Uri getContentUri() {
        return DATA_URI;
    }

    private ContentObserver langObserver = new ContentObserver(null) {

        @Override
        public void onChange(boolean selfChange, Uri uri) {
            super.onChange(selfChange, uri);
            Log.d(TAG, "onChange uri :" + uri);

            checkLanguageChanged();
        }
    };

    private synchronized void checkLanguageChanged() {
        String localList = SystemApi.getInstance().getLocalSupportLanguageList();
        String languageList = SystemApi.getInstance().getLocalAndServerSupportLanguageList();
        Log.d(TAG, "onDefaultLanguageChanged localList(本地支持的语言) : " + localList+", serverAndLocalList(服务端本地都支持的语言取交集) :"+languageList);
        if (TextUtils.isEmpty(localList) || TextUtils.isEmpty(languageList)){
            return;
        }

        TypeToken<List<LanguageBean>> token = new TypeToken<List<LanguageBean>>() {};
        List<LanguageBean> localSupportList = new Gson().fromJson(localList, token.getType());
        List<LanguageBean> serverAndLocalList = new Gson().fromJson(languageList, token.getType());

        for (LanguageBean bean : serverAndLocalList) {
            if (bean.getIsDefault() == LanguageBean.UseState.DEFAULT
                    && !this.isCurLanguageInServerAndLocalList(serverAndLocalList, mLangCode) // 发现当前使用的语言被服务端下线
                    && !bean.getLangCode().equals(mLangCode)) {

                Log.d(TAG, "will change to server Main LanguageBean : " + bean);
                //使用切换语言之前的语言,提示更新
                showToast(bean.getLangCode());
//                SystemApi.getInstance().registerStatusListener(Definition.ROBOT_LANGUAGE, mStatusListener);
                RobotSettingApi.getInstance().setRobotString(Definition.ROBOT_LANGUAGE, bean.getLangCode());//切换服务端默认主语言为系统语言
                unRegisterListener(langObserver);
            }
        }

    }

    private boolean isCurLanguageInServerAndLocalList(List<LanguageBean> serverAndLocalList, String curLangCode) {
        for (LanguageBean bean : serverAndLocalList) {
            if (bean.getLangCode().equals(curLangCode)) {
                return true;
            }
        }
        return false;
    }

    private StatusListener mStatusListener = new StatusListener() {

        @Override
        public void onStatusUpdate(String type, String data) throws RemoteException {
            super.onStatusUpdate(type, data);
            Log.d(TAG, "onStatusUpdate type :" + type + ", data : " + data);
            if (Definition.ROBOT_LANGUAGE.equals(type)) {
                //切换完成,提示更新
//                showToast(data);
            }
        }
    };

    private void showToast(final String data) {
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                Toast.makeText(mContext,
                        mContext.getString(R.string.default_language_changed, getLanguageName(mLangCode), getLanguageName(data))
                        ,Toast.LENGTH_LONG).show();
            }
        });
    }


    private String getLanguageName(String langCode) {
        Log.d(TAG, "getLanguageName code : " + langCode);
        switch (langCode) {
            case LANGUAGE_CODE_ZH:
                return mContext.getString(R.string.language_chinese);
            case LANGUAGE_CODE_EN:
                return mContext.getString(R.string.language_english);
            case LANGUAGE_CODE_KO:
                return mContext.getString(R.string.language_korean);
            case LANGUAGE_CODE_JA:
                return mContext.getString(R.string.language_japanese);
            default:
                return mContext.getString(R.string.language_english);
        }
    }


    public static final class LanguageField implements BaseColumns {
        static final String TABLE_NAME = "language_list";

        static final String LANG_CODE = "langCode";
        static final String LANG_NAME = "langName";
        static final String IS_DEFAULT = "isDefault";
        static final String SUPPORT = "support";
    }
}
