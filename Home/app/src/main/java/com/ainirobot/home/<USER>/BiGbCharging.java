/*
 *   Copyright (C) 2017 OrionStar Technology Project
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.ainirobot.home.bi;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;
import com.ainirobot.home.control.ControlManager;

/**
 * 充电 BI
 *
 * @FileName: com.ainirobot.home.bi.BiGbCharging.java
 * @author: Orion
 * @date: 2018-12-20 11:00
 */
public class BiGbCharging {
    public static final String CHARGE_ALL = "all";
    private static final long INVALID_TIME = -1l;

    private ChargingReport mChargingReport;
    private ChargingFinishReport mChargingFinishReport;
    private StopChargingReport mStopChargingReport;
    private long currentTime = INVALID_TIME;

    public BiGbCharging() {
        super();
        mChargingReport = new ChargingReport();
        mChargingFinishReport = new ChargingFinishReport();
        mStopChargingReport = new StopChargingReport();
    }

    public ChargingReport charging() {
        mChargingReport.clearReportDatas();
        currentTime = System.currentTimeMillis();
        return mChargingReport.ctime().eq(ControlManager.getControlManager().getSystemStatusManager().getBatteryLevel());
    }

    public ChargingFinishReport chargeResult() {
        mChargingFinishReport.clearReportDatas();
        int period = (INVALID_TIME == currentTime ? (int) INVALID_TIME : (int) ((System.currentTimeMillis() - currentTime) / 1000));
        currentTime = INVALID_TIME;
        return mChargingFinishReport.period(period).ctime().eq(ControlManager.getControlManager().getSystemStatusManager().getBatteryLevel());
    }

    public StopChargingReport stopChargingBaseResult(){
        mStopChargingReport.clearReportDatas();
        return mStopChargingReport.baseResult();
    }

    /**
     * @埋点名称: gb_charging
     * @埋点描述: 充电任务
     * @埋点目的: 统计充电任务的触发次数
     * @上报时机: 触发充电任务时，上报
     * @params: occur --- 触发条件[0 - 全部(任何类型充电都报1条这个); 1 - 语⾳回充;  3 - 低电回充; 4 - 设置回充(定时+触屏); 5 - 微信回充; 6 - OTA回充] byte
     * @params: eq --- 电量
     * @params: ctime --- 埋点产生时的时间戳 int(8)
     */
    public static class ChargingReport extends BaseBiReport {

        public ChargingReport() {
            super("gb_charging");
        }

        public ChargingReport ctime() {
            addData("ctime", System.currentTimeMillis());
            return this;
        }

        public ChargingReport occur(String charge) {
            addData("occur", Occur.vauleOfCharge(charge).getOccur());
            return this;
        }

        public ChargingReport eq(int eq) {
            addData("eq", eq);
            return this;
        }

    }

    /**
     * @埋点名称: gb_charging_finish
     * @埋点描述: 充电完成
     * @埋点目的: 统计充电完成次数、时长
     * @上报时机: 充电完成时，上报
     * @params: eq --- 电量
     * @params: period --- 充电时长（单位：秒）
     * @params: ctime --- 埋点产生时的时间戳 int(8)
     */
    public static class ChargingFinishReport extends BaseBiReport {
        public ChargingFinishReport() {
            super("gb_charging_finish");
        }

        public ChargingFinishReport ctime() {
            addData("ctime", System.currentTimeMillis());
            return this;
        }

        public ChargingFinishReport eq(int eq) {
            addData("eq", eq);
            return this;
        }

        public ChargingFinishReport period(int period) {
            addData("period", period);
            return this;
        }
    }

    /**
     * @埋点名称: gb_charging_end
     * @埋点描述: 主动脱离充电桩上报
     * @埋点目的: 统计充电任务的触发次数
     * @上报时机: 触发脱离充电桩时，上报
     * @params: mode --- 离开方式[0 - rom上报; 1 - 定时结束脱离;  2 - 点击退出脱离; 99 - 外部api调用脱离充电桩接口]
     * @params: eq --- ui电量
     * @params: ctime --- 埋点产生时的时间戳
     */
    public static class StopChargingReport extends BaseBiReport {

        private static final String TABLE_NAME = "gb_charging_end";
        public StopChargingReport() {
            super(TABLE_NAME);
        }

        public StopChargingReport baseResult(){
            addData("status",0);
            addData("period","");
            addData("real_eq","");
            addData("link_id","");
            addData("wakeup_id","");
            return this;
        }

        public StopChargingReport ctime() {
            addData("ctime", System.currentTimeMillis());
            return this;
        }

        public StopChargingReport eq(int eq) {
            addData("eq", eq);
            return this;
        }

        public StopChargingReport mode(int mode){
            addData("mode",mode);
            return this;
        }
    }

    enum Occur {
        ota(Definition.CHARGE_OTA),
        low(Definition.CHARGE_LOW),
        setting(Definition.CHARGE_SETTING),
        wechat(Definition.CHARGE_WECHAT),
        charge_slow(Definition.CHARGE_SLOW),
        all(CHARGE_ALL);

        private byte occur;

        Occur(String charge) {
            switch (charge) {
                case Definition.CHARGE_OTA:
                    occur = 0x06;
                    break;
                case Definition.CHARGE_LOW:
                    occur = 0x03;
                    break;
                case Definition.CHARGE_SETTING:
                    occur = 0x04;
                    break;
                case Definition.CHARGE_WECHAT:
                    occur = 0x05;
                    break;
                case Definition.CHARGE_SLOW:
                    occur = 0x07;
                    break;
                default:
                    occur = 0;
                    break;
            }
        }

        public byte getOccur() {
            return occur;
        }

        public static Occur vauleOfCharge(String charge) {
            switch (charge) {
                case Definition.CHARGE_OTA:
                    return ota;
                case Definition.CHARGE_LOW:
                    return low;
                case Definition.CHARGE_SETTING:
                    return setting;
                case Definition.CHARGE_WECHAT:
                    return wechat;
                case Definition.CHARGE_SLOW:
                    return charge_slow;
                default:
                    return all;
            }
        }
    }
}
