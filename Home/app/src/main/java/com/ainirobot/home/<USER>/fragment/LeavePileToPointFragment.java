package com.ainirobot.home.ui.fragment;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.ui.UIController;

public class LeavePileToPointFragment extends BaseFragment implements View.OnClickListener {

    private static final String TAG = "LeavePileToPointFragment:Home";
    private TextView mTitle;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @SuppressLint("LongLogTag")
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        Log.i(TAG, "on create view");
        View view = inflater.inflate(R.layout.fragment_leave_pile_to_point, null);
        mTitle = view.findViewById(R.id.title);
        return view;
    }

    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Log.d(TAG, "onViewCreated " );
        showLeavePileUI();
    }

    private void showLeavePileUI() {
        mTitle.setText(getResources().getString(R.string.leave_pile_title));
    }

    private void showGoPoint() {
        mTitle.setText(getResources().getString(R.string.go_standby_point));
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_START_GO_STANDBY_POINT);
    }

    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
        switch (type) {
            case LEAVE_PILE_SUCCESS:
                this.showGoPoint();
                break;
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.confirm_button:
                break;
            default:
                break;
        }
    }
}