package com.ainirobot.home.ota.database;

import static com.ainirobot.home.ota.bean.VersionData.STATUS.NA;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_LIST;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteException;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import com.ainirobot.home.ota.constants.OtaConstants;


public class DataBaseHelper extends SQLiteOpenHelper {

    private static final String TAG = OtaConstants.TAG_PREFIX + DataBaseHelper.class.getSimpleName();

    public final static String DATABASE_NAME = "downgrade.db";
    public final static String TABLE_NAME = "ota";

    public static int VERSION = 1;
    private final Context mContext;

    public DataBaseHelper(Context context, String name, SQLiteDatabase.CursorFactory factory, int
            version) {
        super(context, name, factory, version);
        mContext = context;
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        Log.d(TAG, "onCreate.");
        createDataBase(db);
    }

    void createDataBase(SQLiteDatabase db) {
        Log.e(TAG, "createDataBase " + TABLE_NAME);
        try {
            String updateSql = "CREATE TABLE " + TABLE_NAME
                    + "(_id integer primary key autoincrement, name text UNIQUE,"
                    + "currentVersion text, targetVersion text, status int)";

            db.execSQL(updateSql);

            String cmd = "INSERT INTO " + TABLE_NAME + "(name, status) values(?, ?)";
            for (String os : OS_LIST) {
                db.execSQL(cmd, new Object[]{os, NA.ordinal()});
            }
        } catch (SQLiteException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.d(TAG, "onUpgrade.");
    }
}
