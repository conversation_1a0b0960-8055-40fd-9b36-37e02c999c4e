/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.home.bi;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;
import com.ainirobot.home.control.ControlManager;

import java.util.UUID;

public class DormancyReport {

    private static final String DORMANCY_NAME = "dormancy_name";
    private static final String DORMANCY_REASON = "dormancy_reason";
    private static final String EQ =  "eq";
    private static final String TASK_ID = "task_id";
    private static final String CTIME = "ctime";

    private String mDormancyName;
    private String mTaskId;

    public void setDormancyName(String name) {
        mDormancyName = name;
        mTaskId = UUID.randomUUID().toString();
    }

    public class DormancyStartReport extends BaseBiReport {
        private static final String TABLE_NAME = "gb_dormancy_start";

        public DormancyStartReport() {
            super(TABLE_NAME);
        }

        @Override
        public void report() {
            addData(DORMANCY_NAME, mDormancyName);
            addData(EQ, ControlManager.getControlManager().getSystemStatusManager().getBatteryLevel());
            addData(TASK_ID, mTaskId);
            addData(CTIME, System.currentTimeMillis());
            super.report();
        }
    }

    public class DormancyCancelReport extends BaseBiReport {

        private static final String TABLE_NAME = "gb_dormancy_cancel";

        public DormancyCancelReport() {
            super(TABLE_NAME);
        }

        public DormancyCancelReport addReason(String reason) {
            addData(DORMANCY_REASON, reason);
            return this;
        }

        @Override
        public void report() {
            addData(DORMANCY_NAME, mDormancyName);
            addData(EQ, ControlManager.getControlManager().getSystemStatusManager().getBatteryLevel());
            addData(TASK_ID, mTaskId);
            addData(CTIME, System.currentTimeMillis());
            super.report();
        }
    }

    public class DormancyErrorReport extends BaseBiReport {

        private static final String TABLE_NAME = "gb_dormancy_error";

        public DormancyErrorReport() {
            super(TABLE_NAME);
        }

        public DormancyErrorReport addReason(String reason) {
            addData(DORMANCY_REASON, reason);
            return this;
        }

        @Override
        public void report() {
            addData(DORMANCY_NAME, mDormancyName);
            addData(EQ, ControlManager.getControlManager().getSystemStatusManager().getBatteryLevel());
            addData(TASK_ID, mTaskId);
            addData(CTIME, System.currentTimeMillis());
            super.report();
        }
    }
}
