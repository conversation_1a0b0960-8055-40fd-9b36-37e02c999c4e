/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.home.service;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.module.ModuleCallbackApi;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.control.ControlManager;

import java.util.ArrayList;
import java.util.List;

/**
 * provides to CoreService, in order to call function provided by module app
 */

public class ModuleCallback extends ModuleCallbackApi {
    private static final String TAG = "ModuleCallback:Home";
    private Handler mMainHandler;

    private Context mContext;

    public ModuleCallback(Context context) {
        mContext = context;
        mMainHandler = ControlManager.getControlManager().getMainHandler();
    }

    @Override
    public boolean onSendRequest(int reqId, String reqType, String reqText, String reqParam)
            throws RemoteException {
        Log.d(TAG, "New request: " + " type is:" + reqType + " text is:" + reqText +" reqParam = "+reqParam);
        Message msg = mMainHandler.obtainMessage(ModuleDef.MSG_NEW_REQUEST);
        Bundle bundle = new Bundle();
        bundle.putInt(ModuleDef.MSG_BUNDLE_ID, reqId);
        bundle.putString(ModuleDef.MSG_BUNDLE_TEXT,
                (TextUtils.isEmpty(reqType) || reqType.equals("me_ui&me_ui")) ? reqText : reqType);
        bundle.putString(ModuleDef.MSG_BUNDLE_PARAM, reqParam);
        msg.setData(bundle);
        mMainHandler.sendMessage(msg);


        List<String> stringList = new ArrayList<String>() {{
            add("general_command&up_command");
            add("general_command&set_command");
            add("general_command&down_command");
            add("general_command&max_command");
            add("general_command&min_command");
        }};
        if (stringList.contains(reqType)) {
            return false;
        }

        return true;
    }

    /**
     * Handle message received from HW service
     *
     * @param hwFunction
     * @param hwReport
     * @throws RemoteException
     */
    @Override
    public void onHWReport(int hwFunction, String command, String hwReport)
            throws RemoteException {
//        Log.d(TAG, "HW report: " + " hwFunction is:" + hwFunction + " command:" + command +
//                " hwReport is:" + hwReport);

        Message msg = mMainHandler.obtainMessage(ModuleDef.MSG_HW_RESPORT);
        Bundle bundle = new Bundle();
        bundle.putInt(ModuleDef.MSG_BUNDLE_ID, hwFunction);
        bundle.putString(ModuleDef.MSG_BUNDLE_COMMAND, command);
        bundle.putString(ModuleDef.MSG_BUNDLE_TEXT, hwReport);
        msg.setData(bundle);
        mMainHandler.sendMessage(msg);
    }

}
