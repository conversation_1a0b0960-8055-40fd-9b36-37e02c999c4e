/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.home.utils;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.support.annotation.ColorRes;
import android.support.annotation.DrawableRes;
import android.support.annotation.StringRes;

import com.ainirobot.home.ApplicationWrapper;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

import java.io.InputStream;
import java.util.Hashtable;

public class ResUtil {

    public static String getString(@StringRes int resId, Object... formatArgs) {

        return ApplicationWrapper.getContext().getString(resId, formatArgs);
    }

    public static Drawable getDrawable(@DrawableRes int resId){
        return ApplicationWrapper.getContext().getResources().getDrawable(resId);
    }

    public static int getColor(@ColorRes int resId){
        return ApplicationWrapper.getContext().getResources().getColor(resId);
    }

    /**
     * create two-dimension code
     *
     * @param url string url
     * @param widthPx  px
     * @param heightPx px
     * @param margin  blank margin of boundary
     * @return
     */
    public static Bitmap createQRImage(String url, final int widthPx, final int heightPx,String margin, Bitmap centerLogo) {
        try {

            if (url == null || "".equals(url) || url.length() < 1) {
                return null;
            }
            Hashtable<EncodeHintType, Object> hints = new Hashtable<>();
            hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
            hints.put(EncodeHintType.MARGIN,margin);
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);// 中间加入log ,提高容错率.

            BitMatrix bitMatrix = new QRCodeWriter().encode(url,
                    BarcodeFormat.QR_CODE, widthPx, heightPx, hints);
            int[] pixels = new int[widthPx * heightPx];

            for (int y = 0; y < heightPx; y++) {
                for (int x = 0; x < widthPx; x++) {
                    if (bitMatrix.get(x, y)) {
                        pixels[y * widthPx + x] = 0xff000000;
                    } else {
                        pixels[y * widthPx + x] = 0xffffffff;
                    }
                }
            }
            Bitmap bitmap = Bitmap.createBitmap(widthPx, heightPx,
                    Bitmap.Config.ARGB_8888);
            bitmap.setPixels(pixels, 0, widthPx, 0, 0, widthPx, heightPx);

            // add centerLogo in QrCode
            if (centerLogo != null){
                bitmap = addLogo(bitmap, centerLogo);
            }
            return bitmap;
//            //必须使用compress方法将bitmap保存到文件中再进行读取,直接返回的bitmap是没有任何压缩的，内存消耗大！
//            return bitmap.compress(Bitmap.CompressFormat.JPEG, 100, new FileOutputStream(filePath));
        } catch (WriterException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 在二维码中间添加Logo图案
     */
    private static Bitmap addLogo(Bitmap src, Bitmap logo) {
        if (src == null) {
            return null;
        }

        if (logo == null) {
            return src;
        }

        //获取图片的宽高
        int srcWidth = src.getWidth();
        int srcHeight = src.getHeight();
        int logoWidth = logo.getWidth();
        int logoHeight = logo.getHeight();

        if (srcWidth == 0 || srcHeight == 0) {
            return null;
        }

        if (logoWidth == 0 || logoHeight == 0) {
            return src;
        }

        //logo大小为二维码整体大小的1/5
        float scaleFactor = srcWidth * 1.0f / 5 / logoWidth;
        Bitmap bitmap = Bitmap.createBitmap(srcWidth, srcHeight, Bitmap.Config.ARGB_8888);
        try {
            Canvas canvas = new Canvas(bitmap);
            canvas.drawBitmap(src, 0, 0, null);
            canvas.scale(scaleFactor, scaleFactor, srcWidth / 2, srcHeight / 2);
            canvas.drawBitmap(logo, (srcWidth - logoWidth) / 2, (srcHeight - logoHeight) / 2, null);

            canvas.save();
            canvas.restore();
        } catch (Exception e) {
            bitmap = null;
            e.getStackTrace();
        }

        return bitmap;
    }


    /** 从assets 文件夹中读取图片 */
    public static Bitmap loadImageFromAsserts(final Context ctx, String fileName) {
        try {
            InputStream is = ctx.getResources().getAssets().open(fileName);
            return BitmapFactory.decodeStream(is);
        } catch (OutOfMemoryError | Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Bitmap getCenterLogoBitmap(int resId){
        return BitmapFactory.decodeResource(ApplicationWrapper.getContext().getResources(), resId);
    }


    public static Intent createExpicitIntent(String pkgName, String className, String action) {
        ComponentName component = new ComponentName(pkgName, className);
        Intent intent = new Intent(action);
        intent.setComponent(component);
        return intent;
    }

    public static Intent createExpicitIntent(String pkgName,String clsName){
        ComponentName component = new ComponentName(pkgName, clsName);
        Intent intent = new Intent();
        intent.setComponent(component);
        return intent;
    }
}
