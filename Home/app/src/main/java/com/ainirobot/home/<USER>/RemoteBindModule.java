package com.ainirobot.home.module;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.bean.QrCodeBean;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.utils.ResUtil;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.annotations.SerializedName;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class RemoteBindModule extends BaseModule {

    private static final String TAG = "RemoteBindModule:Home";

    private static final int THREAD_POOL_NUMBER = 1;
    private static final int REQUEST_TIME_PERIOD = 5000;
    private static RemoteBindModule sInstance = null;
    private ScheduledExecutorService mRequestExecutor;
    private Context mContext;

    private enum BindStatus {
        IDLE,
        BIND_FAILED,
        BIND_GETQRCODE,
        BIND_SHOWQRCODE,
    }

    private BindStatus mBindStatus = BindStatus.IDLE;

    public static RemoteBindModule getsInstance() {
        if (null == sInstance) {
            sInstance = new RemoteBindModule();
        }
        return sInstance;
    }

    private RemoteBindModule() {
    }

    public void init(Context context) {
        mContext = context;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        switch (intent) {
            case Definition.REQ_BIND_FAILED:
                Log.d(TAG, "intent=" + intent + ",status=" + mBindStatus);
                if (mBindStatus != BindStatus.IDLE) {
                    SystemApi.getInstance().finishModuleParser(reqId, true);
                    return true;
                }
                mBindStatus = BindStatus.BIND_FAILED;
                showBindFailedPage();
                break;
        }
        SystemApi.getInstance().finishModuleParser(reqId, true);
        return true;
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
        switch (type) {
            case ModuleDef.LOCAL_MESSAGE_TO_BIND:
                showRemoteBindPage();
                break;
            case ModuleDef.LOCAL_MESSAGE_BIND_END:
                stop();
                break;
            case ModuleDef.LOCAL_MESSAGE_REQUEST_QR_CODE:
                mBindStatus = BindStatus.BIND_GETQRCODE;
                SystemApi.getInstance().remoteRequestQrcode(Definition.MODULE_REQ_ID, new CommandListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.d(TAG, "remoteQrcode args = " + result + ", msg = " + message);
                        if (!TextUtils.isEmpty(message)) {
                            try {
                                QrCodeBean bean = new Gson().fromJson(message, QrCodeBean.class);
                                if (bean != null && !TextUtils.isEmpty(bean.getData()) && bean.getExspireTime() > 0) {
                                    Log.e(TAG, "exspireTime:" + bean.getExspireTime());
                                    UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.MSG_QR_CODE
                                            , mGson.toJson(bean));

                                    requestBindStateTimer();
                                } else {
                                    //TODO:zhujie
                                    Log.e(TAG, "data err");
                                    UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.MSG_QR_CODE,"");
                                }
                            } catch (JsonSyntaxException e) {
                                e.printStackTrace();
                                UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.MSG_QR_CODE,"");
                            }
                        }
                    }
                });
                break;
            case ModuleDef.LOCAL_MESSAGE_GET_REMOTE_BIND_STATUS:
                mBindStatus = BindStatus.BIND_SHOWQRCODE;
                SystemApi.getInstance().remoteNextEvent();
                break;
        }
    }

    private void showBindFailedPage() {
        Log.i(TAG, "showBindFailedPage");
        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_BIND_FAILED, null, null);
    }

    private void showRemoteBindPage() {
        Log.i(TAG, "showRemoteBindPage");
        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_BIND, null, null);
    }

    private void showBindSuccessPage(String name) {
        Log.i(TAG, "showBindSuccessPage");
        SkillManager.getInstance().speechPlayText(ResUtil.getString(R.string.bind_tts_1, name));
        Bundle bundle = new Bundle();
        bundle.putString("robot_name", name);
        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_BIND_SUCCESS, bundle, null);
    }

    private void requestBindStateTimer() {
        cancelBindStateTimer();
        mRequestExecutor = new ScheduledThreadPoolExecutor(THREAD_POOL_NUMBER);
        mRequestExecutor.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                try {
                    handleQrcode();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }, REQUEST_TIME_PERIOD, REQUEST_TIME_PERIOD, TimeUnit.MILLISECONDS);
    }

    private void cancelBindStateTimer() {
        if (mRequestExecutor != null && !mRequestExecutor.isShutdown()) {
            mRequestExecutor.shutdownNow();
            mRequestExecutor = null;
        }
    }

    private void handleQrcode() {
        String info = SystemApi.getInstance().getBindInfo();
        if (!TextUtils.isEmpty(info)) {
            BindState state = new Gson().fromJson(info, BindState.class);
            if (state == null) {
                showBindFailedPage();
            } else if (state.getBindState() == Definition.BIND_SUCCESS) {
                UIController.getInstance().sendMessageToFragment(
                        UIController.MESSAGE_TYPE.MSG_QR_BIND_SUCCESS, "");
                showBindSuccessPage(state.getRobotName());
                cancelBindStateTimer();
            } else {
                Log.d(TAG, "state:" + state.getBindState());
            }
        } else {
            Log.e(TAG, "info err:" + info);
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        mBindStatus = BindStatus.IDLE;
        SystemApi.getInstance().onRemoteBindFinished();
    }

    private static class BindState {
        @SerializedName(Definition.BIND_STATUS)
        private int mBindState;
        @SerializedName(Definition.ROBOT_NAME)
        private String mRobotName;

        public int getBindState() {
            return mBindState;
        }

        public String getRobotName() {
            return mRobotName;
        }

        @Override
        public String toString() {
            return "BindState{" +
                    "mBindState=" + mBindState +
                    ", mRobotName='" + mRobotName + '\'' +
                    '}';
        }
    }
}
