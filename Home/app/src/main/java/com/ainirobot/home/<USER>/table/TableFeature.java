package com.ainirobot.home.data.table;

import static com.ainirobot.home.bean.Feature.DBFiled.FILED_FEATURE_ID;
import static com.ainirobot.home.bean.Feature.DBFiled.FILED_ISBACK;
import static com.ainirobot.home.bean.Feature.DBFiled.FILED_NAME;
import static com.ainirobot.home.bean.Feature.DBFiled.FILED_PRIORITY;

import com.ainirobot.home.bean.Feature;

public class TableFeature {

    public static final String FEATURE_DROP = "DROP TABLE IF EXISTS " + Feature.DBFiled.TABLE_NAME;
    public static final String FEATURE_DELETE = "DELETE FROM " + Feature.DBFiled.TABLE_NAME;
    public static final String FEATURE_DELETE_SEQUENCE = "DELETE FROM sqlite_sequence WHERE name =  '" + Feature.DBFiled.TABLE_NAME + "'";

    public static final String FEATURE_INSERT = "INSERT INTO " + Feature.DBFiled.TABLE_NAME + " (" +
            FILED_FEATURE_ID + "," + FILED_NAME + "," + FILED_PRIORITY + "," + FILED_ISBACK + ") VALUES " +
            "(17,                 'ota',                     1,                 0)," +
            "(19,                 'inspection',              1,                 0)," +
            "(20,                 'standby',                 2,                 0)," +
            "(21,                 'emergency',               3,                 0)," +
            "(26,                 'auto_charge',             5,                 0)," +
            "(27,                 'set_charge_pile',         5,                 0)," +
            "(207,                'launcher',                12,                0)," +
            "(28,                 'charging',                12,                0)," +
            "(25,                 'reposition',              12,                0)," +
            "(60,                 'bind_server',              1,                0)," +
            "(208,                'remote_reposition',       12,                0)," +
            "(112,                'settingmodule',           1,                 1)," +
            "(1000,               'all',                     12,                0)," +
            "(29,                 'hw_state',                1,                 0)," +
            "(209,                'radar',                   1,                 0)," +
            "(211,                'dormancy',                1,                 0)," +
            "(30,                 'remote_control',          12,                0)," +
            "(31,                 'full_lock',                2,                0)," +
            "(32,                 'push_map_need_switch',     2,                0)," +
            "(24,                 'ble_close',                2,                0)," +
            "(50,                 'wheel_over',               13,               0)," +
            "(114,                'push_map_no_switch',       1,                1)," +
            "(113,                'lite_lock',                1,                1)," +
            "(115,                'shutdown',                 1,                0)," +
            "(116,                'custom_target',            1,                0)," +
            "(117,                'stop_charging',            1,                0)," +
            "(223,                'navi_sensor',              4,                0)," +
            "(52,                 'load_map',                 1,                0)," +
            "(224,                'import_map',               1,                1)," +
            "(53,                 'stop_charging_confirm',    1,                0)," +
            "(13,                 'req_ota_downgrade',        1,                0);";
}
