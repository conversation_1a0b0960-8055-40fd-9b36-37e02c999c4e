package com.ainirobot.home.ui.fragment;


import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.content.res.Resources;
import android.content.res.XmlResourceParser;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.support.v4.app.Fragment;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.utils.FileUtils;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.HomeConstant;
import com.ainirobot.home.R;
import com.ainirobot.home.bean.ShortcutResolveInfo;
import com.ainirobot.home.bean.ShowAppBean;
import com.ainirobot.home.utils.AppSettingInfoHelper;
import com.ainirobot.home.utils.ResUtil;
import com.ainirobot.home.utils.ToastUtil;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.xmlpull.v1.XmlPullParserException;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;

/**
 * A simple {@link Fragment} subclass.
 */
public class LauncherFragment extends BaseFragment {

    private static final String TAG = "LauncherFragment:Home";
    private static final String ROBOT_DROP_PASSWORD_ONLY_SHOW_DEFAULT
            = "robot_drop_password_only_show_default";
    private static final String ROBOT_REPOSITION_PKG = "com.ainirobot.reposition";
    private static final String ELEVATOR_PKG_NAME = "com.ainirobot.elevator";
    //静态密码&&非韩国时区
    private final static String STATIC_PWD_ANY_TIME_ZONE = "static_password_and_any_time_zone";
    //静态密码&&韩国时区
    private final static String STATIC_PWD_KO_TIME_ZONE = "static_password_and_ko_time_zone";
    //动态密码&&韩国时区
    private final static String DYNAMIC_PWD_KO_TIME_ZONE = "dynamic_password_and_ko_time_zone";
    //动态密码&&非韩国时区
    private final static String DYNAMIC_PWD_NON_KO_TIME_ZONE = "dynamic_password_and_non_ko_time_zone";
    //首次激活&韩国时区
    private final static String FIRST_CONFIG_END_KO_TIME_ZOON = "first_confing_end_ko_time_zone";
    private static final String MAPTOOL_PACKAGE_NAME = "com.ainirobot.maptool";
    private static final String ELECTRIC_DOOR_PACKAGE_NAME = "com.ainirobot.edoor";

    private static final int SHOW_APP = 1;
    private static final int HIDE_APP = 0;
    private PackageReceiver mPackageReceiver;
    private List<ResolveInfo> mAppLists = new ArrayList<>();
    private GridView mGridView;
    private AppsAdapter mAppsAdapter;

    /**
     * 从设置——自定义桌面中获取的数据
     */
    private List<ShowAppBean> mAppListFromSetting = new ArrayList<>();

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        Log.d(TAG, "onCreateView");
        View view = inflater.inflate(R.layout.fragment_launcher, container, false);
        mGridView = (GridView) view.findViewById(R.id.app_list);
        mGridView.setOnItemClickListener(onItemClickListener);

        mAppsAdapter = new AppsAdapter(getActivity());
        mGridView.setAdapter(mAppsAdapter);

        return view;
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d("LauncherFragment", "Home launcher fragment resume");
        loadApps();

        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_PACKAGE_ADDED);
        filter.addAction(Intent.ACTION_PACKAGE_REMOVED);
        filter.addDataScheme("package");
        mPackageReceiver = new PackageReceiver();
        getActivity().registerReceiver(mPackageReceiver, filter);
    }

    @Override
    public void onPause() {
        super.onPause();
        getActivity().unregisterReceiver(mPackageReceiver);
    }

    /**
     * 首次激活韩国时区逻辑
     */
    private boolean firstConfigKoData() {
        boolean notReboot = false;
        if (SystemApi.getInstance().isApiConnectedService()) {
            notReboot = SystemApi.getInstance().isFirstConfigFinishedNotReboot();
        }
        Log.d(TAG, "notReboot : " + notReboot);
        return notReboot;
    }

    private String getShowAppType() {
        boolean isMatchMd5Pwd = getActivity().getIntent().getBooleanExtra("isMatchMd5Pwd", false);
        String timezone = RobotSettings.getSysTimeZone();
        boolean firstConfig = firstConfigKoData();
        Log.d(TAG, " getShowAppType:: isMatch:" + isMatchMd5Pwd + ", timezone:" + timezone + " firstConfig：" + firstConfig);
        if (firstConfig && HomeConstant.Timezone.SEOUL.equalsIgnoreCase(timezone)) {
            return FIRST_CONFIG_END_KO_TIME_ZOON;
        }
        //静态密码(isMatchMd5Pwd == false)
        if (!isMatchMd5Pwd) {
            if (HomeConstant.Timezone.SEOUL.equalsIgnoreCase(timezone)) {//静态密码&&韩国时区
                return STATIC_PWD_KO_TIME_ZONE;
            } else {//静态密码(isMatchMd5Pwd == false)&&非韩国时区
                return STATIC_PWD_ANY_TIME_ZONE;
            }
        } else {//动态密码(isMatchMd5Pwd == true)
            if (HomeConstant.Timezone.SEOUL.equalsIgnoreCase(timezone)) {//动态密码&&韩国时区
                return DYNAMIC_PWD_KO_TIME_ZONE;
            } else {//动态密码&&非韩国时区
                return DYNAMIC_PWD_NON_KO_TIME_ZONE;
            }
        }
    }

    private void initData() {
        String model = RobotSettings.getProductModel();
        Log.d(TAG, " initData:: model:" + model);
        String type = ProductInfo.isDeliveryProduct() ? getShowAppType() : "";
        Log.d(TAG, " initData:: type:>" + type + "<");
        List<String> hideList = new ArrayList<>(Arrays.asList(ApplicationWrapper.getContext().
                getResources().getStringArray(R.array.hideList)));
        showAllSpecialApps(hideList);
        sortDefault();
        switch (type) {
            case FIRST_CONFIG_END_KO_TIME_ZOON:
                Log.d(TAG, " 韩国首次激活展示全部，不做单独处理");
                break;
            case STATIC_PWD_KO_TIME_ZONE://只展示ko_static_password_showList中的内容
                handleKoZoonData();
                getAPPListFromSetting();
                break;
            //静态密码&&非韩国时区
            case STATIC_PWD_ANY_TIME_ZONE:
                //动态密码&&非韩国时区
            case DYNAMIC_PWD_NON_KO_TIME_ZONE:
                getAPPListFromSetting();
                break;
            case DYNAMIC_PWD_KO_TIME_ZONE:
            default:
                break;
        }
    }

    private void loadApps() {
        Intent mainIntent = new Intent(Intent.ACTION_MAIN, null);
        mainIntent.addCategory(Intent.CATEGORY_LAUNCHER);
        //获取设备上所有具有启动器的应用程序的信息
        mAppLists = getActivity().getPackageManager().queryIntentActivities(mainIntent, 0);
        int length = mAppLists.size();
        Log.d(TAG, "loadApps-getPackageManager，所有具有启动器的应用， mAppLists length:" + length);

        if (SystemApi.getInstance().isSecondaryDevelopmentProhibited()) {
            List<String> showList = Arrays.asList(ApplicationWrapper.getContext().getResources().
                    getStringArray(R.array.showList));
            mAppLists.removeIf(resolveInfo -> !showList.contains(resolveInfo.activityInfo.packageName));
            sortDefault(showList);

            mAppsAdapter.updateData(mAppLists);
            mAppsAdapter.notifyDataSetChanged();
            Log.d(TAG, "loadApps: isSecondaryDevelopmentProhibited");
            return;
        }

        //初始化设置——自定义桌面中数据，排除没有启动器的应用
        initAPPListFromSetting();

        if (enableDropDownBarPasswordOnlyShowDefault()) {
            String defaultApp = getDefaultApp();
            Log.d(TAG, "loadApps defaultApp:" + defaultApp);
            for (int i = length - 1; i >= 0; i--) {
                ResolveInfo info = mAppLists.get(i);
                if (!defaultApp.contains(info.activityInfo.packageName)) {
                    mAppLists.remove(info);
                }
            }
        } else {
            initData();
        }
        deleteElevatorPkg();
        checkElectricDoor();
        checkSupportSecondaryDevelopment();
        String sysTimeZone = RobotSettings.getSysTimeZone();
        Log.d(TAG, "loadApps sysTimeZone:" + sysTimeZone);
        if (HomeConstant.Timezone.SEOUL.equalsIgnoreCase(sysTimeZone)
                && ProductInfo.isDeliveryOverSea()) {
            Log.d(TAG, "start loadMaptoolShortcut");
            loadMaptoolShortcut(mAppLists);// 招财海外版本并且韩国时区才添加maptool 快捷方式到Home桌面.
        }
        mAppsAdapter.updateData(mAppLists);
        mAppsAdapter.notifyDataSetChanged();
    }

    /**
     * 电动门app控制检查,当前机器没有电动门则不显示其控制app
     */
    private void checkElectricDoor() {
        if (!FileUtils.hasElectricDoor()) {
            Iterator<ResolveInfo> iterator = mAppLists.iterator();
            while (iterator.hasNext()) {
                ResolveInfo info = iterator.next();
                if (TextUtils.equals(ELECTRIC_DOOR_PACKAGE_NAME, info.activityInfo.packageName)) {
                    iterator.remove();
                    break;
                }
            }
        }
    }
    private void checkSupportSecondaryDevelopment() {
        if (TextUtils.equals(RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_SETTING_NOT_ALLOW_SECONDARY_DEVELOPMENT), "1")) {
            Iterator<ResolveInfo> iterator = mAppLists.iterator();
            while (iterator.hasNext()) {
                ResolveInfo info = iterator.next();
                if (!info.activityInfo.packageName.startsWith("com.ainirobot")) {
                    iterator.remove();
                    break;
                }
            }
        }
    }

    /**
     * 加载MapTool 的资源包中的 shortcuts.xml 生成快捷方式入口.
     *
     * @param list
     */
    private void loadMaptoolShortcut(List<ResolveInfo> list) {

        PackageManager packageManager = ApplicationWrapper.getContext().getPackageManager();
        Resources resources = null;
        try {
            resources = packageManager.getResourcesForApplication(MAPTOOL_PACKAGE_NAME);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        if (resources == null) {
            Log.d(TAG, "The resources for the given application could not be loaded.Resources is null");
            return;
        }

        int identifier = resources.getIdentifier("shortcuts", "xml", MAPTOOL_PACKAGE_NAME);
        Log.d(TAG, "initMetaData identifier:" + identifier);
        try {
            XmlResourceParser xrp = resources.getXml(identifier);

            while (xrp.getEventType() != XmlResourceParser.END_DOCUMENT) {
                if (xrp.getEventType() == XmlResourceParser.START_TAG && "shortcut".equals(xrp.getName())) {
                    String action = xrp.getAttributeValue(null, "action");
                    String icon = xrp.getAttributeValue(null, "icon");
                    String shortcutId = xrp.getAttributeValue(null, "shortcutId");
                    String label = xrp.getAttributeValue(null, "shortcutShortLabel");
                    String targetClass = xrp.getAttributeValue(null, "targetClass");
                    String targetPackage = xrp.getAttributeValue(null, "targetPackage");

                    Log.d(TAG, "initMetaData action:" + action + ",icon:" + icon + ",shortcutId:" + shortcutId + ",label:" + label
                            + ",targetClass:" + targetClass + ",targetPackage:" + targetPackage);

                    String labelStr = getStringResourceValue(resources, label);
                    int mipmapResId = getMipmapResourceId(resources, icon);
                    Log.d(TAG, "labelStr:" + labelStr + ", drawable:" + mipmapResId);

                    ShortcutResolveInfo sResolveInfo = new ShortcutResolveInfo();
                    sResolveInfo.setAction(action);
                    sResolveInfo.setLabel(labelStr);
                    sResolveInfo.setShortcutIcon(resources.getDrawable(mipmapResId, null));
                    sResolveInfo.setShortcutId(shortcutId);
                    sResolveInfo.setTargetClass(targetClass);
                    sResolveInfo.setTargetPackage(targetPackage);
                    list.add(sResolveInfo);
                }
                xrp.next();
            }
        } catch (XmlPullParserException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    private int getMipmapResourceId(Resources resources, String iconName) {
        return resources.getIdentifier(iconName, "mipmap", MAPTOOL_PACKAGE_NAME);
    }

    private String getStringResourceValue(Resources resources, String stringName) {
        int resourceId = resources.getIdentifier(stringName, "string", MAPTOOL_PACKAGE_NAME);
        return resources.getString(resourceId);
    }

    private void deleteElevatorPkg() {
//        if (!ProductInfo.isElevatorCtrlProduct()) {
        if (!SystemApi.getInstance().isSupportElevator()) {
            Iterator<ResolveInfo> iterator = mAppLists.iterator();
            while (iterator.hasNext()) {
                ResolveInfo info = iterator.next();
                if (TextUtils.equals(ELEVATOR_PKG_NAME, info.activityInfo.packageName)) {
                    iterator.remove();
                    break;
                }
            }
        }
    }

    private void sortDefault() {
        List<String> showList = Arrays.asList(ApplicationWrapper.getContext().getResources().
                getStringArray(R.array.showList));
        sortDefault(showList);
    }

    private void sortDefault(List<String> showList) {
        Log.d(TAG, " sortDefault:: ");
        mAppLists.sort(new Comparator<ResolveInfo>() {
            @Override
            public int compare(ResolveInfo o1, ResolveInfo o2) {
                int index1 = showList.indexOf(o1.activityInfo.packageName);
                int index2 = showList.indexOf(o2.activityInfo.packageName);
                if (index1 == -1) index1 = mAppLists.size();
                if (index2 == -1) index2 = mAppLists.size();
                if (index1 > index2) {
                    return 1;
                } else if (index1 == index2) {
                    return 0;
                } else {
                    return -1;
                }
            }
        });
    }

    /**
     * 获取设置——自定义桌面中数据的顺序
     */
    private void getAPPListFromSetting() {
        Log.d(TAG, " getAPPListFromSetting:: mAppListFromSetting size ——》" + mAppListFromSetting.size());
        if (!isSettingAppListEmpty()) {//设置中有自定义桌面数据
            sortAppDisplay(mAppListFromSetting);
            removeSettingHideApp(mAppListFromSetting);
        }
    }

    private void handleKoZoonData() {
        List<String> koShowList = new ArrayList<>(Arrays.asList(getResources().getStringArray(R.array.ko_static_password_showList)));
        Log.d(TAG, " handleKoZoonData:: 韩国时区默认显示app列表 长度 ——》" + koShowList.size());

        List<ResolveInfo> tempList = new ArrayList<>();
        Log.d(TAG, " handleKoZoonData:: 要显示的桌面app列表长度-Pre，mAppLists ——》 size=" + mAppLists.size());
        if (isSettingAppListEmpty()) {//自定义桌面数据为空
            for (int i = 0; i < koShowList.size(); i++) {
                for (int j = 0; j < mAppLists.size(); j++) {
                    ResolveInfo info = mAppLists.get(j);
                    if (koShowList.get(i).equals(info.activityInfo.packageName)) {
                        tempList.add(info);
                        break;
                    }
                }
            }
            mAppLists.clear();
            mAppLists.addAll(tempList);
        }
        Log.d(TAG, " handleKoZoonData:: 要显示的桌面app列表长度-Final，mAppLists ——》 size=" + mAppLists.size());
    }

    /**
     * 系统设置中隐藏的APP不在Home上进行展示
     */
    private void removeSettingHideApp(List<ShowAppBean> appListFromSetting) {
        try {
            int length = mAppLists.size();
            Log.d(TAG, " removeSettingHideApp:: 要显示的桌面app列表长度-Pre，mAppLists ——》 size=" + mAppLists.size());
            for (int i = length - 1; i >= 0; i--) {
                ResolveInfo info = mAppLists.get(i);
                String packName = info.activityInfo.packageName;
                for (int j = 0; j < appListFromSetting.size(); j++) {
                    ShowAppBean bean = appListFromSetting.get(j);
                    String fromSettingPackName = bean.getResolveInfo().activityInfo.packageName;
                    boolean isShow = bean.isChecked();
                    if (packName.equals(fromSettingPackName) && !isShow) {
                        mAppLists.remove(info);
                        Log.d(TAG, " removeSettingHideApp:: mAppLists ——》移除掉：" + packName
                                + " name——》" + info.activityInfo.loadLabel(getActivity().getPackageManager()).toString());
                        break;
                    }
                }
            }
            Log.d(TAG, " removeSettingHideApp:: 要显示的桌面app列表长度-Final，mAppLists ——》 size=" + mAppLists.size());
        } catch (Exception e) {
            Log.d(TAG, " 从设置中同步APP显示失败");
        }
    }

    /**
     * 根据设置——自定义桌面设置的顺序排序
     */
    private void sortAppDisplay(List<ShowAppBean> appListFromSetting) {
        if (appListFromSetting == null) {
            Log.d(TAG, " sortAppDisplay:: appListFromSetting ——》为空");
            return;
        }
        Log.d(TAG, " sortAppDisplay:: appListFromSetting ——》 size=" + appListFromSetting.size());
        Log.d(TAG, " sortAppDisplay:: mAppLists ——》 size=" + (mAppLists != null ? mAppLists.size() : 0));

        mAppLists.clear();
        for (int i = 0; i < appListFromSetting.size(); i++) {
            ResolveInfo bean = appListFromSetting.get(i).getResolveInfo();
            mAppLists.add(bean);
        }
        Log.d(TAG, " sortAppDisplay::-Final mAppLists ——》 size=" + (mAppLists != null ? mAppLists.size() : 0));
    }

    private void showAllSpecialApps(List<String> hideList) {
        String systemOSType = RobotSettings.getGlobalSettings(getContext(), Definition.ROBOT_SETTINGS_SYSTEM_OS_TYPE, "");
        Log.d(TAG, "showAllSpecialApps systemOSType=" + systemOSType);
        if (TextUtils.equals(systemOSType, Definition.OSType.AGENTOS.getValue())) {
            hideList.add(Definition.SPEECH_PACKAGE_NAME);
        } else {
            hideList.add(Definition.AGENT_PACKAGE_NAME);
        }
        Log.d(TAG, " showAllSpecialApps:: hideList size=" + hideList.size());
        showSpecialApp(hideList);
        int length = mAppLists.size();
        for (int i = length - 1; i >= 0; i--) {
            ResolveInfo info = mAppLists.get(i);
            if (hideList.contains(info.activityInfo.packageName)) {
                mAppLists.remove(info);
            }
        }
        Log.d(TAG, " showAllSpecialApps:: mAppLists ——》 size=" + (mAppLists != null ? mAppLists.size() : 0));
    }

    private void showSpecialApp(List<String> hideList) {
        int showApp = SettingsUtil.getInt(getActivity(), SettingsUtil.ROBOT_SETTING_ORION_FACTORY_MMI, HIDE_APP);
        Log.d(TAG, " showSpecialApp showApp: " + showApp);
        if (SettingsUtil.getInt(getActivity(), SettingsUtil.ROBOT_SETTING_ORION_FACTORY_MMI, HIDE_APP)
                == SHOW_APP) {
            hideList.remove(SettingsUtil.ROBOT_SETTING_ORION_FACTORY_MMI);
        }
        if (SettingsUtil.getInt(getActivity(), SettingsUtil.ROBOT_SETTING_RUNNING_TEST, HIDE_APP)
                == SHOW_APP) {
            hideList.remove(SettingsUtil.ROBOT_SETTING_RUNNING_TEST);
        }
    }

    private boolean enableDropDownBarPasswordOnlyShowDefault() {

        boolean enableDropDownBarPassword =
                RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_DROPDOWN_BAR_PASSWORD)
                        == Definition.ROBOT_SETTING_ENABLE;
        boolean enableDropDownBarPasswordOnlyShowDefault = SettingsUtil
                .getInt(ApplicationWrapper.getContext(),
                        ROBOT_DROP_PASSWORD_ONLY_SHOW_DEFAULT, 0) != 0;
        Log.d(TAG, "hasDropDownBarPassword enableDropDownBarPassword: "
                + enableDropDownBarPassword + ", enableDropDownBarPasswordOnlyShowDefault: "
                + enableDropDownBarPasswordOnlyShowDefault);
        return enableDropDownBarPassword && enableDropDownBarPasswordOnlyShowDefault;
    }

    private String getDefaultApp() {
        String packageName = RobotSettingApi.getInstance().
                getRobotString(Definition.BOOT_APP_PACKAGE_NAME);
        if (null == packageName) {
            packageName = Definition.MODULE_PACKAGE_NAME;
        }
        return packageName;
    }

    public class AppsAdapter extends BaseAdapter {

        private LayoutInflater mInflater;
        private List<ResolveInfo> mData = new ArrayList<>();

        public AppsAdapter(Context context) {
            mInflater = LayoutInflater.from(context);
        }

        public void updateData(List<ResolveInfo> list) {
            mData.clear();
            mData.addAll(list);
        }

        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder viewHolder = null;
            if (convertView == null) {
                viewHolder = new ViewHolder();
                convertView = mInflater.inflate(R.layout.gridview_item, null);
                viewHolder.imageView = (ImageView) convertView.findViewById(R.id.item_image);
                viewHolder.textView = (TextView) convertView.findViewById(R.id.item_text);
                convertView.setLayoutParams(new ViewGroup.LayoutParams(190, ViewGroup.LayoutParams.WRAP_CONTENT));
                convertView.setTag(viewHolder);
            } else {
                viewHolder = (ViewHolder) convertView.getTag();
            }
            ResolveInfo info = mData.get(position);

            Drawable drawable = null;
            String label = "";
            if (info instanceof ShortcutResolveInfo) {
                ShortcutResolveInfo shortcutResolveInfo = (ShortcutResolveInfo) info;
                drawable = shortcutResolveInfo.getShortcutIcon();
                label = shortcutResolveInfo.getLabel();
            } else {
                PackageManager packageManager = ApplicationWrapper.getContext().getPackageManager();
                drawable = info.activityInfo.loadIcon(packageManager);
                label = info.activityInfo.loadLabel(packageManager).toString();
                Log.d(TAG, "system label : " + label);
            }
            viewHolder.imageView.setImageDrawable(drawable);
            viewHolder.textView.setText(label);

            return convertView;
        }

        public final int getCount() {
            return mData.size();
        }

        public final Object getItem(int position) {
            return mData.get(position);
        }

        public final long getItemId(int position) {
            return position;
        }
    }

    static class ViewHolder {
        ImageView imageView;
        TextView textView;
    }

    AdapterView.OnItemClickListener onItemClickListener = new AdapterView.OnItemClickListener() {
        @Override
        public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
            ResolveInfo info = (ResolveInfo) mGridView.getItemAtPosition(position);
            if (info instanceof ShortcutResolveInfo) {
                handleClickShortcut((ShortcutResolveInfo) info);
                return;
            }
            String pkg = info.activityInfo.packageName;
            String cls = info.activityInfo.name;
            Log.d(TAG, "launcher jump to pkg:" + pkg + ",cls" + cls);
            //判断是否快捷定位
            if (pkg.equals(ROBOT_REPOSITION_PKG)) {
                this.repositionHandle(pkg, cls);
            } else {
                ComponentName component = new ComponentName(pkg, cls);
                Intent i = new Intent();
                i.setComponent(component);
                startActivity(i);
                SystemApi.getInstance().startAppControl(pkg);
            }
        }

        private void handleClickShortcut(ShortcutResolveInfo info) {
            Log.d(TAG, "handleClickShortcut : " + info.toString());
            Intent intent = new Intent(info.getAction());
            intent.setComponent(new ComponentName(info.getTargetPackage(), info.getTargetClass()));
            startActivity(intent);
            SystemApi.getInstance().startAppControl(info.getTargetPackage());
        }

        //快捷定位 判断是否已经定位
        private void repositionHandle(final String pkg, final String cls) {
            SystemApi.getInstance().isRobotEstimate(0, new CommandListener() {
                @Override
                public void onResult(int result, String message) {
                    super.onResult(result, message);
                    Log.d(TAG, "isRobotEstimate result : " + result + ", msg :" + message);
                    if (result == Definition.RESULT_OK && "true".equals(message)) {
                        ToastUtil.showToast(ApplicationWrapper.getContext(), ResUtil.getString(R.string.reposition_state_ok));
                    } else {
                        ComponentName component = new ComponentName(pkg, cls);
                        Intent i = new Intent();
                        i.setComponent(component);
                        startActivity(i);
                        SystemApi.getInstance().startAppControl(pkg);
                    }
                }
            });
        }
    };

    private class PackageReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            Log.d(TAG, "onReceive action : " + (intent == null ? "intent is null" : intent.getAction()));
            loadApps();
        }
    }

    /**
     * 设置中自定义桌面数据是否为空，mAppListFromSetting是否为空
     */
    private boolean isSettingAppListEmpty() {
        return mAppListFromSetting == null || mAppListFromSetting.isEmpty();
    }

    /**
     * 获取设置——自定义桌面中数据
     */
    private void initAPPListFromSetting() {
        String showAppJson = AppSettingInfoHelper.getAppSettingInfo();
        Log.d(TAG, "initAPPListFromSetting:: showAppJson ——》" + showAppJson);
        if (!showAppJson.isEmpty()) {
            List<ShowAppBean> appListFromSetting = new Gson().fromJson(showAppJson, new TypeToken<List<ShowAppBean>>() {
            }.getType());
            if (appListFromSetting != null) {
                mAppListFromSetting.clear();
                mAppListFromSetting.addAll(appListFromSetting);
            }
        }
        //排除没有启动器的应用
        boolean isSettingAppDataChanged = false;
        for (int i = mAppListFromSetting.size() - 1; i >= 0; i--) {
            ShowAppBean bean = mAppListFromSetting.get(i);
            String packageName = bean.getResolveInfo().activityInfo.packageName;
            boolean hasLauncher = false;//是否有启动器
            for (int j = 0; j < mAppLists.size(); j++) {
                ResolveInfo info = mAppLists.get(j);
                if (packageName.equals(info.activityInfo.packageName)) {
                    hasLauncher = true;
                    break;
                }
            }
            if (!hasLauncher) {
                Log.d(TAG, "initAPPListFromSetting:: 移除掉没有启动器的应用：" + packageName);
                mAppListFromSetting.remove(bean);
                isSettingAppDataChanged = true;
            }
        }
        Log.d(TAG, "initAPPListFromSetting:: mAppListFromSetting ——》 size=" + mAppListFromSetting.size());
        if (isSettingAppDataChanged) {
            Log.d(TAG, "initAPPListFromSetting:: mAppListFromSetting 数据有变化，更新设置中的数据");
            String jsonString = new Gson().toJson(mAppListFromSetting);
            AppSettingInfoHelper.saveAppSettingInfo(jsonString);
        }
    }
}
