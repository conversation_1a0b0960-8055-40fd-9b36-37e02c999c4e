package com.ainirobot.home.ota.parser;

import android.util.Log;
import android.util.Xml;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.utils.FileUtils;
import com.ainirobot.home.ota.utils.Preferences;

import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

import java.io.IOException;
import java.io.InputStream;
import java.util.Iterator;
import java.util.Vector;

public class VersionPullParser implements IParserInterface {
    private static final String TAG = "VersionPullParser";

    private Preferences mPrefs;

    public VersionPullParser() {
        mPrefs = Preferences.getInstance();
    }

    @Override
    public Vector<PackData> parse(InputStream in) {

        Vector<PackData> listData = null;
        PackData data = null;
        boolean isD150 = FileUtils.isD150();
        Log.d(TAG, "isD150=" + isD150);

        XmlPullParser parser = Xml.newPullParser();
        try {
            parser.setInput(in, "utf-8");
            int event = parser.getEventType();
            while (event != XmlPullParser.END_DOCUMENT) {
                switch (event) {
                    case XmlPullParser.START_DOCUMENT:
                        listData = new Vector<PackData>();
                        break;
                    case XmlPullParser.START_TAG:
                        if (parser.getName().equals("version_info")) {
                            if (parser.getAttributeName(0).equals("robot_version") && !mPrefs.getPrefsRollback()) {
                                String targetVersion = parser.getAttributeValue(0);
                                mPrefs.setServerTargetVersion(targetVersion);
                            }

                        } else if (parser.getName().equals("data")) {
                            data = new PackData();
                            if (parser.getAttributeName(0).equals("name")) {
                                data.setOsName(parser.getAttributeValue(0));
                                data.setOsMd5Name(data.getOsName() + PackData.MD5);
                            }
                            // 新增处理 motors 属性
                            if (parser.getAttributeCount() > 1 && parser.getAttributeName(1).equals("motors")) {
                                data.setMotors(parser.getAttributeValue(1));
                            }
                        } else if (parser.getName().equals("fileName")) {
                            event = parser.next();
                            data.setUpdateFileName(parser.getText());
                        } else if (parser.getName().equals("md5")) {
                            event = parser.next();
                            data.setUpdateFileMd5(parser.getText());
                        } else if (parser.getName().equals("version")) {
                            event = parser.next();
                            data.setTargetVersion(parser.getText());
                        }
                        break;
                    case XmlPullParser.END_TAG:
                        if (parser.getName().equals("data")) {
                            listData.add(data);
                            data = null;
                        }
                        break;
                }
                event = parser.next();
            }
        } catch (XmlPullParserException e) {
            e.printStackTrace();
            listData = null;
        } catch (IOException e) {
            e.printStackTrace();
            listData = null;
        } catch (NullPointerException e) {
            e.printStackTrace();
            listData = null;
        }

        //D150版本去掉psb，换成sub_psb
        if(isD150 && listData != null && listData.size() > 0) {
            //把osName=="psb"的数据删除
            //把osName=="sub_psb"的数据替换成osName=="psb"
            Iterator<PackData> iterator = listData.iterator(); // 创建迭代器

            while (iterator.hasNext()) {
                PackData packData = iterator.next();
                Log.d(TAG, "parseConfig: packData=" + packData.toString());

                if ("psb".equals(packData.getOsName())) {
                    iterator.remove(); // 安全删除
                    Log.d(TAG, "parseConfig: remove psb");
                } else if ("sub_psb".equals(packData.getOsName()) && Definition.MOTORS_D150.equals(packData.getMotors())) {
                    packData.setOsName("psb"); // 替换 osName
                    packData.setOsMd5Name("psb" + PackData.MD5);// 替换 osMd5Name
                    Log.d(TAG, "parseConfig: replace sub_psb to psb");
                }
            }
        }

        //遍历打印
        if(listData != null && listData.size() > 0) {
            for(PackData packData : listData) {
                Log.d(TAG, "parseConfig:Final: packData=" + packData.toString());
            }
        }

        return listData;
    }

}