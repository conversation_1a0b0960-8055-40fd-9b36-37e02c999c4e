package com.ainirobot.home.utils;

import org.jdeferred2.Deferred;
import org.jdeferred2.impl.DeferredObject;

class DeferredObjectWrapper<D, F, P> extends DeferredObject<D, F, P> {

    @Override
    public Deferred<D, F, P> resolve(D resolve) {
        try {
            super.resolve(resolve);
        }catch (Exception e) {
            e.printStackTrace();
        }
        return this;
    }

    @Override
    public Deferred<D, F, P> reject(F reject) {
        try {
            super.reject(reject);
        }catch (Exception e) {
            e.printStackTrace();
        }
        return this;
    }
}
