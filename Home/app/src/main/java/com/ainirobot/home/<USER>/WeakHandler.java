/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.home.utils;

import android.app.Activity;
import android.app.Fragment;
import android.os.Handler;

import java.lang.ref.WeakReference;

public class WeakHandler extends Handler {

    public WeakReference<Activity>     mRef;
    public WeakReference<Fragment> mReference;

    public WeakHandler(Activity ref) {
        mRef = new WeakReference<>(ref);
    }

    public WeakHandler(Fragment reference){
        mReference = new WeakReference<>(reference);
    }

    protected WeakReference<Activity> getRef() {
        return mRef;
    }

    public WeakReference<Fragment> getReference(Fragment reference){
        return mReference;
    }

    public boolean conflict(){
        Activity activity = getRef().get();
        return activity==null||activity.isFinishing();
    }
}
