package com.ainirobot.home.bi;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;


public class BiOutsideWarningReport extends BaseBiReport {
    public static final int ENTER = 1;
    public static final int EXIT = 2;
    private static final String TABLE_NAME = "base_robot_outside_warning";

    private static final String CTIME = "ctime";

    private static final String ACTION_TYPE = "action_type";

    public BiOutsideWarningReport() {
        super(TABLE_NAME);
    }

    public BiOutsideWarningReport addType(int type) {
        addData(ACTION_TYPE, type);
        return this;
    }

    @Override
    public void report() {
        addData(CTIME, System.currentTimeMillis());
        super.report();
    }

}
