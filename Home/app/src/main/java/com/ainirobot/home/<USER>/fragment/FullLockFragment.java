package com.ainirobot.home.ui.fragment;

import android.os.Bundle;
import android.support.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AlphaAnimation;
import android.view.animation.TranslateAnimation;
import android.widget.TextView;

import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.ui.view.GifView;

public class FullLockFragment extends BaseFragment {

    private static final String TAG = "FullLockFragment:Home";
    private TextView tv_sn;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        Log.i(TAG, "on create view");
        View view = inflater.inflate(R.layout.fragment_full_lock, null);
        tv_sn = (TextView) view.findViewById(R.id.tv_sn);
        String sn = RobotSettings.getSystemSn();
        sn = "SN : " + sn;
        tv_sn.setText(sn);
        if (getArguments() != null){
            String lockMsg = getArguments().getString(ModuleDef.MSG_BUNDLE_TEXT, ApplicationWrapper.getContext().getString(R.string.contact_owner));

            TextView tvMsg = (TextView) view.findViewById(R.id.tv_lockmsg);
            tvMsg.setText(lockMsg);
        }
        return view;
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
    }
}