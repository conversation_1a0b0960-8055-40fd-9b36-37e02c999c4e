/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.home.data;

import android.content.ContentProvider;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.UriMatcher;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.util.Log;

import com.ainirobot.home.bean.Feature;
import com.ainirobot.home.bean.Semantics;

public class DB<PERSON>rovider extends ContentProvider {

    private static final String TAG = "DBProvider:Home";
    public static final String authority = "com.ainirobot.home";
    private DBHelper dbHelper;
    private static final UriMatcher URI_MATCHER = new UriMatcher(UriMatcher.NO_MATCH);

    private static final int CODE_FEATURE = 1;
    private static final int CODE_INTENT = 2;

    static {
        URI_MATCHER.addURI(authority, Semantics.DBFiled.TABLE_NAME, CODE_INTENT);
        URI_MATCHER.addURI(authority, Feature.DBFiled.TABLE_NAME, CODE_FEATURE);
    }

    @Override
    public boolean onCreate() {
        Log.i(TAG, "onCreate");
        this.dbHelper = new DBHelper(this.getContext(), DBHelper.DB_NAME, null,
                DBHelper.DATABASE_VERSION);
        return false;
    }

    @Nullable
    @Override
    public Cursor query(@NonNull Uri uri, @Nullable String[] projection, @Nullable String selection,
                        @Nullable String[] selectionArgs, @Nullable String sortOrder) {
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        switch (URI_MATCHER.match(uri)) {
            case CODE_FEATURE:
                return db.query(Feature.DBFiled.TABLE_NAME, projection, selection, selectionArgs,
                        null, null, sortOrder);

            case CODE_INTENT:
                return db.query(Semantics.DBFiled.TABLE_NAME, projection, selection, selectionArgs,
                        null, null, sortOrder);

            default:
                break;
        }
        return null;
    }

    @Nullable
    @Override
    public String getType(@NonNull Uri uri) {
        return null;
    }

    @Nullable
    @Override
    public Uri insert(@NonNull Uri uri, @Nullable ContentValues values) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        long rowId = 0;
        Uri insertUri = null;
        switch (URI_MATCHER.match(uri)) {
            case CODE_FEATURE:
                rowId = db.insert(Feature.DBFiled.TABLE_NAME, null, values);
                break;

            case CODE_INTENT:
                rowId = db.insert(Semantics.DBFiled.TABLE_NAME, null, values);
                break;

            default:
                break;
        }
        Log.d(TAG, "insert rowID:" + rowId);
        if (rowId > 0) {
            insertUri = ContentUris.withAppendedId(uri, rowId);
        }
        Log.i(TAG, "lllll----inserturi:"+insertUri.toString());
        return insertUri;
    }

    @Override
    public int delete(@NonNull Uri uri, @Nullable String selection, @Nullable String[]
            selectionArgs) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        int count = 0;
        switch (URI_MATCHER.match(uri)) {
            case CODE_FEATURE:
                count = db.delete(Feature.DBFiled.TABLE_NAME, selection, selectionArgs);
                break;

            case CODE_INTENT:
                count = db.delete(Semantics.DBFiled.TABLE_NAME, selection, selectionArgs);
                break;

            default:
                break;
        }
        Log.d(TAG, "delete count:" + count);
        return count;
    }

    @Override
    public int update(@NonNull Uri uri, @Nullable ContentValues values, @Nullable String selection,
                      @Nullable String[] selectionArgs) {
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        int count = 0;
        switch (URI_MATCHER.match(uri)) {
            case CODE_FEATURE:
                count = db.update(Feature.DBFiled.TABLE_NAME, values, selection, selectionArgs);
                break;

            case CODE_INTENT:
                count = db.update(Semantics.DBFiled.TABLE_NAME, values, selection, selectionArgs);
                break;

            default:
                break;
        }
        return count;
    }
}
