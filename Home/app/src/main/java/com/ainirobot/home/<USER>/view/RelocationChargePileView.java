package com.ainirobot.home.ui.view;

import android.content.Context;
import android.content.res.Configuration;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.TextView;

import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.R;
import com.ainirobot.home.utils.ResType;
import com.ainirobot.home.utils.ResUtil;

/**
 * 开始充电桩定位View
 */
public class RelocationChargePileView extends FrameLayout {
    private static final String TAG = RelocationChargePileView.class.getSimpleName();
    public ImageButton mChargePileLocateCancel;
    public GifView mGifView;
    public Button mChargePileLocateConfirm;

    public static final int INIT = 1;
    private TextView mTitleDes;

    public RelocationChargePileView(Context context) {
        this(context, null);
    }

    public RelocationChargePileView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RelocationChargePileView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public RelocationChargePileView(Context context, @Nullable AttributeSet attrs, int defStyleAttr,
                                    int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initSubViews(context);
    }

    private void initSubViews(Context context){
        View mView = LayoutInflater.from(context).inflate(R.layout.layout_reposition_guide, this);
        mChargePileLocateCancel = (ImageButton) mView.findViewById(R.id.reposition_cancel);
        mTitleDes = (TextView) mView.findViewById(R.id.locate_guide_subtitle);
        mGifView = (GifView) mView.findViewById(R.id.locate_guide_bg);
        mChargePileLocateConfirm = (Button) mView.findViewById(R.id.locate_guide_confirmBtn);
    }

    public void updateChargePileView(int type){
        switch (type){
            case INIT:
                mTitleDes.setText(R.string.reposition_reset_remind_msg);
                mChargePileLocateConfirm.setVisibility(GONE);
                mGifView.setMovieResource(ResType.GIF_PUSH_TO_CHARGE_PILE.getResIdByType());
                break;
        }

    }

}
