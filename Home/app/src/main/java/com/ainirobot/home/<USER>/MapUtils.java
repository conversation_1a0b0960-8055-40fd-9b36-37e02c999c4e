/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.home.utils;

import android.content.Context;
import android.icu.util.ULocale;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.R;
import com.ainirobot.home.bean.MapInfo;
import com.ainirobot.home.bean.PushMapBean;
import com.google.gson.Gson;

import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class MapUtils {

    private static final String TAG = MapUtils.class.getSimpleName();

    public static final String SDCARD_PATH = Environment.getExternalStorageDirectory() + "/";
    /**
     * 地图导入目录
     */
    private static final String IMPORT_MAP_PATH = SDCARD_PATH + "Download/map_import/";
    /**
     * 地图信息 JSON 文件
     */
    public static final String MAP_INFO_JSON = "mapinfo.json";

    /**
     * 导入地图源文件是否存在且不为空
     */
    public static boolean isImportMapExist(String mapName) {
        File importMapDir = new File(getImportMapFilePath(mapName));
        return importMapDir.exists() && importMapDir.list() != null;
    }

    public static String getImportMapFilePath(String mapName) {
        return IMPORT_MAP_PATH + mapName + File.separator;
    }

    public static boolean isImportMapInfoJsonExists(String mapName) {
        File file = getImportMapInfoJsonFile(mapName);
        return file.exists() && file.length() > 0;
    }

    private static File getImportMapInfoJsonFile(String mapName) {
        String path = getImportMapFilePath(mapName) + MAP_INFO_JSON;
        Log.d(TAG, "getImportMapInfoJsonFile: path=" + path);
        return new File(path);
    }

    /**
     * 获取 mapinfo.json 信息
     */
    public static MapInfo getImportMapInfoFromJsonFile(String mapName) {
        Log.d(TAG, "getImportMapInfoFromJsonFile: " + mapName);
        try {
            MapInfo mapInfo = loadLocalImportMapInfo(mapName);
            if (mapInfo != null) {
                Log.d(TAG, "getImportMapInfoFromJsonFile: mapInfo=" + mapInfo);
                return mapInfo;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static MapInfo loadLocalImportMapInfo(String mapName) {
        File mapInfoFile = getImportMapInfoJsonFile(mapName);
        if (!mapInfoFile.exists() || mapInfoFile.length() <= 0) {
            return null;
        }
        String mapInfoJson = loadFileData2String(mapInfoFile);
        if (TextUtils.isEmpty(mapInfoJson)) {
            return null;
        }
        MapInfo mapInfoBean = null;
        Gson mGson = new Gson();
        try {
            JSONObject jsonObject = new JSONObject(mapInfoJson);
            String info = jsonObject.optString("mapInfo");
            mapInfoBean = mGson.fromJson(info, MapInfo.class);
            String updateTime = jsonObject.optString("version");
            mapInfoBean.setUpdateTime(updateTime);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return mapInfoBean;
    }

    private static String loadFileData2String(File dataFile) {
        try {
            FileInputStream fis = new FileInputStream(dataFile);
            int total = fis.available();
            byte[] dataBytes = new byte[total];
            int len = fis.read(dataBytes);
            if (total == len) {
                return new String(dataBytes);
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取返回点
     * 默认定位点，如果定位点不存在，返回待机点，如果待机点不存在，返回接待点
     *
     * @return 点位信息
     */
    public static Pose getTarget() {
        Context context = ApplicationWrapper.getContext();
        String poseName = context.getString(R.string.positioning_spot);
        Pose pose = SystemApi.getInstance().getSpecialPose(poseName);
        if (pose == null) {
            poseName = context.getString(R.string.stand_by_spot);
            pose = SystemApi.getInstance().getSpecialPose(poseName);
        }
        if (pose == null) {
            poseName = context.getString(R.string.reception_pose);
            pose = SystemApi.getInstance().getSpecialPose(poseName);
        }
        Log.d(TAG, "special poseName " + poseName + " , navigation target : " + pose);
        if (pose != null) {
            pose.setName(poseName);
        }
        return pose;
    }

    /**
     * 获取招财豹返回点
     * @return
     */
    public static Pose getDeliveryTarget() {

        String location = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_SETTING_LOW_BATTERY_NAVI_LOCATION);

        Log.d(TAG, "location: " + location);
        if(TextUtils.isEmpty(location)){
            //未设置返回点,返回默认值
            return getTarget();
        }else{
            if(location == "-1"){
                //不导航，返回null
                return null;
            }else{
                Pose pose = SystemApi.getInstance().getSpecialPose(location);
                return pose;
            }
        }
    }

    /**
     * 云端push地图
     */
    public static void downLoadWholeMapPkg(PushMapBean pushMapInfo, Gson gson, CommandListener listener) {
        downLoadWholeMapPkg(pushMapInfo, true, false, gson, listener);
    }

    /**
     * 小助手导入地图
     * @param hasLocalMap 是否导入地图，（小助手adb push地图包到机器端）
     */
    public static void downLoadWholeMapPkg(PushMapBean pushMapInfo,
                                           boolean overwriteLocalPlace, boolean hasLocalMap,
                                           Gson gson, CommandListener listener) {
        Map<String, Object> map = new HashMap<>();
        map.put(Definition.JSON_MAP_NAME, pushMapInfo.getMapName());
        map.put(Definition.JSON_MAP_URL, pushMapInfo.getMapUrl());
        map.put(Definition.JSON_MAP_UUID, pushMapInfo.getMapUuid());
        map.put(Definition.JSON_MAP_MD5, pushMapInfo.getPgmMd5());
        map.put(Definition.JSON_MAP_UPDATE_TIME, pushMapInfo.getVersion());
        map.put(Definition.JSON_MAP_EXTRA_MD5, pushMapInfo.getExtraMd5());
        map.put(Definition.JSON_MAP_EXTRA_ID, pushMapInfo.getExtraFileId());
        map.put(Definition.JSON_MAP_EXTRA_URL, pushMapInfo.getExtraUrl());
        map.put(Definition.JSON_MAP_SUPPORT_TYPE, pushMapInfo.getMapSupportType());
        map.put(Definition.JSON_MAP_OVERWRITE_LOCAL_PLACE, overwriteLocalPlace);
        map.put(Definition.JSON_MAP_HAS_LOCAL_MAP, hasLocalMap);

        SystemApi.getInstance().downLoadWholeMapPkg(0, gson.toJson(map), listener);
    }

    public static float getStopChargeMoveOffset() {
        float distance = 0.0f;
        if (!ProductInfo.isCarryProduct()) {
            return distance;
        }
        int robotStructureMode = RobotSettingApi.getInstance()
                .getRobotInt("robot_setting_navi_robot_structure_mode");
        float factoryRobotRadiusF = RobotSettingApi.getInstance()
                .getRobotFloat("robot_setting_navi_factory_robot_radius");
        if (robotStructureMode == 1) {
            distance = 0.17f;
        } else if (robotStructureMode == 2) {
            distance = (factoryRobotRadiusF - 0.4f > 0 ? factoryRobotRadiusF - 0.4f  + 0.1f : 0.1f);
        }
        return distance;
    }
}