/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.home.ota.config;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.ota.constants.OtaConstants;
import com.ainirobot.home.ota.network.NetDefine;
import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;

/**
 * "配置项":{
 * "开机升级":true,
 * "强制升级":true,
 * "静默升级": {
 * "是否启用":true,
 * "下载后直接升级":true,
 * "下载后手动升级": false
 * },
 * "PSB升级":true,
 * "AC_CLIENT升级":true,
 * "BMS升级":true,
 * "MOTOR_HORIZON升级":true,
 * "MOTOR_VERTICAL升级":true,
 * "MOTOR_LEFT升级":true,
 * "MOTOR_RIGHT升级":true,
 * "TK1升级":true,
 * "TX1升级":true
 * "server_domain":"https://jiedai.ainirobot.com"
 * "develop_server_domain":"https://test-api.orionbase.cn",
 * "prerelease_server_domain":"https://api.orionbase.cn"
 * }
 */

public class OtaConfig {
    private static final String TAG = OtaConstants.TAG_PREFIX + OtaConfig.class.getSimpleName();
    @SerializedName("开机升级")
    private Boolean mBootUpdateEnabled = true;
    @SerializedName("强制升级")
    private Boolean mForceUpdateEnabled = true;
    @SerializedName("夜间升级")
    private Boolean mNighttimeUpdateEnabled = true;
    @SerializedName("静默升级")
    private SilentUpdate mSilentUpdate = new SilentUpdate();
    @SerializedName("静默下载")
    private boolean mSilentDownloadEnabled = true;
    @SerializedName("PSB升级")
    private boolean mPsbUpdateEnabled = true;
    @SerializedName("PSB_S")
    private boolean mPsbSUpdateEnabled = false;
    @SerializedName("AC_CLIENT升级")
    private boolean mAcClientUpdateEnabled = true;
    @SerializedName("BMS升级")
    private boolean mBmsUpdateEnabled = true;
    @SerializedName("MOTOR_HORIZON升级")
    private boolean mMotorHUpdateEnabled = true;
    @SerializedName("MOTOR_VERTICAL升级")
    private boolean mMotorVUpdateEnabled = true;
    @SerializedName("MOTOR_LEFT升级")
    private boolean mMotorLUpdateEnabled = true;
    @SerializedName("MOTOR_RIGHT升级")
    private boolean mMotorRUpdateEnabled = true;
    @SerializedName("TK1升级")
    private boolean mTk1UpdateEnabled = true;
    @SerializedName("TX1升级")
    private boolean mTx1UpdateEnabled = true;
    @SerializedName("server_domain")
    private String mServerDomain = "";
    @SerializedName("正式服务地址分区")
    private ServerZone[] mServerZone;
    @SerializedName("develop_server_domain")
    private String mDevelopDomain = "";
    @SerializedName("prerelease_server_domain")
    private String mPreReleaseDomain = "";

    private class SilentUpdate {
        @SerializedName("是否启用")
        private Boolean mSilentUpdateEnabled = true;
        @SerializedName("下载后直接升级")
        private Boolean mAutoUpdateEnabled = true;
        @SerializedName("下载后手动升级")
        private Boolean mManualUpdateEnabled = false;
    }

    private class ServerZone {
        @SerializedName("服务器区位")
        private String zone;
        @SerializedName("域名")
        private String mServerDomain;
    }

    public static OtaConfig INSTANCE = new OtaConfig();

    public static OtaConfig getInstance() {
        return INSTANCE;
    }

    public static void copyFrom(OtaConfig config) {
        INSTANCE.mBootUpdateEnabled = config.mBootUpdateEnabled;
        INSTANCE.mForceUpdateEnabled = config.mForceUpdateEnabled;
        INSTANCE.mNighttimeUpdateEnabled = config.mNighttimeUpdateEnabled;
        INSTANCE.mSilentUpdate = config.mSilentUpdate;
        INSTANCE.mPsbUpdateEnabled = config.mPsbUpdateEnabled;
        INSTANCE.mAcClientUpdateEnabled = config.mAcClientUpdateEnabled;
        INSTANCE.mBmsUpdateEnabled = config.mBmsUpdateEnabled;
        INSTANCE.mMotorHUpdateEnabled = config.mMotorHUpdateEnabled;
        INSTANCE.mMotorVUpdateEnabled = config.mMotorVUpdateEnabled;
        INSTANCE.mMotorLUpdateEnabled = config.mMotorLUpdateEnabled;
        INSTANCE.mMotorRUpdateEnabled = config.mMotorRUpdateEnabled;
        INSTANCE.mTk1UpdateEnabled = config.mTk1UpdateEnabled;
        INSTANCE.mTx1UpdateEnabled = config.mTx1UpdateEnabled;
        INSTANCE.mServerDomain = config.mServerDomain;
        INSTANCE.mDevelopDomain = config.mDevelopDomain;
        INSTANCE.mPreReleaseDomain = config.mPreReleaseDomain;
        INSTANCE.mSilentDownloadEnabled = config.mSilentDownloadEnabled;
        INSTANCE.mPsbSUpdateEnabled = config.mPsbSUpdateEnabled;
    }

    public boolean isBootUpdateEnabled() {
        return mBootUpdateEnabled;
    }

    public boolean isForceUpdateEnabled() {
        return mForceUpdateEnabled;
    }

    public boolean isNightimeUpdateEnabled() {
        return mNighttimeUpdateEnabled;
    }

    public boolean isPsbUpdateEnabled() {
        return mPsbUpdateEnabled;
    }

    public boolean isPsbSUpdateEnabled() {
        return mPsbSUpdateEnabled;
    }

    public boolean isAcClientUpdateEnabled() {
        return mAcClientUpdateEnabled;
    }

    public boolean isBmsUpdateEnabled() {
        return mBmsUpdateEnabled;
    }

    public boolean isMotorHUpdateEnabled() {
        return mMotorHUpdateEnabled;
    }

    public boolean isMotorVUpdateEnabled() {
        return mMotorVUpdateEnabled;
    }

    public boolean isMotorLUpdateEnabled() {
        return mMotorLUpdateEnabled;
    }

    public boolean isMotorRUpdateEnabled() {
        return mMotorRUpdateEnabled;
    }

    public boolean isTk1UpdateEnabled() {
        return mTk1UpdateEnabled;
    }

    public boolean isTx1UpdateEnabled() {
        return mTx1UpdateEnabled;
    }

    public boolean isSilentUpdateEnabled() {
        return mSilentUpdate.mSilentUpdateEnabled;
    }

    public boolean isSilentAutoUpdateEnabled() {
        return mSilentUpdate.mAutoUpdateEnabled;
    }

    public boolean isSilentManualUpdateEnabled() {
        return mSilentUpdate.mManualUpdateEnabled;
    }

    public boolean isSilentDownloadEnabled() {
        return mSilentDownloadEnabled;
    }

    public String getServerDomain() {
        int env = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_SYSTEM_ENV);
        Log.d(TAG, "system env: " + env);
        switch (env) {
            case NetDefine.RELEASE:
                return mServerDomain;
            case NetDefine.DEVELOP:
                return mDevelopDomain;
            case NetDefine.PRE_RELEASE:
                return mPreReleaseDomain;
            default:
                return null;
        }
    }

    @Override
    public String toString() {
        return "OtaConfig{" +
                "mBootUpdateEnabled='" + mBootUpdateEnabled + '\'' +
                ", mForceUpdateEnabled='" + mForceUpdateEnabled + '\'' +
                ", mSilentUpdateEnabled='" + mSilentUpdate.mSilentUpdateEnabled + '\'' +
                ", mSilentAutoUpdateEnabled='" + mSilentUpdate.mAutoUpdateEnabled + '\'' +
                ", mSilentManualUpdateEnabled='" + mSilentUpdate.mManualUpdateEnabled + '\'' +
                ", mSilentDownloadEnabled=" + mSilentDownloadEnabled + '\'' +
                ", mPsbUpdateEnabled='" + mPsbUpdateEnabled + '\'' +
                ", mAcClientUpdateEnabled='" + mAcClientUpdateEnabled + '\'' +
                ", mBmsUpdateEnabled='" + mBmsUpdateEnabled + '\'' +
                ", mMotorHUpdateEnabled='" + mMotorHUpdateEnabled + '\'' +
                ", mMotorVUpdateEnabled='" + mMotorVUpdateEnabled + '\'' +
                ", mMotorLUpdateEnabled='" + mMotorLUpdateEnabled + '\'' +
                ", mMotorRUpdateEnabled='" + mMotorRUpdateEnabled + '\'' +
                ", mTk1UpdateEnabled='" + mTk1UpdateEnabled + '\'' +
                ", mTx1UpdateEnabled=" + mTx1UpdateEnabled + '\'' +
                ", mServerDomain=" + mServerDomain + '\'' +
                ", mDevelopDomain=" + mDevelopDomain + '\'' +
                ", mPreReleaseDomain=" + mPreReleaseDomain +
                '}';
    }

    /**
     * 处理正式服务器分区数据
     */
    public void processServerZoneData(){

        String zone = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_SETTINGS_CLOUD_SERVER_ZONE);
        String zoneDefault = Definition.CloudServerZone.DEFAULT.getValue();
        ServerZone serverZone;
        
        Log.d(TAG,"processServerZoneData zone: " + zone);

        if((this.mServerZone != null) && (this.mServerZone.length > 0)){
            serverZone = this.getServerZone(zone);
            if(serverZone == null){
                serverZone = this.getServerZone(zoneDefault);
            }
            Log.d(TAG,"getServerZoneConfig: " + new Gson().toJson(serverZone));
            this.copyReleaseServerZoneToServer(serverZone);
        }

    }

    private void copyReleaseServerZoneToServer(ServerZone serverZone){
        if(serverZone != null){
            this.mServerDomain  = serverZone.mServerDomain;
        }
    }

    /**
     * 根据分区获取服务器分区配置
     * @param zone
     * @return
     */
    private ServerZone getServerZone(String zone){
        if((this.mServerZone != null) && (this.mServerZone.length > 0)){
            for(int i=0;i<this.mServerZone.length;i++){
                if(this.mServerZone[i].zone.equals(zone)){
                    return this.mServerZone[i];
                }
            }
        }
        return null;
    }

}
