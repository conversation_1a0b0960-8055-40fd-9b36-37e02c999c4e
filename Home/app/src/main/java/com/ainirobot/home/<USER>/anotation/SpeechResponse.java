package com.ainirobot.home.bi.anotation;

import android.support.annotation.IntDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * speech response annotation
 *
 * @version V1.0.0
 * @date 2019/4/10 15:50
 */
@Retention(RetentionPolicy.SOURCE)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@IntDef({SpeechResponse.SPEECH_NOT_SUPPORT
        , SpeechResponse.SPEECH_NOT_RESPONSE
        , SpeechResponse.SPEECH_RESPONSE
        , SpeechResponse.SPEECH_TRANSMISSION
        , SpeechResponse.SPEECH_BACKGROUND
        , SpeechResponse.SPEECH_ADD_QUEUE})
public @interface SpeechResponse {
    final int SPEECH_NOT_SUPPORT = 0;
    final int SPEECH_NOT_RESPONSE = 1;
    final int SPEECH_RESPONSE = 2;
    final int SPEECH_TRANSMISSION = 3;
    final int SPEECH_BACKGROUND = 4;
    final int SPEECH_ADD_QUEUE = 5;
}
