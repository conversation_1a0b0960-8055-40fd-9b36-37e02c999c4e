package com.ainirobot.home.ui.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.ui.UIController;

/**
 * 强制恢复出厂设置Fragment
 * 当OTA升级成功后，如果检测到需要强制恢复出厂设置，则显示此Fragment
 */
public class FactoryResetFragment extends BaseFragment implements View.OnClickListener {
    private static final String TAG = "FactoryResetFragment";

    private static final String ACTION_FACTORY_RESET = "android.intent.action.FACTORY_RESET";
    private static final String EXTRA_REASON = "android.intent.extra.REASON";
    private static final String EXTRA_WIPE_EXTERNAL_STORAGE = "android.intent.extra.WIPE_EXTERNAL_STORAGE";
    private static final String EXTRA_WIPE_ESIMS = "com.android.internal.intent.extra.WIPE_ESIMS";

    private TextView mTitleView;
    private TextView mContentView;
    private TextView mReasonView;
    private Button mConfirmButton;

    private boolean mIsMandatory = false;
    private String mResetReason;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.i(TAG, "onCreate");
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        Log.i(TAG, "onCreateView");
        View view = inflater.inflate(R.layout.fragment_factory_reset, container, false);
        initData();
        initViews(view);
        return view;
    }

    @Override
    public void onStart() {
        super.onStart();
        Log.i(TAG, "onStart");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.d(TAG, "onDestroyView");
    }

    private void initData() {
        if (getArguments() != null) {
            mIsMandatory = getArguments().getBoolean(ModuleDef.FACTORY_RESET_MANDATORY, false);
            mResetReason = getArguments().getString(ModuleDef.FACTORY_RESET_REASON);
        }
        Log.i(TAG, "initData: isMandatory=" + mIsMandatory + ", reason=" + mResetReason);
    }

    private void initViews(View view) {
        mTitleView = view.findViewById(R.id.tv_factory_reset_title);
        mContentView = view.findViewById(R.id.tv_factory_reset_content);
        mReasonView = view.findViewById(R.id.tv_factory_reset_reason);
        mConfirmButton = view.findViewById(R.id.btn_factory_reset_confirm);

        mConfirmButton.setOnClickListener(this);

        // 设置标题
        mTitleView.setText(R.string.ota_factory_reset_title);

        // 设置UI内容 - 现在只有确认按钮，始终为强制模式
        mContentView.setText(R.string.ota_factory_reset_mandatory_content);
        mConfirmButton.setText(R.string.ota_factory_reset_mandatory_confirm);

        // 设置原因文本
        if (mResetReason != null && !mResetReason.isEmpty()) {
            mReasonView.setText(mResetReason);
            mReasonView.setVisibility(View.VISIBLE);
        } else {
            mReasonView.setVisibility(View.GONE);
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.btn_factory_reset_confirm) {
            Log.i(TAG, "User confirmed factory reset");
            performFactoryReset();
        }
    }

    /**
     * 执行恢复出厂设置
     */
    private void performFactoryReset() {
        Log.i(TAG, "performFactoryReset: starting factory reset process");

        final Intent intent = new Intent(ACTION_FACTORY_RESET);
        intent.setPackage("android");
        intent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND);
        intent.putExtra(EXTRA_REASON, ApplicationWrapper.getContext().getPackageName() + " OTA Factory Reset");
        intent.putExtra(EXTRA_WIPE_EXTERNAL_STORAGE, true);
        intent.putExtra(EXTRA_WIPE_ESIMS, true);
        ApplicationWrapper.getContext().sendBroadcast(intent);
    }

    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
        // 可以根据需要处理消息
    }
}
