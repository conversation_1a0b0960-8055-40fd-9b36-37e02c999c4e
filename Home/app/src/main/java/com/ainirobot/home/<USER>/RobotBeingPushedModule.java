package com.ainirobot.home.module;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.home.ui.UIController;

public class RobotBeingPushedModule extends BaseModule {
    private static final String TAG = "RobotBeingPushedModule:Home";

    private static RobotBeingPushedModule sInstance = null;

    private Context mContext;

    private int mReqId;

    public static RobotBeingPushedModule getInstance() {
        if (sInstance == null) {
            sInstance = new RobotBeingPushedModule();
        }
        return sInstance;
    }

    private RobotBeingPushedModule() {
    }

    public void init(Context context) {
        mContext = context;
    }

    @SuppressLint("LongLogTag")
    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.d(TAG, "onNewSemantics text = " + text + ", params = " + params + ", intent = " + intent);
        this.mReqId = reqId;
        switch (intent) {
            case Definition.REQ_ROBOT_BEING_PUSHED:
                UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_MAP_OUTSIDE, null, null);
                break;
            case Definition.REQ_ROBOT_BEING_PUSHED_RELEASE:
                stop();
                break;
        }
        return true;
    }

    @Override
    public void onMessageFromLocal(int type) {
    }

    @Override
    protected void onStop() {
        super.onStop();
    }
}
