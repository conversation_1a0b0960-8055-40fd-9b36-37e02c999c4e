package com.ainirobot.home.module;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.ui.UIController;

import java.util.Timer;
import java.util.TimerTask;

public class LoadMapModule extends BaseModule {
    private static final String TAG = LoadMapModule.class.getSimpleName();
    private static final long TEST_TIME = 5_000;
    private Timer mTimer;
    private int mProgress;
    private boolean mIsLoadMapProgressFinished;
    private boolean mIsLoadMapFinished;

    private static class SingletonHolder {
        private static final LoadMapModule mInstance = new LoadMapModule();
    }

    public static LoadMapModule getInstance() {
        return LoadMapModule.SingletonHolder.mInstance;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.i(TAG, "reqId=" + reqId + "; intent=" + intent + "; params=" + params + " " + getState());
        switch (intent) {
            case Definition.REQ_LOAD_MAP:
                showLoadingView();
                loadCurrentMap();
                break;
        }
        return super.onNewSemantics(reqId, intent, text, params);
    }

    private void showLoadingView() {
        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_LOAD_MAP, null, null);
        testProgress();
    }

    private void testProgress() {
        mIsLoadMapProgressFinished = false;
        mIsLoadMapFinished = false;
        mProgress = 0;
        mTimer = new Timer();
        mTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (mProgress < 80 || mIsLoadMapFinished) {
                    mProgress += 2;
                } else {
                    mProgress++;
                }
                if (mProgress < 100) {
                    UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.LOAD_MAP_PROGRESS, String.valueOf(mProgress));
                } else {
                    mTimer.cancel();
                    mIsLoadMapProgressFinished = true;
                    if (mIsLoadMapFinished) {
                        stop();
                    }
                }
            }
        }, 0, 50);
    }

    private void loadCurrentMap() {
        SystemApi.getInstance().loadCurrentMap(0, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                Log.e(TAG, "loadCurrentMap result:" + result + " msg:" + message + " extra:" + extraData);
                if (!isRunning()) {
                    return;
                }
                if (result == Definition.RESULT_OK && TextUtils.equals(message, "succeed")) {
                    mIsLoadMapFinished = true;
                    if (mIsLoadMapProgressFinished) {
                        stop();
                    }
                } else {//加载失败
                    mTimer.cancel();
                    UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.LOAD_MAP_FAILED, null);
                }
            }
        });
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
        if (type == ModuleDef.LOCAL_MESSAGE_SKIP_LOAD_MAP) {
            stop();
        }
    }

    @Override
    protected void onStop() {
        SystemApi.getInstance().onNavigationLoadMapFinished();
        super.onStop();
    }
}
