package com.ainirobot.home.bi;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

/**
 * Charging warning report
 *
 * @version V1.0.0
 * @date 2019/5/07 14:43
 */
public class BiChargingWarningReport extends BaseBiReport {

    private static final String TABLE_NAME = "gb_charging_error";
    private static final String SCENE = "scene";
    private static final String CTIME = "ctime";
    private static final String HEARTBEAT = "heartbeat";
    private static final String UI_PER = "ui_per";
    private static final String BMS_PER = "bms_per";

    public static final int SCENE_TYP_AUTO_CHARGE = -1;
    public static final int SCENE_TYP_DISAPPEAR = 0;
    public static final int SCENE_TYP_POP_UP = 1;
    public static final int SCENE_TYP_CLICK_VIEW_SOLUTION_BUTTON = 2;

    public BiChargingWarningReport() {
        super(TABLE_NAME);
        initData();
    }

    private void initData() {
        addData(SCENE, "");
        addData(HEARTBEAT, "");
        addData(CTIME, "");
        addData(UI_PER, "");
        addData(BMS_PER, "");
    }

    public BiChargingWarningReport addScene(String scene) {
        addData(SCENE, scene);
        return this;
    }

    public BiChargingWarningReport addHeartbeat(String heartbeat) {
        addData(HEARTBEAT, heartbeat);
        return this;
    }

    public BiChargingWarningReport addUIPer(String uiPer) {
        addData(UI_PER, uiPer);
        return this;
    }

    public BiChargingWarningReport addBmsPer(String bmsPer) {
        addData(BMS_PER, bmsPer);
        return this;
    }

    @Override
    public void report() {
        addData(CTIME,System.currentTimeMillis());
        super.report();
    }
}
