package com.ainirobot.home.bi;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;
import com.ainirobot.home.bi.anotation.SettingItem;

/**
 * settings item change report
 *
 * @version V1.0.0
 * @date 2019/4/30 14:43
 */
public class BiSettingsReport extends BaseBiReport {

    private static final String TABLE_NAME = "gb_setting";
    private static final String OPTION = "option";
    private static final String VALUE = "value";
    private static final String CTIME = "ctime";

    public BiSettingsReport() {
        super(TABLE_NAME);
        initData();
    }

    private void initData() {
        addData(OPTION, "");
        addData(VALUE, "");
        addData(CTIME, "");
    }

    public BiSettingsReport addOption(@SettingItem String option) {
        addData(OPTION, option);
        return this;
    }

    public BiSettingsReport addValue(String value) {
        addData(VALUE, value);
        return this;
    }

    @Override
    public void report() {
        addData(CTIME,System.currentTimeMillis());
        super.report();
    }
}
