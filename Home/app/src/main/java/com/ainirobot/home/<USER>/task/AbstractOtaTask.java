package com.ainirobot.home.ota.task;

import static com.ainirobot.home.ota.bean.VersionData.STATUS.NA;
import static com.ainirobot.home.ota.bean.VersionData.STATUS.WAITING;
import static com.ainirobot.home.ota.constants.OtaConstants.CMD_TIMEOUT;
import static com.ainirobot.home.ota.constants.OtaConstants.CO_NET_NODE_ID_AC_CLIENT;
import static com.ainirobot.home.ota.constants.OtaConstants.CO_NET_NODE_ID_BMS;
import static com.ainirobot.home.ota.constants.OtaConstants.CO_NET_NODE_ID_MOTORHEAD_H;
import static com.ainirobot.home.ota.constants.OtaConstants.CO_NET_NODE_ID_MOTORHEAD_V;
import static com.ainirobot.home.ota.constants.OtaConstants.CO_NET_NODE_ID_PSB;
import static com.ainirobot.home.ota.constants.OtaConstants.CO_NET_NODE_ID_PSB_S;
import static com.ainirobot.home.ota.constants.OtaConstants.DOWNLOAD_PATH;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_AC_CLIENT;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_BMS;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_HOST;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_MOTOR_HORIZON;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_MOTOR_LEFT;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_MOTOR_RIGHT;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_MOTOR_VERTICAL;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_PSB;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_PSB_S;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_TK1;
import static com.ainirobot.home.ota.constants.OtaConstants.OS_TX1;
import static com.ainirobot.home.ota.constants.OtaConstants.OTA_INSTALL_PATH;
import static com.ainirobot.home.ota.network.NetDefine.OTA_INSTALL_PROGRESS.OTA_INSTALL_DEFAULT;

import android.content.Context;
import android.os.Handler;
import android.util.Log;

import com.ainirobot.home.ota.bean.DowngradeParams;
import com.ainirobot.home.ota.bean.InstallData;
import com.ainirobot.home.ota.bean.VersionData;
import com.ainirobot.home.ota.config.OtaConfig;
import com.ainirobot.home.ota.constants.DowngradeConstants;
import com.ainirobot.home.ota.constants.OtaConstants;
import com.ainirobot.home.ota.database.DataBaseManager;
import com.ainirobot.home.ota.executor.ThreadPoolManager;
import com.ainirobot.home.ota.httpclient.RequestListener;
import com.ainirobot.home.ota.httpclient.Response;
import com.ainirobot.home.ota.network.NetDefine;
import com.ainirobot.home.ota.network.NetHelper;
import com.ainirobot.home.ota.parser.IParserInterface;
import com.ainirobot.home.ota.service.OtaApiHelper;
import com.ainirobot.home.ota.utils.Preferences;
import com.ainirobot.home.ota.utils.Scp;
import com.ainirobot.home.ota.utils.SettingsUtils;

import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

public abstract class AbstractOtaTask {
    protected final static int OTA_INSTALL_PACKAGE_TIMEOUT = -1;
    private static final String TAG = AbstractOtaTask.class.getSimpleName();
    protected Context mContext;
    protected Preferences mPreference;
    protected NetHelper mNetHelper;
    protected OtaApiHelper mOtaApiHelper;
    protected DataBaseManager dataBaseManager;
    protected DowngradeConstants.InstallStatus mInstallStatus;
    protected Scp scpHelper;
    protected static ConcurrentLinkedQueue<InstallData> installTaskQueue =
            new ConcurrentLinkedQueue<>();
    protected String mInstallErrReason;
    protected boolean installThreadRunning;
    protected HostInstallTask mInstallTask;
    protected Handler mainHandler;
    protected IParserInterface mUpdateConfigParser;
    protected OtaConfig mOtaConfig;
    protected OtaTaskFactory mOtaTaskFactory;
    protected ThreadPoolManager mThreadPoolManager;

    //os update through head algorithm board
    protected InstallData[] downOsList = {
            new InstallData(OS_TX1)
    };
    protected List<InstallData> headOsArray = Arrays.asList(downOsList);
    //os update through can api
    protected InstallData[] canOsList = {
            new InstallData(OS_MOTOR_HORIZON),
            new InstallData(OS_MOTOR_VERTICAL),
            new InstallData(OS_PSB),
            new InstallData(OS_PSB_S),
            new InstallData(OS_AC_CLIENT),
            new InstallData(OS_BMS),
    };
    protected List<InstallData> canOsArray = Arrays.asList(canOsList);
    //os update through navigation board
    protected InstallData[] navOsList = {
            new InstallData(OS_MOTOR_LEFT),
            new InstallData(OS_MOTOR_RIGHT),
            new InstallData(OS_TK1),
    };
    protected List<InstallData> navOsArray = Arrays.asList(navOsList);

    // ota_status listener.
    private RequestListener mOtaStatusListener = new RequestListener() {
        @Override
        public void onSuccess(Response response) {
            Log.d(TAG, "uploadUpdateStatus. response=" + response);
            //Log.d(TAG, "responseString:" + response.getContentString());
        }

        @Override
        public void onFailure(int errCode, String errMsg) {
            Log.d(TAG, "uploadUpdateStatus. " + errCode + " " + errMsg);
        }
    };

    public AbstractOtaTask(Context context, Preferences preferences, NetHelper netHelper,
                           OtaApiHelper otaApiHelper, DataBaseManager dataBaseManager, Scp scpHelper,
                           Handler mainHandler, IParserInterface iParserInterface, OtaTaskFactory otaTaskFactory, OtaConfig otaConfig, ThreadPoolManager threadPoolManager) {
        this.mContext = context;
        this.mPreference = preferences;
        this.mNetHelper = netHelper;
        this.mOtaApiHelper = otaApiHelper;
        this.dataBaseManager = dataBaseManager;
        this.scpHelper = scpHelper;
        this.mainHandler = mainHandler;
        this.mUpdateConfigParser = iParserInterface;
        this.mOtaTaskFactory = otaTaskFactory;
        this.mOtaConfig = otaConfig;
        this.mThreadPoolManager = threadPoolManager;
    }

    protected void uploadUpdateProgress(NetDefine.OTA_TYPE type, NetDefine.OTA_STATUS status,
                                        String desc, NetDefine.OTA_INSTALL_PROGRESS progress) {
        mNetHelper.uploadUpdateStatus(new DowngradeParams(mContext), type,
                mPreference.getServerTargetVersion(), mPreference.getBaseVersion(),
                mPreference.getVersionId(), status, desc, progress, mOtaStatusListener);
    }

    protected void updateOtaUpdateStatus(String osName, VersionData.STATUS status, String currentVersion, String targetVersion) {
        if (osName == null || osName.isEmpty()) {
            Log.d(TAG, "updateOtaUpdateStatus osName is null.");
            return;
        }
        Log.d(TAG, "updateOtaUpdateStatus. osName:" + osName + " status:" + status + ", data:" + (dataBaseManager != null));
        VersionData data = dataBaseManager.getOsData(osName);
        data.setStatus(status);
        data.setCurrentVersion(currentVersion);
        data.setTargetVersion(targetVersion);
        dataBaseManager.update(data);
    }

    protected void uploadUpdateStatus(NetDefine.OTA_TYPE type, NetDefine.OTA_STATUS status,
                                    String desc) {
        mNetHelper.uploadUpdateStatus(new DowngradeParams(mContext), type,
                mPreference.getServerTargetVersion(),
                mPreference.getBaseVersion(), mPreference.getVersionId(), status, desc,
                OTA_INSTALL_DEFAULT, mOtaStatusListener);
    }

    protected void uploadUpdateStatus(NetDefine.OTA_TYPE type, NetDefine.OTA_STATUS status,
                                    String desc, NetDefine.OTA_INSTALL_PROGRESS progress) {
        mNetHelper.uploadUpdateStatus(new DowngradeParams(mContext), type,
                mPreference.getServerTargetVersion(),
                mPreference.getBaseVersion(), mPreference.getVersionId(), status, desc,
                progress, mOtaStatusListener);
    }

    protected void setOtaUpdateFailedReason(String reason, String timeout) {
        mInstallErrReason = timeout == null ? reason : reason + " " + CMD_TIMEOUT;
        mPreference.setUpgradeFailedReason(mInstallErrReason);
    }

    protected void clearInstallTaskQueue() {
        synchronized (installTaskQueue) {
            installTaskQueue.clear();
        }
    }

    protected String installOsToString() {
        StringBuilder b = new StringBuilder();
        boolean isFirstOs = true;

        b.append("Ota {");
        for (InstallData os : installTaskQueue) {
            if (isFirstOs) {
                b.append("'").append(os.getOsName()).append('\'');
                isFirstOs = false;
            } else {
                b.append(", '").append(os.getOsName()).append('\'');
            }
        }
        b.append("}");

        Log.d(TAG, b.toString());
        return b.toString();
    }

    protected boolean clearInstallFile() {
        boolean result = true;
        File dir = new File(OTA_INSTALL_PATH);
        if (dir.isDirectory()) {
            File[] files = dir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (!file.delete()) {
                        result = false;
                        Log.e(TAG, "delete install file " + file.getName() + " failed!");
                    }
                }
            }
            if (!dir.delete()) {
                result = false;
                Log.e(TAG, "delete install file " + dir.getName() + " failed!");
            }
        }
        return result;
    }

    protected void clearDownloadFile() {
        boolean result = true;
        File file = new File(DOWNLOAD_PATH);
        if (file.exists() && !file.delete()) {
            result = false;
        }
        Log.e(TAG, "delete download install file " + file.getAbsolutePath()
                + "/" + file.getName() + " ret:" + result);
    }

    protected String getCanBoardVersionInternal(int board) {
        String result = null;
        switch (board) {
            case CO_NET_NODE_ID_PSB:
                result = mOtaApiHelper.getCanPsbVersion(10 * 1000);
                break;
            case CO_NET_NODE_ID_PSB_S:
                result = mOtaApiHelper.getCanPsbSVersion(10 * 1000);
                break;

            case CO_NET_NODE_ID_MOTORHEAD_H:
                result = mOtaApiHelper.getCanMotorHVersion(10 * 1000);
                break;

            case CO_NET_NODE_ID_MOTORHEAD_V:
                result = mOtaApiHelper.getCanMotorVVersion(10 * 1000);
                break;

            case CO_NET_NODE_ID_AC_CLIENT:
                result = mOtaApiHelper.getCanAcClientVersion(10 * 1000);
                break;

            case CO_NET_NODE_ID_BMS:
                result = mOtaApiHelper.getCanBmsVersion(10 * 1000);
                break;
        }
        return result;
    }

    protected boolean needOsInstall(String osName) {
        return needOsInstallFromOtaConfig(osName) &&
                mPreference.getOsCheckConfig(osName) &&
                (canOsArray.contains(new InstallData(osName)) ||
                        navOsArray.contains(new InstallData(osName)) ||
                        headOsArray.contains(new InstallData(osName)) ||
                        OS_HOST.equals(osName));
    }

    private boolean needOsInstallFromOtaConfig(String osName) {
        switch (osName) {
            case OtaConstants.OS_MOTOR_HORIZON:
                return mOtaConfig.isMotorHUpdateEnabled();
            case OtaConstants.OS_MOTOR_VERTICAL:
                return mOtaConfig.isMotorVUpdateEnabled();
            case OtaConstants.OS_PSB:
                return mOtaConfig.isPsbUpdateEnabled();
            case OtaConstants.OS_PSB_S:
                return mOtaConfig.isPsbSUpdateEnabled();
            case OtaConstants.OS_AC_CLIENT:
                return mOtaConfig.isAcClientUpdateEnabled();
            case OtaConstants.OS_BMS:
                return mOtaConfig.isBmsUpdateEnabled();
            case OtaConstants.OS_MOTOR_LEFT:
                return mOtaConfig.isMotorLUpdateEnabled();
            case OtaConstants.OS_MOTOR_RIGHT:
                return mOtaConfig.isMotorRUpdateEnabled();
            case OtaConstants.OS_TK1:
                return mOtaConfig.isTk1UpdateEnabled();
            case OtaConstants.OS_TX1:
                return mOtaConfig.isTx1UpdateEnabled();
            default:
                break;
        }

        return true;
    }

    protected boolean checkIfWaitForInstalling(VersionData data) {
        return data.getStatus() != NA && data.getStatus() == WAITING;
    }

    protected void clearInstallData() {
        SettingsUtils.setSettingsGlobalOtaInstallingOs(mContext, "");
        mPreference.setOtaIsUpgrading(false);
        clearDataBaseUpdateResult();
        clearInstallFile();
        mPreference.clearPreference();
    }

    private void clearDataBaseUpdateResult() {
        List<VersionData> list = dataBaseManager.getAllVersion();
        for (VersionData data : list) {
            Log.d(TAG, "clear db status. os=" + data.getName());
            data.setStatus(NA);
            data.setTargetVersion(null);
            data.setCurrentVersion(null);
            dataBaseManager.update(data);
        }
    }

    public void setInstallStatus(DowngradeConstants.InstallStatus mInstallStatus) {
        this.mInstallStatus = mInstallStatus;
    }

    public DowngradeConstants.InstallStatus getInstallStatus() {
        return mInstallStatus;
    }

    public void setInstallThreadRunning(boolean installThreadRunning) {
        this.installThreadRunning = installThreadRunning;
    }

    public boolean isInstallThreadRunning() {
        return installThreadRunning;
    }

    protected String getOtaUpdateFailedReason() {
        return mInstallErrReason;
    }
}
