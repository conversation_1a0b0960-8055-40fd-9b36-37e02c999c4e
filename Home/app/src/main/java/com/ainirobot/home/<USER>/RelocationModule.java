package com.ainirobot.home.module;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.ainirobot.coreservice.bean.MultiFloorInfo;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.client.subtype.DeviceSubType;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.control.RadarManager;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.report.Constant;
import com.ainirobot.home.report.TaskReport;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.utils.LocationUtil;
import com.ainirobot.home.utils.ResUtil;
import com.ainirobot.home.utils.ResetHeadUtils;
import com.ainirobot.home.utils.ToastUtil;
import com.google.gson.reflect.TypeToken;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Random;

/**
 * 包含开机重定位功能和非开机重定位流程。
 * 所有产品线都使用统一的定位流程：
 * 开机重定位,先使用最近的定位点定位。
 * 非开机重定位流程：先判断是否有TopIR，有，则判断当前地图，其是否有Target,有则先Targe定位。 无TopIR：是否有充电桩，有：用充电桩，无：定位点定位。
 * <p>
 * 上述都没有启动手动定位.
 */
public class RelocationModule extends BaseModule {

    private static final String TAG = RelocationModule.class.getSimpleName();

    public static final int RELOCATION_TYPE_TARGET = 1;
    private static final int RELOCATION_TYPE_VISION = 2;
    private static final int RELOCATION_TYPE_VISION_TARGET = 3;
    private static final int RELOCATION_TYPE_OTHER = 4;

    private Context mContext = ApplicationWrapper.getContext();
    private Object LOCK = new Object();
    private int mReqId;
    protected Handler mHandler = null;
    private static final int FIX_POSE_PREVIOUS_POINT = 0;
    private static final int TARGET_POSE_QRCODE = 1;
    private static final int FIX_POSE_CHARGE_PILE = 2;
    private static final int FIX_POSE_ANCHOR_POINT = 3;
    private static final int VISION_LOCATE = 4;
    //二维码重定位，点击重试后每次重定位间隔时间 默认1秒
    private static final long RELOCATION_RETRY_INTERVAL = 1000;
    //定位成功后,延时stopModule
    private static final long STOP_MODULE_DELAY_TIME = 2000;
    private volatile Pose mAnchorPose = null;
    private volatile Pose mChargePile = null;
    private volatile int mRetryCnt = 0;
    private RelocationState mRepositeState = RelocationState.IDLE;
    private boolean isBootRelocate = false; // 是否为开机自检后启动的重定位.
    private int mRelocationType = RELOCATION_TYPE_OTHER;
    private String mCurrentMapName;
    private long mLastMapNameUpdateTime;
    private static final long MAP_NAME_CACHE_DURATION = 5000; // 缓存5秒

    private ResetHeadUtils mResetHeadUtils;
    private ArrayList<MultiFloorInfo> mFloorList = new ArrayList<>();

    private RelocationModule() {
    }

    private static class SingletonHolder {
        private static final RelocationModule mInstance = new RelocationModule();
    }

    public static RelocationModule getInstance() {
        return RelocationModule.SingletonHolder.mInstance;
    }

    private enum RelocationState {
        IDLE,
        RADAR_OPENING,
        RADAR_OPENED,
        CHOOSE_LOCATE_TYPE,
        FIX_POSE_START,
        FIX_POSE_END,
        CHARGING_CHECK,
        RELOCATION_START,
        RELOCATION_SUCCESS,
        RELOCATION_END
    }

    public void init(Context context){
        this.mContext = context;
        if (mHandler == null) {
            initHandler();
        }
        LocationUtil.getInstance().preloadLocationInfo();
        mResetHeadUtils = new ResetHeadUtils(TAG, mHandler);
    }

    private void initHandler() {
        this.mHandler = new Handler(Looper.myLooper()) {
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);
                final Bundle bundle = msg.getData();
                Log.d(TAG, "Reposition========msg:" + msg.what);
                switch (msg.what) {
                    case ModuleDef.MSG_REPOSITION_SUCCESS_TIMER:
                        stop();
                        break;
                    case ModuleDef.MSG_REPOSITION_CANCEL:
                        TaskReport.getInstance().setTaskResult(Constant.TaskEvent.task_cancel);
                        stop();
                        break;
                    case ModuleDef.MSG_REPOSITION_RETRY_MSG:
                        startDelayRelocation(msg.arg1);
                        break;
                    default:
                        break;
                }
            }
        };
    }


    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {

        Log.i(TAG, "reqId=" + reqId + "; intent=" + intent + "; params=" + params + " " + getState());
        switch (intent) {
            case Definition.REQ_SPEECH_WAKEUP:
                SystemApi.getInstance().finishModuleParser(reqId, false);
                break;

            case Definition.REQ_REPOSITION:
                if (mRepositeState != RelocationState.IDLE) {
                    finishInviladAction(reqId);
                    return false;
                }
                analyseParam(reqId, params);
                if(ProductInfo.isAlnilamPro()){
                    queryFloorList();
                }else {
                    showLoadingView();
                }
                int isEnabledBootRelocate = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_AUTO_RELOCATE_ENABLED);
                if (isBootRelocate && isEnabledBootRelocate != 0) { // 开机重定位流程,需要先使用之前保存的附近的点尝试Fix定位。
                    startPreviousPointLocate(reqId);
                } else {
                    Log.d(TAG, "check TopIr, isBootRelocate:" + isBootRelocate + "isEnabledBootRelocate:" + isEnabledBootRelocate);
                    nextCheckTopIrLocate();//onNewSemantics
                }
                break;
            case ModuleDef.REQ_STOP:
                TaskReport.getInstance().setTaskResult(Constant.TaskEvent.task_cancel);
                if (mRepositeState == RelocationState.FIX_POSE_END) {
                    SystemApi.getInstance().finishModuleParser(reqId, false);
                    return true;
                }
                updateRepositionState(RelocationState.FIX_POSE_END);
                stop();
                break;
            default:
                break;
        }

        return super.onNewSemantics(reqId, intent, text, params);
    }

    /**
     * 开机重定位时，先使用关机保存点位进行Fix定位.
     *
     * @param reqId
     */
    private void startPreviousPointLocate(int reqId) {
        Pose pose = LocationUtil.getInstance().getPose();
        Log.i(TAG, "tryLocateFromRecordPose normal");
        SystemApi.getInstance().setFixedEstimate(reqId, mGson.toJson(pose), new LocationCommandListener(reqId, FIX_POSE_PREVIOUS_POINT));
    }

    class LocationCommandListener extends CommandListener {

        int reqId;
        int mode;

        LocationCommandListener(int reqId, int mode) {
            this.reqId = reqId;
            this.mode = mode;
        }

        @Override
        public void onResult(int result, String message) {
            Log.d(TAG, "LocationCommandListener result: " + result + " , message = " + message + "," + isRunning());
            if (!isRunning()) {
                return;
            }

            if (result == Definition.RESULT_OK && "succeed".equals(message)) {
                relocateSuccess(mode);
            } else {
                relocateFailed(mode);
            }
            if (mode == FIX_POSE_PREVIOUS_POINT) {
                LocationUtil.getInstance().clearData();
            }
        }
    }


    /**
     * 先选择当前楼层
     */
    private void queryFloorList() {
        SystemApi.getInstance().queryMultiFloorConfig(0, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                Log.d(TAG, "queryMultiFloorConfig onResult msg : " + message + ", extraData : " + extraData);
                if (Definition.RESULT_OK == result && !TextUtils.isEmpty(message)) {
                    TypeToken<ArrayList<MultiFloorInfo>> token = new TypeToken<ArrayList<MultiFloorInfo>>() {
                    };
                    mFloorList = mGson.fromJson(message, token.getType());
                    if (mFloorList == null || mFloorList.size() == 0) {
                        showToast(ResUtil.getString(R.string.elevator_reposition_no_navi_map));
                        stop();
                        return;
                    }
                    showLoadingAlnilamProView();
                } else {
                    showToast(ResUtil.getString(R.string.elevator_reposition_no_navi_map));
                    stop();
                }
            }
        });
    }

    private void showLoadingView() {
        SkillManager.getInstance().closeSpeechAsrRecognize();
        Bundle bundle = new Bundle();
        bundle.putBoolean(ModuleDef.QRCODE_LOADING, true);
        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_REPOSITION, bundle, null);
    }

    /**
     * 显示具体重定位状态页面之前，先显示Loading页面.
     */
    private void showLoadingAlnilamProView() {
        getCurrentMapName(new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                Log.d(TAG, "checkCurrentMap:onResult: result=" + result + " message=" + message );
                if (result == Definition.RESULT_OK) {
                    SkillManager.getInstance().closeSpeechAsrRecognize();
                    Bundle bundle = new Bundle();
                    bundle.putBoolean(ModuleDef.QRCODE_LOADING, true);
                    bundle.putBoolean(ModuleDef.IS_MAP_IN_FLOOR_LIST, isMapInFloorList(message));
                    UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_REPOSITION, bundle, null);
                }
            }
        });
    }


    // 获取地图名称的
    private void getCurrentMapName(CommandListener listener) {
        // 如果缓存存在且在有效期内，直接返回缓存的值
        if (mCurrentMapName != null &&
                (System.currentTimeMillis() - mLastMapNameUpdateTime) < MAP_NAME_CACHE_DURATION) {
            listener.onResult(Definition.RESULT_OK, mCurrentMapName, null);
            return;
        }

        // 否则重新获取并更新缓存
        SystemApi.getInstance().getMapName(mReqId, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                if (result == Definition.RESULT_OK) {
                    mCurrentMapName = message;
                    mLastMapNameUpdateTime = System.currentTimeMillis();
                }
                listener.onResult(result, message, extraData);
            }
        });
    }



    /**
     * 当前用作导航的地图是否在多楼层地图列表中
     * @param mapName
     * @return
     */
    private boolean isMapInFloorList(String mapName) {
        for (MultiFloorInfo info : mFloorList){
            if (info.getMapName().equals(mapName)){
                return true;
            }
        }
        return false;
    }

    private void showToast(final String text) {
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                Toast.makeText(ApplicationWrapper.getContext(),
                        text,
                        Toast.LENGTH_SHORT).show();
            }
        });
    }


    private void analyseParam(int reqId, String params) {
        mReqId = reqId;
        if (!TextUtils.isEmpty(params)) {
            try {
                JSONObject jsonObject = new JSONObject(params);
                isBootRelocate = jsonObject.getBoolean(Definition.REPOSITION_BY_BOOT);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        // Mini 和 招财的硬件都没有视觉定位. 和祝捷沟通，整合版本暂时不考虑视觉定位.
        Log.d(TAG, "analyseParam isBootRelocate : " + isBootRelocate);

    }

    /**
     * 第一步，先判断当前是否有使用中的地图
     * 有则进行下一步，没有则提示用户切换地图（定位流程中没有自动切换地图的逻辑）
     */
    private void checkCurrentMap() {
        Log.d(TAG, "checkCurrentMap: isRunning=" + isRunning());
        getCurrentMapName(new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                Log.d(TAG, "checkCurrentMap:onResult: result=" + result +
                        " message=" + message + " isRunning=" + isRunning());
                if (!isRunning()) {
                    return;
                }
                if (result == Definition.RESULT_OK && !TextUtils.isEmpty(message)) {
                    checkEstimateStatus();
                } else {
                    speakResultTTS(ResUtil.getString(R.string.reposition_please_switch_map));
                    stop();
                }
            }
        });
    }

    /**
     * 第二步，检查定位状态
     */
    private void checkEstimateStatus() {
        Log.d(TAG, "checkEstimateStatus: isRunning=" + isRunning());
        SystemApi.getInstance().isRobotEstimate(mReqId, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                Log.d(TAG, "checkEstimateStatus:onResult: result=" + result +
                        " message=" + message + " isRunning=" + isRunning());
                if (!isRunning()) {
                    return;
                }
                if (result != Definition.RESULT_OK ||
                        TextUtils.isEmpty(message) || !"true".equals(message)) {
                    // 当前依旧是未定位状态
                    openRadar();
                } else {
                    stop();
                }
            }
        });
    }

    private void openRadar() {
        Log.d(TAG, "openRadar: ");
        SkillManager.getInstance().closeSpeechAsrRecognize();
        updateRepositionState(RelocationState.RADAR_OPENING);
        RadarManager.openRadar(new RadarManager.RadarListener() {
            @Override
            public boolean onSucceed() {
                updateRepositionState(RelocationState.RADAR_OPENED);
                Log.d(TAG, "openRadar: isRunning=" + isRunning());
                if (!isRunning()) {
                    return false;
                }
                initChargePilePose();
                return true;
            }
        });
    }

    private void getCurrentMapInfo() {
        SystemApi.getInstance().getMapInfo(mReqId, null, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                Log.i(TAG, "getMapInfo:onResult: result:" + result + " message:" + message
                        + " extraData:" + extraData + "  " + isRunning());
                if (!isRunning()) {
                    return;
                }
                parseMapInfo(message);
            }
        });
    }

    private void parseMapInfo(String message) {
        try {
            boolean hasTopIR = SystemApi.getInstance().hasTopIR();
            boolean hasMono = SystemApi.getInstance().hasMono();
            boolean hasTopMono = SystemApi.getInstance().hasTopMono();
            JSONObject jsonObject = new JSONObject(message);
            boolean mapHasVision = jsonObject.optBoolean("mapHasVision");
            boolean mapHasTarget = jsonObject.optBoolean("mapHasTarget");
            int hasTarget = jsonObject.optInt(Definition.JSON_NAVI_TARGET_DATA_STATE, 0);
            Log.d(TAG, " hasTopIR=" + hasTopIR + " mapHasVision=" + mapHasVision +
                    " mapHasTarget=" + mapHasTarget + " hasTarget=" + hasTarget
                    + " hasMono=" + hasMono + " hasTopMono=" + hasTopMono);
            int mode;
            if ((hasTopIR || hasTopMono) && mapHasTarget && hasTarget == 1) {
                if (mapHasVision && hasMono) {//有Target定位和视觉定位
                    mRelocationType = RELOCATION_TYPE_VISION_TARGET;
                } else {//只有Target定位
                    mRelocationType = RELOCATION_TYPE_TARGET;
                }
                mode = Definition.RelocationMode.TARGET.getValue();
            } else if (mapHasVision && hasMono) {//只有视觉定位
                mRelocationType = RELOCATION_TYPE_VISION;
                mode = Definition.RelocationMode.VISION.getValue();
            } else {//既没有Target定位，也没有视觉定位
                mRelocationType = RELOCATION_TYPE_OTHER;
                startSpecialPointReLocation(false, true);
                return;
            }
            startRelocation(mode);
        } catch (JSONException e) {
            e.printStackTrace();
            Log.d(TAG, "get current map info ERROR .");
            stop();
            finishInviladAction(mReqId);
        }
    }

    private void startRelocation(final int mode) {
        if (mRepositeState != RelocationState.RADAR_OPENED) {
            finishInviladAction(mReqId);
            return;
        }
        updateRepositionState(RelocationState.RELOCATION_START);
        SystemApi.getInstance().setChassisRelocation(mReqId,
                mode,
                null, new CommandListener() {
                    @Override
                    public void onResult(int result, String message, String extraData) {
                        super.onResult(result, message, extraData);
                        Log.i(TAG, "startSilentRelocation result:"
                                + result + " message:" + message + "  " + isRunning());
                        if (!isRunning()) {
                            return;
                        }
                        if (result == Definition.RESULT_OK && "succeed".equals(message)) {
                            updateRepositionState(RelocationState.RELOCATION_END);
                            startRelocationSuccess();
                        } else {
                            startRelocationFailed(mode);//提示推到二维码下面
                        }
                    }

                    @Override
                    public void onError(int errorCode, String errorString, String extraData)
                            throws RemoteException {
                        super.onError(errorCode, errorString, extraData);
                        Log.i(TAG, "starFirstChargePileReposition: errorCode:" + errorCode
                                + " errorString:" + errorString);
                        startRelocationFailed(mode); //提示推到二维码下面
                    }
                });
    }

    private void startSpecialPointReLocation(boolean needBackToQrcodeView, boolean hasTTS) {
        updateRepositionState(RelocationState.CHOOSE_LOCATE_TYPE);
        if (null != mChargePile) {
            if (null != mAnchorPose) {//充电桩，定位点都存在
                startLocationBasedOnChargingType();
            } else {//只有充电桩
                startPileLocation();
            }
        } else if (null != mAnchorPose) {//只有定位点
            startAnchorLocation();
        } else {
            if (hasTTS) {
                speakResultTTS(ResUtil.getString(R.string.charge_need_estimate_first));
            }
            UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.RELOCATE_CHOOSE_TYPE, String.valueOf(needBackToQrcodeView));
        }
    }

    /**
     * 根据充电类型选择定位方式
     */
    private void startLocationBasedOnChargingType() {
        if (LocationUtil.getInstance().isChargingTypePile()) {
            startPileLocation();
        } else {
            startAnchorLocation();
        }
    }

    /**
     * 当前地图是否配置充电桩,
     * 获取当前地图的充电桩点位，如果存在则进行充电桩定位,否则获取定位，走定位点定位
     */
    private void initChargePilePose() {
        SystemApi.getInstance().getLocation(mReqId, Definition.CHARGING_POLE_TYPE, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                Log.i(TAG, "initChargePilePose result:" + result + " message:" + message
                        + "  " + isRunning());
                if (!isRunning()) {
                    return;
                }
                try {
                    JSONObject jsonObject = new JSONObject(message);
                    boolean state = jsonObject.optBoolean(Definition.JSON_NAVI_SITE_EXIST,
                            false);
                    if (state) {
                        if (null == mChargePile) {
                            mChargePile = new Pose();
                        }
                        float x = (float) jsonObject.getDouble(Definition.JSON_NAVI_POSITION_X);
                        float y = (float) jsonObject.getDouble(Definition.JSON_NAVI_POSITION_Y);
                        float theta = (float) jsonObject.getDouble(Definition.JSON_NAVI_POSITION_THETA);
                        mChargePile.setX(x);
                        mChargePile.setY(y);
                        mChargePile.setTheta(theta);
                        mChargePile.setTypeId(jsonObject.getInt(Definition.JSON_NAVI_TYPE_ID));
                        mChargePile.setPriority(jsonObject.getInt(Definition.JSON_NAVI_PRIORITY));
                        Log.d(TAG, "onResult: mChargePile " + mChargePile.toJson());
                    } else {
                        mChargePile = null;
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                    mChargePile = null;
                }
                initAnchorPointPose();
            }
        });
    }


    private void updateRepositionState(RelocationState newState) {
        synchronized (LOCK) {
            Log.d(TAG, "oldstate:" + mRepositeState + ",newstate:" + newState);
            mRepositeState = newState;
        }
    }

    private RelocationState getReceptionState() {
        synchronized (LOCK) {
            return mRepositeState;
        }
    }

    private void finishInviladAction(int reqId) {
        Log.d(TAG, "finish invilad action id:" + reqId);
        SystemApi.getInstance().finishModuleParser(reqId, true);
    }

    private void speakResultTTS(String playTTS) {
        Log.i(TAG, "speakResultTTS:" + playTTS);
        SkillManager.getInstance().speechPlayText(playTTS);
    }

    /**
     * 初始化定位点 并缓存到 mAnchorPose
     */
    private void initAnchorPointPose() {
        SystemApi.getInstance().getLocation(mReqId, Definition.POSITIONING_POINT_TYPE, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                Log.i(TAG, "Get anchor point result:" + result + " message:" + message
                        + "  " + isRunning());
                if (!isRunning()) {
                    return;
                }
                try {
                    JSONObject jsonObject = new JSONObject(message);
                    boolean state = jsonObject.optBoolean(Definition.JSON_NAVI_SITE_EXIST,
                            false);
                    if (state) {
                        if (null == mAnchorPose) {
                            mAnchorPose = new Pose();
                        }
                        float x = (float) jsonObject.getDouble(Definition.JSON_NAVI_POSITION_X);
                        float y = (float) jsonObject.getDouble(Definition.JSON_NAVI_POSITION_Y);
                        float theta = (float) jsonObject.getDouble(Definition.JSON_NAVI_POSITION_THETA);
                        mAnchorPose.setX(x);
                        mAnchorPose.setY(y);
                        mAnchorPose.setTheta(theta);
                        mAnchorPose.setTypeId(jsonObject.getInt(Definition.JSON_NAVI_TYPE_ID));
                        mAnchorPose.setPriority(jsonObject.getInt(Definition.JSON_NAVI_PRIORITY));
                        Log.d(TAG, "onResult: mAnchorPose " + mAnchorPose.toJson());
                        getCurrentMapInfo();
                        return;
                    } else {
                        mAnchorPose = null;
                    }
                } catch (JSONException e) {
                    mAnchorPose = null;
                    e.printStackTrace();
                }
                getCurrentMapInfo();
                Log.d(TAG, "anchor point not exist");
            }
        });
    }

    private void showAnchorPoint() {
        //提示推到二维码下面
        speakResultTTS(mContext.getString(R.string.qrcode_anchor_point_tts));
        UIController.getInstance().sendMessageToFragment(
                UIController.MESSAGE_TYPE.ANCHOR_POINT_REPOSITION_FAILURE,
                "");
    }

    public void stopRelocation() {
        Log.d(TAG, "stopQrCodeRelocation: ");
        if (mHandler != null) {
            mHandler.removeMessages(ModuleDef.MSG_REPOSITION_RETRY_MSG);
        }
        updateRepositionState(RelocationState.RELOCATION_END);
    }

    /**
     * 定位成功
     *
     * @param locateMode
     */
    private void relocateSuccess(int locateMode) {
        Log.i(TAG, "relocateSuccess: locateMode=" + locateMode + " mRepositeState=" + mRepositeState);
        if (mRepositeState == RelocationState.RELOCATION_SUCCESS) {
            return;
        }
        switch (locateMode) {
            case TARGET_POSE_QRCODE:
                speakResultTTS(mContext.getString(R.string.qrcode_reposition_success_tts));
                break;
            case FIX_POSE_PREVIOUS_POINT:
            case FIX_POSE_CHARGE_PILE:
            case FIX_POSE_ANCHOR_POINT:
            case VISION_LOCATE:
                speakResultTTS(mContext.getString(R.string.reposition_reset_success));
                break;
        }
        updateRepositionState(RelocationState.RELOCATION_SUCCESS);
        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.REPOSITION_SUCCEED, "");

        Message msg = Message.obtain();
        msg.what = ModuleDef.MSG_REPOSITION_SUCCESS_TIMER;
        mHandler.sendMessageDelayed(msg, locateMode == TARGET_POSE_QRCODE ? 3000 : STOP_MODULE_DELAY_TIME);
    }

    /**
     * 定位失败
     *
     * @param locateMode 定位类型
     */
    private void relocateFailed(int locateMode) {
        Log.i(TAG, "relocateFailed: locateMode : " + locateMode);
        switch (locateMode) {
            case FIX_POSE_PREVIOUS_POINT:
                nextCheckTopIrLocate();//relocateFailed
                break;
            case FIX_POSE_CHARGE_PILE:
                speakResultTTS(ResUtil.getString(R.string.reposition_fail));
                UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.CHARGE_PILE_LOCATE_FAILURE,
                        Definition.RESULT_ESTIMATE_FAIL_MATCH_ERR);
                naviCmdTimeOutReport();
                break;
            case FIX_POSE_ANCHOR_POINT:
                showAnchorPoint();
                break;
            case TARGET_POSE_QRCODE:
                sendRetryRelocationMsg(Definition.RelocationMode.TARGET.getValue());
                break;
            case VISION_LOCATE:
                sendRetryRelocationMsg(Definition.RelocationMode.VISION.getValue());
                break;
        }
    }

    private void nextCheckTopIrLocate() {
        checkCurrentMap();
    }

    public void startRelocationSuccess() {
        if (!isRunning()) {
            return;
        }
        stop();
    }

    public void startRelocationFailed(int lastMode) {
        if (!isRunning() || getReceptionState() != RelocationState.RELOCATION_START) {
            return;
        }
        int resourceId;
        if (ProductInfo.isDeliveryProduct()|| ProductInfo.isMeissaPlus()) {
            resourceId = new Random().nextInt(2) == 1
                    ? R.string.reposition_qrcode_reminder_tts1
                    : R.string.reposition_qrcode_reminder_tts2;
        } else {
            resourceId = R.string.reposition_qrcode_reminder_tts2;
        }
        speakResultTTS(mContext.getString(resourceId));
        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.TARGET_VISION_GUIDE, String.valueOf(mRelocationType));

        startDelayRelocation(lastMode);
    }

    private void startDelayRelocation() {
        int mode = Definition.RelocationMode.TARGET.getValue();
        if (mRelocationType == RELOCATION_TYPE_VISION) {
            mode = Definition.RelocationMode.VISION.getValue();
        }
        startDelayRelocation(mode);
    }

    private void startDelayRelocation(int lastMode) {
        int newMode = lastMode;
        if (mRelocationType == RELOCATION_TYPE_VISION_TARGET) {
            newMode = lastMode == Definition.RelocationMode.TARGET.getValue() ?
                    Definition.RelocationMode.VISION.getValue() :
                    Definition.RelocationMode.TARGET.getValue();
        }
        SystemApi.getInstance().setChassisRelocation(mReqId, newMode, null,
                new LocationCommandListener(mReqId, newMode == Definition.RelocationMode.TARGET.getValue() ?
                        TARGET_POSE_QRCODE : VISION_LOCATE));
    }

    private void sendRetryRelocationMsg(int lastMode) {
        if (mHandler != null) {
            Message message = mHandler.obtainMessage();
            message.what = ModuleDef.MSG_REPOSITION_RETRY_MSG;
            message.arg1 = lastMode;
            mHandler.sendMessageDelayed(message, RELOCATION_RETRY_INTERVAL);
        } else {
            startDelayRelocation(lastMode);
        }
    }

    /**
     * 开始定位点定位
     */
    public void startAnchorPointFixRelocation() {
        if (null == mAnchorPose) {
            Log.d(TAG, "current anchorPose is null ");
            showAnchorPoint();
            return;
        }
        Log.d(TAG, "startAnchorPointSet: mAnchorPose= " + mAnchorPose.toJson());
        mResetHeadUtils.checkResetHeadState(mReqId, new ResetHeadUtils.ResetHeadListener() {
            @Override
            public void onResetHeadSuccess() {
                SystemApi.getInstance().setChassisRelocation(mReqId,
                        Definition.RelocationMode.FIXED.getValue(),
                        mAnchorPose, new LocationCommandListener(mReqId, FIX_POSE_ANCHOR_POINT));
                //最后再考虑如何执行手动跳转到MapTool, 执行手动重定位逻辑.
            }

            @Override
            public void onResetHeadFailed() {
                //重置头部失败，报错
                speakResultTTS(ResUtil.getString(R.string.reset_head_failed));
                UIController.getInstance().sendMessageToFragment(
                        UIController.MESSAGE_TYPE.RESET_HEAD_FAILURE, "");
                Log.d(TAG, "onResult: reset head failed");
            }
        });
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
        if(!ProductInfo.isAlnilamPro()){
            int enabled = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_ELEVATOR_CONTROL_ENABLED);
            if (enabled == 1) {
                Log.d(TAG, "onMessageFromLocal ROBOT_SETTING_ELEVATOR_CONTROL_ENABLED == 1");
                return;
            }
        }
        Log.d(TAG, "Reposition========type:" + type);
        switch (type) {
            case ModuleDef.LOCAL_MESSAGE_REPOSITION_CANCEL:
                if (mHandler != null) {
                    mHandler.sendEmptyMessage(ModuleDef.MSG_REPOSITION_CANCEL);
                }
                break;
            case ModuleDef.MSG_REPOSITION_QRCODE_REPOSITION_REBOOT:
                Intent intent = new Intent(Intent.ACTION_REBOOT);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                mContext.startActivity(intent);
                break;
            case ModuleDef.MSG_REPOSITION_QRCODE_CHOOSE_OTHER: //选择其他定位方式
                stopRelocation();
                startSpecialPointReLocation(true, false);
                break;
            case ModuleDef.MSG_REPOSITION_QRCODE_ANCHOR_SET:    //开始定位
                startAnchorPointFixRelocation();
                break;
            case ModuleDef.MSG_REPOSITION_QRCODE_REPOSITION://从选择定位页返回
                updateRepositionState(RelocationState.RELOCATION_START);
                startDelayRelocation();
                break;
            case ModuleDef.LOCAL_MESSAGE_CHOOSE_ANCHOR_POINT:
                if (mAnchorPose == null) { // 当前地图不存在定位点
                    speakResultTTS(ResUtil.getString(R.string.relocate_by_anchor_point_not_found));
                    ToastUtil.showToast(mContext, ResUtil.getString(R.string.relocate_by_anchor_point_not_found));
                } else {
                    startAnchorLocation();
                }
                break;
            case ModuleDef.LOCAL_MESSAGE_CHOOSE_CHARGE_PILE:
                if (mChargePile == null) { // 当前地图不存在充电桩
                    speakResultTTS(ResUtil.getString(R.string.relocate_by_charge_pile_not_found));
                    ToastUtil.showToast(mContext, ResUtil.getString(R.string.relocate_by_charge_pile_not_found));
                } else {
                    startPileLocation();
                }
                break;
            case ModuleDef.LOCAL_MESSAGE_CHARGE_START:
                handleBatteryCharging(true);
                break;
            case ModuleDef.LOCAL_MESSAGE_CHARGE_END:
                handleBatteryCharging(false);
                break;
            case ModuleDef.LOCAL_MESSAGE_CHARGE_PILE_LOCATING:// 充电桩定位中
                chargePileLocating(mChargePile);
                break;
            default:
                break;
        }

    }

    private void startPileLocation() {
        updateRepositionState(RelocationState.CHARGING_CHECK);
        speakResultTTS(ResUtil.getString(R.string.reposition_reset_remind_msg));
        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.RELOCATE_CHOOSE_CHARGE_PILE,"");
        if (ControlManager.getControlManager().getSystemStatusManager().getIsCharging()){
            handleBatteryCharging(true);
        }
    }

    private void startAnchorLocation() {
        updateRepositionState(RelocationState.FIX_POSE_START);
        speakResultTTS(ResUtil.getString(R.string.reposition_reset_remind_msg_locate));
        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.RELOCATE_CHOOSE_ANCHOR_POINT,"");
    }

    /**
     * 接收带参数 param 的fragment消息
     *
     * @param type
     * @param param
     */
    @Override
    public void onMessageFromLocal(int type, Object param) {
        super.onMessageFromLocal(type);
        if(!ProductInfo.isAlnilamPro()) {
            int enabled = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_ELEVATOR_CONTROL_ENABLED);
            if (enabled == 1) {
                Log.d(TAG, "onMessageFromLocal ROBOT_SETTING_ELEVATOR_CONTROL_ENABLED == 1");
                return;
            }
        }
        Log.d(TAG, "Reposition========type:" + type);
        if (type == ModuleDef.LOCAL_MESSAGE_TO_CHOOSE_LOCATE_TYPE) {
            if (mHandler != null) mHandler.removeMessages(ModuleDef.MSG_REPOSITION_RETRY_MSG);
            startSpecialPointReLocation((boolean) param, false);
        }
    }

    private void handleBatteryCharging(boolean isBatteryCharging) {
        if (mRepositeState == RelocationState.CHARGING_CHECK) {
            UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.REPOSITION_CHARGING_CHECK, isBatteryCharging + "");
        }
    }

    private void chargePileLocating(final Pose chargingPilePose) {
        if (!isRunning() || chargingPilePose == null) {
            Log.d(TAG, "not running || chargingPilePose is null");
            TaskReport.getInstance().setTaskResult(Constant.TaskEvent.task_fail);
            speakResultTTS(ResUtil.getString(R.string.elevator_reposition_charge_pile_point_not_exist));
            UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.CHARGE_PILE_LOCATE_FAILURE,
                    Definition.RESULT_ESTIMATE_FAIL_MATCH_ERR);
            return;
        }

        RadarManager.openRadar(new RadarManager.RadarListener() {
            @Override
            public boolean onSucceed() {
                startChargePileFixLocation(chargingPilePose);
                return true;
            }
        });
    }

    public void startChargePileFixLocation(Pose chargingPilePose) {
        Log.d(TAG, "startChargePileFixLocation  chargingPilePose= " + chargingPilePose.toJson());
        SystemApi.getInstance().setChassisRelocation(mReqId,
                Definition.RelocationMode.FIXED.getValue(),
                chargingPilePose, new LocationCommandListener(mReqId, FIX_POSE_CHARGE_PILE));
    }


    @Override
    protected void onStop() {
        UIController.getInstance().sendMessageToFragment(UIController.MESSAGE_TYPE.REPOSITION_EXIT,
                "");
        mAnchorPose = null;
        mChargePile = null;
        updateRepositionState(RelocationState.IDLE);
        SkillManager.getInstance().openSpeechAsrRecognize();
        Log.d(TAG, "onStop: mRepositeState " + mRepositeState);
        SystemApi.getInstance().onRepositionFinished();
        release(mReqId, RESULT_OK, null);
        if (mResetHeadUtils != null) {
            mResetHeadUtils.stop();
        }
    }

    @Override
    protected void release(int reqId, int result, Bundle data) {
        Log.i(TAG, "release reqId:" + reqId);
        updateRepositionState(RelocationState.IDLE);

        if (mHandler != null) {
            mHandler.removeMessages(ModuleDef.MSG_REPOSITION_SUCCESS_TIMER);
            mHandler.removeMessages(ModuleDef.MSG_REPOSITION_CANCEL);
            mHandler.removeMessages(ModuleDef.MSG_REPOSITION_RETRY_MSG);
        }
        SystemApi.getInstance().finishModuleParser(reqId, RESULT_FAILURE != result);
        super.release(reqId, result, data);
    }

    private void naviCmdTimeOutReport() {
        Log.d(TAG, "nviCmdTimeOutReport");
        long timestamp = System.currentTimeMillis();
        String cacheId = RobotSettings.getSystemSn() + "-" + timestamp;
        SystemApi.getInstance().naviTimeOutCmdReport(0, timestamp, cacheId,
                Definition.TYPE_ACTION_CHARGE_PILE_ESTIMATE_FAILUE, "ChargePileEstimateFail", new CommandListener() {
                    @Override
                    public void onResult(int result, String message) {
                        Log.i(TAG, "nviCmdTimeOutReport: " + result + " message:" + message);
                    }
                });
    }
}
