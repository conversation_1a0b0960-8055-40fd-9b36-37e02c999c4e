package com.ainirobot.home.module;

import android.media.MediaPlayer;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.ActionListener;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.R;
import com.ainirobot.home.bi.RemoteControlReport;
import com.ainirobot.home.floatdialog.FloatDialogManager;
import com.ainirobot.home.report.Constant;
import com.ainirobot.home.report.TaskReport;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.utils.LocationUtil;
import com.ainirobot.home.utils.SystemUtils;

import org.json.JSONException;
import org.json.JSONObject;

public class RemoteControlModule extends BaseModule {

    private static final String TAG = "RemoteControlModule";
    private final static int EVENT_GO_POSE = 0x1;
    private final static int EVENT_GO_POSITION = 0x2;
    private final static int EVENT_ARRIVED = 0x3;
    private final static int EVENT_STOP_SOUND = 0x4;

    private MediaPlayer mMediaPlayer;
    private int mReqID = 0;
    private volatile boolean isRunning = false;
    private String reqType;
    private Pose nextPose;
    private String placeName;

    private static RemoteControlModule mInstance = new RemoteControlModule();

    private RemoteControlModule() {
    }

    public static RemoteControlModule getInstance() {
        return mInstance;
    }

    private final Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            Log.d(TAG, "msg: " + msg);
            String params = (String) msg.obj;
            switch (msg.what) {
                case EVENT_GO_POSE:
                    Pose pose1 = new Pose();
                    try {
                        JSONObject object = new JSONObject(params);
                        pose1.setX(Float.parseFloat(object.optString("axis_x")));
                        pose1.setY(Float.parseFloat(object.optString("axis_y")));
                        float mTheta = 0.0f;
                        Pose currentPose = SystemApi.getInstance().getCurrentPose();
                        if (currentPose != null) {
                            mTheta = currentPose.getTheta();
                        }
                        pose1.setTheta(mTheta);
                    } catch (JSONException e) {
                        e.printStackTrace();
                        stop();
                    }
                    playNavigationSound();
                    goPosition(mReqID, pose1);
                    break;
                case EVENT_GO_POSITION:
                    if (TextUtils.isEmpty(params)) {
                        sendRemoteResponse(reqType, -100, "system error: param error");
                        stop();
                    }
                    String name = params;
                    double speed = 0;
                    try {
                        JSONObject param = new JSONObject(params);
                        name = param.optString("name", params);
                        speed = param.optDouble("speed", 0);
                    } catch (JSONException e) {
                        Log.e(TAG, "goPosition parse params error: " + e.getMessage());
                    }
                    Pose pose = getPose(name);
                    Log.i(TAG, "pose: " + pose);
                    if (pose != null) {
                        playNavigationSound();
                        goPosition(mReqID, pose,
                            speed != 0 ? speed : SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED);
                    } else {
                        sendRemoteResponse(reqType, -7, "destination no exist");
                        stop();
                    }
                    break;
                case EVENT_ARRIVED:
                case EVENT_STOP_SOUND:
                    stopNavigationSound();
                    break;
                default:
                    break;
            }

        }
    };

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.i(TAG, "onNewRequest, intent: " + intent + " text: " + text + " params:" + params);
        FloatDialogManager.getInstance().showRemoteControlDailog(ApplicationWrapper.getContext()
                .getString(R.string.remote_control));
        mReqID = reqId;
        reqType = intent;
        Message msg;
        switch (intent) {
            case Definition.REQ_SYSTEM_GO_POSITION://开始一键导航
                mHandler.removeMessages(EVENT_GO_POSITION);
                mHandler.removeMessages(EVENT_GO_POSE);
                msg = new Message();
                msg.what = EVENT_GO_POSITION;
                msg.obj = params;
                mHandler.sendMessageDelayed(msg, 100);
                break;

            case Definition.REQ_SYSTEM_GO_POSE:
                mHandler.removeMessages(EVENT_GO_POSITION);
                mHandler.removeMessages(EVENT_GO_POSE);
                msg = new Message();
                msg.what = EVENT_GO_POSE;
                msg.obj = params;
                mHandler.sendMessageDelayed(msg, 100);
                break;

            case Definition.REQ_SYSTEM_STOP_NAVIGATION://取消一键导航
                mHandler.removeMessages(EVENT_GO_POSITION);
                mHandler.removeMessages(EVENT_GO_POSE);
                new RemoteControlReport()
                        .addAction(0)
                        .addMsg(0)
                        .report();
                this.stop();
                break;

            default:
                break;
        }
        return true;
    }

    private Pose getPose(String placeName) {
        return SystemApi.getInstance().getSpecialPose(placeName);
    }

    private void goPosition(int reqId, Pose pose) {
        Log.i(TAG, "go pose, :" + pose.toString() + " ;robot running status: " + isRunning);
        goPosition(reqId, pose, SettingsUtil.ROBOT_SETTING_DEFAULT_LINEAR_SPEED);
    }

    private void goPosition(int reqId, Pose pose, double linearSpeed) {
        Log.i(TAG, "go pose, :" + pose.toString() + " ;robot running status: " + isRunning + " linearSpeed: " + linearSpeed);
        if (isRunning) {
            nextPose = pose;
            SystemApi.getInstance().stopPoseNavigation(reqId);
        } else {
            int taskPriority = Definition.NavigationPriority.PRIORITY_VIDEO_CALL_NAVIGATION.getPriority();
            SystemApi.getInstance().startPoseNavigation(reqId, pose, 0.5, 20 * 1000,
                linearSpeed, SettingsUtil.ROBOT_SETTING_DEFAULT_ANGULAR_SPEED,
                5, taskPriority, mNaviActionListener);
        }
        placeName = pose.getName();
        isRunning = true;
    }

    private void stopGoPosition() {
        SystemApi.getInstance().stopPoseNavigation(mReqID);
    }

    @Override
    protected void onFinish() {
        mHandler.removeMessages(EVENT_STOP_SOUND);
        mHandler.sendEmptyMessage(EVENT_STOP_SOUND);
        super.onFinish();
    }

    @Override
    protected void onStop() {
        Log.i(TAG, "onstop....");
        if (isRunning) {
            sendRemoteResponse(reqType, -9, "navigation is interrupt by other task");
        }
        stopGoPosition();
        FloatDialogManager.getInstance().removeAll();
        SystemApi.getInstance().onRemoteControlFinished();
        mReqID = 0;
        isRunning = false;
        reqType = null;
        super.onStop();
    }

    private ActionListener mNaviActionListener = new ActionListener() {
        @Override
        public void onResult(int status, String responseString, String extraData) {
            Log.i(TAG, "onResult, status:" + status
                    + ", responseString: " + responseString + ", isRunning: " + isRunning);
            if (isRunning) {
                isRunning = false;
            } else {
                return;
            }
            switch (status) {
                case Definition.RESULT_OK://一键导航成功
                    if (Definition.NAVIGATION_OK.equals(responseString)){
                        sendRemoteResponse(reqType, 0, null);
                        new RemoteControlReport()
                                .addAction(1)
                                .addMsg(1)
                                .report();
                    }else {
                        sendRemoteResponse(reqType, -2, "navigation failure");
                    }
                    stop();
                    break;
                case Definition.ACTION_RESPONSE_STOP_SUCCESS:
                    if (nextPose != null) {
                        Pose pose = nextPose;
                        nextPose = null;
                        goPosition(mReqID, pose);
                    }
                    break;
                case Definition.RESULT_FAILURE:
                case Definition.RESULT_STOP:
                default:
                    sendRemoteResponse(reqType, -2, "navigation failure");
                    stop();
                    break;
            }
        }

        @Override
        public void onStatusUpdate(int status, String data, String extraData) {
            Log.i(TAG, "onStatusUpdate, status: " + status + " , data:" + data);
            if (!isRunning) {
                return;
            }
            switch (status) {
                case Definition.STATUS_NAVI_OUT_MAP:
                    isRunning = false;
                    sendRemoteResponse(reqType, -3, "target point out of map");
                    stop();
                    break;
                case Definition.STATUS_NAVI_GLOBAL_PATH_FAILED:
                    isRunning = false;
                    sendRemoteResponse(reqType, -4, "global path plan failed");
                    stop();
                    break;
                case Definition.STATUS_GOAL_OCCLUDED:
                case Definition.STATUS_NAVI_AVOID:
                case Definition.STATUS_GOAL_OCCLUDED_END:
                case Definition.STATUS_NAVI_AVOID_END:
                case Definition.STATUS_NAVI_OBSTACLES_AVOID:
                    break;
                case Definition.STATUS_START_NAVIGATION:
                    Bundle bundle = new Bundle();
                    bundle.putString("name", placeName);
                    UIController.getInstance().showFragment(
                            UIController.FRAGMENT_TYPE.FRAGMENT_REMOTE_CONTROL, bundle, null);
                    break;
                default:
                    break;
            }
        }

        @Override
        public void onError(int errorCode, String errorString, String extraData) {
            Log.i(TAG, "onError, errorCode:" + errorCode
                    + ", errorString: " + errorString + ", isRunning: " + isRunning);
            new RemoteControlReport()
                    .addAction(1)
                    .addMsg(errorCode)
                    .report();
            if (isRunning) {
                isRunning = false;
            } else {
                return;
            }
            switch (errorCode) {
                case Definition.ERROR_NOT_ESTIMATE:
                    sendRemoteResponse(reqType, -5, "robot is not estimate");
                    break;
                case Definition.ERROR_IN_DESTINATION:
                    sendRemoteResponse(reqType, -6, "robot already in destination");
                    break;
                case Definition.ERROR_DESTINATION_NOT_EXIST:
                    sendRemoteResponse(reqType, -7, "destination no exist");
                    break;
                case Definition.ERROR_DESTINATION_CAN_NOT_ARRAIVE:
                    sendRemoteResponse(reqType, -8, "destination can not arrive");
                    break;
                case Definition.ERROR_NAVIGATION_FAILED:
                    sendRemoteResponse(reqType, -9, "remote chassis is error");
                    break;
                case Definition.ACTION_RESPONSE_ALREADY_RUN:
                    sendRemoteResponse(reqType, -100, "system error: action already run");
                    break;
                case Definition.ACTION_RESPONSE_REQUEST_RES_ERROR:
                    sendRemoteResponse(reqType, -100, "system error: action request resource error");
                    break;
            }
            isRunning = false;
            mHandler.sendEmptyMessage(EVENT_STOP_SOUND);
            stop();
        }
    };

    private void sendRemoteResponse(String reqType, int errorCode, String errorMsg) {
        if (TextUtils.isEmpty(reqType)) {
            return;
        }
        JSONObject object = new JSONObject();
        try {
            object.put("type", reqType);
            object.put("result", errorCode);
            object.put("errmsg", errorMsg);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        Log.i(TAG, "send remote response, data: " + object.toString());
        SystemApi.getInstance().sendStatusReport(Definition.STATUS_REMOTE_NAVI, object.toString());
    }

    private void playNavigationSound() {
        if (!LocationUtil.getInstance().isNeedPlaySound()) {
            Log.d(TAG, "switch isNeedPlaySound not open");
            return;
        }
        if (mMediaPlayer != null) {
            if (mMediaPlayer.isPlaying()) {
                mMediaPlayer.stop();
            }
            mMediaPlayer.release();
            mMediaPlayer = null;
        }
        Log.i(TAG, "play navigation sound");
        mMediaPlayer = MediaPlayer.create(ApplicationWrapper.getContext(), R.raw.piano);
        mMediaPlayer.setLooping(true);
        mMediaPlayer.start();
    }

    private void stopNavigationSound() {
        try {
            if (mMediaPlayer != null) {
                if (mMediaPlayer.isPlaying()) {
                    mMediaPlayer.stop();
                }
                mMediaPlayer.release();
                mMediaPlayer = null;
            }
        } catch (IllegalStateException e) {
            e.printStackTrace();
        }
    }
}
