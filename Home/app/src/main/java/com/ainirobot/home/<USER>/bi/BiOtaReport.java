package com.ainirobot.home.ota.bi;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;
import com.ainirobot.home.ota.constants.OtaConstants;
import com.ainirobot.home.ota.utils.Preferences;
import com.ainirobot.home.ota.utils.Utils;

/**
 * ota bi report
 *
 */
public class BiOtaReport extends BaseBiReport {
    private final static String TAG = OtaConstants.TAG_PREFIX + BiOtaReport.class.getSimpleName();
    private static final String TABLE_NAME = "gb_ota";
    private static final String TYPE = "type";
    private static final String RESULT = "result";
    private static final String OLD_VERSION = "old_version";
    private static final String RES_VERSION = "res_version";
    private static final String TARGET_VERSION = "target_version";
    private static final String UI_PER = "ui_per";
    private static final String FAILED_REASON = "failed_reason";
    private static final String CTIME = "ctime";

    public static final String TYPE_SETTING = "setting"; //点击UI进行升级
    public static final String TYPE_AUTO = "auto"; //夜间自动升级
    public static final String TYPE_MANUAL = "manual";//手动选择安装包升级
    public static final String TYPE_FOCUS = "focus";//强制升级
    public static final String TYPE_SILENT = "silent";//静默升级
    public static final String TYPE_POWER_ON = "poweron";//开机升级

    public static final String RESULT_SUCCESS = "success"; //升级成功
    public static final String RESULT_FAILED = "failed"; //升级失败
    public static final String RESULT_ROLLBACK_SUCCESS = "rollback_success";//回滚成功
    public static final String RESULT_ROLLBACK_FAILED = "rollback_failed";//回滚失败
    public static final String RESULT_BATTERY_LESS = "battery_less"; //由于电量不足无法升级

    private Preferences mPrefs;
    private String mResult;
    private String mType;

    public BiOtaReport() {
        super(TABLE_NAME);
        mPrefs = Preferences.getInstance();
        initData();
    }

    private void initData() {
        addData(TYPE, "");
        addData(RESULT, "");
        addData(OLD_VERSION, "");
        addData(RES_VERSION, "");
        addData(TARGET_VERSION, "");
        addData(UI_PER, "");
        addData(FAILED_REASON, "");
    }

    public BiOtaReport addType(String type) {
        addData(TYPE, type);
        return this;
    }

    public BiOtaReport addResult(String result) {
        mResult = getBiResult(result);
        addData(RESULT, mResult);
        return this;
    }

    public BiOtaReport addOldVersion(String old_version) {
        addData(OLD_VERSION, old_version);
        return this;
    }

    public BiOtaReport addResVersion(String res_version) {
        addData(RES_VERSION, res_version);
        return this;
    }

    public BiOtaReport addTargetVersion(String target_version) {
        addData(TARGET_VERSION, target_version);
        return this;
    }

    public BiOtaReport addFailedReason(String failed_reason) {
        addData(FAILED_REASON, failed_reason);
        return this;
    }

    public BiOtaReport addUiPer(int ui_per) {
        addData(UI_PER, ui_per);
        return this;
    }

    public BiOtaReport addCTime() {
        addData(CTIME, System.currentTimeMillis());
        return this;
    }

    private boolean isValidType(String type) {
        return TYPE_SETTING.equals(type) ||
                TYPE_AUTO.equals(type) ||
                TYPE_MANUAL.equals(type) ||
                TYPE_SILENT.equals(type) ||
                TYPE_POWER_ON.equals(type) ||
                TYPE_FOCUS.equals(type);
    }

    private boolean isValidResult(String result) {
        return RESULT_SUCCESS.equals(result) ||
                RESULT_FAILED.equals(result) ||
                RESULT_ROLLBACK_SUCCESS.equals(result) ||
                RESULT_ROLLBACK_FAILED.equals(result)||
                RESULT_BATTERY_LESS.equals(result);
    }

    private boolean isValidReport() {
        return isValidType(mType) && isValidResult(mResult);
    }

    @Override
    public void report() {
        mType = getBiType();

        if (!isValidReport()) {
            Log.d(TAG, "No need report. type: " + mType
                    + ", result: " + mResult);
            return ;
        }

        addCTime();
        addType(mType);
        addOldVersion(mPrefs.getBaseVersion());
        addTargetVersion(mPrefs.getServerTargetVersion());
        addResVersion(Utils.getSystemProperties("ro.product.releasenum", ""));
        super.report();
    }

    private String getBiType() {
        String startingMode = Preferences.getInstance().getPrefsStartingMode();

        if (startingMode == null || startingMode.isEmpty()) {
            Log.d(TAG, "no starting_mode");
            return null;
        }

        switch (startingMode) {
            case OtaConstants.STARTING_MODE_NIGHTTIME:
            case OtaConstants.STARTING_MODE_REMOTE:
                startingMode = TYPE_AUTO;
                break;

            case OtaConstants.STARTING_MODE_FORCE:
            case OtaConstants.STARTING_MODE_SETTING_FORCE:
                startingMode = TYPE_FOCUS;
                break;

            case OtaConstants.STARTING_MODE_SETTING:
                startingMode = TYPE_SETTING;
                break;

            case OtaConstants.STARTING_MODE_SETTING_MANUAL:
                startingMode = TYPE_MANUAL;
                break;

            case OtaConstants.STARTING_MODE_SILENT:
                startingMode = TYPE_SILENT;
                break;

            case OtaConstants.STARTING_MODE_POWER_ON:
                startingMode = TYPE_POWER_ON;
                break;

            default:
                break;
        }

        Log.d(TAG, "starting mode: " + startingMode);
        return startingMode;
    }

    private String getBiResult(String result) {
        String biResult = result;
        boolean isRollback = mPrefs.getPrefsRollback();

        Log.d(TAG, "ota result: " + biResult + ", isRollback: " + isRollback);
        if (Definition.JSON_OTA_RESULT_SUCCESS.equals(result)) {
            if (isRollback) {
                biResult = RESULT_ROLLBACK_SUCCESS;
            } else {
                biResult = RESULT_SUCCESS;
            }
        } else if (Definition.JSON_OTA_RESULT_FAILED.equals(result)){
            if (isRollback) {
                biResult = RESULT_ROLLBACK_FAILED;
            } else {
                biResult = RESULT_FAILED;
            }
        }

        Log.d(TAG, "bi result: " + biResult);
        return biResult;
    }
}
