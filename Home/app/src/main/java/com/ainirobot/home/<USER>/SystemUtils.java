/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.home.utils;

import android.app.ActivityManager;
import android.app.ActivityManager.RunningServiceInfo;
import android.app.ActivityManager.RunningTaskInfo;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.inputmethod.InputMethodInfo;
import android.view.inputmethod.InputMethodManager;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.LaunchActivity;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.lang.reflect.Array;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Properties;

public class SystemUtils {
    private static final String TAG = "SystemUtils:Home";

    private static String serialNum = "";
    private static final String DEVICE_PROPERTIES = "device.properties";
    private static final String DEVICE_CONFIG_DIR = "/persist/orionoem/";
    private static final String sSystemImei = RobotSettings.getIMEI(ApplicationWrapper.getContext());

    public static String getSystemSerialNo() {
        if (!TextUtils.isEmpty(serialNum)) {
            return serialNum;
        }
        serialNum = RobotSettings.getSystemSn();
        return serialNum;
    }

    public static String getLast6SystemSerialNo() {
        if (TextUtils.isEmpty(getSystemSerialNo())) {
            return "";
        } else if (serialNum.length() <= 6) {
            return serialNum;
        } else {
            return serialNum.substring(serialNum.length() - 6);
        }
    }

    /**
     * 包名判断是否为主进程
     */
    public static boolean isMainProcess(Context context) {
        return context.getPackageName().equals(getProcessName(context));
    }

    /**
     * 获取进程名称
     */
    public static String getProcessName(Context context) {
        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningAppProcessInfo> runningApps = am.getRunningAppProcesses();
        if (runningApps == null) {
            return null;
        }
        for (ActivityManager.RunningAppProcessInfo proInfo : runningApps) {
            if (proInfo.pid == android.os.Process.myPid()) {
                if (proInfo.processName != null) {
                    return proInfo.processName;
                }
            }
        }
        return null;
    }

    public static boolean isServiceRunning(Class cls, Context context) {
        if (cls == null || TextUtils.isEmpty(cls.getName())) {
            return false;
        }
        ActivityManager manager = (ActivityManager) context
                .getSystemService(Context.ACTIVITY_SERVICE);
        try {
            List<RunningServiceInfo> list = manager
                    .getRunningServices(1000);
            if (list != null && list.size() > 0) {
                for (RunningServiceInfo info : list) {
                    if (cls.getName().equals(info.service.getClassName())) {
                        return true;
                    }
                }
            }
        } catch (SecurityException e) {
            e.printStackTrace();
        }
        return false;
    }

    public static boolean isActivityRunning(Class cls, Context context) {
        if (cls == null || TextUtils.isEmpty(cls.getName())) {
            return false;
        }
        ActivityManager manager = (ActivityManager) context
                .getSystemService(Context.ACTIVITY_SERVICE);
        try {
            List<RunningTaskInfo> list = manager
                    .getRunningTasks(100);

            for (RunningTaskInfo info : list) {
                if (cls.getName().equals(info.baseActivity.getClassName())) {
                    return true;
                }
            }
        } catch (SecurityException e) {
            e.printStackTrace();
        }
        return false;
    }

    public static ComponentName getActivityTop(Context context) {
        ActivityManager manager = (ActivityManager) context
                .getSystemService(Context.ACTIVITY_SERVICE);
        try {
            List<ActivityManager.RunningTaskInfo> list = manager
                    .getRunningTasks(1);
            if (list != null && list.size() > 0) {
                return list.get(0).topActivity;
            }
        } catch (SecurityException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static ComponentName getActivityRecent(Context context, String pkg) {
        ActivityManager manager = (ActivityManager) context
                .getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningTaskInfo> recents = manager.getRunningTasks(5);
        if (recents == null || recents.isEmpty()) {
            return null;
        }

        ComponentName top = recents.get(0).topActivity;
        Log.d(TAG, "Get activity recent top : " + top + "   " + pkg);
        if (top == null) {
            return null;
        }

        if (recents.get(0).numActivities > 1) {
            return top;
        }

        Log.d(TAG, "Get activity recent top not null : " + recents.size());
        if (pkg.equals(top.getPackageName())) {
            return top;
        }

        if (recents.size() > 1) {
            return recents.get(1).topActivity;
        }

        return top;
    }

    public static boolean isInRecent(Context context, String pkg) {
        ActivityManager manager = (ActivityManager) context
                .getSystemService(Context.ACTIVITY_SERVICE);
        List<RunningTaskInfo> taskInfos = manager.getRunningTasks(10);

        if (taskInfos == null || taskInfos.isEmpty()) {
            return false;
        }

        for (RunningTaskInfo taskInfo : taskInfos) {
            ComponentName base = taskInfo.topActivity;
            Log.d(TAG, "Running task : " + (base != null ? base.getPackageName()
                    : "base is null"));
            if (base != null && base.getPackageName().equals(pkg)) {
                return true;
            }
        }
        return false;
    }

    public static void setDefaultLauncher() {
        Context context = ApplicationWrapper.getContext();

        PackageManager pm = context.getPackageManager();

        String examplePackageName = context.getPackageName(); // package name
        String exampleActivityName = LaunchActivity.class.getName(); // launcher activity name

        Intent queryIntent = new Intent();
        queryIntent.addCategory(Intent.CATEGORY_HOME);
        queryIntent.setAction(Intent.ACTION_MAIN);

        List<ResolveInfo> homeActivities = pm.queryIntentActivities(queryIntent, 0);
        if (homeActivities == null) {
            return;
        }

        final ComponentName defaultLauncher = new ComponentName(examplePackageName, exampleActivityName);
        int activityNum = homeActivities.size();
        ComponentName[] set = new ComponentName[activityNum];
        int defaultMatch = -1;
        for (int i = 0; i < activityNum; i++) {
            ResolveInfo info = homeActivities.get(i);
            set[i] = new ComponentName(info.activityInfo.packageName, info.activityInfo.name);
            if (defaultLauncher.equals(set[i])) {
                defaultMatch = info.match;
            }
        }

        if (defaultMatch == -1) {
            return;
        }

        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_MAIN);
        filter.addCategory(Intent.CATEGORY_HOME);
        filter.addCategory(Intent.CATEGORY_DEFAULT);

        try {
            Class<?> comClas = Class.forName("android.content.ComponentName");
            Object comArray = Array.newInstance(comClas, 1);
            Method mSetActivityController = PackageManager.class.getMethod(
                    "replacePreferredActivity", Class.forName("android.content.IntentFilter"),
                    int.class, comArray.getClass(), comClas);

            mSetActivityController.invoke(context.getPackageManager(), filter,
                    IntentFilter.MATCH_CATEGORY_EMPTY, set, defaultLauncher);
        } catch (ClassNotFoundException | NoSuchMethodException
                | IllegalAccessException | InvocationTargetException
                | IllegalArgumentException e) {
            e.printStackTrace();
        }
    }

    public static void setThreeFinger(boolean enable) {
        Log.d(TAG, "Set three finger : " + enable);
        try {
            Context context = ApplicationWrapper.getContext();
            Settings.Secure.putInt(context.getContentResolver(), "user_setup_complete", enable ? 1 : 0);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 是否使用的是绑定码,激活机器
     *  只有海外ROM，不管当前系统语言是什么,均使用绑定码绑定.
     *  国内的ROM,非中文时，均使用绑定码绑定.
     * @return
     */
    public static boolean isUsingBindCode() {
        String systemLanguage = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_LANGUAGE);
        Log.d(TAG, "sys lang = " + systemLanguage);
        return ProductInfo.isOverSea() || (!systemLanguage.contains("zh"));
    }

    public static boolean hasTopIR() {
        return getDeviceProperties() != null
                && "1".equals(getDeviceProperties()
                .getProperty("TOPIR_mini2", "0"));
    }

    private static Properties getDeviceProperties() {
        File file = new File(DEVICE_CONFIG_DIR, DEVICE_PROPERTIES);
        if (file == null || !file.exists()) {
            Log.d(TAG, "getDeviceProperties file is not exists.");
            return null;
        }
        Properties properties = new Properties();
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
            properties.load(fis);
        } catch (IOException e) {
            e.printStackTrace();
            return properties;
        } finally {
            IOUtils.close(fis);
        }
        Log.d(TAG, "getDeviceProperties: start --->>>");
        printProperty(properties);
        Log.d(TAG, "getDeviceProperties: end <<<---");
        return properties;
    }

    private static void printProperty(Properties properties) {
        if (properties == null) {
            Log.d(TAG, "printProperty properties is null");
            return;
        }
        for (Map.Entry<Object, Object> objectObjectEntry : properties.entrySet()) {
            Object key = objectObjectEntry.getKey();
            Object value = objectObjectEntry.getValue();
            Log.d(TAG, "key=" + key + " value=" + value);
        }
    }

    public static void checkImeAvailable(Context context) {
        InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
        String defaultIme = Settings.Secure.getString(context.getContentResolver(), Settings.Secure.DEFAULT_INPUT_METHOD);
        List<InputMethodInfo> methodList = imm.getInputMethodList();
        for (InputMethodInfo imi : methodList) {
            if (imi.getId().equalsIgnoreCase(defaultIme)) {
                Log.e(TAG, "imi:" + imi.getId() + ", default:" + defaultIme);
                return;
            }
        }
        if (methodList.size() > 0) {
            Log.e(TAG, "IME not support, current:" + defaultIme + ", support:" + methodList.get(0).getId());
            Settings.Secure.putString(context.getContentResolver(), Settings.Secure.DEFAULT_INPUT_METHOD, methodList.get(0).getId());
        } else {
            Log.e(TAG, "IME not support, current:" + defaultIme + ", support list null");
        }
    }

    public static String getSystemImei() {
        return sSystemImei;
    }
}
