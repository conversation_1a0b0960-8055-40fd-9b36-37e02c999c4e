package com.ainirobot.home.ota.database;

import static com.ainirobot.home.ota.constants.OtaConstants.AUTHORITY;

import android.content.ContentProvider;
import android.content.ContentValues;
import android.content.UriMatcher;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.util.Log;

import com.ainirobot.home.ota.constants.OtaConstants;

import java.util.Objects;

public class DowngradeProvider extends ContentProvider {
    private static final String TAG = OtaConstants.TAG_PREFIX + DowngradeProvider.class.getSimpleName();

    private DataBaseHelper dataBaseHelper;
    private SQLiteDatabase sqLiteDatabase;

    private static final UriMatcher matcher = new UriMatcher(UriMatcher.NO_MATCH);

    static {
        int CODE_UPDATE = 1;
        matcher.addURI("content://" + AUTHORITY, "downgrade", CODE_UPDATE);
    }

    @Override
    public boolean onCreate() {
        Log.d(TAG, "onCreate.");
        dataBaseHelper = new DataBaseHelper(getContext(), DataBaseHelper.DATABASE_NAME, null
                , DataBaseHelper.VERSION);
        return true;
    }

    @Override
    public Cursor query(@NonNull Uri uri, @Nullable String[] projection, @Nullable String
            selection, @Nullable String[] selectionArgs, @Nullable String sortOrder) {

        Cursor cursor;
        sqLiteDatabase = dataBaseHelper.getReadableDatabase();
        cursor = sqLiteDatabase.query(DataBaseHelper.TABLE_NAME
                , projection, selection, selectionArgs
                , null, null, sortOrder);
        cursor.setNotificationUri(Objects.requireNonNull(getContext()).getContentResolver(), uri);

        return cursor;
    }

    @Nullable
    @Override
    public String getType(@NonNull Uri uri) {
        return null;
    }

    @Nullable
    @Override
    public Uri insert(@NonNull Uri uri, @Nullable ContentValues values) {
        sqLiteDatabase = dataBaseHelper.getWritableDatabase();

        long rowId = sqLiteDatabase.insert(DataBaseHelper.TABLE_NAME, null, values);
        sqLiteDatabase.close();
        Log.d(TAG, "insert id=" + rowId);

        if (rowId < 0) {
            Log.e(TAG, "insert failed.");
            return null;
        }

        return Uri.withAppendedPath(uri, rowId + "");
    }

    @Override
    public int delete(@NonNull Uri uri, @Nullable String selection, @Nullable String[]
            selectionArgs) {

        sqLiteDatabase = dataBaseHelper.getWritableDatabase();
        int count = sqLiteDatabase.delete(DataBaseHelper.TABLE_NAME, selection, selectionArgs);
        sqLiteDatabase.close();
        Log.d(TAG, "delete count=" + count);

        return count;
    }

    @Override
    public int update(@NonNull Uri uri, @Nullable ContentValues values, @Nullable String
            selection, @Nullable String[] selectionArgs) {

        sqLiteDatabase = dataBaseHelper.getWritableDatabase();
        int count = sqLiteDatabase.update(DataBaseHelper.TABLE_NAME, values, selection,
                selectionArgs);
        sqLiteDatabase.close();

        return count;
    }
}
