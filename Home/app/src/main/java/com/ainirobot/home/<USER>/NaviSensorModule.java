package com.ainirobot.home.module;

import android.content.Context;
import android.content.Intent;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.ui.UIController;

/**
 * 处理底盘异常的Module
 */
public class NaviSensorModule extends BaseModule{

    private static final String TAG = NaviSensorModule.class.getSimpleName();
    private Context mContext = null;

    private NaviSensorModule(){
    }

    public void init(Context context) {
        this.mContext = context;
    }

    private static class SingletonHolder {
        private static final NaviSensorModule mInstance = new NaviSensorModule();
    }

    public static NaviSensorModule getInstance() {
        return NaviSensorModule.SingletonHolder.mInstance;
    }


    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.d(TAG, "reqId=" + reqId + "; intent=" + intent + "; params=" + params);
        switch (intent) {
            case Definition.REQ_CHASSIS_SENSOR_ERROR:
                SkillManager.getInstance().closeSpeechAsrRecognize();
                SkillManager.getInstance().cancleAudioOperation();
                UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_NAVI_SENSOR_STATE, null, null);
                releaseElevator();
                SystemApi.getInstance().resetHead(0, null);
                break;
            case Definition.REQ_CHASSIS_SENSOR_NORMAL:
                stop();
                break;
        }
        SystemApi.getInstance().finishModuleParser(reqId, true);
        return true;
    }

    private void releaseElevator() {
        SystemApi.getInstance().releaseElevator(0, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                Log.d(TAG, "releaseElevator result : " + result + ", message : " + message + ", extraData :" + extraData);
            }

            @Override
            public void onError(int errorCode, String errorString, String extraData) throws RemoteException {
                super.onError(errorCode, errorString, extraData);
            }
        });
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
        switch (type){
            case ModuleDef.LOCAL_MESSAGE_NAVI_SENSOR_REBOOT:
                realReboot();
                break;
            default:
                break;
        }
    }

    private void realReboot() {
        Log.i(TAG, "complete machine reboot");
        Intent intent = new Intent(Intent.ACTION_REBOOT);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        mContext.startActivity(intent);
    }

    @Override
    protected void onStop() {
        super.onStop();
        SystemApi.getInstance().onChassisSensorFinished();
        SkillManager.getInstance().openSpeechAsrRecognize();
    }
}
