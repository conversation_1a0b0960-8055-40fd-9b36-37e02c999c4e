package com.ainirobot.home.module;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.bi.BiDormancyBeginReport;
import com.ainirobot.home.bi.BiDormancyEndReport;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.utils.SystemUtils;

import org.json.JSONException;
import org.json.JSONObject;

public class StandByModule extends BaseModule {

    private static final String TAG = StandByModule.class.getSimpleName() + ":Home";

    private static StandByModule sInstance = null;
    private Context mContext;

    private Status mCurrentStatus = Status.IDLE;
    private int START_COUNT;

    public static StandByModule getInstance() {
        if (sInstance == null) {
            sInstance = new StandByModule();
        }
        return sInstance;
    }

    private StandByModule() {
    }

    public void init(Context context) {
        mContext = context;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.d(TAG, "reqId=" + reqId + "; intent=" + intent + "; params=" + params);
        boolean standbyInOpkMode = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_STANDBY_OPK_MODE_SWITCH) == Definition.ROBOT_SETTING_ENABLE;
        Log.d(TAG, "standby in opk mode: " + standbyInOpkMode);
        switch (intent) {
            case Definition.REQ_STANDBY_START:
                if (mCurrentStatus == Status.RUNNING) {
                    Log.i(TAG, "start failed , Current status is " + mCurrentStatus);
                    sendResponseOfStartToServer(params, Definition.ALREADY_IN_STANDBY);
                    return true;
                }
                if(!standbyInOpkMode){
                    standby(reqId,intent,text,params);
                }
                break;
            case Definition.REQ_STANDBY_STOP:
                if (mCurrentStatus != Status.RUNNING) {
                    Log.i(TAG, "stop failed , Current status is " + mCurrentStatus);
                    sendResponseOfStopToServer(params, Definition.CAN_NOT_EXIT_STANDBY, false);
                    if ( mCurrentStatus == Status.IDLE){
                        Log.i(TAG, "stop failed , 重新休眠");
                        if(!standbyInOpkMode){
                            standby(reqId,intent,text,params);
                        }
                    }
                    return true;
                }
                mCurrentStatus = Status.RESUMING;
                sendResponseOfStopToServer(params, Definition.STANDBY_REPORT_SUCCESS, true);
                if(!standbyInOpkMode){
                    SystemApi.getInstance().robotStandbyEnd(reqId);
                    UIController.getInstance().sendMessageToFragment(
                            UIController.MESSAGE_TYPE.STANDBY_EXIT, String.valueOf(true));
                }
                break;
            case Definition.REQ_STANDBY_FINISH:
                SystemApi.getInstance().robotStandbyEnd(reqId);
                UIController.getInstance().sendMessageToFragment(
                        UIController.MESSAGE_TYPE.STANDBY_EXIT, String.valueOf(true));
                break;
        }
        SystemApi.getInstance().finishModuleParser(reqId, true);
        return false;
    }


    private void standby(int reqId, String intent, String text, String params){
        //mCurrentStatus 用来判断Status.IDLE 和 Status.RESTARTING，决定是否重新加载界面
        Bundle bundle2 = new Bundle();
        bundle2.putInt(ModuleDef.STANDBY_START_COUNT, START_COUNT++);
        mCurrentStatus = Status.RUNNING;
        SystemUtils.setThreeFinger(false);
        SkillManager.getInstance().stopTTSOnly();
        UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_STANDBY,
                bundle2, null);
        SystemApi.getInstance().robotStandby(reqId, null);
        sendResponseOfStartToServer(params, Definition.STANDBY_REPORT_SUCCESS);
    }

    private void sendResponseOfStartToServer(String params, String result) {
        String from = BiDormancyBeginReport.VALUE_FROM_SYSTEM;
        int reportType = BiDormancyBeginReport.VALUE_TYPE_FUNCTION;
        try {
            JSONObject jsonObject = new JSONObject(params);
            if (jsonObject.has(Definition.JSON_STANDBY_FROM)) {
                from = jsonObject.getString(Definition.JSON_STANDBY_FROM);
                if (from.startsWith(Definition.JSON_CMD_FROM)) {
                    String id = jsonObject.getString(Definition.JSON_CMD_ID);
                    String type = jsonObject.getString(Definition.JSON_CMD_TYPE);
                    SystemApi.getInstance().taskCommandReport(-1, id, type, result,
                            "", null);
                    reportType = BiDormancyBeginReport.VALUE_TYPE_REMOTE;
                } else {
                    reportType = BiDormancyBeginReport.VALUE_TYPE_OTHER;
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        new BiDormancyBeginReport().addFrom(from).addType(reportType).report();
    }

    public void sendResponseOfStopToServer(String params, String result, boolean report) {
        if (TextUtils.isEmpty(params)) {
            return;
        }
        String from = "";
        try {
            JSONObject jsonObject = new JSONObject(params);
            if (jsonObject.has(Definition.JSON_STANDBY_FROM)) {
                from = jsonObject.getString(Definition.JSON_STANDBY_FROM);
                if (from.startsWith(Definition.JSON_CMD_FROM)) {
                    String id = jsonObject.getString(Definition.JSON_CMD_ID);
                    String type = jsonObject.getString(Definition.JSON_CMD_TYPE);
                    SystemApi.getInstance().taskCommandReport(-1, id, type, result,
                            "", null);
                    if (report) {
                        new BiDormancyEndReport().addEndType(BiDormancyEndReport.TYPE_MULTIPLE_REMOTE)
                                .report();
                    }
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void getRobotStatus() {
        getEmergencyStatus();
        getMultiFunctionSwitchStatus();
        getBmsWarningStatus();
    }

    private void getBmsWarningStatus() {
        SystemApi.getInstance().getRobotStatus(Definition.STATUS_BMS_WARNING, new StatusListener() {
            @Override
            public void onStatusUpdate(String type, String data) {
                if (TextUtils.isEmpty(data)) {
                    return;
                }
                Log.d(TAG, "getBmsWarningStatus onStatusUpdate: type=" + type + ",data=" + data);
                try {
                    JSONObject object = new JSONObject(data);
                    int status = object.optInt("status");
                    Log.d(TAG, "status: " + status);
                    UIController.getInstance().sendMessageToFragment(
                            UIController.MESSAGE_TYPE
                                    .BMS_WARNING_STATUS,
                            String.valueOf(status));
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private void getMultiFunctionSwitchStatus() {
        SystemApi.getInstance().getMultiFunctionSwitchState(-1, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                Log.d(TAG, "getEmergencyStatus result " + result + ", message " + message
                        + ", extraData " + extraData);
                try {
                    JSONObject jsonObject = new JSONObject(message);
                    String switchState = jsonObject.getString(ModuleDef
                            .MULTI_FUNC_SWITCH_STATE);
                    UIController.getInstance().sendMessageToFragment(
                            UIController.MESSAGE_TYPE
                                    .STANDBY_MULTI_FUNC_SWITCH_STATUS,
                            String.valueOf(TextUtils.equals(switchState, ModuleDef.MULTIPLE_PRESS)));
                } catch (JSONException e) {
                    e.printStackTrace();
                }

            }
        });
    }

    private void getEmergencyStatus() {
        SystemApi.getInstance().getEmergencyStatus(-1, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                Log.d(TAG, "getEmergencyStatus result " + result + ", message " + message
                        + ", extraData " + extraData);
                UIController.getInstance().sendMessageToFragment(
                        UIController.MESSAGE_TYPE.STANDBY_EMERGENCY_STATUS,
                        String.valueOf(TextUtils.equals(message, ModuleDef.EMERGENCY_PRESS)));
            }
        });
    }

    @Override
    public void onMessageFromLocal(int type) {
        super.onMessageFromLocal(type);
        switch (type) {
            case ModuleDef.LOCAL_MESSAGE_START_STANDBY:
                mCurrentStatus = Status.IDLE;
                SystemApi.getInstance().updateStandby(Definition.START, null);
                break;
            case ModuleDef.LOCAL_MESSAGE_STOP_STANDBY:
                SystemApi.getInstance().updateStandby(Definition.STOP, null);
                break;
            case ModuleDef.LOCAL_MESSAGE_EXIT_STANDBY:
                SystemApi.getInstance().updateStandby(Definition.FINISHED, null);
                stop();
                break;
            case ModuleDef.LOCAL_MESSAGE_INIT:
                getRobotStatus();
                break;
        }
    }

    @Override
    protected void onStop() {
        SystemUtils.setThreeFinger(true);
        mCurrentStatus = Status.IDLE;
        super.onStop();
    }

    enum Status {
        IDLE, RUNNING, RESUMING
    }
}
