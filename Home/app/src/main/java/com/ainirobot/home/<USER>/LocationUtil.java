package com.ainirobot.home.utils;

import static com.ainirobot.coreservice.client.Definition.NAVI_OTA_UPDATE_DONE;
import static com.ainirobot.coreservice.client.Definition.POSE;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;


public class LocationUtil {

    private static final String LOG_TAG = "LocationUtil";

    private static Pose mPose;

    private static Pose mChargePilePose;

    private boolean isCharging = false;

    private boolean mIsNeedPlaySound = false;

    private final ExecutorService mExecutor = Executors.newSingleThreadExecutor(new ThreadFactory() {
        private final AtomicInteger mCount = new AtomicInteger(1);

        @Override
        public Thread newThread(Runnable r) {
            return new Thread(r, "locate_record #" + mCount.getAndIncrement());
        }
    });

    private LocationUtil() {
        registerRemoteRebootBroadcast();
    }

    /**
     * In order to remote reboot from RomoteControlService
     */
    private void registerRemoteRebootBroadcast() {
        IntentFilter filter = new IntentFilter(Definition.ACTION_REMOTE_REBOOT);
        filter.addAction(NAVI_OTA_UPDATE_DONE);
        ApplicationWrapper.getContext().registerReceiver(new RemoteRebootReceiver(), filter);
    }

    private static class LocationManagerHolder {
        private static LocationUtil locationManager = new LocationUtil();
    }

    public static LocationUtil getInstance() {
        return LocationUtil.LocationManagerHolder.locationManager;
    }

    public void startRecordPose() {
        mExecutor.execute(new RecordPoseTask());
    }

    private static void recordPose() {
        boolean connected = SystemApi.getInstance().isApiConnectedService();
        if (!connected) {
            Log.d(LOG_TAG, "recordPose , but SystemApi is not connected");
            return;
        }
        recordChargePose();
        SystemApi.getInstance().isRobotEstimate(0, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                super.onResult(result, message);
                Log.d(LOG_TAG, "isRobotEstimate result : " + result + ", msg :" + message);
                if (result == Definition.RESULT_OK && "true".equals(message)) {
                    recordNearPose();
                }
            }
        });
    }

    private static void recordNearPose(){
        Log.d(LOG_TAG, "record near Pose Before Reboot");
        SystemApi.getInstance().getPosition(0, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(LOG_TAG, "recordPoseBeforeReboot message " + message);
                if (result < 0 || TextUtils.isEmpty(message)) {
                    return;
                }

                try {
                    JSONObject object = new JSONObject(message);
                    float px = (float) object.optDouble("px");
                    float py = (float) object.optDouble("py");
                    if (px != 0 && py != 0) {
                        SharedPrefUtil.getInstance().putString(ModuleDef.KEY_SP_NEAR_POSE, message);
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private static void recordChargePose() {
        Log.d(LOG_TAG, "record charge Pose Before Reboot");
        SystemApi.getInstance().getLocation(0,
                Definition.CHARGING_POLE_TYPE, new CommandListener() {
                    @Override
                    public void onResult(int result, String message) {
                        super.onResult(result, message);
                        Log.i(LOG_TAG, "Get charging pile result:" + result + " message:" + message);

                        try {
                            JSONObject object = new JSONObject(message);
                            float px = (float) object.optDouble("px");
                            float py = (float) object.optDouble("py");
                            if (px != 0 && py != 0) {
                                SharedPrefUtil.getInstance().putString(ModuleDef.KEY_SP_CHARGE_PILE_POSE, message);
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                });
    }

    private static void preloadPoseInfoForLocate() {
        String poseInfo = SharedPrefUtil.getInstance().getString(ModuleDef.KEY_SP_NEAR_POSE, "");
        Log.d(LOG_TAG, "preloadPoseInfoForLocate message " + poseInfo);
        if (!TextUtils.isEmpty(poseInfo)) {
            if (mPose == null) {
                mPose = new Pose();
            }
            try {
                JSONObject object = new JSONObject(poseInfo);
                mPose.setX((float) object.optDouble("px"));
                mPose.setY((float) object.optDouble("py"));
                mPose.setTheta((float) object.optDouble("theta"));
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        // preload charge pile pose info
        String chargePoseInfo = SharedPrefUtil.getInstance().getString(ModuleDef.KEY_SP_CHARGE_PILE_POSE, "");
        Log.d(LOG_TAG, "preloadChargePilePoseInfo message " + chargePoseInfo);
        if (TextUtils.isEmpty(chargePoseInfo)) {
            return;
        }
        setChargePilePose(chargePoseInfo);
    }

    private static void setChargePilePose(String message) {
        try {
            JSONObject object = new JSONObject(message);
            boolean isExist = object.getBoolean(Definition.JSON_NAVI_SITE_EXIST);
            if (isExist) {
                if (mChargePilePose == null) {
                    mChargePilePose = new Pose();
                }

                mChargePilePose.setX((float) object.optDouble("px"));
                mChargePilePose.setY((float) object.optDouble("py"));
                mChargePilePose.setTheta((float) object.optDouble("theta"));
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * preload robot last pose and charge pile pose info
     */
    public void preloadLocationInfo() {
        mExecutor.execute(new PreloadPoseTask());
    }

    public boolean canChargePileLocate() {
        return isCharging && mChargePilePose != null;
    }

    public void reloadChargePilePose(){
        if(mChargePilePose != null){
            return;
        }
        SystemApi.getInstance().getLocation(0,
                Definition.CHARGING_POLE_TYPE, new CommandListener() {
                    @Override
                    public void onResult(int result, String message) {
                        super.onResult(result, message);
                        Log.i(LOG_TAG, "reload charging pile result:" + result + " message:" + message);
                        setChargePilePose(message);
                    }
                });
    }


    public void setIsCharging(boolean charging) {
        Log.d(LOG_TAG, "setIsCharging " + charging);
        isCharging = charging;
    }

    public boolean isNeedPlaySound() {
        return mIsNeedPlaySound;
    }

    public void setIsNeedPlaySound(boolean need) {
        Log.d(LOG_TAG, "mIsNeedPlaySound " + need);
        mIsNeedPlaySound = need;
    }

    public void clearData() {
        mPose = null;
        mChargePilePose = null;
        mExecutor.execute(new ClearPoseTask());
    }

    public Pose getPose() {
        return mPose;
    }

    public Pose getChargePilePose() {
        return mChargePilePose;
    }


    static class RecordPoseTask implements Runnable {

        @Override
        public void run() {
            recordPose();
        }
    }

    static class ClearPoseTask implements Runnable {

        @Override
        public void run() {
            try {
                SharedPrefUtil.getInstance().remove(ModuleDef.KEY_SP_NEAR_POSE);
                SharedPrefUtil.getInstance().remove(ModuleDef.KEY_SP_CHARGE_PILE_POSE);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    static class PreloadPoseTask implements Runnable {

        @Override
        public void run() {
            preloadPoseInfoForLocate();
        }
    }

    /**
     * monitor the remote reboot action
     */
    private class RemoteRebootReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent == null) {
                return;
            }

            String action = intent.getAction();
            if (TextUtils.isEmpty(action)) {
                return;
            }

            Log.d(LOG_TAG, "RemoteRebootReceiver action " + action);

            switch (action) {
                case Definition.ACTION_REMOTE_REBOOT:
                    recordPose();
                    break;
                case NAVI_OTA_UPDATE_DONE:
                    String pose = intent.getStringExtra(POSE);
                    Log.d(LOG_TAG, "pose = " + pose);
                    recordPoseWhenOtaDone(pose);
                    break;
                default:
                    break;
            }
        }
    }

    private void recordPoseWhenOtaDone(String pose) {
        if (TextUtils.isEmpty(pose)) {
            return;
        }
        try {
            JSONObject object = new JSONObject(pose);
            float px = (float) object.optDouble("px");
            float py = (float) object.optDouble("py");
            if (px != 0 && py != 0) {
                SharedPrefUtil.getInstance().putString(ModuleDef.KEY_SP_CHARGE_PILE_POSE, pose);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public String getChargePoseNameByChargingType() {
        return isChargingTypeWire() ?
                Definition.LOCATE_POSITION_POSE :
                Definition.START_BACK_CHARGE_POSE;
    }

    public String getLocationNameByChargingType() {
        return isChargingTypeWire() ?
                Definition.LOCATE_POSITION_POSE :
                Definition.START_CHARGE_PILE_POSE;
    }

    public int getLocationTypeIdByChargingType() {
        return isChargingTypeWire() ?
                Definition.POSITIONING_POINT_TYPE :
                Definition.CHARGING_POINT_TYPE;
    }

    /**
     * MapTool高级设置中充电方式是否为线充
     * @return
     */
    public boolean isChargingTypeWire() {
        String chargingType = RobotSettingApi.getInstance().
                getRobotString(Definition.ROBOT_SETTINGS_CHARGING_TYPE);
        Log.d(LOG_TAG, "isChargingTypeWire: chargingType=" + chargingType);
        return Definition.CHARGING_TYPE_WIRE.equals(chargingType);
    }

    /**
     * MapTool高级设置中充电方式是否为桩充
     * 消毒豹产品线只有充电方式为桩充时,才用该 AutoChargeModule 回充或者用 RepositionModule 定位　;
     * 线充时才用 AutoGoChargeModule 回充，用QrcodeRepositionModule 定位
     * @return
     */
    public boolean isChargingTypePile() {
        String chargingType = RobotSettingApi.getInstance().
                getRobotString(Definition.ROBOT_SETTINGS_CHARGING_TYPE);
        Log.d(LOG_TAG, "isChargingType Pile: chargingType=" + chargingType);
        return Definition.CHARGING_TYPE_PILE.equals(chargingType);
    }

    public boolean isElevatorControlEnabled(){
        return SystemApi.getInstance().isElevatorControlEnable();
    }

}
