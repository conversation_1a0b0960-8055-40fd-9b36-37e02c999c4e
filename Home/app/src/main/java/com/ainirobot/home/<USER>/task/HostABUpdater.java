package com.ainirobot.home.ota.task;

import static com.ainirobot.home.ota.constants.OtaConstants.OS_HOST;
import static com.ainirobot.home.ota.constants.OtaConstants.OTA_INSTALL_PATH;

import android.content.Context;
import android.net.Uri;
import android.os.UpdateEngine;
import android.os.UpdateEngineCallback;
import android.util.Log;

import com.ainirobot.home.ota.constants.OtaConstants;
import com.ainirobot.home.ota.utils.Preferences;
import com.ainirobot.home.ota.utils.SystemUtils;
import com.ainirobot.home.ota.utils.Utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

public class HostABUpdater extends HostInstallTask {
    private static final String TAG = OtaConstants.TAG_PREFIX + HostABUpdater.class.getSimpleName();
    public static final String ROOTDIR = OTA_INSTALL_PATH;//Environment.getDataDirectory() + "/ota_package";

    public static final String PATH_PAYLOAD_BIN = ROOTDIR + "/payload.bin";
    public static final String PATH_PAYLOAD_PROPERTIES = ROOTDIR + "/payload_properties.txt";

    UpdateEngine mUpdateEngine;

    public HostABUpdater(Context context){
        super(context);
        mUpdateEngine = new UpdateEngine();
    }

    UpdateEngineCallback mUpdateEngineCallback = new UpdateEngineCallback() {

        @Override
        public void onStatusUpdate(int status, float percent) {
            if (status == UpdateEngine.UpdateStatusConstants.DOWNLOADING) {// 回调状态，升级进度
                DecimalFormat df = new DecimalFormat("#");
                String progress = df.format(percent * 100);
                Log.d(TAG, "update progress: " + progress);
            }
        }

        @Override
        public void onPayloadApplicationComplete(int errorCode) {
            Log.d(TAG, "payload complete, errorCode: " + errorCode);
            if (errorCode == UpdateEngine.ErrorCodeConstants.SUCCESS) {// 回调状态
                if (mListener != null) {
                    mListener.onInstallSuccess();
                }
            } else {
                if (mListener != null) {
                    mListener.onInstallError(new Exception(Integer.valueOf(errorCode).toString()));
                }
            }
        }
    };

    private void startUpdateSystem() {
        File file = new File(Preferences.getInstance().getOsPackagePath(OS_HOST));
        if (!file.exists()) {
            if (mListener != null) {
                mListener.onInstallError(new Exception("Host file doesn't exist."));
            }
            return;
        }
        Log.d(TAG, "startUpdateSystem before unzip :" + SystemUtils.getTotalExternalAvailableSize());
        if (!Utils.unZip(file, null, ROOTDIR)) {
            if (mListener != null) {
                mListener.onInstallError(new Exception("Host file unzip failed"));
            }
            return;
        }

        Uri uri = Uri.fromFile(new File(PATH_PAYLOAD_BIN));
        mUpdateEngine.bind(mUpdateEngineCallback);// 绑定callback
        try {
            mUpdateEngine.applyPayload(uri.toString(), 0L, 0L, getPayloadProperties());// 进行升级
        }catch (RuntimeException ex) {
            if (mListener != null) {
                mListener.onInstallError(new Exception("Host update failed"));
            }
        }
    }

    // 读取payload_properties.txt
    private static String[] getPayloadProperties() {
        try {
            File file = new File(PATH_PAYLOAD_PROPERTIES);
            InputStreamReader is = new InputStreamReader(new FileInputStream(file));
            BufferedReader br = new BufferedReader(is);
            List<String> lines = new ArrayList<>();
            String line;
            while ((line = br.readLine()) != null) {
                Log.d(TAG, "getPayloadProperties line: " + line);
                lines.add(line);
            }
            br.close();
            is.close();
            return lines.toArray(new String[lines.size()]);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    protected Integer doInBackground(Void... params) {
        startUpdateSystem();
        return 0;
    }
}
