/*
 *   Copyright (C) 2017 OrionStar Technology Project
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 */

package com.ainirobot.home.report;

import com.ainirobot.home.ModuleDef;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * 在此写用途
 *
 * @FileName: com.ainirobot.moduleapp.report.Constant.java
 * @author: Orion
 * @date: 2019-03-08 10:57
 */

public class Constant {
    public static LinkedHashMap<Integer, TaskBaseInfo> TASKBASEINFO;
    public static LinkedHashMap<String, TaskBaseInfo> ACTIVIEYBASEINFO;
    public static List<String> WHITE_LIST_PACKAGE;

    static {
        // Attention : order is very important
        TASKBASEINFO = new LinkedHashMap<Integer, TaskBaseInfo>() {{
            put(ModuleDef.FEATURE_OTA, new TaskBaseInfo(ModuleDef.FEATURE_OTA, "升级", "OTA", "update"));
            put(ModuleDef.FEATURE_AUTO_CHARGE, new TaskBaseInfo(ModuleDef.FEATURE_AUTO_CHARGE, "自动回充", "去充电", "self_recharge"));
            put(ModuleDef.FEATURE_EMERGENCY, new TaskBaseInfo(ModuleDef.FEATURE_EMERGENCY, "急停", "急停", "emergency"));
            put(ModuleDef.FEATURE_STANDBY, new TaskBaseInfo(ModuleDef.FEATURE_STANDBY, "休眠", "休眠", "sleep"));
            put(ModuleDef.FEATURE_SET_CHARGE_PILE, new TaskBaseInfo(ModuleDef.FEATURE_SET_CHARGE_PILE, "设置充电桩位置", "设置充电桩位置", "set_charge_position"));
            put(ModuleDef.FEATURE_INSPECTION, new TaskBaseInfo(ModuleDef.FEATURE_INSPECTION, "自检", "自检", "self_inspect"));
            put(ModuleDef.FEATURE_LAUNCHER, new TaskBaseInfo(ModuleDef.FEATURE_LAUNCHER, "桌面", "桌面中", "desktop"));
            put(ModuleDef.FEATURE_CHARGING, new TaskBaseInfo(ModuleDef.FEATURE_CHARGING, "充电中", "充电中", "recharging"));
            put(ModuleDef.FEATURE_REPOSITION, new TaskBaseInfo(ModuleDef.FEATURE_REPOSITION, "重定位", "重定位", "reposition"));
            put(ModuleDef.FEATURE_REMOTE_REPOSITION, new TaskBaseInfo(ModuleDef.FEATURE_REMOTE_REPOSITION, "远程重定位", "云端重定位", "remote_reposition"));
            put(ModuleDef.FEATURE_FULL_LOCK, new TaskBaseInfo(ModuleDef.FEATURE_FULL_LOCK, "锁定","锁定", "locked"));
            put(ModuleDef.FEATURE_REMOTE_CONTROL, new TaskBaseInfo(ModuleDef.FEATURE_NAVIGATION, "导航", "导肮", "navigation"));
            put(ModuleDef.FEATURE_POWER_LOW, new TaskBaseInfo(ModuleDef.FEATURE_POWER_LOW, "电量低", "电量低", "power_low"));
            put(ModuleDef.FEATURE_MAP_OUTSIDE, new TaskBaseInfo(ModuleDef.FEATURE_MAP_OUTSIDE, "防盗报警", "防盗报警", "map_outside_warning"));
            put(ModuleDef.FEATURE_ROBOT_BEING_PUSHED, new TaskBaseInfo(ModuleDef.FEATURE_ROBOT_BEING_PUSHED, "推动报警", "推动报警", "robot_being_pushed"));
            put(ModuleDef.FEATURE_MULTI_ROBOT_ERROR, new TaskBaseInfo(ModuleDef.FEATURE_MULTI_ROBOT_ERROR, "多机异常", "多机异常", "multi_robot_error"));
        }};
    }

    static {
        // Attention : order is very important
        ACTIVIEYBASEINFO = new LinkedHashMap<String, TaskBaseInfo>() {{
            //put("com.ainirobot.inspection", new TaskBaseInfo(ModuleDef.FEATURE_INSPECTION, "自检", "自检"));
            put("com.ainirobot.settings", new TaskBaseInfo(ModuleDef.FEATURE_SETTING_MODULE, "设置中", "设置", "in_setup"));
            put("com.ainirobot.maptool", new TaskBaseInfo(ModuleDef.FEATURE_MAP, "地图", "地图工具", "map_manage"));
        }};
    }

    static {
        WHITE_LIST_PACKAGE = new ArrayList<String>(){{
            add("com.ainirobot.moduleapp");
            add("com.ainirobot.home");
        }};
    }

    public static class TaskInfo {
        private TaskEvent currentResult = TaskEvent.task_finish;
        private TaskBaseInfo nextTask;


        public TaskEvent getCurrentResult() {
            return currentResult;
        }

        public void setCurrentResult(TaskEvent currentResult) {
            this.currentResult = currentResult;
        }

        public TaskBaseInfo getNextTask() {
            return nextTask;
        }

        public void setNextTask(TaskBaseInfo nextTask) {
            this.nextTask = nextTask;
        }
    }

    public static enum TaskEvent {
        task_start("task_start"),
        task_finish("task_finish"),
        task_cancel("task_cancel"),
        task_fail("task_fail");

        private String name;

        TaskEvent(String name) {
            this.name = name;
        }
    }

    public static class TaskBaseInfo {
        private int featureId;
        private String name;
        private String alias;
        private String featureType;

        public TaskBaseInfo(int featureId, String name, String alias, String featureType) {
            super();
            this.featureId = featureId;
            this.name = name;
            this.alias = alias;
            this.featureType = featureType;
        }

        public int getFeatureId() {
            return featureId;
        }

        public String getName() {
            return name;
        }

        public String getAlias() {
            return alias;
        }

        public String getFeatureType() {
            return featureType;
        }
    }
}

