/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.home.ui.fragment;

import android.app.Activity;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Message;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.bean.QrCodeBean;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.net.INetConnectionChangeListener;
import com.ainirobot.home.net.NetManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.utils.ResUtil;
import com.ainirobot.home.utils.SystemUtils;
import com.ainirobot.home.utils.WeakHandler;
import com.ainirobot.home.utils.WifiUtils;

import org.json.JSONException;
import org.json.JSONObject;


/**
 * 只有国内并且系统语言中文的情况下，才加载使用QRcodeFragment　作为二维码激活，其他情况均使用绑定码激活.
 */
public class QRcodeFragment extends BaseFragment implements INetConnectionChangeListener, View.OnClickListener {

    private static final String TAG = "QRcodeFragment:Home";
    private ImageView mQrCode;
    private TextView mQrCodeLoading, mBindCodeTx;
    private MyWeakHandler mHandler;
    private RelativeLayout mRlLoading;
    private LinearLayout mLbindCode;
    private TextView mQrCodeRetry;
    private LinearLayout mSnNumberLayout;
    private TextView mSnNumberTx;
    private String mUrl, mCodePC;
    private boolean mIsNeedRequestQR = false; // is has request qrcode;
    private boolean hasPlay = false;
    private long mExspireTime;


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mHandler = new MyWeakHandler(getActivity());
    }


    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_qr_code, null);
        mRlLoading = (RelativeLayout) view.findViewById(R.id.rl_loading);
        mLbindCode = (LinearLayout) view.findViewById(R.id.bind_code_pc);
        mSnNumberLayout = (LinearLayout) view.findViewById(R.id.sn_number);
        mQrCodeLoading = (TextView) view.findViewById(R.id.qr_code_loading);
        mQrCode = (ImageView) view.findViewById(R.id.qr_code);
        mQrCodeRetry = (TextView) view.findViewById(R.id.qr_code_load_retry);
        mQrCodeRetry.setOnClickListener(this);
        mBindCodeTx = (TextView) view.findViewById(R.id.bind_code_tx);
        mSnNumberTx = (TextView) view.findViewById(R.id.sn_number_tx);
        return view;
    }


    @Override
    public void onStart() {
        super.onStart();
        Log.d(TAG, "QRcodeFragment onStart showLoading");
        showLoading();
        requestQrCode();
        NetManager.getInstance().registerNetReceiver();
    }


    public void setQrCodeView(QrCodeBean bean) {
        Log.d(TAG, "QRcodeFragment bean = " + bean);
        if (bean == null || TextUtils.isEmpty(bean.getData())) {
            Log.e(TAG, "qrUrl is Null ====");
            mIsNeedRequestQR = true;
            showQrCodeFail();
            return;
        }
        try {
            String decodeStr = new String(Base64.decode(bean.getData(), Base64.DEFAULT));
            mUrl = new JSONObject(decodeStr).optString("wx_url");
            mExspireTime = bean.getExspireTime();
            mCodePC = bean.getBindCode();
            createQrCode(mUrl);
            startInspireIn();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * @param qrUrl
     */
    private void createQrCode(final String qrUrl) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                Bitmap centerLogo = ResUtil.getCenterLogoBitmap(R.drawable.first_qrcode_logo);
                Bitmap qrImageBitmap = ResUtil.createQRImage(qrUrl, 670, 670, "1", centerLogo);
                if (mHandler != null) {
                    Message message = Message.obtain();
                    message.what = MSG_QRCODE_COMPLETE;
                    message.obj = qrImageBitmap;
                    mHandler.sendMessage(message);
                }
            }
        }).start();
    }

    private void showQrCodeSuccess(Bitmap bitmap) {
        Log.d(TAG, "showQrCodeSuccess ...");
        mRlLoading.setVisibility(View.GONE);
        mQrCodeRetry.setVisibility(View.GONE);
        mQrCode.setImageBitmap(bitmap);
        mQrCode.setVisibility(View.VISIBLE);
        mLbindCode.setVisibility(View.VISIBLE);
        mQrCodeLoading.setText(ResUtil.getString(R.string.wx_qr_code_loading_success));
        mBindCodeTx.setText(mCodePC);
        mSnNumberLayout.setVisibility(View.VISIBLE);
        mSnNumberTx.setText(SystemUtils.getLast6SystemSerialNo());
    }

    private void showQrCodeFail() {
        Log.d(TAG, "showQrCodeFail ...");
        mRlLoading.setVisibility(View.VISIBLE);
        mQrCodeRetry.setVisibility(View.VISIBLE);
        mQrCode.setVisibility(View.GONE);
        mLbindCode.setVisibility(View.GONE);
        mSnNumberLayout.setVisibility(View.GONE);
        mQrCodeLoading.setText(ResUtil.getString(R.string.wx_qr_code_loading_fail));
        mQrCodeRetry.setText(ResUtil.getString(R.string.wx_qr_code_load_retry));
    }

    private void showLoading() {
        Log.d(TAG, "showLoading ...");
        mRlLoading.setVisibility(View.VISIBLE);
        mQrCodeRetry.setVisibility(View.GONE);
        mQrCodeLoading.setVisibility(View.VISIBLE);
        mQrCodeLoading.setText(ResUtil.getString(R.string.wx_qr_code_loading));
        mQrCode.setVisibility(View.GONE);
        mLbindCode.setVisibility(View.GONE);
        mSnNumberLayout.setVisibility(View.GONE);
    }

    private void showExpiresInUI() {
        Log.d(TAG, "showExpiresInUI ...");
        mRlLoading.setVisibility(View.GONE);
        mLbindCode.setVisibility(View.GONE);
        mSnNumberLayout.setVisibility(View.GONE);
        mQrCodeLoading.setVisibility(View.VISIBLE);
        mQrCodeLoading.setText(ResUtil.getString(R.string.wx_qr_code_load_retry_again_des));
        mQrCode.setImageResource(R.drawable.first_qrcode_inspire_image);
        mQrCode.setVisibility(View.VISIBLE);
        mQrCodeRetry.setVisibility(View.VISIBLE);
        mQrCodeRetry.setTextColor(Color.WHITE);
        mQrCodeRetry.setText(ResUtil.getString(R.string.wx_qr_code_load_retry_again));
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.qr_code_load_retry:
                showLoading();
                requestQrCode();
                break;
            default:
                break;
        }
    }

    private static final int MSG_QRCODE_COMPLETE = 0x003;
    private static final int MSG_EXPIRES_IN_TIME = 0x004;

    class MyWeakHandler extends WeakHandler {

        public MyWeakHandler(Activity ref) {
            super(ref);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (conflict())
                return;
            switch (msg.what) {
                case MSG_EXPIRES_IN_TIME:
                    showExpiresInUI();
                    break;
                case MSG_QRCODE_COMPLETE:
                    Bitmap bitmap = (Bitmap) msg.obj;
                    showQrCodeSuccess(bitmap);
                    String systemLanguage = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_LANGUAGE);
                    Log.d(TAG, "systemLanguage = " + systemLanguage);
                    if (!hasPlay && systemLanguage.contains("zh")) {
                        hasPlay = true;
                        SkillManager.getInstance().speechPlayText(ResUtil.getString(R.string.wx_qr_code_tts));
                    }
                    break;
            }
        }
    }

    private void requestQrCode() {
        Log.d(TAG, "requestQrCode");
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_REQUEST_QR_CODE);
    }

    private void checkBindState() {
        Log.d(TAG, "checkBindState");
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_GET_REMOTE_BIND_STATUS);
    }

    /**
     * stop loop user's wx  bind state
     */
    private void stopLoopBindState() {
        if (mHandler != null) {
            mHandler.removeMessages(MSG_EXPIRES_IN_TIME);
        }
    }

    private void startInspireIn() {
        if (mHandler != null) {
            mHandler.sendEmptyMessageDelayed(MSG_EXPIRES_IN_TIME, mExspireTime * Definition.SECOND);
        }
        checkBindState();
    }

    @Override
    public void onConnectionChanged(int state) {
        Log.d(TAG, "onConnectionChanged ==== ");
        if (WifiUtils.isConnected(ApplicationWrapper.getContext())) {
            // 再次请求二维码
            if (mIsNeedRequestQR) {
                Log.d(TAG, "onConnectionChanged  msg_qrcode_request_again");
                ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_NET_CONNECTED);
            }
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mHandler != null) {
            mHandler.removeMessages(MSG_QRCODE_COMPLETE);
            mHandler.removeMessages(MSG_EXPIRES_IN_TIME);

        }
        hasPlay = false;
        NetManager.getInstance().unRegisterNetReceiver();
        Log.d(TAG, "QRcodeFragment onDestroy ");
    }

    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
        Log.d(TAG, "onMessage : " + type + ", " + message);

        switch (type) {
            case MSG_QR_CODE:
                QrCodeBean bean = mGson.fromJson(message, QrCodeBean.class);
                setQrCodeView(bean);
                break;
            case MSG_QR_BIND_SUCCESS:
                stopLoopBindState();
                break;
        }
    }

}
