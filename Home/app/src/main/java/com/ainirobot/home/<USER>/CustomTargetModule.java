package com.ainirobot.home.module;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.control.ModuleManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.utils.LocationUtil;

import org.json.JSONException;
import org.json.JSONObject;

public class CustomTargetModule extends BaseModule {
    private static final String TAG = CustomTargetModule.class.getSimpleName();

    private Context mContext;
    private Pose curPose;
    private int mReqId;
    private static CustomTargetModule sInstance;

    private CustomTargetModule() {
    }

    public static CustomTargetModule getInstance() {
        if (null == sInstance) {
            sInstance = new CustomTargetModule();
        }
        return sInstance;
    }

    public void init(Context mContext) {
        this.mContext = mContext;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.i(TAG, "reqId=" + reqId + "; intent=" + intent + "; params=" + params);
        this.mReqId = reqId;
        switch (intent) {
            case Definition.REQ_ENABLE_TARGET_CUSTOM:
                getCurrentPosition();
                UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_PUSH_MAP, null,
                        null);
                break;
        }
        return super.onNewSemantics(reqId, intent, text, params);
    }

    private void loadCurrentMap(int reqId) {
        SystemApi.getInstance().loadCurrentMap(reqId, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                Log.d(TAG, " loadCurrentMap : " + message + "   " + isRunning());
                if (!isRunning()) {
                    return;
                }
                if (result == Definition.RESULT_OK && TextUtils.equals(message, "succeed")) {
                    if (null != curPose) {
                        forceEstimate();
                    } else {
                        SystemApi.getInstance().onEnableTargetCustomFinish(Definition.RESULT_NEED_RELOCATION);
                        stop();
                    }
                } else {
                    SystemApi.getInstance().onEnableTargetCustomFinish(Definition.RESULT_NEED_RELOCATION);
                    stop();
                }
            }
        });

    }

    private void getCurrentPosition() {
        SystemApi.getInstance().isRobotEstimate(0, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, " isRobotEstimate onResult: " + message);
                if (result == Definition.RESULT_OK && "true".equals(message)) {
                    SystemApi.getInstance().getPosition(0, new CommandListener() {
                        @Override
                        public void onResult(int result, String message) {
                            Log.d(TAG, "getPosition message " + message);
                            if (result == Definition.RESULT_OK && !TextUtils.isEmpty(message)) {
                                JSONObject object = null;
                                try {
                                    object = new JSONObject(message);
                                    float px = (float) object.optDouble("px");
                                    float py = (float) object.optDouble("py");
                                    float theta = (float) object.optDouble("theta");
                                    curPose = new Pose();
                                    curPose.setX(px);
                                    curPose.setY(py);
                                    curPose.setTheta(theta);
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                    Log.d(TAG, "onResult: " + "pose parse fail");
                                }
                            }
                            loadCurrentMap(mReqId);
                        }
                    });

                } else {
                    loadCurrentMap(mReqId);
                }
            }
        });

    }

    private void forceEstimate() {

        Log.d(TAG, " forceEstimate : " + curPose.toString());
        String poseJson = mGson.toJson(curPose);
        curPose = null;
        SystemApi.getInstance().setForceEstimate(0, poseJson, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, " setForceEstimate onResult: " + message);
                if (result == Definition.RESULT_OK &&
                        !TextUtils.isEmpty(message) && message.equals(Definition.SUCCEED)) {
                    SystemApi.getInstance().onEnableTargetCustomFinish(Definition.RESULT_NOT_NEED_RELOCATION);
                    stop();
                } else {
                    SystemApi.getInstance().onEnableTargetCustomFinish(Definition.RESULT_NEED_RELOCATION);
                    stop();
                }
            }
        });

    }
}
