package com.ainirobot.home.ui.view;

import android.content.Context;
import android.support.annotation.Nullable;
import android.support.constraint.ConstraintLayout;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.home.R;

public class RelocationResultView extends FrameLayout {

    private Context mContext;
    public ImageButton mRepositionCancel;
    public TextView mRepositionCancelText;
    private ConstraintLayout failureLayout;
    public Button mFailSolutionBtn;

    public static final int SUCCESS = 1;
    public static final int CHARGE_PILE_FAIL = 2;
    private TextView resultTv;
    private ImageView resultImage;

    public RelocationResultView(Context context) {
        this(context, null);
    }

    public RelocationResultView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RelocationResultView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public RelocationResultView(Context context, @Nullable AttributeSet attrs, int defStyleAttr,
                                int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initSubViews(context);
    }

    private void initSubViews(Context context) {
        this.mContext = context;
        View view = LayoutInflater.from(mContext).inflate(R.layout.layout_qrcode_relocation_result, this);
        //成功layout
        resultImage = (ImageView)view.findViewById(R.id.iv_tip);
        resultTv = (TextView)view.findViewById(R.id.locateSuccessTV);

        //失败layout
        failureLayout = (ConstraintLayout)view.findViewById(R.id.charge_pile_locate_failure);
        mRepositionCancel = (ImageButton)view.findViewById(R.id.reposition_failure_cancel);
        mRepositionCancelText = (TextView)view.findViewById(R.id.reposition_cancel_text);
        mRepositionCancelText.setVisibility(INVISIBLE);
        view.findViewById(R.id.locate_failure_confirmBtn).setVisibility(INVISIBLE);
        mFailSolutionBtn = (Button)view.findViewById(R.id.locate_failure_see_solution);
    }

    public void updateResultView(int type){
        switch (type){
            case SUCCESS:
                resultImage.setVisibility(VISIBLE);
                resultTv.setVisibility(VISIBLE);
                failureLayout.setVisibility(GONE);
                break;
            case CHARGE_PILE_FAIL:
                resultImage.setVisibility(GONE);
                resultTv.setVisibility(GONE);
                failureLayout.setVisibility(VISIBLE);
                break;
        }
    }

}
