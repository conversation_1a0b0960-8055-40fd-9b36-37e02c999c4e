package com.ainirobot.home.utils;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.widget.EditText;

import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.R;
import com.ainirobot.home.bean.WakeWord;
import com.ainirobot.home.bean.WakeWordSpell;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.ui.view.CustomWakeWordDialog;
import com.ainirobot.home.ui.view.PolyPhoneChooseDialog;
import com.ainirobot.home.ui.view.ToastForDialog;

import java.util.ArrayList;
import java.util.List;

public class WakeUpWordsUtils {
    private static final String TAG = "WakeUpWordsUtils";

    private static WakeUpWordsUtils mInstance;
    private CustomWakeWordDialog mCustomWakeWordDialog = null;
    private PolyPhoneChooseDialog mPolyPhoneChooseDialog;

    private WakeUpWordsUtils() {
    }

    public static WakeUpWordsUtils getInstance() {
        if (mInstance == null) {
            mInstance = new WakeUpWordsUtils();
        }
        return mInstance;
    }

    public void showWakeUpWordSettingDialog(final Context activity) {
        Log.d(TAG, "show wake setting dialog");
        if (mCustomWakeWordDialog != null && mCustomWakeWordDialog.isShowing()){
            mCustomWakeWordDialog.dismiss();
            mCustomWakeWordDialog = null;
        }
        mCustomWakeWordDialog = new CustomWakeWordDialog(activity);
        String setWakeUpWord = SkillManager.getInstance().queryUserSetWakeUpWord();
        setWakeUpWord = setWakeUpWord == null ? "" : setWakeUpWord;
        mCustomWakeWordDialog.setPreviousWakeWords(setWakeUpWord)
                .setDialogClickListener(new CustomWakeWordDialog.ClickListener() {
                    @Override
                    public boolean onConfirmClick(String inputWakeWord) {
                        return queryCustomizeWakeupWord(inputWakeWord, activity);
                    }

                    @Override
                    public boolean onDeleteClick(EditText editText) {
                        if (SkillManager.getInstance().queryUserSetWakeUpWord() == null) {
                            showTips(ApplicationWrapper.getContext()
                                    .getString(R.string.no_deletable_word));
                            return false;
                        }

                        int closeCode = SkillManager.getInstance().closeCustomizeWakeUpWord();
                        if (closeCode == 0) {
                            showTips(ApplicationWrapper.getContext()
                                    .getString(R.string.delete_success));
                            return true;
                        } else {
                            showTips(ApplicationWrapper.getContext()
                                    .getString(R.string.delete_failture));
                            return false;
                        }
                    }
                }).show();
    }

    private boolean queryCustomizeWakeupWord(String inputWakeupWord, Context activity) {
        Log.d(TAG, "inputWakeupWord: " + inputWakeupWord);
        if (!checkInput(inputWakeupWord)) return false;

        ArrayList<WakeWord> wakeWords = new ArrayList<>();
        WakeWord wakeWord = null;
        for (int i = 0; i < inputWakeupWord.length(); i++) {
            wakeWord = new WakeWord();

            String word = String.valueOf(inputWakeupWord.charAt(i));
            String spell = SkillManager.getInstance().queryPinYinFromChinese(word);
            if (spell == null) {
                showTips(String.format(ApplicationWrapper.getContext()
                        .getString(R.string.not_support_chinese_placeholder), word));
                return false;
            }

            String[] split = spell.split(" ");
            for (int j = 0; j < split.length; j++) {
                WakeWordSpell wakeWordSpell = new WakeWordSpell();
                String pinYinMapping = SkillManager.getInstance().queryPinYinMappingTable(split[j]);

                wakeWordSpell.originalStr = split[j];
                wakeWordSpell.str =
                        pinYinMapping != null ? pinYinMapping : split[j].replace(":", "");
                wakeWordSpell.index = j;
                if (j == 0) wakeWordSpell.checked = true;

                wakeWord.spells.add(wakeWordSpell);
            }
            wakeWord.word = word;
            wakeWord.isPolyPhone = split.length > 1;

            wakeWords.add(wakeWord);
        }

        for (int i = 0; i < wakeWords.size(); i++) {
            if (wakeWords.get(i).isPolyPhone) {
                showChooseWordDialog(wakeWords, activity);
                return true;
            }
        }

        return setCustomizeWakeupWord(wakeWords);
    }

    private boolean checkInput(String inputWakeupWord) {
        if (TextUtils.isEmpty(inputWakeupWord)
                || inputWakeupWord.length() < 2
                || inputWakeupWord.length() > 6) {
            showTips(ApplicationWrapper.getContext()
                    .getString(R.string.wake_up_not_conform_rules));
            return false;
        }
        return true;
    }

    private void showTips(String tips) {
        new ToastForDialog(ApplicationWrapper.getContext(), tips);
    }

    private void showChooseWordDialog(final List<WakeWord> wakeWords, Context
            activity) {
        Log.d(TAG, "showChooseWordDialog: 选择多音字");
        if (mPolyPhoneChooseDialog == null) {
            mPolyPhoneChooseDialog = new PolyPhoneChooseDialog(activity);
        } else if (mPolyPhoneChooseDialog.isShowing()) {
            return;
        }

        mPolyPhoneChooseDialog.setWakeWords(wakeWords)
                .setDialogClickListener(new PolyPhoneChooseDialog.ClickListener() {
                    @Override
                    public boolean onConfirmClick(List<WakeWord> wakeWordsAfterChosen) {
                        Log.d(TAG, "showChooseWordDialog:onConfirmClick: 处理多音字选择结果");
                        ArrayList<WakeWord> wakeWordBeansCopy = new ArrayList<>();
                        for (int i = 0; i < wakeWordsAfterChosen.size(); i++) {
                            WakeWord wakeWord = wakeWordsAfterChosen.get(i);
                            if (wakeWord.isPolyPhone) {

                                List<WakeWordSpell> spells = wakeWord.spells;
                                for (int j = 0; j < spells.size(); j++) {
                                    if (spells.get(j).checked) {
                                        wakeWord.chooseSpell = spells.get(j).str;
                                        wakeWordBeansCopy.add(wakeWord);
                                        break;
                                    }
                                }

                                Log.d(TAG, "chooseSpell: " + wakeWord
                                        .chooseSpell);
                            } else {
                                wakeWordBeansCopy.add(wakeWord);
                            }
                        }
                        Log.d(TAG, "showChooseWordDialog:onConfirmClick: 设置自定义唤醒词");
                        return setCustomizeWakeupWord(wakeWordBeansCopy);
                    }

                    @Override
                    public void onCancelClick() {
                    }
                }).show();
    }

    private boolean setCustomizeWakeupWord(ArrayList<WakeWord> wakeWords) {
        StringBuilder word = new StringBuilder();
        StringBuilder spell = new StringBuilder();
        for (int i = 0; i < wakeWords.size(); i++) {
            WakeWord wakeWord = wakeWords.get(i);
            word.append(wakeWord.word);

            if (wakeWord.isPolyPhone) {
                for (int j = 0; j < wakeWord.spells.size(); j++) {
                    if (wakeWord.spells.get(j).checked) {
                        spell.append(wakeWord.spells.get(j).originalStr);
                        break;
                    }
                }
            } else {
                spell.append(wakeWord.spells.get(0).originalStr);
            }
            spell.append(" ");
        }

        int score = SkillManager.getInstance().getPinYinScore(spell.toString().trim(), " ");
        Log.d("WakeUpWordsUtils", "score: " + score);
        if (score < 2.5) {
            showTips(ApplicationWrapper.getContext()
                    .getString(R.string.wake_up_not_conform_rules));
            return false;
        }

        boolean isSetSuccessful = SkillManager.getInstance().setCustomizeWakeUpWord(word.toString
                (), spell.toString(), " ");
        if (isSetSuccessful) {
            showTips(ApplicationWrapper.getContext().getString(R.string.set_success));
            return true;
        } else {
            showTips(ApplicationWrapper.getContext().getString(R.string.set_failture));
            return false;
        }
    }
}
