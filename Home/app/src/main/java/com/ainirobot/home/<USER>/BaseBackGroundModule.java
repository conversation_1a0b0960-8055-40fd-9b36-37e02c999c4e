/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.home.module;

import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.util.Log;

import com.ainirobot.home.ModuleDef;

public class BaseBackGroundModule extends BaseModule {

    private static final int MESSAGE_REQUEST = 1;
    private static final int MESSAGE_CMD_RESPONSE = 2;
    private static final int MESSAGE_HW_REPORT = 3;
    private static final String TAG = "BaseBackGroundModule";

    private HandlerThread mThread;
    protected Handler mHandler;
    private String mThreadName = "";

    public BaseBackGroundModule(String threadName) {
        if (threadName != null) {
            this.mThreadName = threadName;
        }
        initThread(mThreadName);
    }

    private void initThread(String threadName) {
        Log.i(TAG, "thread init!");
        if (mThread == null || !mThread.isAlive()) {
            mThread = new HandlerThread(threadName);
        }
        if (mHandler != null) {
            return;
        }
        mThread.start();
        mHandler = new Handler(mThread.getLooper()) {
            Bundle bundle = null;

            @Override
            public void handleMessage(Message msg) {
                bundle = msg.getData();
                switch (msg.what) {
                    case MESSAGE_REQUEST:
                        if (bundle == null) {
                            return;
                        }
                        onThreadNewSemantics(bundle.getInt(ModuleDef.MSG_BUNDLE_ID), bundle.getString
                                (ModuleDef.MSG_BUNDLE_INTENT), bundle.getString(ModuleDef
                                .MSG_BUNDLE_TEXT), bundle.getString(ModuleDef.MSG_BUNDLE_PARAM));
                        return;

                    case MESSAGE_CMD_RESPONSE:
                        if (bundle == null) {
                            return;
                        }
                        onThreadCmdResponse(bundle.getInt("cmdId"), bundle.getString("cmdType"),
                                bundle.getString("params"));
                        return;

                    case MESSAGE_HW_REPORT:
                        if (bundle == null) {
                            return;
                        }
                        onThreadHWReport(bundle.getInt("hwFunction"), bundle.getString("cmdType"),
                                bundle.getString("params"));
                        return;

                    default:
                        break;
                }
                dispatchHandleMessage(msg);
            }
        };
    }

    protected void onThreadNewSemantics(int reqId, String intent, String text, String params) {
    }

    protected void onThreadCmdResponse(int cmdId, String cmdType, String params) {
    }

    protected void onThreadHWReport(int hwFunction, String cmdType, String params) {
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.i(TAG, "backGround: " + mThreadName + ", handlesemantics:" + intent);
        Bundle bundle = new Bundle();
        bundle.putInt(ModuleDef.MSG_BUNDLE_ID, reqId);
        bundle.putString(ModuleDef.MSG_BUNDLE_INTENT, intent);
        bundle.putString(ModuleDef.MSG_BUNDLE_TEXT, text);
        bundle.putString(ModuleDef.MSG_BUNDLE_PARAM, params);
        sendMessageToMyThread(MESSAGE_REQUEST, bundle);

        return true;
    }

    @Override
    public boolean onHWReport(int hwFunction, String cmdType, String params) {
        Bundle bundle = new Bundle();
        bundle.putInt("hwFunction", hwFunction);
        bundle.putString("cmdType", cmdType);
        bundle.putString("params", params);
        sendMessageToMyThread(MESSAGE_HW_REPORT, bundle);

        return super.onHWReport(hwFunction, cmdType, params);
    }

    @Override
    protected void onStop() {
        mHandler.removeMessages(MESSAGE_REQUEST);
        mHandler.removeMessages(MESSAGE_CMD_RESPONSE);
        mHandler.removeMessages(MESSAGE_HW_REPORT);
        mHandler.removeCallbacksAndMessages(null);
        mHandler = null;
        mThread.quit();
        super.onStop();
    }

    private void sendMessageToMyThread(int msgId, Bundle bundle) {
        if (mHandler == null) {
            initThread(mThreadName);
        }
        Message msg = Message.obtain();
        msg.what = msgId;
        msg.setData(bundle);
        mHandler.sendMessage(msg);
    }

    protected void dispatchHandleMessage(Message msg) {
    }

}
