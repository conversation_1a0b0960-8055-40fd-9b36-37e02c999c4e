package com.ainirobot.home.utils;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class VoiceContentUtil {
    private static List<String> voiceContents = new ArrayList<>();

    public static String getRandomVoiceContent(String ... voiceContent) {
        if (voiceContents != null && voiceContents.isEmpty()) {
            inflateVoiceContents(voiceContent);
        }

        int index = ((int) (System.currentTimeMillis() % voiceContents.size()));
        return voiceContents.get(index);
    }

    private static void inflateVoiceContents(String ... voiceContent) {
        if (voiceContent.length > 0) {
            voiceContents.addAll(Arrays.asList(voiceContent));
        }
    }
}
