package com.ainirobot.home.ota.utils;


import android.util.Log;

import java.io.File;
import java.io.IOException;

import ch.ethz.ssh2.Connection;
import ch.ethz.ssh2.SCPClient;

/**
 * <dependency>
 * <groupId>ch.ethz.ganymed</groupId>
 * <artifactId>ganymed-ssh2</artifactId>
 * <version>build210</version>
 * </dependency>
 */
public class Scp {
    private static final String TAG = Scp.class.getSimpleName();

    private static String IP = "************";
    private static int PORT = 22;
    private static String USER = "ubuntu";
    private static String PASSWORD = "ubuntu";//生成私钥的密码和登录密码，这两个共用这个密码
    private static Connection connection = new Connection(IP, PORT);
    private static String PRIVATEKEY = "/sdcard/ota/id_rsa";
    private static boolean usePassword = true;

    private static Scp instance;

    private Scp() {
    }

    public static synchronized Scp getInstance() {
        if (instance == null) {
            instance = new Scp();
        }
        return instance;
    }

    private boolean isAuthedWithPassword(String user, String password) {
        try {
            return connection.authenticateWithPassword(user, password);
        } catch (IOException e) {
            e.printStackTrace();
        }
        Log.e(TAG, "isAuthedWithPassword failed.");
        return false;
    }

    private boolean isAuthedWithPublicKey(String user, File privateKey, String password) {
        try {
            return connection.authenticateWithPublicKey(user, privateKey, password);
        } catch (IOException e) {
            e.printStackTrace();
        }
        Log.e(TAG, "isAuthedWithPassword failed.");
        return false;
    }

    public boolean isAuth() {
        if (usePassword) {
            return isAuthedWithPassword(USER, PASSWORD);
        } else {
            return isAuthedWithPublicKey(USER, new File(PRIVATEKEY), PASSWORD);
        }
    }

    public boolean getFile(String remoteFile, String path) {
        boolean result = false;
        try {
            connection.connect();
            boolean isAuthed = isAuth();
            if (isAuthed) {
                Log.d(TAG, "Authentication Success.");
                SCPClient scpClient = new SCPClient(connection);
                scpClient.get(remoteFile, path);
                Log.d(TAG, "scp get file done!");
                result = true;
            } else {
                Log.d(TAG, "Authentication Failed!");
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            connection.close();
        }

        return result;
    }

    public boolean putFile(String localFile, String remoteTargetDirectory) {
        boolean result = false;
        try {
            Log.d(TAG, "IP:" + IP + " PORT:" + PORT
                    + " USER:" + USER + " PASSWORD:"
                    + PASSWORD + " usePassword:" + usePassword);

            connection.connect(null, 25000, 25000);

            boolean isAuthed = isAuth();
            if (isAuthed) {
                Log.d(TAG, "Authentication Success.");
                SCPClient scpClient = connection.createSCPClient();
                scpClient.put(localFile, remoteTargetDirectory);
                Log.d(TAG, "scp send file done!");
                result = true;
            } else {
                Log.d(TAG, "Authentication Failed!");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            connection.close();
        }

        return result;
    }

}