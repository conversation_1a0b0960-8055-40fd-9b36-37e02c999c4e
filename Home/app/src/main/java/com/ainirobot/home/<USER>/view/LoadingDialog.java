package com.ainirobot.home.ui.view;

import android.app.AlertDialog;
import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.Display;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.home.R;

import static android.view.WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;

public class LoadingDialog extends AlertDialog {

    private static final String TAG = "LoadingDialog";
    private Context context;
    private AnimationDrawable animationDrawable;
    private String titleText;

    public LoadingDialog(Context context) {
        super(context);
        this.context = context;
        animationDrawable = (AnimationDrawable)context.getResources().getDrawable(R.drawable.anim_inspect_loading);
    }

    public LoadingDialog(Context context, String title) {
        this(context);
        this.titleText = title;
    }

    protected LoadingDialog(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    protected LoadingDialog(Context context, int themeResId) {
        super(context, themeResId);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        View view = LayoutInflater.from(context).inflate(R.layout.layout_loading, null);
        ImageView im_loading = (ImageView) view.findViewById(R.id.im_loading);
        im_loading.setImageDrawable(animationDrawable);
        TextView tvTitle = (TextView) view.findViewById(R.id.tv_loading);
        if (!TextUtils.isEmpty(titleText)) {
            tvTitle.setText(titleText);
        }
        setContentView(view);
        Window win = getWindow();
        win.setBackgroundDrawableResource(R.color.alpha_70);
        WindowManager m = win.getWindowManager();
        Display d = m.getDefaultDisplay();
        WindowManager.LayoutParams p = getWindow().getAttributes();
        p.height = d.getHeight() + 300 ;
        p.width = d.getWidth() + 300;
        getWindow().setAttributes(p);
        WindowManager.LayoutParams lp = win.getAttributes();
        lp.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
        lp.gravity = Gravity.CENTER;
        setCanceledOnTouchOutside(false);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
    }

    public void startLoading(){
        if (animationDrawable != null){
            animationDrawable.start();
        }
        this.show();
    }

    public void stopLoading(){
        if (animationDrawable != null){
            animationDrawable.stop();
        }
        this.dismiss();
    }
}
