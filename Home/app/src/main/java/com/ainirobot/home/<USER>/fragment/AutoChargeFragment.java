/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.home.ui.fragment;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Configuration;
import android.media.AudioManager;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.RemoteException;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.view.GifView;
import com.ainirobot.home.utils.LocationUtil;
import com.ainirobot.home.utils.ResType;

public class AutoChargeFragment extends BaseFragment {

    private static final String TAG = "AutoChargeFragment:Home";
    private TextView timeText;
    private TextView tvTitle;
    private TextView tvDes;
    private GifView goChargeGif;
    private GifView wireChargeGif;
    private ImageView chargeImgView;
    private ImageView lowPowerImage;
    private ImageView waitForCharge;
    private View cancelChargeBtn;
    private ImageView liftImage;

    private String mChargingType;
    private CountDownTimer mCountDownTimer;
    private int mOrientation;
    private boolean mIsChargingTypePile;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_auto_charge, null, false);
        timeText = (TextView) view.findViewById(R.id.auto_charge_btn);
        lowPowerImage = (ImageView) view.findViewById(R.id.low_power_complete);
        goChargeGif = (GifView) view.findViewById(R.id.charge_gif);
        wireChargeGif = (GifView) view.findViewById(R.id.wire_charge_gif);
        chargeImgView = (ImageView) view.findViewById(R.id.charge_bg);
        waitForCharge = (ImageView) view.findViewById(R.id.wait_for_charge);
        tvTitle = (TextView) view.findViewById(R.id.auto_charge_title);
        tvDes = (TextView) view.findViewById(R.id.auto_charge_title_des);
        cancelChargeBtn = (TextView) view.findViewById(R.id.cancel_charge_btn);
        cancelChargeBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_AUTO_CHARGE_CANCEL);
            }
        });
        liftImage = (ImageView) view.findViewById(R.id.img_lift);

        mOrientation = ApplicationWrapper.getContext().getResources().getConfiguration().orientation;
        Log.d(TAG, "onCreateView ..mOrientation: " + mOrientation);
        if (getArguments() != null) {
            mChargingType = getArguments().getString(ModuleDef.CHARGE_TYPE, Definition.CHARGE_SETTING);
            Log.i(TAG, "onCreateView entry = " + mChargingType);
        }
        checkChargingTypeIsPile();
        return view;
    }

    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Log.d(TAG, "onViewCreated entry = " + mChargingType);
        showStartChargeUI();
    }

    @Override
    public void onStart() {
        super.onStart();
        Log.d(TAG, "onStart ...");
    }

    @SuppressLint("StringFormatInvalid")
    private void showStartChargeUI() {
        waitForCharge.setVisibility(View.GONE);
        cancelChargeBtn.setVisibility(View.GONE);
        tvDes.setText("");
        liftImage.setVisibility(View.GONE);
        tvDes.setText("");
        tvTitle.setText(getGoingTitleText());
        if (mChargingType.equals(Definition.CHARGE_LOW)) {
            lowPowerImage.setVisibility(View.VISIBLE);
            tvTitle.setTextColor(getResources().getColor(R.color.color_ff713c));
            startTimer();
            if ((!ProductInfo.isDeliveryProduct() && !ProductInfo.isMeissaPlus()) || ((ProductInfo.isSaiphChargeIr()
                    || ProductInfo.isSaiphXdOrBigScreen() || ProductInfo.isSaiphPro()
                    || ProductInfo.isAlnilamPro()
                    || ProductInfo.isMeissaPlus()
                    || ProductInfo.isMeissa2())
                    && LocationUtil.getInstance().isChargingTypePile())) {
                tvTitle.setText(R.string.charge_lower_power_title);
                tvDes.setText(getGoingDesTextLower());
                timeText.setText(getResources().getString(R.string.charge_lower_power_auto_charge,
                        5 + ""));
            } else {
                tvTitle.setText(R.string.charge_lower_power_title_for_saiph);
                tvDes.setText(R.string.charge_lower_power_title_des_saiph);
                timeText.setText(R.string.charge_lower_power_would_shut_down);
                if (RobotSettingApi.getInstance().getRobotInt("robot_setting_low_battery_tts_switch") == 1) {
                    playLowTts();
                    DelayTask.submit("IntervalTts", new Runnable() {
                        @Override
                        public void run() {
                            playLowTts();
                        }
                    }, 20000, 20000);
                }
            }
        } else {
            if (mOrientation == Configuration.ORIENTATION_LANDSCAPE) {
                goChargeGif.setMovieResource(
                        ResType.GO_CHARGING.getResIdByType()
                );
                goChargeGif.setVisibility(View.VISIBLE);
            } else {
                chargeImgView.setImageResource(
                        ResType.GO_CHARGING.getResIdByType()
                );
                chargeImgView.setVisibility(View.VISIBLE);
            }
            if (Definition.CHARGE_SETTING_TIMING.equals(mChargingType)) {
                tvTitle.setText(R.string.charge_timing_title);
                tvDes.setTextColor(getResources().getColor(R.color.white));
                tvDes.setTextSize(17);
                tvDes.setText(getResources().getString(R.string.charge_timing_describe, 5 + ""));
                startTimer();
            } else {
                ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_ESTIMATE);
            }
        }
    }

    private void startTimer() {
        mCountDownTimer = new CountDownTimer(5000, 1000) {
            @SuppressLint("StringFormatInvalid")
            @Override
            public void onTick(long millisUntilFinished) {
                if (Definition.CHARGE_SETTING_TIMING.equals(mChargingType)) {
                    tvDes.setText(getResources().getString(R.string.charge_timing_describe,
                            millisUntilFinished / 1000 + ""));
                } else {
                    updataTimeTipByModle(getResources().getString(R.string.charge_lower_power_auto_charge,
                            millisUntilFinished / 1000 + ""));
                }
            }

            @Override
            public void onFinish() {
                Log.d(TAG, "finish count");
                ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_ESTIMATE);
                if (Definition.CHARGE_SETTING_TIMING.equals(mChargingType)) {
                    tvTitle.setText(getGoingTitleText());
                    tvDes.setText("");
                } else {
                    updataTimeTipByModle("");
                }
            }
        }.start();
    }

    /**
     * 招财豹不需要充电倒计时
     *
     * @param text
     */
    private void updataTimeTipByModle(String text) {
        if (!ProductInfo.isDeliveryProduct() && !ProductInfo.isMeissaPlus() || ((ProductInfo.isSaiphChargeIr()
                || ProductInfo.isSaiphXdOrBigScreen() || ProductInfo.isSaiphPro()
                || ProductInfo.isAlnilamPro()
                || ProductInfo.isMeissaPlus()
                || ProductInfo.isMeissa2())
                && LocationUtil.getInstance().isChargingTypePile())) {
            timeText.setText(text);
        }
    }

    private void playLowTts() {
        try {
            final AudioManager audioManager = (AudioManager) ApplicationWrapper.getContext()
                    .getSystemService(Context.AUDIO_SERVICE);
            ControlManager.getControlManager().setLastVolumeIndex(audioManager.getStreamVolume(AudioManager.STREAM_MUSIC));
            audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, 13, 0);
            SkillManager.getInstance().speechPlayText(getString(R.string.auto_charge_low_battery_tip),
                    new TextListener() {
                        @Override
                        public void onComplete() {
                            audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, ControlManager.getControlManager().getLastVolumeIndex(), 0);
                        }

                        @Override
                        public void onError() {
                            audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, ControlManager.getControlManager().getLastVolumeIndex(), 0);
                        }

                        @Override
                        public void onStop() {
                            audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, ControlManager.getControlManager().getLastVolumeIndex(), 0);
                        }
                    });
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.i(TAG, "onDestroy ... ");

        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
            mCountDownTimer = null;
        }
        // 退出充电时，播报打断
        SkillManager.getInstance().stopTTSOnly();
        DelayTask.cancel("IntervalTts");
//        SkillManager.getInstance().stopTTSOnly();
    }

    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
        switch (type) {
            case AUTO_CHARGE_GOING:
                break;
            case AUTO_CHARGE_FAIL:
                updateChargeUI(getFailedDesText());
                break;
            case AUTO_CHARGE_FAIL_NOT_EXIST_CHARGING_PILE_POSE:
            case AUTO_CHARGE_FAIL_NOT_EXIST_CHARGING_WIRE_POSE:
                updateChargeUI(message);
                break;
            case AUTO_CHARGE_WAIT_IN_AREA:
                updateChargeWaitUI(message);
                break;
            case AUTO_CHARGE_GO_CHARGE_POINT:
                showStartChargeUI();
                break;
            case DOEMANCY_START:
                if (mCountDownTimer != null) {
                    mCountDownTimer.cancel();
                    mCountDownTimer = null;
                }
                break;
            case AUTO_CHARGE_NAVI_ARRIVED:
                if (!mIsChargingTypePile) {
                    lowPowerImage.setVisibility(View.GONE);
                    goChargeGif.setVisibility(View.GONE);
                    chargeImgView.setVisibility(View.GONE);
                    wireChargeGif.setVisibility(View.VISIBLE);
                    wireChargeGif.setMovieResource(
                            ResType.GIF_WIRE_CHARGE_TIP.getResIdByType()
                    );
                    tvTitle.setText(getResources().getString(R.string.wire_charge_des_locate_tips));
                }
                break;
            case WAIT_LIFT:
            case GO_IN_LIFT:
            case IN_LIFT:
            case GO_OUT_LIFT:
                showLiftUI(type);
                break;
            case GO_OUT_LIFT_SUCCESS:
                showStartChargeUI();
                break;
            default:
                break;
        }
    }

    private void showLiftUI(UIController.MESSAGE_TYPE type) {
        switch (type) {
            case WAIT_LIFT:
                tvTitle.setText(R.string.wait_lift_title);
                tvDes.setText("");
                liftImage.setBackgroundResource(ResType.WAIT_LIFT.getResIdByType());
                break;
            case GO_IN_LIFT:
                tvTitle.setText(R.string.go_in_lift_title);
                tvDes.setText("");
                liftImage.setBackgroundResource(ResType.GO_IN_LIFT.getResIdByType());
                break;
            case IN_LIFT:
                tvTitle.setText(R.string.in_lift_title);
                tvDes.setText("");
                liftImage.setBackgroundResource(ResType.IN_LIFT.getResIdByType());
                break;
            case GO_OUT_LIFT:
                tvTitle.setText(R.string.go_out_lift_title);
                tvDes.setText("");
                liftImage.setBackgroundResource(ResType.GO_OUT_LIFT.getResIdByType());
                break;
        }
        lowPowerImage.setVisibility(View.GONE);
        goChargeGif.setVisibility(View.GONE);
        liftImage.setVisibility(View.VISIBLE);
    }

    private void updateChargeWaitUI(String message) {
        tvTitle.setText(R.string.charge_wait_in_area);
        tvDes.setText(message);
        if (mOrientation == Configuration.ORIENTATION_LANDSCAPE) {
            lowPowerImage.setVisibility(View.GONE);
            goChargeGif.setVisibility(View.GONE);
            waitForCharge.setImageResource(ResType.WAIT_FOR_CHARGE.getResIdByType());
            waitForCharge.setVisibility(View.VISIBLE);
            cancelChargeBtn.setVisibility(View.VISIBLE);
        } else {
        }
    }

    private void updateChargeUI(String failedDes) {
        tvTitle.setText(R.string.charge_fail_title);
        tvTitle.setTextColor(getResources().getColor(R.color.color_ff713c));
        tvDes.setText(failedDes);
        if (mOrientation == Configuration.ORIENTATION_LANDSCAPE) {
//                    goChargeGif.setMovieResource(R.drawable.go_to_charge_mini);
//                    goChargeGif.setVisibility(View.VISIBLE);
        } else {
            timeText.setText("");
            lowPowerImage.setVisibility(View.GONE);
            chargeImgView.setImageResource(R.drawable.go_charging_failed);
            chargeImgView.setVisibility(View.VISIBLE);
        }
    }

    private String getGoingTitleText() {
        return getResources().getString(mIsChargingTypePile ?
                R.string.charge_is_going_charge_pile : R.string.charge_is_going_locate_pile);
    }

    private String getGoingDesTextLower() {
        return getResources().getString(mIsChargingTypePile ?
                R.string.charge_lower_power_title_des : R.string.charge_lower_power_title_des_locate);
    }

    private String getFailedDesText() {
        return getResources().getString(mIsChargingTypePile ?
                R.string.charge_fail_des : R.string.charge_fail_des_locate);
    }

    private void checkChargingTypeIsPile() {
        mIsChargingTypePile = SystemApi.getInstance().hasChargeIR() &&
                LocationUtil.getInstance().isChargingTypePile();//有ChargeIR且选择桩充
        Log.i(TAG, "checkChargingTypeIsPile isChargingTypePile = " + mIsChargingTypePile);
    }
}
