/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.home.control;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.bean.Feature;
import com.ainirobot.home.bean.Semantics;
import com.ainirobot.home.data.DataOper;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RequestAnalysis {
    private static final String TAG = "RequestAnalysis:Home";

    private List<Semantics> mSemanticList;
    private List<String> mAllSupportedSemantics;
    private List<Feature> featureList;

    public RequestAnalysis(Context context) {
        mSemanticList = new ArrayList<>();
        mAllSupportedSemantics = new ArrayList<>();
        featureList = new ArrayList<>();
        initDataFromDB(context);
    }

    public Semantics getSupportSemantics(String semanticsText) {
        Log.i(TAG, "getSupportSemantics semanticsText: " + semanticsText);
        Semantics mSemanitcs = null;
        if (TextUtils.isEmpty(semanticsText)) {
            return null;
        }
        for (Semantics semantics : mSemanticList) {
            switch (semantics.getPatternType()) {
                case ModuleDef.PATTERN_TYPE_CMD:
                    if (semanticsText.equals(semantics.getPattern())) {
                        mSemanitcs = semantics;
                        return mSemanitcs;
                    }
                    break;
                case ModuleDef.PATTERN_TYPE_TEXT:
                    Matcher matcher = Pattern.compile(semantics.getPattern()).matcher
                            (semanticsText);
                    if (matcher.find()) {
                        mSemanitcs = semantics;
                        return mSemanitcs;
                    }
                    break;
            }
        }
        return mSemanitcs;
    }

    public List<String> getAllSupportSemantics() {
        return mAllSupportedSemantics;
    }

    private void initDataFromDB(Context context) {
        List<Semantics> list = DataOper.getAllSemantics(context);
        if (list == null || list.size() <= 0) {
            mSemanticList.clear();
        }
        mSemanticList = list;
        featureList = DataOper.getAllFeature(context);
        Log.i(TAG, "semantic list size:"+list.size()+" feature list size:"+featureList.size());
    }

    public boolean getIsBackground(int moduleId){
        for(Feature feature : featureList){
            if(feature.getFeatureId() == moduleId){
                return feature.isBackGround();
            }
        }
        Log.i(TAG, "get isBackGround,can not find module in data");
        return false;
    }

    public String getFeatureName(int moduleId) {
        for (Feature feature : featureList) {
            if (feature.getFeatureId() == moduleId) {
                return feature.getName();
            }
        }
        Log.i(TAG, "get feature name fail,FeatureId="+moduleId);
        return "";
    }
}
