/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.home.ui.view;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;

import com.ainirobot.home.R;

/**
 * OTA升级成功后强制恢复出厂设置对话框
 */
public class OTAFactoryResetDialog extends Dialog implements View.OnClickListener {
    private static final String TAG = "OTAFactoryResetDialog";

    private Context mContext;
    private OnFactoryResetListener mListener;
    private TextView mTitleView;
    private TextView mContentView;
    private Button mConfirmButton;
    private Button mCancelButton;
    private boolean mIsForceMandatory = false;

    public interface OnFactoryResetListener {
        void onConfirm();
        void onCancel();
    }

    public OTAFactoryResetDialog(Context context) {
        super(context, R.style.OTADialog);
        this.mContext = context;
        initDialog();
    }

    private void initDialog() {
        Window window = getWindow();
        if (window != null) {
            window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
            window.addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED);
            window.addFlags(WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_ota_factory_reset);
        initViews();
        initData();
    }

    private void initViews() {
        mTitleView = findViewById(R.id.tv_title);
        mContentView = findViewById(R.id.tv_content);
        mConfirmButton = findViewById(R.id.btn_confirm);
        mCancelButton = findViewById(R.id.btn_cancel);

        mConfirmButton.setOnClickListener(this);
        mCancelButton.setOnClickListener(this);
    }

    private void initData() {
        mTitleView.setText(R.string.ota_factory_reset_title);

        if (mIsForceMandatory) {
            // 强制模式：修改内容文字，隐藏取消按钮
            mContentView.setText(R.string.ota_factory_reset_mandatory_content);
            mConfirmButton.setText(R.string.ota_factory_reset_mandatory_confirm);
            mCancelButton.setVisibility(View.GONE);

            // 调整确认按钮的布局参数，使其占满整个宽度
            android.widget.LinearLayout.LayoutParams confirmParams =
                (android.widget.LinearLayout.LayoutParams) mConfirmButton.getLayoutParams();
            confirmParams.setMarginStart(0);
            confirmParams.weight = 1;
            mConfirmButton.setLayoutParams(confirmParams);
        } else {
            // 普通模式：显示正常内容和两个按钮
            mContentView.setText(R.string.ota_factory_reset_content);
            mConfirmButton.setText(R.string.ota_factory_reset_confirm);
            mCancelButton.setText(R.string.ota_factory_reset_cancel);
            mCancelButton.setVisibility(View.VISIBLE);

            // 恢复确认按钮的正常布局参数
            android.widget.LinearLayout.LayoutParams confirmParams =
                (android.widget.LinearLayout.LayoutParams) mConfirmButton.getLayoutParams();
            confirmParams.setMarginStart((int) (8 * mContext.getResources().getDisplayMetrics().density));
            confirmParams.weight = 1;
            mConfirmButton.setLayoutParams(confirmParams);
        }
    }

    public void setOnFactoryResetListener(OnFactoryResetListener listener) {
        this.mListener = listener;
    }

    /**
     * 设置为强制模式
     * 在强制模式下，用户必须选择恢复出厂设置，不能取消
     */
    public void setForceMandatory(boolean forceMandatory) {
        this.mIsForceMandatory = forceMandatory;
        if (mContentView != null) {
            // 如果视图已经初始化，重新设置数据
            initData();
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.btn_confirm) {
            Log.i(TAG, "User clicked confirm button");
            if (mListener != null) {
                mListener.onConfirm();
            }
            dismiss();
        } else if (id == R.id.btn_cancel) {
            if (mIsForceMandatory) {
                Log.w(TAG, "User tried to cancel in mandatory mode - not allowed");
                // 在强制模式下，不允许取消，调用onCancel会重新显示对话框
                if (mListener != null) {
                    mListener.onCancel();
                }
                // 不调用dismiss()，保持对话框显示
            } else {
                Log.i(TAG, "User clicked cancel button");
                if (mListener != null) {
                    mListener.onCancel();
                }
                dismiss();
            }
        }
    }

    @Override
    public void show() {
        Log.i(TAG, "Showing OTA factory reset dialog");
        super.show();
    }

    @Override
    public void dismiss() {
        Log.i(TAG, "Dismissing OTA factory reset dialog");
        super.dismiss();
    }

    @Override
    public void onBackPressed() {
        if (mIsForceMandatory) {
            Log.w(TAG, "Back key pressed in mandatory mode - not allowed");
            // 在强制模式下，禁用返回键
            return;
        }
        super.onBackPressed();
    }
}
