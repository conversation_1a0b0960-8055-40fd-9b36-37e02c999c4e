package com.ainirobot.home.ota.httpclient;

import android.text.TextUtils;

import com.ainirobot.home.ota.httpclient.body.RequestBody;

import java.util.HashMap;

public final class Request {
    private final String mUrl;
    private final String mMethod;
    private final String mSessionId;
    private HashMap<String,String> mHeaders;
    private HashMap<String,String> mUrlParams;
    private final RequestBody mBody;
    private final Object mTag;

    private Request(Builder builder) {
        this.mUrl = builder.url;
        this.mMethod = builder.method;
        this.mSessionId = builder.session;
        this.mHeaders = builder.headers;
        this.mUrlParams = builder.urlParams;
        this.mBody = builder.body;
        this.mTag = (builder.tag != null) ? builder.tag : this;
    }

    public String getUrl(){
        return mUrl;
    }

    public String getMethod(){
        return mMethod;
    }

    public String getSessionId(){
        return mSessionId;
    }

    public HashMap<String,String> getUrlParams(){
        return mUrlParams;
    }

    public HashMap<String,String> getHeaders(){
        return mHeaders;
    }

    public RequestBody getBody(){
        return mBody;
    }

    public Object getTag(){
        return mTag;
    }

    public static class Builder {
        private static final String GET = "GET";
        public static final String POST = "POST";

        private String url;
        private String method;
        private String session;
        private HashMap<String, String> headers;
        private HashMap<String, String> urlParams;
        private RequestBody body;
        private Object tag;

        public Builder() {
            this.method = GET;
            headers = new HashMap<>();
            urlParams = new HashMap<>();
        }

        public Builder url(String url) {
            this.url = url;
            return this;
        }

        public Builder urlParam(String key, String value) {
            urlParams.put(key, value);
            return this;
        }

        public Builder session(String session) {
            this.session = session;
            return this;
        }

        public Builder header(String key, String value) {
            headers.put(key, value);
            return this;
        }

        public Builder get() {
            return method(GET, null);
        }

        public Builder post(RequestBody body) {
            return method(POST, body);
        }

        private Builder method(String method, RequestBody body) {
            if (TextUtils.isEmpty(method)) {
                throw new IllegalArgumentException("method is empty");
            }

            this.method = method;
            this.body = body;
            return this;
        }

        /**
         * It can be used later to cancel the request
         */
        public Builder tag(Object tag) {
            this.tag = tag;
            return this;
        }

        public Request build(){
            return new Request(this);
        }

    }
}
