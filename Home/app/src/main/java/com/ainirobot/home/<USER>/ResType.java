package com.ainirobot.home.utils;

import android.content.res.Configuration;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.R;

/**
 * Data: 2022/10/27 11:06
 */
public enum ResType {
    /**
     * 海外版开机加载页面logo
     */
    INSPECT_OVERSEAS {
        @Override
        public int getLandScapeRes() {
            return R.drawable.inspect_title;
        }


        @Override
        public int getDefaultRes() {
            return R.drawable.inspect_title;
        }
    },
    /**
     * 默认开机加载页logo
     */
    INSPECT {
        @Override
        public int getLandScapeRes() {
            return R.drawable.inspect_title;
        }


        @Override
        public int getDefaultRes() {
            return R.drawable.inspect_title;
        }
    },
    PRODUCT_ICON {
        @Override
        public int getSaiphProRes() {
            return R.drawable.first_bind_logo_pro;
        }

        @Override
        public int getMiniRes() {
            return getDefaultRes();
        }

        @Override
        public int getMeissa2Res() {
            return R.drawable.first_bind_logo_meissa2;
        }

        @Override
        public int getDeliveryRes() {
            return getSaiphProRes();
        }

        @Override
        public int getDefaultRes() {
            return R.drawable.first_bind_logo1;
        }
    },
    /**
     * 急停被按下 gif
     */
    GIF_EMERGENCY {
        @Override
        public int getMiniRes() {
            //mini是gif
            return R.drawable.emergency_press_mini;
        }

        @Override
        public int getSlimRes() {
            return ProductInfo.isSlimDoor() ? R.drawable.dslim_img_emergency : R.drawable.slim_img_emergency;
        }

        @Override
        public int getSaiphProRes() {
            return R.drawable.luckipro_emergency_anim;
        }

        @Override
        public int getLandScapeRes() {
            return R.drawable.luckipro_emergency_anim;
        }

        @Override
        public int getDeliveryRes() {
            return R.drawable.lucki_emergency_anim;
        }

        @Override
        public int getMeissaPlusRes() {
            return R.drawable.lucki_emergency_anim;
        }

        @Override
        public int getDefaultRes() {
            return R.drawable.emergency_press;
        }
    },
    WAIT_FOR_CHARGE {
        @Override
        public int getMiniRes() {
            return R.drawable.waitforcharger_mini;
        }

        @Override
        public int getMeissa2Res() {
            return R.drawable.waitforcharger_nova;
        }

        @Override
        public int getSaiphProRes() {
            return R.drawable.waitforcharger_pro;
        }

        @Override
        public int getLandScapeRes() {
            return getMiniRes();
        }

        @Override
        public int getCarryRes() {
            return R.drawable.waitforcharger_carry;
        }

        @Override
        public int getDeliveryRes() {
            return R.drawable.waitforcharger_lucki;
        }

        @Override
        public int getMeissaPlusRes() {
            return R.drawable.waitforcharger_plus;
        }
        @Override
        public int getDefaultRes() {
            return R.drawable.emergency_press;
        }
    },
    /**
     * 去充电桩 gif
     */
    GO_CHARGING {
        @Override
        public int getMiniRes() {
            //mini是gif
            return R.drawable.go_to_charge_mini;
        }

        @Override
        public int getMeissa2Res() {
            return R.drawable.go_to_charge_meissa2;
        }

        @Override
        public int getSaiphProRes() {
            //pro webp格式动画
            return ProductInfo.isAlnilamPro() ? R.drawable.autodoor_backtocharge : R.drawable.go_to_charge_pro;
        }

        @Override
        public int getLandScapeRes() {
            return getMiniRes();
        }

        @Override
        public int getCarryRes() {
            return R.drawable.carry_backtocharge_00000_ispt;
        }

        @Override
        public int getSlimRes() {
            return ProductInfo.isSlimDoor() ? R.drawable.dslim_backtocharge : R.drawable.slim_backtocharge;
        }

        @Override
        public int getDeliveryRes() {
            return R.drawable.lucki_backtocharge;
        }

        @Override
        public int getMeissaPlusRes() {
            return R.drawable.plus_backtocharge;
        }

        @Override
        public int getDefaultRes() {
            //默认是图片
            return R.drawable.go_charging;
        }
    },
    /**
     * 到达充电点，需要手动插充电线 gif
     */
    GIF_WIRE_CHARGE_TIP {

        @Override
        public int getSaiphProRes() {
            return ProductInfo.isAlnilamPro() ? R.drawable.autodoor_cable : R.drawable.wire_charge_tips_pro;
        }

        @Override
        public int getSlimRes() {
            return ProductInfo.isSlimDoor() ? R.drawable.dslim_cable : R.drawable.slim_cable;
        }

        @Override
        public int getMeissa2Res() {
            return R.drawable.wire_charge_tips_meissa2;
        }

        @Override
        public int getDeliveryRes() {
            return R.drawable.wire_charge_tips_saiph;
        }

        @Override
        public int getDefaultRes() {
            return R.drawable.wire_charge_tips;
        }
    },
    /**
     * 设置充电桩成功 gif
     */
    GIF_SET_CHARGE_SUC {
        @Override
        public int getMiniRes() {
            return R.drawable.push_to_charge_pile_mini;
        }

        @Override
        public int getSaiphProRes() {
            return ProductInfo.isAlnilamPro() ? R.drawable.autodoor_oncharging_wholebody : R.drawable.push_to_charge_pile_pro;
        }

        @Override
        public int getSlimRes() {
            return ProductInfo.isSlimDoor() ? R.drawable.dslim_oncharging_wholebody : R.drawable.slim_oncharging_wholebody;
        }

        @Override
        public int getLandScapeRes() {
            return R.drawable.push_to_charge_pile_mini;
        }

        @Override
        public int getDefaultRes() {
            return R.drawable.set_charge_success;
        }
    },
    /**
     * 推到充电桩上 gif
     */
    GIF_PUSH_TO_CHARGE_PILE {
        @Override
        public int getMiniRes() {
            return R.drawable.push_to_charge_pile_mini;
        }

        @Override
        public int getSaiphProRes() {
            return ProductInfo.isAlnilamPro() ? R.drawable.autodoor_push_to_charge : R.drawable.push_to_charge_pile_pro;
        }

        @Override
        public int getSlimRes() {
            return ProductInfo.isSlimDoor() ? R.drawable.dslim_push_to_charge : R.drawable.slim_push_to_charge;
        }

        @Override
        public int getMeissa2Res() {
            return R.drawable.push_to_charge_pile_meissa2;
        }

        @Override
        public int getLandScapeRes() {
            return R.drawable.push_to_charge_pile_mini;
        }

        @Override
        public int getDeliveryRes() {
            return R.drawable.push_to_charge_saiph;
        }

        @Override
        public int getCarryRes() {
            return R.drawable.carry_pushtocharge_00000_ispt;
        }

        @Override
        public int getMeissaPlusRes() {
            return R.drawable.push_to_charge_saiph;
        }

        @Override
        public int getDefaultRes() {
            return R.drawable.set_charge_start;
        }
    },
    /**
     * 离开充电桩 png
     */
    PNG_LEAVING_CHARGE_PILE {
        @Override
        public int getCarryRes() {
            return R.drawable.carry_img_offcharger;
        }

        @Override
        public int getSaiphProRes() {
            return ProductInfo.isAlnilamPro() ? R.drawable.autodoor_img_offcharger : R.drawable.pro_img_offcharger;
        }
        @Override
        public int getSlimRes() {
            return ProductInfo.isSlimDoor() ? R.drawable.dslim_img_offcharger : R.drawable.slim_img_offcharger;
        }
        @Override
        public int getMiniRes() {
            return R.drawable.mini_img_offcharger;
        }

        @Override
        public int getMeissa2Res() {
            return R.drawable.nova_img_offcharger;
        }

        @Override
        public int getDeliveryRes() {
            return R.drawable.lucki_img_offcharger;
        }

        @Override
        public int getMeissaPlusRes() {
            return R.drawable.luckiplus_img_offcharger;
        }

        @Override
        public int getDefaultRes() {
            return R.drawable.mini_img_offcharger;
        }
    },

    WAIT_LIFT {
        @Override
        public int getSaiphProRes() {
            return R.drawable.img_lift_waiting;
        }

        @Override
        public int getSlimRes() {
            return ProductInfo.isSlimDoor() ? R.drawable.img_lift_waiting_dslim : R.drawable.img_lift_waiting;
        }

        @Override
        public int getDefaultRes() {
            return R.drawable.img_lift_waiting;
        }
    },
    GO_IN_LIFT {
        @Override
        public int getSaiphProRes() {
            return R.drawable.img_lift_go_in;
        }

        @Override
        public int getSlimRes() {
            return ProductInfo.isSlimDoor() ? R.drawable.img_lift_go_in_dslim : R.drawable.img_lift_go_in;
        }

        @Override
        public int getDefaultRes() {
            return R.drawable.img_lift_go_in;
        }
    },
    IN_LIFT {
        @Override
        public int getSaiphProRes() {
            return R.drawable.img_lift_in;
        }

        @Override
        public int getSlimRes() {
            return ProductInfo.isSlimDoor() ? R.drawable.img_lift_in_dslim : R.drawable.img_lift_in;
        }

        @Override
        public int getDefaultRes() {
            return R.drawable.img_lift_in;
        }
    },
    GO_OUT_LIFT {
        @Override
        public int getSaiphProRes() {
            return R.drawable.img_lift_go_out;
        }

        @Override
        public int getSlimRes() {
            return ProductInfo.isSlimDoor() ? R.drawable.img_lift_go_out_dslim : R.drawable.img_lift_go_out;
        }

        @Override
        public int getDefaultRes() {
            return R.drawable.img_lift_go_out;
        }
    },
    SHUTDOWN_ROBOT {
        @Override
        public int getLandScapeRes() {
            return R.drawable.shutdown_img_warn;
        }

        @Override
        public int getMeissaPlusRes() {
            return R.drawable.shutdown_robot_plus;
        }

        @Override
        public int getDefaultRes() {
            return R.drawable.shutdown_robot;
        }
    };

    /**
     * mini资源
     */
    public int getMiniRes() {
        return getLandScapeRes();
    }

    /**
     * CarryBot资源
     */
    public int getCarryRes() {
        return getLandScapeRes();
    }

    public int getSlimRes() {
        return getLandScapeRes();
    }

    /**
     * 小秘2资源，默认使用横屏资源
     */
    public int getMeissa2Res() {
        return getLandScapeRes();
    }
    /**
     * 招财豹pro资源
     */
    public int getSaiphProRes() {
        return getLandScapeRes();
    }

    /**
     * 横屏资源,默认使用default资源
     */
    public int getLandScapeRes() {
        return getDefaultRes();
    }

    /**
     * 递送项目资源，默认使用default资源
     */
    public int getDeliveryRes() {
        return getDefaultRes();
    }

    /**
     * 小秘Plus项目资源，默认使用default资源
     */
    public int getMeissaPlusRes() {
        return getDefaultRes();
    }

    /**
     * default资源
     */
    public abstract int getDefaultRes();

    /**
     * 是否为横屏
     */
    public static boolean isLandScape() {
        Configuration configuration = ApplicationWrapper.getContext().getResources().getConfiguration();
        return configuration.orientation == Configuration.ORIENTATION_LANDSCAPE;
    }

    private boolean isLandScape = isLandScape();

    /**
     * 根据类型，获得适合当前设备的资源
     *
     * @return 符合当前设备类型的资源id
     */
    public int getResIdByType() {
        //Slim
        if(ProductInfo.isSlimProduct()){
            return getSlimRes();
        }
        //Carry项目
        if(ProductInfo.isCarryProduct()){
            return getCarryRes();
        }

        //招财豹pro项目
        if (ProductInfo.isSaiphPro() || ProductInfo.isAlnilamPro()) {
            return getSaiphProRes();
        }

        //mini项目
        if (ProductInfo.isMiniProduct()) {
            return getMiniRes();
        }

        //小秘2
        if (ProductInfo.isMeissa2()) {
            return getMeissa2Res();
        }

        if (ProductInfo.isMeissaPlus()) {
            return getMeissaPlusRes();
        }

        //横屏使用资源
        if (isLandScape) {
            return getLandScapeRes();
        }

        //递送项目资源
        if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus()) {
            return getDeliveryRes();
        }

        return getDefaultRes();
    }
}