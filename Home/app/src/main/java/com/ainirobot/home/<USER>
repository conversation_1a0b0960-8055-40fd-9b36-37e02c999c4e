/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.home;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;

import com.ainirobot.home.service.ModuleService;
import com.ainirobot.home.utils.SystemUtils;

public class SystemActivity extends BaseActivity {

    private static final String TAG = "SystemActivity:Home";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if (!SystemUtils.isServiceRunning(ModuleService.class, ApplicationWrapper.getContext())) {
            Intent intent = new Intent(this, LaunchActivity.class);
            startService(intent);
            this.finish();
        } else {
            switchFragment(getIntent());
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        Log.d(TAG, "On new intent");
        switchFragment(intent);
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    public void moveToHome() {
        Log.d(TAG, "Move to home");
        this.finish();
    }

    @Override
    public void onBackPressed() {

    }
}



