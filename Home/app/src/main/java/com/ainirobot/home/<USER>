/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.home;


import android.app.Application;
import android.content.Context;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.upload.bi.BiReport;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.fallback.blackbug.BlackBugCheck;
import com.ainirobot.home.ota.constants.OtaConstants;
import com.ainirobot.home.ota.utils.Preferences;
import com.ainirobot.home.utils.LocalUtils;
import com.ainirobot.home.utils.SystemUtils;
import com.liulishuo.filedownloader.FileDownloader;
import com.liulishuo.filedownloader.connection.FileDownloadUrlConnection;

import java.net.Proxy;

public class ApplicationWrapper extends Application {

    private static final String TAG = "ApplicationWrapper:Home";
    private static ApplicationWrapper sWrapper;
    private static boolean mIsAlreadyOpenAsr = false;

    @Override
    public void onCreate() {
        sWrapper = this;
        ControlManager.initialize(this);
        super.onCreate();

        if (SystemUtils.isMainProcess(this)) {
            init();
            BlackBugCheck.getInstance(this).startCheck();
        }

        Preferences.initialize(this);
        Preferences.getInstance().setTx1PackagePath(OtaConstants.HEAD_INSTALL_PATH);
        initFileDownLoader();
    }

    private void init() {
        LocalUtils.storage2SystemSettings(this,
                Definition.VERSION_ROBOT_OS, getString(R.string.apk_version));
        BiReport.init();
    }

    public static Context getContext() {
        return sWrapper.getApplicationContext();
    }

    public static void setIsAlreadyOpenAsr(boolean openState) {
        mIsAlreadyOpenAsr = openState;
    }

    public static boolean getIsAlreadyOpenAsr() {
        return mIsAlreadyOpenAsr;
    }

    /**
     * ota下載 初始化
     */
    private void initFileDownLoader() {
        FileDownloader.setupOnApplicationOnCreate(this)
                .connectionCreator(new FileDownloadUrlConnection
                        .Creator(new FileDownloadUrlConnection.Configuration()
                        .connectTimeout(15_000) // set connection timeout.
                        .readTimeout(15_000) // set read timeout.
                        .proxy(Proxy.NO_PROXY) // set proxy
                ))
                .commit();
    }

}
