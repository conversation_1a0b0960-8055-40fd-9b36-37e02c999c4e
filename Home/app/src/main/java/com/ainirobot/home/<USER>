/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.home;


import static android.view.WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN;
import static android.view.WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
import static android.view.WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.database.ContentObserver;
import android.graphics.PixelFormat;
import android.media.AudioManager;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Message;
import android.provider.Settings;
import android.support.v4.app.Fragment;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.WindowManager;
import android.widget.FrameLayout;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.home.bi.BiModuleChangeNotifier;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.report.TaskReport;
import com.ainirobot.home.service.ModuleService;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.UIController.FRAGMENT_TYPE;
import com.ainirobot.home.ui.fragment.BaseFragment;
import com.ainirobot.home.utils.AudioCalUtils;
import com.ainirobot.home.utils.SystemUtils;
import com.ainirobot.home.utils.ToastUtil;

import java.util.Timer;
import java.util.TimerTask;

public class LaunchActivity extends BaseActivity {

    private static final String TAG = "LaunchActivity:Home";

    private WindowManager wm;
    private WindowManager.LayoutParams wmParams;
    private boolean isDefaultApp = false;
    private boolean isNewIntent = false;
    private SettingsContentObserver mSettingsContentObserver;
    private FrameLayout fl_root;

    private static final String NORMAL_ROBOT = "0";
    private static final String REPAIR_ROBOT = "1";

    //from AudioManager.VOLUME_CHANGED_ACTION
    public static final String VOLUME_CHANGED_ACTION = "android.media.VOLUME_CHANGED_ACTION";
    public static final String INTENT_INSPECTION_SKIP = "intent_inspection_skip";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        fl_root = (FrameLayout) findViewById(R.id.fl_root);
        initRootLayoutBg();
        initWindowsParamForMute();
        mSettingsContentObserver = new SettingsContentObserver(LaunchActivity.this, new Handler());
        if (ProductInfo.isMiniProduct() || ProductInfo.isMeissa2()) {
            registerReceiver(mBroadcastReceiver, new IntentFilter(VOLUME_CHANGED_ACTION));
        } else {
            registerVolumeChangeReceiver();
        }

        registerReceiver(mBroadcastReceiver, new IntentFilter(INTENT_INSPECTION_SKIP));

        if (!SystemUtils.isServiceRunning(ModuleService.class, ApplicationWrapper.getContext())) {
            Intent intent = new Intent(this, ModuleService.class);
            startService(intent);
            setFragment(FRAGMENT_TYPE.FRAGMENT_INSPECT.getFragment(), null);
            Log.d(TAG, "Launch activity on create : service not running");
        } else {
            Log.e(TAG, "Launch activity on create : wait service connect");
            setFragment(FRAGMENT_TYPE.FRAGMENT_LAUNCHER.getFragment(), null);
        }
    }

    private void initRootLayoutBg() {
        String robot_sample = Settings.Global.getString(getContentResolver(), "robot_sample");
        Log.i(TAG, "initRootLayoutBg: robot_sample = " + robot_sample);
        if (!TextUtils.isEmpty(robot_sample)) {
            switch (robot_sample) {
                case NORMAL_ROBOT:
                    fl_root.setBackgroundResource(R.drawable.bg);
                    break;
                case REPAIR_ROBOT:
                    fl_root.setBackgroundResource(R.drawable.fanxiuji_bg1_img);
                    break;
                default:
                    fl_root.setBackgroundResource(R.drawable.bg);
                    break;
            }
        } else {
            fl_root.setBackgroundResource(R.drawable.bg);
        }
    }

    final BroadcastReceiver mBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null) {
                if (VOLUME_CHANGED_ACTION.equals(intent.getAction())) {
                    mSettingsContentObserver.onChange(false);
                }else if(INTENT_INSPECTION_SKIP.equals(intent.getAction())){
                    onInspectionSkip();
                }
            }
        }
    };

    private void onInspectionSkip(){
        Log.d(TAG,"onInspectionSkip");

        this.startDefaultApp();
        finishInspection(Definition.INSPECTION_FINISHED_FIRST);

        new CountDownTimer(1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
            }

            @Override
            public void onFinish() {
                BaseFragment launcher = FRAGMENT_TYPE.FRAGMENT_LAUNCHER.getFragment();
                setFragment(launcher, null);
            }
        }.start();
    }

    private void initWindowsParamForMute() {
        wm = getWindowManager();
        wmParams = new WindowManager.LayoutParams();
        wmParams.type = WindowManager.LayoutParams.TYPE_APPLICATION;
        wmParams.format = PixelFormat.RGBA_8888;
        wmParams.flags = FLAG_LAYOUT_IN_SCREEN | FLAG_NOT_TOUCH_MODAL | FLAG_NOT_FOCUSABLE;
        wmParams.gravity = Gravity.CENTER_HORIZONTAL | Gravity.TOP;
        wmParams.width = 100;
        wmParams.height = 100;
    }

    private void registerVolumeChangeReceiver() {
        mSettingsContentObserver = new SettingsContentObserver(this, new Handler());
        getApplicationContext().getContentResolver().registerContentObserver
                (android.provider.Settings.System.CONTENT_URI, true, mSettingsContentObserver);
    }

    private void unregisterVolumeChangeReceiver() {
        getApplicationContext().getContentResolver().unregisterContentObserver(mSettingsContentObserver);
    }

    public class SettingsContentObserver extends ContentObserver {
        Context context;
        AudioManager manager;
        int lastMusicVol;

        public SettingsContentObserver(Context c, Handler handler) {
            super(handler);
            context = c;
            manager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
            lastMusicVol = manager.getStreamVolume(AudioManager.STREAM_MUSIC);
        }

        @Override
        public boolean deliverSelfNotifications() {
            return super.deliverSelfNotifications();
        }

        @Override
        public void onChange(boolean selfChange) {
            super.onChange(selfChange);
            int curMusicVol = manager.getStreamVolume(AudioManager.STREAM_MUSIC);
            boolean change = (lastMusicVol != curMusicVol);
            if (change) {
                Log.d(TAG, "music vol change from " + lastMusicVol + " to " + curMusicVol);
                lastMusicVol = curMusicVol;
                if ((!ProductInfo.isDeliveryProduct() && !ProductInfo.isMeissaPlus() && !ProductInfo.isMeissa2()) &&
                        RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_CLOSE_VOLUME_CHANGE_TOAST) != 1) {
                    ToastUtil.show(getApplicationContext(), AudioCalUtils.getCurAudioIndex(
                            manager.getStreamVolume(AudioManager.STREAM_MUSIC)));
                }
            }
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        isNewIntent = true;
        Log.d(TAG, "onNewIntent  :" + intent);
        super.onNewIntent(intent);
        setIntent(intent);

        if (Intent.ACTION_MAIN.equals(intent.getAction())) {
            if (!UIController.getInstance().isCurrentActivity(this)) {
                Log.d(TAG, "Not current activity");
                UIController.getInstance().showCurrentFragment();
            }

            if (SystemApi.getInstance().isApiConnectedService()) {
                Log.d(TAG, "Stop app control : start home");
                SystemApi.getInstance().stopAppControl();
            }
        } else {
            if (!UIController.getInstance().isCurrentActivity(this)) {
                Log.d(TAG, "Not current activity");
                UIController.getInstance().showCurrentFragment();
            } else {
                switchFragment(intent);
            }
        }
    }

    @Override
    protected void onResume() {
        Log.i(TAG, "onResume: ");
        super.onResume();
        //bi business
        BiModuleChangeNotifier.enterDefaultModule();
        //1.避免ModuleApp的sandbox 进程在后台died后,显示Launcher. 避免此时system module 逻辑还未执行显示, 而显示了Launcher.
        int curModule = ControlManager.getControlManager().getCurrentModule();
        Log.d(TAG, "onResume isDefaultApp:" + isDefaultApp + ", curModule:" + curModule + ",isCurrentActivity:" + UIController.getInstance().isCurrentActivity(this));
        if ((isDefaultApp && UIController.getInstance().isCurrentActivity(this))) {
            Log.d(TAG, "Need restart default app");
            startDefaultApp();
        }

        if (SystemApi.getInstance().isApiConnectedService() && curModule == ModuleDef.FEATURE_LAUNCHER) {
            Log.d(TAG, "report launcher module");
            TaskReport.getInstance().reportModuleChange(ModuleDef.FEATURE_LAUNCHER);
        }

        Fragment oldFragment = getSupportFragmentManager().findFragmentById(R.id.activity_main);
        Log.d(TAG, "Launch activity onResume : fragment is " + oldFragment);
        if (oldFragment == null) {
            BaseFragment launcher = FRAGMENT_TYPE.FRAGMENT_LAUNCHER.getFragment();
            setFragment(launcher, null);
        }
        isNewIntent = false;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (ProductInfo.isMiniProduct() || ProductInfo.isMeissa2()) {
            unregisterReceiver(mBroadcastReceiver);
        } else {
            unregisterVolumeChangeReceiver();
        }
    }

    @Override
    public void onBackPressed() {
        //实现Home键效果
        Intent intent = new Intent(Intent.ACTION_MAIN);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.addCategory(Intent.CATEGORY_HOME);
        startActivity(intent);
    }

    @Override
    public void moveToHome() {
        if (isLauncher()) {
            Log.d(TAG, "Current already is launcher");
            return;
        }

        startDefaultApp();
        finishInspection(Definition.INSPECTION_FINISHED_FIRST);

        new CountDownTimer(1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
            }

            @Override
            public void onFinish() {
                BaseFragment launcher = FRAGMENT_TYPE.FRAGMENT_LAUNCHER.getFragment();
                setFragment(launcher, null);
            }
        }.start();
    }

    private boolean isLauncher() {
        Fragment oldFragment = getSupportFragmentManager().findFragmentById(R.id.activity_main);
        Fragment fragment = FRAGMENT_TYPE.FRAGMENT_LAUNCHER.getFragment();
        return oldFragment != null && (oldFragment.getClass().equals(fragment.getClass()));
    }

    @Override
    protected void sendMessageToActivity(Message msg) {
        if (msg == null) {
            Log.e(TAG, "null msg");
            return;
        }

        Log.d(TAG, "handle msg:" + msg.arg1);
        switch (msg.arg1) {
            case Definition.FIRST_CONFIG_END_TO_DEFAULT:
            case ModuleDef.MESSAGE_ENTER_DEFAULT_APP:
                startDefaultApp();
                finishInspection(Definition.INSPECTION_FINISHED_FIRST_CONFIG);
                break;
            case Definition.FIRST_CONFIG_END_TO_NEW_MAP:
            case Definition.FIRST_CONFIG_END_TO_SYNC_CLOUD_MAP:
                startMapApp(msg.arg1);
                finishInspection(Definition.INSPECTION_FINISHED_FIRST_CONFIG);
                break;
            default:
                Log.e(TAG, "can not handle this msg:" + msg.arg1);
                break;
        }
    }

    private void startDefaultApp() {
        String packageName = RobotSettingApi.getInstance().
                getRobotString(Definition.BOOT_APP_PACKAGE_NAME);
        if (TextUtils.isEmpty(packageName)) {
            packageName = "com.ainirobot.moduleapp";
        }

        Log.d(TAG, "Start default app : " + packageName);
        PackageManager pm = this.getPackageManager();
        Intent intent = pm.getLaunchIntentForPackage(packageName);
        if (intent != null) {
            isDefaultApp = true;
            startActivity(intent);
            if (SystemApi.getInstance() != null && SystemApi.getInstance().isApiConnectedService()) {
                SystemApi.getInstance().startAppControl(packageName);
            }
            checkDefaultApp(packageName);
        }
    }

    private void checkDefaultApp(final String pkg) {
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                Log.d(TAG, "Check default app : " + pkg);
                if (SystemUtils.isInRecent(getBaseContext(), pkg)) {
                    Log.d(TAG, "Default app started");
                    isDefaultApp = false;
                }
            }
        }, 500);
    }

    private void startMapApp(int msg) {
        String packageName = "com.ainirobot.maptool";
        String className = ".activity.MainActivity";

        Log.d(TAG, "Start map app : " + packageName);

        PackageManager pm = this.getPackageManager();
        Intent intent = pm.getLaunchIntentForPackage(packageName);
        if (intent != null) {
            intent.putExtra(Definition.FIRST_CONFIG_END, msg);
            startActivity(intent);
        }
    }

    private void finishInspection(String inspectionType) {
        Log.d(TAG, "On inspection finished : activity");
        SystemApi.getInstance().onInspectionFinished(inspectionType);
        SystemApi.getInstance().remotePostPrepared(ModuleDef.MODULE_STATE_IDLE, null);
    }


}
