/*
 *
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.ainirobot.home;

public class ModuleDef {

    public static final String PREFIX = "Home_";
    //----------------- module pattern type ----------------//
    public static final int PATTERN_TYPE_CMD = 1;
    public static final int PATTERN_TYPE_TEXT = 2;

    public static final String WHEEL_MOTOR_BLOCKED = "wheel_motor_blocked";

    //----------------- module feature ---------------------//
    public static final int FEATURE_ALL = 1000;
    public static final int FEATURE_CONTROL = 1001;
    public static final int FEATURE_NONE = -1;
    public static final int FEATURE_NAVIGATION = 9;
    public static final int FEATURE_MAP = 16;
    public static final int FEATURE_OTA = 17;
    public static final int FEATURE_INSPECTION = 19;
    public static final int FEATURE_STANDBY = 20;
    public static final int FEATURE_EMERGENCY = 21;
    public static final int FEATURE_BLE_CLOSE = 24;
    public static final int FEATURE_AUTO_CHARGE = 26;
    public static final int FEATURE_SET_CHARGE_PILE = 27;
    public static final int FEATURE_LAUNCHER = 207;
    public static final int FEATURE_CHARGING = 28;
    public static final int FEATURE_HW_ABNORMAL = 29;
    public static final int FEATURE_SETTING_MODULE = 112;
    public static final int FEATURE_REPOSITION = 25;
    public static final int FEATURE_REMOTE_BIND = 60;
    public static final int FEATURE_REMOTE_REPOSITION = 208;
    public static final int FEATURE_OPEN_RADAR = 209;
    public static final int FEATURE_RESET = 210;
    public static final int FEATURE_DORMANCY = 211;
    public static final int FEATURE_REMOTE_CONTROL = 30;
    public static final int FEATURE_FULL_LOCK = 31;
    public static final int FEATURE_REMOTE_PUSH_MAP_NEED_SWITCH = 32;
    public static final int FEATURE_LITE_LOCK = 113;
    public static final int FEATURE_REMOTE_PUSH_MAP_NO_SWITCH = 114;
    public static final int FEATURE_SHUTDOWN = 115;
    public static final int FEATURE_POWER_LOW = 306;
    public static final int FEATURE_MAP_DRIFT = 45;
    public static final int FEATURE_MAP_OUTSIDE = 46;
    public static final int FEATURE_TIME_WARNING = 47;
    public static final int FEATURE_ROBOT_BEING_PUSHED = 48;
    public static final int FEATURE_D430_CALIBRATION = 49;
    public static final int FEATURE_WHEEL_OVER_CONTROL = 50;
    public static final int FEATURE_MULTI_ROBOT_ERROR = 51;
    public static final int FEATURE_NAVI_LOAD_MAP = 52;
    public static final int FEATURE_STOP_CHARGE_CONFIRM = 53;
    public static final int FEATURE_LEAVE_PILE_TO_POINT = 54;
    public static final int FEATURE_CUSTOM_TARGET = 116;
    public static final int FEATURE_HW_E70 = 222;
    public static final int FEATURE_NAVI_SENSOR_STATE = 223;
    public static final int FEATURE_NAVI_IMPORT_MAP = 224;
    public static final int FEATURE_OTA_DOWNGRADE = 13;


    public static final int FEATURE_REMOTE_STOP_CHARGING = 117;

    //----------------- module state ----------------------//
    public static final int MODULE_STATE_IDLE = 0;
    public static final int MODULE_STATE_RUNNING = 1;
    public static final int MODULE_STATE_WAITING = 2;
    public static final int MODULE_STATE_PAUSE = 3;
    public static final int MODULE_STATE_READY = 4;

    //----------------- message ---------------------------//
    public static final int MSG_NEW_REQUEST = 101;
    public static final int MSG_CMD_RESPONSE = 102;
    public static final int MSG_HW_RESPORT = 103;
    public static final int MSG_MODULE_STOP = 104;
    public static final int MSG_MODULE_WAIT = 105;

    public static final String MSG_BUNDLE_ID = "bundle_id";
    public static final String MSG_BUNDLE_TEXT = "bundle_text";
    public static final String MSG_BUNDLE_INTENT = "bundle_intent";
    public static final String MSG_BUNDLE_COMMAND = "bundle_command";
    public static final String MSG_BUNDLE_PARAM = "bundle_param";
    public static final String MSG_BUNDLE_MODULE = "bundle_module";

    public static final String REQ_WAKEUP = "req_wakeup";
    public static final String REQ_STOP = "req_stop";


    //----------------- action ------------------------//
    /**
     * ota start
     */
    public static final String OTA_BROADCAST_ACTION_TO_ABOUT_ROBOT = "com.ainirobot.moduleapp.ota.user_touch";
    public static final String OTA_BROADCAST_ACTION_TO_REMOTE_CONTROL = "com.ainirobot.moduleapp.ota.kill.otaservice";

    public static final String CUSTOM_WAKEUP_WORD = "com.ainirobot.home.custom_wakeup_word";


    public static final String STATUS_PROCESS = "status_process_state";

    public static final String FIRST_BOOT = "first_boot";
    public static final String NORMAL_CHECK = "normal_check";
    public static final String NORMAL_CHECK_FAILED = "normal_check_failed";

    //--------------------------ota------------------------------------//
    public static final String PARAM_IS_ROLLBACK = "param_is_rollback";
    public static final String KEY_SP_OTA_REBOOT = "ota_reboot";

    /**
     * 招财豹开机自检、定时回充、OTA自检失败，自动重试5次重启
     * 是否可以执行自动重启重试，默认是0,自检正常需要将该值
     */
    public static final int SETTING_CAN_REBOOT_ROBOT = 0;
    public static final int SETTING_CANNOT_REBOOT_ROBOT = 1;
    public static final int SETTING_CAN_REBOOT_ROBOT_COUNT = 2;

    //-------------------------- auto charge -------------------------//
//    public static final String SET_PILE_TYPE = "set_pile_type";
    public static final String SET_PILE_START_TYPE = "set_pile_start_type";
    public static final int SET_PILE_FROM_CORE = 1;
    public static final int SET_PILE_FROM_CHARGE = 2;
    public static final String CHARGE_TYPE = "charge_type";

    public static final int UI_SHOW_FRAGMENT = 1;
    public static final int UI_SEND_MESSAGE = 2;
    public static final int UI_MOVE_TO_BACK = 3;
    public static final int UI_SEND_REQUEST = 4;

    public static final int SET_PILE_STATUS_START = 1;
    public static final int SET_PILE_STATUS_CHARGING = 2;
    public static final int SET_PILE_STATUS_SETTING = 3;
    public static final int SET_PILE_STATUS_SUCCESS = 4;
    public static final int SET_PILE_STATUS_FAIL = 5;

    public static final String PARAM_LEVEL = "level";
    public static final String PARAM_BMS_TEM = "bmsTemp";
    public static final String PARAM_WAIT_UPGRADE = "isUpgrade";

    //---------------------msg for activity-----------------------
    public static final int MESSAGE_EXIT_MAPTOOL = 0x10000;
    public static final int MESSAGE_ENTER_DEFAULT_APP = 0xFFFF;

    //------------------------inspection--------------------------
    public static final String START_INSPECTION = "start_inspection";
    public static final String OPEN_RADAR_FAILED = "open_radar_failed";

    //------------------------reposition--------------------------
    public static final String E70_RESTART_SUCC_REPOSITION = "e70_restart_succ_reposition";

    //------------------------remote relocate--------------------------
    public static final String REQ_REMOTE_RELOCATE = "req_remote_relocate";

    public static final int INSPECT_STATUS_ING = 0;
    public static final int INSPECT_STATUS_END = 1;
    public static final int INSPECT_STATUS_ERR = 2;

    public static final String EMERGENCY_PRESS = "1";
    public static final String EMERGENCY_RELEASE = "0";

    public static final String MULTIPLE_PRESS = "1";
    public static final String MULTIPLE_RELEASE = "2";

    public static final String MULTI_FUNC_SWITCH_STATE = "state";

    public static final String INSPECT_ERROR_MESSAGE = "inspect_error_message";
    public static final String INSPECT_NAVI_ERROR_ID = "inspect_navi_error_id";

    // Factory Reset Fragment constants
    public static final String FACTORY_RESET_MANDATORY = "factory_reset_mandatory";
    public static final String FACTORY_RESET_REASON = "factory_reset_reason";

    //--------------------UI MESSAGE-------------------
    public static final int LOCAL_MESSAGE_ESTIMATE = 1;
    public static final int LOCAL_MESSAGE_STOP_CHARGING = 2;
    public static final int LOCAL_MESSAGE_STOP_SET_PILE = 3;
    public static final int LOCAL_MESSAGE_STOP_INSPECTION = 4;
    public static final int LOCAL_MESSAGE_PAUSE_OTA = 5;
    public static final int LOCAL_MESSAGE_CONTINUE_OTA = 6;
    public static final int LOCAL_MESSAGE_CANCEL_OTA = 7;
    public static final int LOCAL_MESSAGE_CHARGE_START = 8;
    public static final int LOCAL_MESSAGE_CHARGE_END = 9;
    public static final int LOCAL_MESSAGE_TO_BIND = 10;
    public static final int LOCAL_MESSAGE_BIND_END = 12;
    public static final int LOCAL_MESSAGE_REQUEST_QR_CODE = 13;
    public static final int LOCAL_MESSAGE_NET_CONNECTED = 14;
    public static final int LOCAL_MESSAGE_GET_REMOTE_BIND_STATUS = 15;
    public static final int LOCAL_MESSAGE_REPOSITION_CANCEL = 16;
    public static final int LOCAL_MESSAGE_HW_STATUS_CANNOT_RECOVERY = 17;
    public static final int LOCAL_MESSAGE_CW_BI_CLICK_VS_BUTTON = 18;
    public static final int LOCAL_MESSAGE_AUTO_CHARGE_CANCEL = 19;
    public static final int LOCAL_MESSAGE_START_STANDBY = 20;
    public static final int LOCAL_MESSAGE_STOP_STANDBY = 21;
    public static final int LOCAL_MESSAGE_EXIT_STANDBY = 22;
    public static final int LOCAL_MESSAGE_INIT = 23;
    public static final int LOCAL_MESSAGE_SHUTDOWN_CANCEL = 25;
    public static final int LOCAL_MESSAGE_SHUTDOWN_NOW = 26;
    public static final int LOCAL_MESSAGE_EXIT_HW_MODULE = 27;
    public static final int LOCAL_MESSAGE_HW_AUTOMATIC_RECOVERY_TIMEOUT = 28;
    public static final int LOCAL_MESSAGE_MAP_DRIFT_START_DROP_POSITION = 29;
    public static final int LOCAL_MESSAGE_TIME_WARNING_STOP = 30;
    public static final int LOCAL_MESSAGE_TIME_WARNING_STOP_ALWAYS = 31;
    public static final int LOCAL_MESSAGE_BLE_PASSWORD_UNLOCKED = 32;
    public static final int LOCAL_MESSAGE_WHEEL_OVER_UNLOCK = 33;
    //梯控
    public static final int LOCAL_MESSAGE_ELEVATOR_CONFIRM_FLOOR = 34 ;
    public static final int LOCAL_MESSAGE_ELEVATOR_POINT_LOCATE = 35 ;
    public static final int LOCAL_MESSAGE_ELEVATOR_BACK_TO_CHOOSE_FLOOR = 36 ;
    public static final int LOCAL_MESSAGE_ELEVATOR_CHARGE_LOCATE = 37 ;
    public static final int LOCAL_MESSAGE_ELEVATOR_CHARGE_LOCATING = 38 ;
    public static final int LOCAL_MESSAGE_CHOOSE_CHARGE_PILE = 39;
    public static final int LOCAL_MESSAGE_CHOOSE_ANCHOR_POINT = 40;
    public static final int LOCAL_MESSAGE_CHARGE_PILE_LOCATING = 41;
    public static final int LOCAL_MESSAGE_TO_CHOOSE_LOCATE_TYPE = 42;
    public static final int LOCAL_MESSAGE_CHOOSE_VISION_LOCATING = 43;
    public static final int LOCAL_MESSAGE_NAVI_SENSOR_REBOOT = 44;
    public static final int LOCAL_MESSAGE_LEVEL_CHANGED = 45;
    public static final int LOCAL_MESSAGE_SKIP_LOAD_MAP = 46;
    public static final int LOCAL_MESSAGE_CONFIRM_STOP_CHARGE = 47;
    public static final int LOCAL_MESSAGE_START_GO_STANDBY_POINT = 48;

    //--------------------AUDIO-------------------------
    public static final String AUDIO_UP_MSG = "audio_up";
    public static final String AUDIO_DOWN_MSG = "audio_down";
    public static final String AUDIO_SET_NUM_MSG = "audio_set";
    public static final String AUDIO_MAX = "audio_max";
    public static final String AUDIO_MIN = "audio_min";

    public static final String INTENT_CUSTOM_WAKEUP_WORD = "req_wakeup_word";

    // ---------------------Reposition status----------------------------- //
    /**
     * 重定位状态
     * 0 手动定位正在定位中  1定位成功 2 手动定位定位失败  3退出
     * 4视觉定位开始转圈 5视觉定位定位失败
     */
    public static final int REPOSITION_START = 0;
    public static final int REPOSITION_SUCCEED = 1;
    public static final int REPOSITION_FAILURE = 2;
    public static final int REPOSITION_EXIT = 3;
    public static final int REPOSITION_VISION_CIRCLE = 4;
    public static final int REPOSITION_VISION_FAILED = 5;
    public static final int REPOSITION_MENUAL_GUIDE = 6;
    public static final int REPOSITION_VISION_SUCCESS = 7;
    public static final int REPOSITION_CHARGING_CHECK = 8;
    public static final int REPOSITION_CLEAR_REASON = 9;

    public static final int MSG_REPOSITION_GUIDE_CONFIRM = 138;
    public static final int MSG_REPOSITION_FAILURE_RETRY = 139;
    public static final int MSG_REPOSITION_FAILURE_SKIP = 140;
    public static final int MSG_REPOSITION_VISION_RETRY = 141;
    public static final int MSG_REPOSITION_VISION_MENUAL = 142;
    public static final int MSG_REPOSITION_CIRCLE_TIMER = 143;
    public static final int MSG_REPOSITION_SUCCESS_TIMER = 144;
    public static final int MSG_REPOSITION_CANCEL = 146;

    public static final int MSG_HWABNORMAL_REBOOT_TIMER = 147;

    public static final int MSG_REPOSITION_QRCODE_CHOOSE_OTHER = 149;
    public static final int MSG_REPOSITION_QRCODE_ANCHOR_SET = 150;
    public static final int MSG_REPOSITION_QRCODE_REPOSITION = 151;
    public static final int MSG_REPOSITION_QRCODE_REPOSITION_REBOOT = 152;
    public static final int MSG_REPOSITION_SHOW_MENUAL = 153;
    public static final int MSG_HWABNORMAL_DELAY_EXIT = 154;
    public static final int MSG_REPOSITION_RETRY_MSG = 155;
    public static final int MSG_REMOTE_STOP_CHARGING_EXIT = 156;

    public static final String KEY_SP_NEAR_POSE = "key_sp_near_pose";
    public static final String KEY_SP_CHARGE_PILE_POSE = "key_sp_charge_pile_pose";

    public static final String HW_ABNORMAL_STATUS = "hw_abnormal_status";

    public static final String PLAY_TTS = "play_tts";

    // special point name
    public static final String AUTO_BACK_RECEPTION_POINT = "接待点";
    public static final String QRCODE_LOADING = "qrcode_loading";
    public static final String STANDBY_START_COUNT = "standby_start_count";
    public static final String QRCODE_FAILURE_TYPE = "qrcode_failure_type";
    public static final String QRCODE_FAILURE_WITHOUT_MAP = "qrcode_failure_without_map";
    public static final String QRCODE_FAILURE_LOSE_POINT_WITH_ANCHOR =
            "qrcode_failure_lose_point_with_anchor";
    public static final String QRCODE_FAILURE_LOSE_POINT_WITHOUT_ANCHOR =
            "qrcode_failure_lose_point_without_anchor";
    public static final String QRCODE_FAILURE_LOSE_POINT_WITHOUT_TARGET =
            "qrcode_failure_lose_point_without_target";

    public static final int NOT_UPLOADED = 0;
    public static final int UPLOADED_MAP = 1;
    public static final int UPLOADED_PLACE = 1 << 1;
    public static final int UPLOADED = UPLOADED_MAP | UPLOADED_PLACE;
    public static final String RESULT_SUCCEED = "succeed";
    public static final String FLOOR_LIST = "floor_list";

    public static final String IS_MAP_IN_FLOOR_LIST = "is_map_in_floor_list";



    public static final String CURRENT_MAP_NAME = "current_map_name";

}
