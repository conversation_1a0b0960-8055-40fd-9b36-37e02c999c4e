/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.home;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.support.v4.app.Fragment;
import android.support.v4.app.FragmentActivity;
import android.support.v4.app.FragmentTransaction;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;

import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.fragment.BaseFragment;

import java.lang.ref.WeakReference;
import java.util.Set;

public class BaseActivity extends FragmentActivity {

    private static String TAG;
    public Handler mHandler = new UiHandler(this);

    public static final String FRAGMENT = "fragment";
    public static final String BUNDLE = "bundle";
    public static final String ACTION_ID = "action_id";
    private int mActionId = -1;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        TAG = getClass().getName();
        Log.i(TAG, "On create");
        setContentView(R.layout.activity_main);
        UIController.getInstance().setUIHandler(mHandler);
    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.d(TAG, "On resume");
        UIController.getInstance().setUIHandler(mHandler);
        UIController.getInstance().actionOver(mActionId);
        hideNavigation();
    }

    private void hideNavigation() {
        View decorView = getWindow().getDecorView();
        int uiOptions = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                | View.SYSTEM_UI_FLAG_FULLSCREEN;
        decorView.setSystemUiVisibility(uiOptions);
    }

    static class UiHandler extends Handler {

        private final WeakReference<BaseActivity> mActivity;

        UiHandler(BaseActivity activity) {
            mActivity = new WeakReference<>(activity);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            Log.d(TAG, msg.what + "");
            final BaseActivity activity = mActivity.get();
            if (activity == null) {
                Log.d(TAG, "Activity is null : " + msg.what);
                UIController.getInstance().actionOver(msg.arg2);
                return;
            }

            switch (msg.what) {
                case ModuleDef.UI_SHOW_FRAGMENT:
                    activity.switchFragment((Intent) msg.obj);
                    break;

                case ModuleDef.UI_MOVE_TO_BACK:
                    Log.d(TAG, "Move to home");
                    activity.moveToHome();
                    UIController.getInstance().actionOver(msg.arg2);
                    break;

                case ModuleDef.UI_SEND_MESSAGE:
                    activity.sendMessageToFragment(msg);
                    UIController.getInstance().actionOver(msg.arg2);
                    break;

                case ModuleDef.UI_SEND_REQUEST:
                    activity.sendMessageToActivity(msg);
                    break;

                default:
                    break;
            }
        }
    }

    public void moveToHome() {

    }

    private void sendMessageToFragment(Message msg) {
        BaseFragment fragment = (BaseFragment) getSupportFragmentManager().findFragmentById(R.id.activity_main);
        if (msg.obj == null || fragment == null) {
            Log.d(TAG, "sendMessageToFragment not work , msg.obj : "+ msg.obj +", fragment : "+ fragment);
            return;
        }
        fragment.onMessage(UIController.MESSAGE_TYPE.values()[msg.arg1], (String) msg.obj);
    }

    protected void sendMessageToActivity(Message msg) {

    }

    protected void switchFragment(Intent intent) {
        UIController.FRAGMENT_TYPE type = (UIController.FRAGMENT_TYPE) intent.getSerializableExtra(FRAGMENT);
        Bundle bundle = intent.getBundleExtra(BUNDLE);
        if (type == null) {
            Log.d(TAG, "Type is null");
            type = UIController.getInstance().getCurrentFragment();
            bundle = UIController.getInstance().getCurrentBundle();
        }

        Log.d(TAG, "Switch fragment : " + type.name());
        setFragment(type.getFragment(), bundle);
        mActionId = intent.getIntExtra(ACTION_ID, -1);
    }

    protected synchronized void setFragment(BaseFragment fragment, Bundle bundle) {
        Log.d(TAG, "set fragment " + fragment.getClass().getSimpleName());
        FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
        Fragment oldFragment = getSupportFragmentManager().findFragmentById(R.id.activity_main);
        if (oldFragment != null) {
            if (oldFragment.getClass().equals(fragment.getClass())) {
                Bundle oldBundle = oldFragment.getArguments();
                boolean equal = isBundlesEqual(oldBundle, bundle);
                Log.d(TAG, "isBundlesEqual : " + equal);
                if (equal) {
                    Log.d(TAG, "same to old fragment , return");
                    return;
                }
            }
        }
        if (bundle != null) {
            try {
                fragment.setArguments(bundle);
            } catch (IllegalStateException e) {
                e.printStackTrace();
            }
        }
        transaction.replace(R.id.activity_main, fragment);
        transaction.commitAllowingStateLoss();
    }


    private boolean isBundlesEqual(Bundle old, Bundle current) {

        if (old == null && current == null) {
            return true;

        } else if (old != null && current != null) {
            if (old.size() != current.size()) {
                return false;
            }

            // old size == current.size here.
            Set<String> oldKeys = old.keySet();
            for (String key : oldKeys) {
                if (!current.containsKey(key)) {
                    return false;
                }

                Object valueOld = old.get(key);
                Object valueCurrent = current.get(key);
                if (valueOld instanceof Bundle && valueCurrent instanceof Bundle &&
                        !isBundlesEqual((Bundle) valueOld, (Bundle) valueCurrent)) {
                    return false;

                } else if (valueOld == null) {
                    if (valueCurrent != null) {
                        return false;
                    }

                } else if (!valueOld.equals(valueCurrent)) {
                    return false;
                }
            }
            return true;

        } else if (old == null) {
            // current is always not null here.
            return current.size() == 0;
        } else {
            // current is null , old is always not null here.
            return old.size() == 0;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "onDestroy");
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        Log.d(TAG, "On touch event");
        if (MotionEvent.ACTION_DOWN == event.getAction()) {
            BaseFragment fragment = (BaseFragment) getSupportFragmentManager().findFragmentById(R.id.activity_main);
            if (fragment != null) {
                fragment.onMessage(UIController.MESSAGE_TYPE.TOUCH_EVENT_DOWN, null);
            }
        }
        return super.onTouchEvent(event);
    }

}
