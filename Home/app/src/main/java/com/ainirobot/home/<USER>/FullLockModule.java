package com.ainirobot.home.module;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.bi.BIRobotLockReport;
import com.ainirobot.home.control.SkillManager;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.utils.SystemUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Iterator;
import java.util.Locale;

public class FullLockModule extends BaseModule {

    private static final String TAG = "FullLockModule";
    private static FullLockModule mInstance;
    private static final int LOCK_IDLE = 0;
    private static final int LOCK_ING = 1;

    private int mStatus = LOCK_IDLE;


    private FullLockModule(){}
    public static FullLockModule getInstance(){
        if(mInstance == null){
            mInstance = new FullLockModule();
        }
        return mInstance;
    }

    @Override
    public boolean onNewSemantics(int reqId, String intent, String text, String params) {
        Log.i(TAG, "handle semantics, intent: " + intent + " , lock status: " + mStatus + " params: " + params);
        String lockMsg = ApplicationWrapper.getContext().getString(R.string.contact_owner);
        String lockMsgI18n;
        String defaultLocale = Locale.US.toString();
        String currentLocale = Locale.getDefault().toString();
        Log.d(TAG, "currentLocale ——》: " + currentLocale );
        if (!TextUtils.isEmpty(params)) {
            try {
                JSONObject json = new JSONObject(params);
                lockMsg = json.optString(Definition.JSON_REMOTE_LOCK_MSG);
                lockMsgI18n = json.optString("lock_msg_i8n");
                Log.d(TAG, " lockMsg ——》: " + lockMsg + ", lockMsgI18n ——》: " + lockMsgI18n);
                String lockMsgI18nDefault = null;
                String lockMsgI18nTarget = null;
                if (!TextUtils.isEmpty(lockMsgI18n)){
                    JSONObject jsonObject = new JSONObject(lockMsgI18n);
                    Log.d(TAG, " currentLocale: " + currentLocale + ", defaultLocale: " + defaultLocale);
                    lockMsgI18nDefault = jsonObject.optString(defaultLocale);
                    lockMsgI18nTarget = jsonObject.optString(currentLocale);
                    Log.d(TAG, "lockMsgI18nDefault: " + lockMsgI18nDefault + ", lockMsgI18nTarget: " + lockMsgI18nTarget);
                }
                if ("zh_CN".equals(currentLocale)){
                    if (TextUtils.isEmpty(lockMsgI18nTarget)) {
                        lockMsg = TextUtils.isEmpty(lockMsgI18nDefault) ? lockMsg : lockMsgI18nDefault;
                    } else {
                        lockMsg = lockMsgI18nTarget;
                    }
                }else {
                    String defaultMsg = ApplicationWrapper.getContext().getString(R.string.contact_owner);
                    lockMsg = TextUtils.isEmpty(lockMsgI18nTarget) ? defaultMsg : lockMsgI18nTarget;
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        switch (intent) {
            case "system_remote_lock_full":
                if(mStatus == LOCK_ING){
                    return false;
                }
                mStatus = LOCK_ING;
                SkillManager.getInstance().closeSpeechAsrRecognize();
                SkillManager.getInstance().cancleAudioOperation();
                Bundle bundle = new Bundle();
                bundle.putString(ModuleDef.MSG_BUNDLE_TEXT, lockMsg);
                UIController.getInstance().showFragment(UIController.FRAGMENT_TYPE.FRAGMENT_FULL_LOCK, bundle, null);
                SystemApi.getInstance().resetHead(0, null);
                SystemUtils.setThreeFinger(false);
                String playText = ApplicationWrapper.getContext().getString(R.string.lock_device) +
                        " " + lockMsg;
                SkillManager.getInstance().speechPlayText(playText);
                SystemApi.getInstance().sendStatusReport("status_robot_lock","lock_full");
                reportLockStart();
                SystemApi.getInstance().stopMove(reqId, null);
                break;

            case "system_remote_lock_full_exit":
                reportLockExit();
                stop();
                break;

            default:
                break;
        }
        return super.onNewSemantics(reqId, intent, text, params);
    }

    @Override
    protected void onStop() {
        Log.i(TAG, "on stop");
        mStatus = LOCK_IDLE;
        SystemUtils.setThreeFinger(true);
        SkillManager.getInstance().openSpeechAsrRecognize();
        SystemApi.getInstance().sendStatusReport("status_robot_lock","lock_full_exit");
        super.onStop();
    }

    private void reportLockExit(){
        BIRobotLockReport report = new BIRobotLockReport();
        report.addEndTime(System.currentTimeMillis());
        report.addLockType(1);
        report.report();
    }

    private void reportLockStart(){
        BIRobotLockReport report = new BIRobotLockReport();
        report.addStartTime(System.currentTimeMillis());
        report.addLockType(1);
        report.report();
    }
}
