package com.ainirobot.home.ui.view;

import android.content.Context;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.ainirobot.home.R;
import com.ainirobot.home.utils.ResUtil;

public class RelocationAnchorView extends FrameLayout {

    private Context mContext;
    private View mView;
    public View mAnchorCancelTV, mAnchorExitTV;
    private TextView mTitleView;
    public Button mResultBtn, mQuestionBtn;
    public final int TYPE_WITHOUT_QRCODE_INIT = 1;
    public final int TYPE_WITH_ANCHOR_INIT = 2;
    public final int TYPE_WITH_ANCHOR = 3;
    public final int TYPE_NOT_FIND_ANCHOR = 4;
    public final int TYPE_RESET_HEAD_FAILED = 5;

    public RelocationAnchorView(Context context) {
        this(context, null);
    }

    public RelocationAnchorView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RelocationAnchorView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public RelocationAnchorView(Context context, @Nullable AttributeSet attrs, int defStyleAttr,
                                int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initSubViews(context);
    }

    private void initSubViews(Context context) {
        this.mContext = context;
        mView = LayoutInflater.from(mContext).inflate(R.layout.layout_qrcode_relocation_anchor,
                this);
        mAnchorCancelTV =  mView.findViewById(R.id.reposition_anchor_cancel_tv);
        mAnchorExitTV =  mView.findViewById(R.id.anchor_cancel_text);
        mTitleView = (TextView) mView.findViewById(R.id.locate_result_title);
        mResultBtn = (Button) mView.findViewById(R.id.anchor_point_btn);
        mQuestionBtn = (Button) mView.findViewById(R.id.anchor_question_btn);
    }

    public void showAnchorView(int type) {
        switch (type) {
            case TYPE_WITHOUT_QRCODE_INIT:
                mAnchorCancelTV.setVisibility(VISIBLE);
                mAnchorExitTV.setVisibility(GONE);
                break;
            case TYPE_WITH_ANCHOR_INIT:
                mTitleView.setText(ResUtil.getString(R.string.reposition_anchor_title_qrCode));
                mQuestionBtn.setVisibility(GONE);
                mAnchorCancelTV.setVisibility(GONE);
                mAnchorExitTV.setVisibility(VISIBLE);
                break;
            case TYPE_WITH_ANCHOR:
                mTitleView.setText(ResUtil.getString(R.string.reposition_anchor_title_qrCode));
                mQuestionBtn.setVisibility(GONE);
                mAnchorCancelTV.setVisibility(VISIBLE);
                mAnchorExitTV.setVisibility(GONE);
                break;
            case TYPE_NOT_FIND_ANCHOR:
                mTitleView.setText(ResUtil.getString(R.string.reposition_not_anchor_title_qrCode));
                mQuestionBtn.setVisibility(View.VISIBLE);
                break;
            case TYPE_RESET_HEAD_FAILED:
                mTitleView.setText(ResUtil.getString(R.string.reset_head_failed));
                mQuestionBtn.setVisibility(View.GONE);
                break;
        }

    }
}
