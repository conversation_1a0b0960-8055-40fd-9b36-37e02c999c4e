package com.ainirobot.home.ui.fragment;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.control.ControlManager;

public class StopChargeConfirmFragment extends BaseFragment implements View.OnClickListener {

    private static final String TAG = "StopChargeConfirmFragment:Home";
    private TextView mConfirmView;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @SuppressLint("LongLogTag")
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        Log.i(TAG, "on create view");
        View view = inflater.inflate(R.layout.fragment_stop_charge_confirm, null);

        mConfirmView = (TextView) view.findViewById(R.id.confirm_button);
        mConfirmView.setOnClickListener(this);

        return view;
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onStop() {
        super.onStop();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.confirm_button:
                Log.i(TAG, "LOCAL_MESSAGE_CONFIRM_STOP_CHARGE");
                ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_CONFIRM_STOP_CHARGE);
                break;
            default:
                break;
        }
    }
}