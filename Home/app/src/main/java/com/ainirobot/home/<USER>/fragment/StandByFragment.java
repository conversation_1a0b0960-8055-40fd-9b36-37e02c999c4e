package com.ainirobot.home.ui.fragment;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.support.annotation.Nullable;
import android.text.Spannable;
import android.text.SpannableString;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.BatteryBean;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.home.ApplicationWrapper;
import com.ainirobot.home.ModuleDef;
import com.ainirobot.home.R;
import com.ainirobot.home.bi.BiDormancyEndReport;
import com.ainirobot.home.control.ControlManager;
import com.ainirobot.home.control.RadarManager;
import com.ainirobot.home.module.StandByModule;
import com.ainirobot.home.ui.UIController;
import com.ainirobot.home.ui.view.CenterImagSpan;
import com.ainirobot.home.ui.view.ChargingWarningDialog;
import com.google.gson.Gson;

import org.json.JSONObject;

public class StandByFragment extends BaseFragment implements View.OnClickListener {

    private static final String TAG = StandByFragment.class.getSimpleName();
    private TextView mTextView, mTv_message, mTv_chargingMsg, mTextView_hint, mTv_switchHint;
    private Button mButton;
    private Button mButtReboot;
    private Drawable dw_charging, dw_charging_slowly, dw_battery_normal, dw_battery_low,
            dw_emergency, dw_resume;
    private static final int NONE = 0;
    private static final int ENERGENCY = 1;
    private static final int RESUME = 2;
    private int mCurrentStatus = NONE;
    private ImageView iv_bg;
    private Gson mGson = new Gson();
    private boolean mIsPressed = false;
    private static final int DANGEROUS_BATTERY_LEVEL = 5;
    private static final int LOW_BATTERY_LEVEL = 10;
    private ChargingWarningDialog mChargingWarningDialog;
    private boolean mIsChargingNormal = true;
    private boolean mIsCharging = false;
    private int mRadarRetryTimes = 0;
    private CountDownTimer mTimer = null;

    private final static int OPEN_RADAR_CYCLE = 3;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container,
                             Bundle savedInstanceState) {
        Log.i(TAG, "on create view");
        View view = inflater.inflate(R.layout.fragment_standby, null);
        initView(view);
        mButton.setOnClickListener(this);
        mTv_chargingMsg.setOnClickListener(this);
        return view;
    }

    private void initView(View view) {
        mTextView = (TextView) view.findViewById(R.id.tv_standby);
        mTextView_hint = (TextView) view.findViewById(R.id.tv_standby_hint);
        iv_bg = (ImageView) view.findViewById(R.id.iv_standby_bg);
        mTv_message = (TextView) view.findViewById(R.id.tv_message);
        mButtReboot = (Button) view.findViewById(R.id.butt_reboot);
        mButtReboot.setOnClickListener(this);
        mTv_chargingMsg = (TextView) view.findViewById(R.id.tv_chargingMsg);
        dw_charging = getResources().getDrawable(R.drawable.standby_charging, null);
        dw_charging_slowly = getResources().getDrawable(R.drawable.standby_charging_slowly, null);
        dw_battery_normal = getResources().getDrawable(R.drawable.standby_battery_normal,
                null);
        dw_battery_low = getResources().getDrawable(R.drawable.standby_battery_low, null);
        dw_emergency = getResources().getDrawable(R.drawable.standby_emergency, null);
        dw_resume = getResources().getDrawable(R.drawable.standby_resume, null);
        dw_charging.setBounds(0, 0, 21, 46);
        dw_charging_slowly.setBounds(0, 0, 53, 53);
        dw_battery_normal.setBounds(0, 0, 21, 46);
        dw_battery_low.setBounds(0, 0, 21, 46);
        dw_emergency.setBounds(0, 0, 69, 69);
        dw_resume.setBounds(0, 0, 55, 52);
        mButton = (Button) view.findViewById(R.id.bt_exitStandby);
        mTv_switchHint = (TextView) view.findViewById(R.id.tv_switchHint);
        mTv_switchHint.append(getString(R.string.standby_switchHint_start));
        mTv_switchHint.append(getSpannable(R.drawable.standby_switch));
        mTv_switchHint.append(getString(R.string.standby_switchHint_end));
        //TODO:home界面会有加载过慢的问题，暂未找到原因，因此需要界面加载后再去加载数据，避免module发送数据界面接收不到
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_INIT);
        Log.d(TAG, "initView:");
    }

    private CharSequence getSpannable(int standby_switch) {
        String defaultKey = "standby_switch";
        final SpannableString ss = new SpannableString(defaultKey);
        CenterImagSpan span = new CenterImagSpan(getContext(), standby_switch);
        ss.setSpan(span, 0, defaultKey.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        return ss;
    }

    @SuppressLint("StringFormatMatches")
    private void showViewByStatus(final int currentStatus) {
        Log.d(TAG, " showViewByStatus:: currentStatus = " + currentStatus);
        switch (currentStatus) {
            case NONE:
                mTv_message.setVisibility(View.GONE);
                break;
            case ENERGENCY:
                mTv_message.setVisibility(View.VISIBLE);
                mTv_message.setCompoundDrawables(dw_emergency, null, null,
                        null);
                mTv_message.setText(getString(R.string.standby_energency));
                break;
            case RESUME:
                dismissChargingWarningDialog();
                mTv_message.setVisibility(View.VISIBLE);
                iv_bg.setImageResource(R.drawable.standby_restore_bg);
                mTv_message.setCompoundDrawables(dw_resume, null, null, null);
                mButton.setVisibility(View.GONE);
                mTv_switchHint.setVisibility(View.GONE);
                mTextView.setVisibility(View.GONE);
                mTextView_hint.setVisibility(View.GONE);
                //startTimer
                if (mTimer != null) {
                    mTimer.cancel();
                    mTimer = null;
                }
                mTimer = new CountDownTimer(5000, 1000) {
                    @Override
                    public void onTick(long millisUntilFinished) {
                        mTv_message.setText(String.format(getString(R.string.standby_restoring),
                                millisUntilFinished / 1000));
                    }

                    @Override
                    public void onFinish() {
                        Log.d(TAG, "CountDownTimer 111111 onFinish mIsPressed: "+mIsPressed);
                        if (!mIsPressed) {
                            checkRadarStatus();
                        } else {
                            mCurrentStatus = NONE;
                            ControlManager.getControlManager().sendMessageToModule(ModuleDef
                                    .LOCAL_MESSAGE_START_STANDBY);
                        }
                    }
                };
                mTimer.start();
                break;
        }

    }

    /**
     * 检查雷达状态
     */
    private void  checkRadarStatus(){

        SystemApi.getInstance().queryRadarStatus(Definition.DEBUG_REQ_ID, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                Log.d(TAG, " checkRadarStatus result:" + result + " message:" + message + ", mRadarRetryTimes:" + mRadarRetryTimes);
                int radarStatus = -111;
                try{
                    JSONObject object = new JSONObject(message);
                    radarStatus = object.getInt(Definition.JSON_NAVI_RADAR_STATUS);
                }catch (Exception e){
                    e.printStackTrace();
                }
                if(radarStatus == Definition.RADAR_STATUS_OPENED){
                    ControlManager.getControlManager().sendMessageToModule(ModuleDef
                            .LOCAL_MESSAGE_EXIT_STANDBY);
                    mRadarRetryTimes = 0;
                } else {
                    //重新进入倒计时，并启动雷达
                    if (mRadarRetryTimes == OPEN_RADAR_CYCLE || mRadarRetryTimes == 2*OPEN_RADAR_CYCLE) {
                        SystemApi.getInstance().updateRadarStatus(Definition.DEBUG_REQ_ID, true, null);
                    } else if (mRadarRetryTimes == 3*OPEN_RADAR_CYCLE){
                        radarRetryFail();
                    }
                    if (mRadarRetryTimes < 3*OPEN_RADAR_CYCLE){
                        mRadarRetryTimes++;
                        UIController.getInstance().sendMessageToFragment(
                                UIController.MESSAGE_TYPE.STANDBY_EXIT, String.valueOf(true));
                    }else {
                        mRadarRetryTimes = 0;
                    }
                }
            }
        });
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onMessage(UIController.MESSAGE_TYPE type, String message) {
        super.onMessage(type, message);
        Log.d(TAG, "onMessage: type = " + type + " , currentStatus = "
                + mCurrentStatus + " , message = " + message);
        switch (type) {
            case STANDBY_EXIT:
                mCurrentStatus = RESUME;
                showViewByStatus(mCurrentStatus);
                break;
            case STANDBY_EMERGENCY_STATUS:
                if (mCurrentStatus != RESUME) {
                    if (Boolean.parseBoolean(message)) {
                        mCurrentStatus = ENERGENCY;
                    } else {
                        mCurrentStatus = NONE;
                    }
                    showViewByStatus(mCurrentStatus);
                }
                break;
            case STANDBY_MULTI_FUNC_SWITCH_STATUS_CHANGE:
                showStandbyMode(false, Boolean.parseBoolean(message));
                break;
            case STANDBY_MULTI_FUNC_SWITCH_STATUS:
                showStandbyMode(true, Boolean.parseBoolean(message));
                break;
            case STANDBY_BATTERY:
                BatteryBean batteryBean = mGson.fromJson(message, BatteryBean.class);
                int batteryLevel = batteryBean.getLevel();
                boolean charging = batteryBean.isCharging();
                if (!mIsPressed && batteryLevel <= DANGEROUS_BATTERY_LEVEL
                        && mCurrentStatus != RESUME) {
                    beginResume(BiDormancyEndReport.TYPE_LOW_BATTERY);
                }
                showBatteryLevel(batteryLevel, charging);
                break;
            case BMS_WARNING_STATUS:
                int status = Integer.parseInt(message);
                if (status == Definition.BMS_WARNING_STATUS_BMS_CHARGING_SLOW) {
                    if (mIsChargingNormal) {
                        showChargingWarningDialog();
                    }
                    mIsChargingNormal = false;
                } else if (status == Definition.BMS_WARNING_STATUS_CAN_CHARGING_NORMAL) {
                    mIsChargingNormal = true;
                    dismissChargingWarningDialog();
                }
                break;
        }
    }

    private void showStandbyMode(boolean isInit, boolean isPressed) {
        Log.d(TAG, "showStandbyMode: currentStatus " + mCurrentStatus
                + " , isInit " + isInit + " , isPressed " + isPressed);
        mIsPressed = isPressed;
        if (mCurrentStatus != RESUME) {
            if (isInit) {
                iv_bg.setImageResource(R.drawable.standby_sleep_bg);
                mTextView.setVisibility(View.VISIBLE);
                mTextView_hint.setVisibility(View.VISIBLE);
                if (isPressed) {
                    mButton.setVisibility(View.GONE);
                    mTv_switchHint.setVisibility(View.VISIBLE);
                } else {
                    mButton.setVisibility(View.VISIBLE);
                    mTv_switchHint.setVisibility(View.GONE);
                }
            } else {
                if (isPressed) {
                    mButton.setVisibility(View.GONE);
                    mTv_switchHint.setVisibility(View.VISIBLE);
                } else {
                    beginResume(BiDormancyEndReport.TYPE_MULTIPLE_FUNCTION_RELEASE);
                }
            }
        }
    }

    private void showBatteryLevel(int batteryLevel, boolean isCharging) {
        this.mIsCharging = isCharging;
        if (isCharging) {
            if (mIsChargingNormal) {
                mTv_chargingMsg.setText(String.format(getString(R.string.standby_charging),
                        batteryLevel));
                mTv_chargingMsg.setTextColor(Color.parseColor("#00E193"));
                mTv_chargingMsg.setCompoundDrawables(dw_charging, null, null, null);
            } else {
                mTv_chargingMsg.setText(String.format(getString(R.string.standby_charging_slowly),
                        batteryLevel));
                mTv_chargingMsg.setTextColor(Color.parseColor("#FF2C2C"));
                mTv_chargingMsg.setCompoundDrawables(dw_charging_slowly, null, null, null);
            }
            mTv_chargingMsg.setTypeface(Typeface.DEFAULT);
        } else {
            dismissChargingWarningDialog();
            mTv_chargingMsg.setText(String.format(getString(R.string.standby_battery), batteryLevel));
            if (batteryLevel > LOW_BATTERY_LEVEL) {
                mTv_chargingMsg.setCompoundDrawables(dw_battery_normal, null, null,
                        null);
                mTv_chargingMsg.setTextColor(Color.parseColor("#FF8D31"));
                mTv_chargingMsg.setTypeface(Typeface.DEFAULT);
            } else {
                mTv_chargingMsg.setCompoundDrawables(dw_battery_low, null, null,
                        null);
                mTv_chargingMsg.setTextColor(Color.parseColor("#FA5151"));
                mTv_chargingMsg.setTypeface(Typeface.DEFAULT_BOLD);
            }
        }

    }

    private void showChargingWarningDialog() {
        if (mChargingWarningDialog != null) {
            Log.d(TAG, "charging warning dialog already show");
        } else {
            mChargingWarningDialog = new ChargingWarningDialog(getContext(), R.style.OTADialog)
                    .setDialogClickListener(new ChargingWarningDialog.ClickListener() {
                        @Override
                        public void onConfirmClick() {
                            Log.d(TAG, "ChargingWarningDialog click ok");
                            mChargingWarningDialog = null;
                        }
                    });
        }
        if (!mChargingWarningDialog.isShowing()) {
            mChargingWarningDialog.show();
        }
    }

    private void dismissChargingWarningDialog() {
        if (null != mChargingWarningDialog && mChargingWarningDialog.isShowing())
            mChargingWarningDialog.dismiss();
    }

    @Override
    public void onPause() {
        if (mTimer != null) {
            mTimer.cancel();
            mTimer = null;
        }
        super.onPause();
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        switch (id) {
            case R.id.bt_exitStandby:
                beginResume(BiDormancyEndReport.TYPE_INPUT);
                break;
            case R.id.tv_chargingMsg:
                Log.d(TAG, "onClick: ");
                if (mIsCharging && !mIsChargingNormal)
                    showChargingWarningDialog();
                break;
            case R.id.butt_reboot:
                reboot();
                break;
        }
    }

    private void beginResume(int typeInput) {
        ControlManager.getControlManager().sendMessageToModule(ModuleDef.LOCAL_MESSAGE_STOP_STANDBY);
        new BiDormancyEndReport().addEndType(typeInput).report();
    }

    private void reboot(){
        Intent intent = new Intent(Intent.ACTION_REBOOT);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        ApplicationWrapper.getContext().startActivity(intent);
    }

    private void radarRetryFail(){
        Log.d(TAG, " radarRetryFail:: mRadarRetryTimes = " + mRadarRetryTimes);
        mButtReboot.setVisibility(View.VISIBLE);
        mTv_message.setText(getString(R.string.radar_fail));
        mButton.setVisibility(View.GONE);
        if (mTimer != null){
            mTimer.cancel();
            mTimer = null;
        }
    }
}