<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ * Copyright (C) 2017 OrionStar Technology Project
  ~ *
  ~ * Licensed under the Apache License, Version 2.0 (the "License");
  ~ * you may not use this file except in compliance with the License.
  ~ * You may obtain a copy of the License at
  ~ *
  ~ *      http://www.apache.org/licenses/LICENSE-2.0
  ~ *
  ~ * Unless required by applicable law or agreed to in writing, software
  ~ * distributed under the License is distributed on an "AS IS" BASIS,
  ~ * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ * See the License for the specific language governing permissions and
  ~ * limitations under the License.
-->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          package="com.ainirobot.home"
          android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.BROADCAST_STICKY"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <!-- 获取手机录音机使用权限，听写、识别、语义理解需要用到此权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>
    <!-- 读取网络信息状态 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <!-- 在SDCard中创建与删除文件的权限 -->
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>
    <!-- 往SDCard写入数据的权限 -->
    <uses-permission android:name="android.permission.RECOVERY" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <!-- SDCard读数据的权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.CAMERA"/>

    <uses-feature android:name="android.hardware.camera"/>

    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.WRITE_SETTINGS"/>
    <uses-permission android:name="android.permission.SET_PREFERRED_APPLICATIONS"/>
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS"/>
    <uses-permission android:name="android.permission.MASTER_CLEAR" />
    <uses-permission android:name="android.permission.GET_PACKAGE_SIZE" />
    <uses-permission android:name="android.permission.GET_META_DATA" />


    <application
        android:name=".ApplicationWrapper"
        android:allowBackup="true"
        android:icon="@drawable/orion_home_icon"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:supportsRtl="true">
        <activity
            android:name=".LaunchActivity"
            android:configChanges="density|screenLayout|screenSize|keyboardHidden|keyboard|orientation|mcc|mnc|locale|layoutDirection"
            android:launchMode="singleInstance"
            android:screenOrientation="fullSensor"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.HOME"/>
            </intent-filter>
            <intent-filter>
                <action android:name="com.orion.meissa.videocall.rvms"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>

        <service
            android:name=".service.ModuleService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.ainirobot.home.SERVICE"/>
            </intent-filter>
        </service>
        <service
            android:name=".service.SkillService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.ainirobot.home.SERVICE"/>
            </intent-filter>
        </service>
        <service android:name=".ota.service.DowngradeService"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.ainirobot.home.DOWNGRADE_SERVICE"/>
            </intent-filter>
        </service>

        <provider
            android:name=".data.DBProvider"
            android:authorities="com.ainirobot.home"
            android:exported="true"/>

        <provider
            android:authorities="com.ainirobot.home.ota.downgradeprovider"
            android:name="com.ainirobot.home.ota.database.DowngradeProvider"
            android:exported="false"/>

        <activity android:name=".SystemActivity"
                  android:configChanges="density|screenLayout|screenSize|keyboardHidden|keyboard|orientation|mcc|mnc|locale|layoutDirection"
                  android:launchMode="singleInstance"
                  android:screenOrientation="fullSensor"
                  android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
    </application>
</manifest>
