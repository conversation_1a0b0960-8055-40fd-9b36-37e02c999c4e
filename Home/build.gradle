// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext{
        compileSdkVersion = 28
        buildToolsVersion = "28.0.3"
        minSdkVersion = 23
        targetSdkVersion = 28

        // unit test
        testInstrumentationRunner = "android.support.test.runner.AndroidJUnitRunner"
        junit = 'junit:junit:4.12'
        androidJUnit = 'com.android.support.test:runner:1.0.2'
        expressoCore = 'com.android.support.test.espresso:espresso-core:3.0.2'
    }

    repositories {
        maven { url 'https://maven.aliyun.com/repository/google/' }
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin/' }
        google()
        jcenter()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:3.6.3'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        maven { url 'https://maven.aliyun.com/repository/google/' }
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin/' }
        google()
        jcenter()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
